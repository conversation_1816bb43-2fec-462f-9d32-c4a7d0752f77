package com.saidigital.tmp.services.user.business.shared.adapter.keycloak;

import com.saidigital.tmp.services.user.business.shared.adapter.keycloak.entity.KeycloakTenantContext;
import com.saidigital.tmp.services.user.business.shared.adapter.keycloak.entity.KeycloakUser;
import com.saidigital.tmp.services.user.business.shared.domain.OAuthClientAccount;
import com.saidigital.tmp.services.user.business.shared.domain.User;
import com.saidigital.tmp.services.user.business.shared.port.FindUserPort;
import org.keycloak.admin.client.resource.UserResource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class KeycloakSharedAdapter implements FindUserPort {

    @Value("${keycloak.serverUrl}")
    private String keycloakServerUrl;

    @Autowired
    private KeycloakTenantContext tenantContext;

    @Override
    public Optional<User> findUserByUsernameAndEmail(String username, String email) {
        var account = tenantContext.getClientAccount();
        var keycloak = tenantContext.getAdminKeycloak();
        var users = keycloak.realm(account.realm()).users().search(
            username, null, null, email, 0, 1
        );
        if (users.size() > 1) {
            throw new RuntimeException("invalid_query");
        }
        if (users.isEmpty()) {
            return Optional.empty();
        }
        var user = users.getFirst();
        return Optional.of(new KeycloakUser(user, account.tenantId()));
    }

    @Override
    public Optional<User> findUserById(String userId) {
        var account = tenantContext.getClientAccount();
        var keycloak = tenantContext.getAdminKeycloak();
        var user = keycloak.realm(account.realm())
            .users().get(userId);
        return Optional.ofNullable(user.toRepresentation())
            .map(ur -> new KeycloakUser(ur, account.tenantId()));
    }

    // TODO: Cacheable here
    public UserResource getUserResource(User user, OAuthClientAccount account) {
        var keycloak = tenantContext.getAdminKeycloak();
        return keycloak.realm(account.realm()).users().get(user.id());
    }

}
