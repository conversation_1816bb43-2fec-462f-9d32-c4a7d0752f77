package com.saidigital.tmp.services.user.business.forgotpassword.adapter.services.messaging;

import com.saidigital.tmp.services.user.business.forgotpassword.adapter.services.messaging.wsdto.EmailNotificationFormRest;
import com.saidigital.tmp.services.user.business.forgotpassword.port.SendForgotPasswordEmailPort;
import com.saidigital.tmp.services.user.config.services.MessagingServiceConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.Map;

@Service
public class MessagingServiceAdapter implements SendForgotPasswordEmailPort {

    private static final Logger log = LoggerFactory.getLogger(MessagingServiceAdapter.class);
    @Autowired
    @Qualifier("messagingServiceWebClient")
    private WebClient webClient;

    @Autowired
    private MessagingServiceConfig config;

    @Override
    public void sendEmail(String tenantId, String destination, String username, String changePwdUrl) {
        var sendEmailApiConfig = config.getRestApi().getSendEmail();
        var forgotPasswordTemplateConfig = config.getMessage().getTemplates().getEmail().getForgotPassword();
        webClient.method(HttpMethod.valueOf(sendEmailApiConfig.getMethod()))
            .uri(sendEmailApiConfig.getPath())
            .bodyValue(new EmailNotificationFormRest(
                tenantId,
                forgotPasswordTemplateConfig.getTemplateId(),
                forgotPasswordTemplateConfig.getDefaultLocale(),
                forgotPasswordTemplateConfig.getFrom(),
                destination,
                Map.of(
                    "username", username,
                    "changePwdUrl", changePwdUrl
                )))
            .exchangeToMono(resp -> {
                log.debug("Sent Forgot Password Email to {} [tenantId={},dest={}]", username, tenantId, destination);
                return Mono.empty();
            })
            .subscribe();
    }
}
