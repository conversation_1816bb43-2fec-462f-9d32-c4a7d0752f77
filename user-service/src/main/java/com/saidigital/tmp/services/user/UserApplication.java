package com.saidigital.tmp.services.user;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Info;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;

import com.saidigital.tmp.services.user.business.shared.adapter.postgres.entity.User;
import com.saidigital.tmp.services.user.business.shared.adapter.postgres.repository.UserRepository;

@SpringBootApplication
@OpenAPIDefinition(info =
	@Info(title = "User API", version = "1.0", description = "Documentation User API v1.0")
)
public class UserApplication {

	public static void main(String[] args) {
		SpringApplication.run(UserApplication.class, args);
	}
	
	@Bean
	UserRepository repository() {
		UserRepository repository = new UserRepository();
		repository.add(new User(1L, 1L, "<PERSON>", 34, "Analyst"));
		repository.add(new User(1L, 1L, "<PERSON>", 37, "Manager"));
		repository.add(new User(1L, 1L, "<PERSON>", 26, "Developer"));
		repository.add(new User(1L, 2L, "Anna London", 39, "Analyst"));		
		repository.add(new User(1L, 2L, "Patrick Dempsey", 27, "Developer"));
		repository.add(new User(2L, 3L, "Kevin Price", 38, "Developer"));		
		repository.add(new User(2L, 3L, "Ian Scott", 34, "Developer"));
		repository.add(new User(2L, 3L, "Andrew Campton", 30, "Manager"));
		repository.add(new User(2L, 4L, "Steve Franklin", 25, "Developer"));
		repository.add(new User(2L, 4L, "Elisabeth Smith", 30, "Developer"));
		return repository;
	}
	
}
