package com.saidigital.tmp.services.user.business.forgotpassword;

import com.saidigital.tmp.services.user.business.forgotpassword.port.ChangePasswordPort;
import com.saidigital.tmp.services.user.business.forgotpassword.port.GenerateForgotPasswordUrlPort;
import com.saidigital.tmp.services.user.business.forgotpassword.port.SendForgotPasswordEmailPort;
import com.saidigital.tmp.services.user.business.forgotpassword.port.VerifyForgotPasswordPort;
import com.saidigital.tmp.services.user.business.shared.domain.User;
import com.saidigital.tmp.services.user.business.shared.port.FindUserPort;
import com.saidigital.tmp.services.user.business.shared.port.GetOAuthClientAccountPort;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.Optional;

@Service
public final class ForgotPasswordUseCase {

    @Autowired
    private GetOAuthClientAccountPort getOAuthClientAccountPort;

    @Autowired
    private SendForgotPasswordEmailPort sendForgotPasswordEmailPort;

    @Autowired
    private FindUserPort findUserPort;

    @Autowired
    private GenerateForgotPasswordUrlPort generateForgotPasswordUrlPort;

    @Autowired
    private VerifyForgotPasswordPort verifyForgotPasswordPort;

    @Autowired
    private ChangePasswordPort changePasswordPort;


    public void sendForgotPasswordEmail(SendForgotPasswordEmailInput input) {
        String username = input.username();
        String email = input.email();

        Optional<User> userOpt = findUserPort.findUserByUsernameAndEmail(username, email);
        if (userOpt.isEmpty()) {
            throw new RuntimeException("user_not_found");
        }
        User user = userOpt.get();

        String changePwdUrl = generateForgotPasswordUrlPort.generateUrl(user);

        sendForgotPasswordEmailPort.sendEmail(
            user.tenantId(), user.email(), user.username(), changePwdUrl
        );
    }

    public void verifyForgotPassword(VerifyForgotPasswordInput input) {
        String userId = input.userId();
        Instant exp = input.expiredAt();
        String sig = input.sig();

        Optional<User> userOpt = findUserPort.findUserById(userId);
        if (userOpt.isEmpty()) {
            throw new RuntimeException("user_not_found");
        }
        User user = userOpt.get();

        verifyForgotPasswordPort.verifyForgotPassword(user, exp, sig, Instant.now());
    }

    public void changeForgotPassword(ChangeForgotPasswordInput input) {
        String userId = input.userId();
        String newPassword = input.newPassword();
        Instant exp = input.expiredAt();
        String sig = input.sig();

        Optional<User> userOpt = findUserPort.findUserById(userId);
        if (userOpt.isEmpty()) {
            throw new RuntimeException("user_not_found");
        }
        User user = userOpt.get();

        verifyForgotPasswordPort.verifyForgotPassword(user, exp, sig, Instant.now());
        changePasswordPort.changePassword(user, newPassword);
    }

    public record SendForgotPasswordEmailInput(String username, String email) {}

    public record VerifyForgotPasswordInput(String userId, Instant expiredAt, String sig) {}

    public record ChangeForgotPasswordInput(String userId, String newPassword, Instant expiredAt, String sig) { }


}
