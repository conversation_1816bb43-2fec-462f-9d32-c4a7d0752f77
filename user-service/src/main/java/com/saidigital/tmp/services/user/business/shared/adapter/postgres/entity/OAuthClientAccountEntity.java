package com.saidigital.tmp.services.user.business.shared.adapter.postgres.entity;


import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "oauth_client_account")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OAuthClientAccountEntity {

    @Id
    @Column(name = "tenant_id", nullable = false)
    private String tenantId;

    @Column(name = "realm", nullable = false)
    private String realm;

    @Column(name = "client_id", nullable = false)
    private String clientId;

    @Column(name = "client_secret", nullable = false)
    private String clientSecret;



}
