package com.saidigital.tmp.services.user.config;

import com.saidigital.tmp.services.user.config.services.MessagingServiceConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

@Configuration
public class WebClientConfig {


    @Bean
    public WebClient messagingServiceWebClient(MessagingServiceConfig config,
                                               WebClient.Builder builder) {
        return builder
            .baseUrl(config.getUrl())
            .defaultHeader("Accept", "application/json")
            .build();
    }
}
