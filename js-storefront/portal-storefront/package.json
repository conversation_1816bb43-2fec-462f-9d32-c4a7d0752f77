{"name": "portal-storefront", "version": "0.0.0", "scripts": {"start": "ng serve", "start:dev": "ng serve --open --host 0.0.0.0 --disable-host-check --ssl=false", "start:debug": "ng serve --proxy-config proxy.conf.json --open --host 0.0.0.0 --disable-host-check=true --source-map", "build": "ng build", "build:dev": "rm -rf dist && ng build", "build:stg": "rm -rf dist && ng build --configuration stage", "build:prod": "rm -rf dist && ng build --configuration production", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr:portal-storefront": "node dist/portal-storefront/server/server.mjs", "build:ssr": "ng build", "prerender": "ng run portal-storefront:prerender"}, "private": true, "dependencies": {"@angular/animations": "^17.3.0", "@angular/cdk": "^17.3.10", "@angular/common": "^17.3.0", "@angular/compiler": "^17.3.0", "@angular/core": "^17.3.0", "@angular/forms": "^17.3.0", "@angular/material": "^17.3.10", "@angular/material-moment-adapter": "^17.3.10", "@angular/platform-browser": "^17.3.0", "@angular/platform-browser-dynamic": "^17.3.0", "@angular/platform-server": "^17.3.0", "@angular/router": "^17.3.0", "@angular/ssr": "^17.3.11", "@danielmoncada/angular-datetime-picker": "^17.0.0", "@googlemaps/js-api-loader": "^1.16.8", "@ngx-translate/core": "^16.0.3", "@ngx-translate/http-loader": "^16.0.0", "ag-grid-angular": "^33.1.1", "ag-grid-community": "^33.1.1", "echarts": "^5.6.0", "express": "^4.18.2", "lodash-es": "^4.17.21", "moment": "^2.18.1", "moment-timezone": "^0.5.47", "ngx-echarts": "^19.0.0", "portal-storefront": "file:", "rxjs": "~7.8.0", "tslib": "^2.3.0", "uuid": "^11.1.0", "zone.js": "~0.14.3"}, "devDependencies": {"@angular-devkit/build-angular": "^17.3.11", "@angular/cli": "^17.3.11", "@angular/compiler-cli": "^17.3.0", "@types/express": "^4.17.17", "@types/google.maps": "^3.58.1", "@types/jasmine": "~5.1.0", "@types/lodash-es": "^4.17.12", "@types/node": "^18.18.0", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.4.2"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}