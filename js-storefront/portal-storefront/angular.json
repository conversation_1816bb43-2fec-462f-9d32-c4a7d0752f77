{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"portal-storefront": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/portal-storefront", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["@angular/material/prebuilt-themes/indigo-pink.css", "src/styles.scss"], "scripts": [], "server": "src/main.server.ts", "prerender": false, "ssr": {"entry": "server.ts"}, "allowedCommonJsDependencies": ["moment", "moment-timezone"]}, "configurations": {"production": {"optimization": true, "sourceMap": {"scripts": false, "styles": false, "vendor": false}, "budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "10kb", "maximumError": "20kb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}, "stage": {"optimization": true, "outputHashing": "all", "sourceMap": {"scripts": false, "styles": false, "vendor": false}, "extractLicenses": false, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.stage.ts"}]}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "portal-storefront:build:production"}, "development": {"buildTarget": "portal-storefront:build:development"}, "stage": {"buildTarget": "portal-storefront:build:stage"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "portal-storefront:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["@angular/material/prebuilt-themes/indigo-pink.css", "src/styles.scss"], "scripts": []}}}}}, "cli": {"analytics": false}}