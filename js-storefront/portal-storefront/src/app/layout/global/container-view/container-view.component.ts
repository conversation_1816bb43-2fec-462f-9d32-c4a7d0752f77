import { Component, effect } from '@angular/core';
import { RouterModule } from '@angular/router';
import { GlobalModule } from '../global.module';
import { FooterComponent } from '../footer/footer.component';
import { LoaderComponent } from '../loader/loader.component';
import { LoadingService } from '../../../core/services';
import { CommonModule } from '@angular/common';
import { LoadingComponent } from '../loading/loading.component';

@Component({
  selector: 'app-container-view',
  templateUrl: './container-view.component.html',
  standalone: true,
  imports: [
    GlobalModule,
    RouterModule,
    FooterComponent,
    CommonModule,
    LoadingComponent
  ],
})
export class ContainerViewComponent {
  public isLoading: boolean = false;

  constructor(private loadingService: LoadingService) {
    effect(() => {
      this.isLoading = this.loadingService.isLoading;
    });
  }
}
