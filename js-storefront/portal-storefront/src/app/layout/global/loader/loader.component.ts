import { Component, OnInit, <PERSON><PERSON><PERSON><PERSON>, effect } from '@angular/core';
import { LoadingService } from '../../../core/services';

@Component({
  selector: 'app-loader',
  templateUrl: './loader.component.html',
  standalone: true,
})
export class LoaderComponent implements OnInit {
  public show: boolean = true;

  constructor(private loadingService: LoadingService) {
    effect(() => {
      this.show = this.loadingService.isLoading;
    });
  }

  ngOnInit() {}

  ngOnDestroy() {}
}
