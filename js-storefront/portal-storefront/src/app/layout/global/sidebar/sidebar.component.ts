import { Component, inject, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { MatTooltipModule} from '@angular/material/tooltip';
import { NavigationEnd, Router, RouterModule } from '@angular/router';
import { LISTOPTIONPORTAL, SIDENAV, PERMISSIONS_CODE } from '../../../core/constants';
import { ScreenSizeService } from '../../../core/services';
import { CommonModule } from '@angular/common';
import { AuthService } from '../../../core/services/auth.service';
import { UserService } from '../../../core/services/user';
import { filter, Subscription } from 'rxjs';
import { NotificationsComponent } from './notifications/notifications.component';
import { NotificationService } from '../../../core/services/sidebar';
import { IconModule } from '../../../core/icon/icon.module';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ROLES } from '../../../core/constants/roles.const';
import { ActionModal, Portal, PortalName } from '../../../core/enums';
import { DialogConfirmComponent } from '../../../core/shared';
import { MatDialog } from '@angular/material/dialog';

@Component({
  selector: 'app-sidebar',
  templateUrl: 'sidebar.component.html',
  standalone: true,
  imports: [
    RouterModule,
    CommonModule,
    TranslateModule,
    NotificationsComponent,
    IconModule,
    MatTooltipModule,
  ],
  providers: [ScreenSizeService, AuthService, NotificationService],
})
export class SidebarComponent implements OnInit, OnDestroy {
  readonly SIDENAV = SIDENAV;
  isOpenMenuMobile = false;
  isOpenLogout = false;
  isVisible = false;
  portalName = Portal.IOT;
  portal = Portal;

  notificationCount = 0;
  browserName: string = '';

  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;

  screenSizeService = inject(ScreenSizeService);
  authService = inject(AuthService);
  notificationService = inject(NotificationService);
  router = inject(Router);
  userService = inject(UserService);
  dialog = inject(MatDialog);
  translateService = inject(TranslateService);

  userInfo: any;
  sidenavByRoles: any = [];
  subscription = new Subscription();
  firstSegment: string = '';
  toggleSwitchPortal: boolean = false;
  isUserHasMultipleRoles: boolean = false;
  listOption: any = LISTOPTIONPORTAL;

  ngOnInit(): void {
    this.userInfo = this.userService.loadRolesFromStorage();
    const checkUserRoles = this.userService.checkUserRoles(this.userInfo?.roles || []);
    const isUserHasMultipleRoles = this.userService.checkUserMultipleRoles(checkUserRoles);
    this.isUserHasMultipleRoles = isUserHasMultipleRoles;
    if (isUserHasMultipleRoles) {
      const path = window.location.pathname;
      const firstSegment = path.split('/').filter(Boolean)[0];
      if (firstSegment && (firstSegment === PortalName.Claims || firstSegment === PortalName.Insurance)) {
        this.firstSegment = firstSegment;
      } else {
        this.firstSegment = '';
      }
      this.listOption[0].isShow = checkUserRoles.insurance;
      this.listOption[1].isShow = checkUserRoles.claims;
      this.listOption[2].isShow = checkUserRoles.iot;
    }
    this.sidenavByRoles = this.getSidenavByRoles(this.userInfo?.roles || [], this.firstSegment, isUserHasMultipleRoles);
    this.portalName = this.getPortal(this.userInfo?.roles || [], this.firstSegment, isUserHasMultipleRoles);
    this.browserName = this.getBrowserName();
    // Calim portal does not have notification
    if (this.portalName !== Portal.Claims) {
      this.fetchNotificationCount();
    }
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  fetchNotificationCount(): void {
    this.notificationService.getNotificationCount().subscribe({
      next: (count) => {
        this.notificationCount = count;
      },
      error: (err) => {
        console.error('Failed to fetch notification count:', err);
        this.notificationCount = 0;
      },
    });
  }

  hasNotifications(): boolean {
    return this.notificationCount > 0;
  }

  displayNotificationCount(): string {
    return this.notificationCount > 99
      ? '99+'
      : this.notificationCount.toString();
  }

  showNotification(): void {
    this.isVisible = true;
    const contentView = document.querySelector('.content-view') as HTMLElement;
    if (contentView) {
      contentView.style.overflow = 'hidden';
    }
    this.fetchNotificationCount();
  }

  handleNotificationClose(): void {
    this.isVisible = false;
  }

  logout(): void {
    this.authService.logout();
  }

  getBrowserName() {
    const agent = window.navigator.userAgent.toLowerCase()
    switch (true) {
      case agent.indexOf('edge') > -1:
        return 'edge';
      case agent.indexOf('opr') > -1 && !!(<any>window).opr:
        return 'opera';
      case agent.indexOf('chrome') > -1 && !!(<any>window).chrome:
        return 'chrome';
      case agent.indexOf('trident') > -1:
        return 'ie';
      case agent.indexOf('firefox') > -1:
        return 'firefox';
      case agent.indexOf('safari') > -1:
        return 'safari';
      default:
        return 'other';
    }
  }

  /**
   * If the roles were defined in SIDENAV, then the user will only see the sidenav items that match their roles.
   * @param roles the roles of the current user
   * @returns sidebar items that match the user's roles
   */
  getSidenavByRoles(userRoles: string[], firstSegment?: string, isUserNotMutileRoles?: boolean): any {
    return this.SIDENAV.filter((item) => {
      // Base on the roles, the user will only see all sidenav items that match their roles for per portal
      if(item.baseUrl === firstSegment || !isUserNotMutileRoles) {
        if (item.roles && item.roles.length) {
          return userRoles.some((role) => item.roles.includes(role));
        }
      }
      return false;
    });
  }

  getPortal(userRoles: string[], firstSegment?: string, isUserNotMutileRoles?: boolean) {
    if ((userRoles.includes(ROLES.COORDINATORGROUP) || userRoles.includes(ROLES.INSURANCEGROUP))
      && (firstSegment === PortalName.Insurance || !isUserNotMutileRoles)) {
      return Portal.Insurance;
    }
    if ((userRoles.includes(ROLES.CASHIERGROUP) || userRoles.includes(ROLES.MARKETINGGROUP) || userRoles.includes(ROLES.TREASURYGROUP) || userRoles.includes(ROLES.CRDEMPLOYEEGROUP))
    && (firstSegment === PortalName.Claims || !isUserNotMutileRoles)) {
      return Portal.Claims;
    }
    return Portal.IOT;
  }

  rediretToPortal(url: string) {
    this.router.navigate([url]);
  }

  popupConfirmSwitchPortal(url: string) {
  const dialogRef = this.dialog.open(DialogConfirmComponent, {
    width: '530px',
    data: {
      title: this.translateService.instant('rolePicker.popup.title'),
      icon: 'ic-switch-portal',
      confirmMsg: this.translateService.instant(
        'rolePicker.popup.description'
      ),
      cancelBtn: this.translateService.instant('common.cancel'),
      submitBtn: this.translateService.instant('common.switch'),
    },
    // autoFocus: true,
  });
  dialogRef
    .afterClosed()
    .pipe(filter((result: any) => result?.action === ActionModal.Submit))
    .subscribe((result) => {
      if (result) {
        this.rediretToPortal(url);
      }
    });
  }
}
