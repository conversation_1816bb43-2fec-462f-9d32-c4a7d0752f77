import { Routes } from '@angular/router';
import { LoginComponent } from './features/auth/login/login.component';
import { AuthGuard } from './core/services/auth.guard';
import { ChangePasswordComponent } from './features/change-password/change-password.component';
import { ResetPasswordComponent } from './features/auth/reset-password/reset-password.component';
import { AuthLayoutComponent } from './features/auth/auth-layout.component';
import { ContainerViewComponent } from './layout/global/container-view/container-view.component';
import { AuthenticationComponent } from './features/auth/authentication/authentication.component';

import { PermissionsGuard } from './core/services/guards';
import { PERMISSIONS_CODE } from './core/constants';
import { RolePickerComponent } from './features/auth/role-picker/role-picker.component';
import { RolePickerGuard } from './core/services/guards/role-picker.guard';
import { DashboardComponent } from './portals/iot-portal/features/dashboard/dashboard.component';
import { SubscriptionsComponent } from './portals/iot-portal/features/subscriptions/subscriptions.component';
import { SubscriptionDetailComponent } from './portals/iot-portal/features/subscriptions/subscription-detail/subscription-detail.component';
import { DeviceListComponent } from './portals/iot-portal/features/device/device-list/device-list.component';
import { DeviceDetailComponent } from './portals/iot-portal/features/device/device-detail/device-detail.component';
import { VehicleListComponent } from './portals/iot-portal/features/vehicle/vehicle-list/vehicle-list.component';
import { CustomerDetailComponent, CustomersComponent, ReportsComponent, TicketListComponent } from './portals/iot-portal/features';
import { VehicleDetailComponent } from './portals/iot-portal/features/vehicle/vehicle-detail/vehicle-detail.component';
import { TicketDetailComponent } from './portals/iot-portal/features/ticket/ticket-detail/ticket-detail.component';
import { VehicleModelComponent } from './portals/iot-portal/features/vehicle-model/vehicle-model.component';
import { B2bCalVehiclesComponent } from './portals/iot-portal/features/b2b-cal-vehicles/b2b-cal-vehicles.component';
import { WarningMasterComponent } from './portals/iot-portal/features/warning-master/warning-master.component';
import { WarningMasterDetailComponent } from './portals/iot-portal/features/warning-master/warning-master-detail/warning-master-detail.component';

export const routes: Routes = [
  {
    path: '',
    redirectTo: 'login',
    pathMatch: 'full',
  },
  {
    path: 'authorize',
    component: RolePickerComponent,
    canActivate: [RolePickerGuard],
  },
  {
    path: '',
    component: AuthLayoutComponent,
    children: [
      { path: 'login', component: LoginComponent },
      { path: 'reset-password', component: ResetPasswordComponent },
      {
        path: 'authentication',
        component: AuthenticationComponent,
        canActivate: [AuthGuard],
      },
    ],
  },
  {
    path: '',
    component: ContainerViewComponent,
    canActivate: [AuthGuard],
    loadChildren: () => import('./portals/iot-portal/iot-portal-routing.module').then(m => m.IOT_PORTAL_ROUTES)
  },
  {
    path: 'insurance',
    canActivate: [AuthGuard],
    loadChildren: () => import('./portals/insurance-portal/insurance-portal-routing.module').then(m => m.INSURANCE_PORTAL_ROUTES)
  },
  {
    path: 'claims',
    canActivate: [AuthGuard],
    loadChildren: () => import('./portals/claims-portal/claim-portal-routing.module').then(m => m.CLAIM_PORTAL_ROUTES)
  },
  { path: '**', redirectTo: '/login' },
];
