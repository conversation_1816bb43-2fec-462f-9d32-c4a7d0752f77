import { CommonModule, DatePipe } from '@angular/common';
import { Component, EventEmitter, inject, Input, OnInit, Output } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { AgGridAngular } from 'ag-grid-angular';
import { ColDef } from 'ag-grid-community';
import { LoanItem } from '../../../core/interfaces/vehicle.interface';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { filter } from 'rxjs';
import { ActionModal, DateFormat } from '../../../core/enums';
import { ModalActionLoanComponent } from '../../b2b-cal-vehicles/modal-action-loan/modal-action-loan.component';
import { MatDialog } from '@angular/material/dialog';
import { CellActionComponent } from '../../b2b-cal-vehicles/cell-action/cell-action.component';
import { UserService } from '../../../core/services/user';
import { PERMISSIONS_CODE } from '../../../core/constants';

@Component({
  selector: 'app-loan-widget',
  standalone: true,
  imports: [CommonModule, MatIconModule, TranslateModule, AgGridAngular],
  providers: [DatePipe],
  templateUrl: './loan-widget.component.html',
  styleUrls: ['./loan-widget.component.scss'],
})
export class LoanWidgetComponent implements OnInit {
  @Input() rowData: LoanItem[];
  @Input() vin: string;
  @Output() refresh = new EventEmitter();
  defaultColDef: ColDef<any> = {
    resizable: false,
    valueFormatter: (params) => (params.value ? params.value : '-'),
    sortable: false,
    menuTabs: [],
  };
  private dialog = inject(MatDialog);
  private translateService = inject(TranslateService);
  private datePipe = inject(DatePipe);

  userService = inject(UserService);

  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;
  colDefs: ColDef<any>[] = [
    {
      headerName: this.translateService.instant('manageLoan.id'),
      headerValueGetter: () => this.translateService.instant('manageLoan.id'),
      field: 'id',
      flex: 1,
    },
    {
      headerName: this.translateService.instant('manageLoan.status'),
      headerValueGetter: () =>
        this.translateService.instant('manageLoan.status'),
      field: 'status',
      cellRenderer: (params) => {
        return params?.value && params?.value?.name
          ? params?.value?.name
          : '-';
      },
      flex: 1,
    },
    {
      headerName: this.translateService.instant('manageLoan.start'),
      headerValueGetter: () =>
        this.translateService.instant('manageLoan.start'),
      field: 'startDate',
      cellRenderer: (params) => {
        return params?.value
          ? this.datePipe.transform(params?.value, DateFormat.ShortDate)
          : '-';
      },
      flex: 1,
    },
    {
      headerName: this.translateService.instant('manageLoan.end'),
      headerValueGetter: () =>
        this.translateService.instant('manageLoan.end'),
      field: 'endDate',
      cellRenderer: (params) => {
        return params?.value
          ? this.datePipe.transform(params?.value, DateFormat.ShortDate)
          : '-';
      },
      flex: 1,
    },
    {
      headerName: '',
      maxWidth: 150,
      minWidth: 150,
     cellRenderer: CellActionComponent,
      cellRendererParams: {
        onClick: (type: string, data: any) => this.enableLoan(true, data),
      },
      cellClass: 'action-grid',
    },
  ];
  rowHeight = 50;
  loanActionForm: FormGroup = new FormGroup({
    id: new FormControl(''),
    vin: new FormControl(''),
    loanId: new FormControl('', Validators.required),
    status: new FormControl('ACTIVE', Validators.required),
    vehicleType: new FormControl(''),
    startDate: new FormControl('', Validators.required),
    endDate: new FormControl('', Validators.required),
    closed: new FormControl(false),
  });

  ngOnInit() {

  }

  enableLoan(isEdit: boolean = true, data?: any): void {
    this.loanActionForm.reset();
    this.loanActionForm.controls['closed'].clearValidators();
    this.loanActionForm.controls['closed'].updateValueAndValidity();
    if (!isEdit) {
      this.loanActionForm.patchValue({ status: 'ACTIVE' });
    } else {
      this.loanActionForm.patchValue(
        { 
          status: data?.status?.code,
          loanId: data?.id,
          endDate: new Date(data?.endDate),
          startDate: new Date(data?.startDate),
        }
      );
    }
    const dialogRef = this.dialog.open(ModalActionLoanComponent, {
      width: '680px',
      maxHeight: '90vh',
      disableClose: true,
      autoFocus: false,
      data: {
        isWidget: true,
        isEdit: isEdit,
        vin: this.vin,
        mainForm: this.loanActionForm,
      },
    });

    dialogRef
      .afterClosed()
      .pipe(filter((result: any) => result?.action === ActionModal.Submit))
      .subscribe(() => {
        this.refresh.emit();
      });
  }
}
