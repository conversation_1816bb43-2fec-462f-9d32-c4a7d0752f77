<div class="edit-towing-form">
  <h2 class="title-dialog">{{ "tickets.towingService" | translate }}</h2>
  <mat-dialog-content
    class="container-dialog"
    [class.loading]="loadingService.isLoading"
  >
    @if (loadingService.isLoading) {
    <div class="spinner"></div>
    }
    <form [formGroup]="mainForm">
      <div class="edit-towing-form__dealer-radio">
        <mat-radio-group formControlName="dealerSelection">
          @for (item of dealerRadio; track $index) {
          <mat-radio-button
            class="custom-radio-button"
            [value]="item?.code"
            (change)="onSelectDealerRadio($event)"
          >
            {{ item?.name }}
          </mat-radio-button>
          }
        </mat-radio-group>
      </div>

      <div class="dropdown-form-group">
        <label class="dropdown-form-group__label .font-weight-600">
          {{ "tickets.dealer" | translate }}<span class="required">*</span>
        </label>
        <mat-form-field>
          <mat-select
            panelClass="custom-scroll-panel custom-panel"
            formControlName="dealerCode"
            (selectionChange)="onSelectDealer($event)"
          >
            <mat-select-trigger>
              <span>
                {{ mainForm?.controls["dealer"]?.value?.displayName }}
              </span>
            </mat-select-trigger>
            <ng-template [ngIf]="dealerOptions && dealerOptions.length">
              <mat-option
                class="custom-option custom-option-towing-service"
                *ngFor="let option of dealerOptions"
                [value]="option?.name"
              >
                <div class="custom-option-box">
                  <div class="custom-option-box__content">
                    <span>{{ option?.displayName }}</span>
                    <span>{{ option?.addressDisplay }}</span>
                  </div>
                  <div class="custom-option-box__icon">
                    {{
                      option?.distanceToTargetPoint
                        ? option?.distanceToTargetPoint + ('common.km' | translate)
                        : ''
                    }}
                  </div>
                </div>
              </mat-option>
            </ng-template>
          </mat-select>
        </mat-form-field>
        @if(mainForm?.controls['dealer']?.value && dealerOptions &&
        dealerOptions.length) {
        <div class="edit-towing-form__icon">
          <p>
            <mat-icon matSuffix svgIcon="ic-phone"></mat-icon>
            {{ mainForm?.controls["dealer"]?.value?.phoneNumber }}
          </p>
          <p>
            <mat-icon matSuffix svgIcon="ic-address"></mat-icon>
            {{ mainForm?.controls["dealer"]?.value?.addressDisplay }}
          </p>

          @if(mainForm?.controls["dealer"]?.value?.distanceToTargetPoint) {
            <p>
              <mat-icon matSuffix svgIcon="ic-address"></mat-icon>
              {{
                (mainForm?.controls["dealer"]?.value?.distanceToTargetPoint || 0) + ('common.km' | translate)
              }}
            </p>
          }
          
        </div>
        }
      </div>

      <app-form-group
        [label]="'tickets.towingCompany' | translate"
        [control]="towingCompany"
        controlId="title"
        [required]="true"
        [errorMessage]="
          'common.requiredField'
            | translate : { field: ('tickets.towingCompany' | translate) }
        "
      ></app-form-group>

      <div class="row">
        <div class="col-6">
          <app-form-group
            [label]="'tickets.personInChargeName' | translate"
            [control]="personInChargeName"
            controlId="title"
            [required]="true"
            [errorMessage]="
              'common.requiredField'
                | translate
                  : { field: ('tickets.personInChargeName' | translate) }
            "
          ></app-form-group>
        </div>

        <div class="col-6">
          <app-form-group
            [label]="'tickets.mobileNumber' | translate"
            [control]="personInChargePhone"
            [type]="'tel'"
            controlId="title"
            [icon]="'ic-phone'"
            [required]="true"
            [errorMessage]="
              'common.requiredField'
                | translate : { field: ('tickets.mobileNumber' | translate) }
            "
          ></app-form-group>
        </div>
      </div>

      <div class="row date-time">
        <div class="col-6">
          <div class="form-group">
            <label class="form-group__label">
              {{ "tickets.estimatedTimeOfArrival" | translate
              }}<span class="required">*</span>
            </label>
            <div
              class="item-hours form-group__input"
              (click)="
                mainForm?.get('estTimeHH')?.value ? false : estTimeHH.focus()
              "
            >
              <input
                class="form-control item-hours__time-hh"
                type="text"
                formControlName="estTimeHH"
                #estTimeHH
                (input)="onInputEstTimeHH($event, 'estTimeHH')"
                maxlength="3"
                appInputNumber
              />
              <span class="item-hours__separator">:</span>
              <input
                type="text"
                (input)="onInputEstTimeMM($event, 'estTimeMM')"
                maxlength="3"
                appInputNumber
                formControlName="estTimeMM"
                #estTimeMM
                class="form-control item-hours__time-mm"
              />
              <mat-icon svgIcon="ic-clock"></mat-icon>
            </div>
          </div>
        </div>
        <div class="col-6">
          <div class="form-group item-date">
            <div class="form-group__input">
              <input
                class="form-control"
                [matDatepicker]="picker1"
                formControlName="estDate"
                (click)="picker1.open()"
                (keydown)="$event.preventDefault()"
                (keypress)="$event.preventDefault()"
              />
              <mat-icon
                svgIcon="ic-calendar"
                (click)="picker1.open()"
              ></mat-icon>
              <mat-datepicker #picker1></mat-datepicker>
            </div>
          </div>
        </div>
        <div
          *ngIf="
            (!mainForm.get('estTimeHH')?.valid &&
              mainForm.get('estTimeHH')?.touched) ||
            (!mainForm.get('estTimeMM')?.valid &&
              mainForm.get('estTimeMM')?.touched) ||
            (!mainForm.get('estDate')?.valid &&
              mainForm.get('estDate')?.touched)
          "
          class="form-group__error col-12"
        >
          <span
            *ngIf="mainForm.get('estTimeHH')?.errors?.['required'] || mainForm.get('estTimeMM')?.errors?.['required'] || mainForm.get('estDate')?.errors?.['required']"
          >
            {{
              "common.requiredField"
                | translate
                  : {
                      field: ("tickets.estimatedTimeOfArrival" | translate)
                    }
            }}
          </span>
        </div>
      </div>

      <div class="m-t-20">
        <div class="dropdown-form-group">
          <label class="dropdown-form-group__label">
            {{ "tickets.status" | translate }}<span class="required">*</span>
          </label>
          <mat-form-field>
            <mat-select
              panelClass="custom-scroll-panel custom-panel"
              [formControl]="status"
            >
              <mat-option
                class="custom-option"
                *ngFor="let option of statusList"
                [value]="option?.code"
              >
                {{ option?.name }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <div
          *ngIf="status.invalid && status.touched"
          class="form-group__error status-error"
        >
          {{
            "common.requiredField"
              | translate : { field: ("tickets.status" | translate) }
          }}
        </div>
      </div>
    </form>
  </mat-dialog-content>

  <mat-dialog-actions class="action-dialog edit-towing-form__actions">
    <button
      type="button"
      class="btn btn--outline cancel-btn"
      (click)="onCancel()"
    >
      {{ "common.cancel" | translate }}
    </button>
    <button
      class="btn btn--primary"
      type="submit"
      (click)="onSubmit()"
      [disabled]="mainForm?.invalid"
    >
      {{
        data.isEdit ? ("common.edit" | translate) : ("common.add" | translate)
      }}
    </button>
  </mat-dialog-actions>
</div>
