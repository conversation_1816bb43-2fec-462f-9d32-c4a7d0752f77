import { CommonModule, DatePipe } from '@angular/common';
import { Component, inject, Input, OnInit } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatRadioModule } from '@angular/material/radio';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { DialogConfirmComponent, FormGroupComponent } from '../../../../core/shared';
import { InputNumberDirective } from '../../../../core/directives/input-interger.directive';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MatInputModule } from '@angular/material/input';
import { OptionDropdown } from '../../../../core/interfaces';
import { TicketsService } from '../../../../core/services/tickets/ticket-detail.service';
import {
  DealerItem,
  MetadataTowingDropdown,
  PerferDealer,
  requestCreateTowingService,
  Ticket,
} from '../../../../core/interfaces/ticket-detail.interface';
import { ActionModal } from '../../../../core/enums';
import { LoadingService, NotificationService } from '../../../../core/services';
import { LoadingComponent } from '../../../../layout/global/loading/loading.component';
import { handleErrors } from '../../../../core/helpers';
import { finalize, Subscription } from 'rxjs';

@Component({
  selector: 'app-modal-create-edit',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    ReactiveFormsModule,
    FormGroupComponent,
    MatRadioModule,
    MatFormFieldModule,
    MatSelectModule,
    MatIconModule,
    InputNumberDirective,
    MatDatepickerModule,
    MatInputModule,
    LoadingComponent,
    MatDialogModule,
  ],
  providers: [
    provideNativeDateAdapter(),
    TicketsService,
    LoadingService,
    DatePipe,
    NotificationService,
  ],
  templateUrl: './modal-create-edit.component.html',
  styleUrls: ['./modal-create-edit.component.scss'],
})
export class ModalCreateEditComponent implements OnInit {
  data = inject(MAT_DIALOG_DATA);
  dialogRef = inject(MatDialogRef<ModalCreateEditComponent>);
  vin: string;
  mainForm!: FormGroup;
  dealerOptions: DealerItem[] = [];
  dealerRadio: OptionDropdown[] = [];
  statusList: OptionDropdown[] = [];
  isLoading: boolean = true;
  mainSubscription = new Subscription();
  private ticketsServiceDetails = inject(TicketsService);
  private datePipe = inject(DatePipe);
  private notificationService = inject(NotificationService);
  private translateService = inject(TranslateService);
  public loadingService = inject(LoadingService);

  dialog = inject(MatDialog);

  ngOnInit() {
    this.mainForm = this.data.mainForm as FormGroup;
    this.vin = this.data.vin;
    this.getDealerList();
  }

  ngOnDestroy() {
    this.mainSubscription && this.mainSubscription.unsubscribe();
  }

  getDealerList() {
    this.loadingService.showLoader();
    this.mainSubscription.add(this.ticketsServiceDetails.getDealerList(this.vin).subscribe(
      (res: MetadataTowingDropdown) => {
        if (res) {
          this.dealerOptions = res.dealers;
          this.dealerRadio = res.dealerOptions;
          this.statusList = res.statuses;
        }
        this.loadingService.hideLoader();
      },
      (errorsRes) => {
        this.loadingService.hideLoader();
        handleErrors(errorsRes, this.notificationService);
      }
    ));
  }

  getPreferDealerList() {
    this.loadingService.showLoader();
    this.mainSubscription.add(this.ticketsServiceDetails.getPreferDealerList(this.vin).subscribe(
      (res: PerferDealer) => {
        if (res) {
          this.mainForm.controls['dealerCode'].patchValue(res.name);
          this.mainForm.controls['dealer'].patchValue(res);
        }
        this.loadingService.hideLoader();
      },
      (error) => {
        handleErrors(error, this.notificationService);
        this.loadingService.hideLoader();
      }
    ));
  }

  public get personInChargeName(): FormControl {
    return this.mainForm.get('personInChargeName') as FormControl;
  }

  public get towingCompany(): FormControl {
    return this.mainForm.get('towingCompany') as FormControl;
  }

  public get personInChargePhone(): FormControl {
    return this.mainForm.get('personInChargePhone') as FormControl;
  }

  public get status(): FormControl {
    return this.mainForm.get('status') as FormControl;
  }

  onSubmit() {
    const dataForm = this.mainForm.value;
    const dataSend: requestCreateTowingService = {
      vin: this.vin,
      dealerCode: dataForm?.dealerCode,
      towingCompany: dataForm?.towingCompany,
      personInChargeName: dataForm?.personInChargeName,
      personInChargePhone: dataForm?.personInChargePhone,
      eta:
        this.datePipe.transform(dataForm?.estDate, 'MM/dd/yyyy') +
        ' ' +
        dataForm?.estTimeHH +
        ':' +
        dataForm?.estTimeMM,
      status: dataForm?.status,
      dealerSelection: dataForm?.dealerSelection,
      distance: dataForm?.dealer?.distanceToTargetPoint || dataForm?.distance,
    };

    if (this.data.isEdit) {
      this.updateTowingService(dataSend, dataForm?.id);
    } else {
      if (this.data?.ticketId) {
        Object.assign(dataSend, {
          ticketID: this.data?.ticketId
        });
      }
      this.createTowingService(dataSend);
    }
  }

  updateTowingService(dataSend: requestCreateTowingService, id: string) {
    this.loadingService.showLoader();
    this.mainSubscription.add(this.ticketsServiceDetails.updateTowingService(dataSend, id).pipe(finalize(() => this.mainForm.reset())).subscribe(
      (res) => {
        if (res) {
          this.notificationService.showSuccess(
            this.translateService.instant('tickets.updateTowingServiceSuccess')
          );
          this.dialogRef.close({ action: ActionModal.Submit, data: res });
        }
        this.loadingService.hideLoader();
      },
      (error) => {
        handleErrors(error, this.notificationService);
        this.loadingService.hideLoader();
      }
    ));
  }

  createTowingService(dataSend: requestCreateTowingService) {
    this.loadingService.showLoader();
    this.mainSubscription.add(this.ticketsServiceDetails.createTowingService(dataSend).pipe(finalize(() => this.mainForm.reset())).subscribe(
      (res) => {
        if (res) {
          this.notificationService.showSuccess(
            this.translateService.instant('tickets.createTowingServiceSuccess')
          );
          this.dialogRef.close({ action: ActionModal.Submit, data: res });
        }
        this.loadingService.hideLoader();
      },
      (error) => {
        handleErrors(error, this.notificationService);
        this.loadingService.hideLoader();
      }
    ));
  }

  onCancel() {
    this.mainForm.reset();
    this.dialogRef.close();
  }

  onInputEstTimeHH(event, controlName) {
    const value = event.target.value;
    if (value.length === 3) {
      value.substr(value.length - 2);
    }
    if (value.length === 1 && +value >= 0) {
      this.mainForm.controls[controlName].patchValue('0' + value);
      return;
    }
    if (value >= 24) {
      this.mainForm.controls[controlName].patchValue(
        '0' + value.substr(value.length - 1)
      );
      return;
    }
    if (value.length === 3 && +value.substr(value.length - 2) >= 24) {
      this.mainForm.controls[controlName].patchValue(
        '0' + value.substr(value.length - 1)
      );
      return;
    }
    if (value.length === 3 && +value.substr(value.length - 2) < 24) {
      this.mainForm.controls[controlName].patchValue(
        value.substr(value.length - 2)
      );
      return;
    }
  }

  onInputEstTimeMM(event, controlName) {
    const value = event.target.value;
    if (value.length === 3) {
      value.substr(value.length - 2);
    }
    if (value.length === 1 && +value >= 0) {
      this.mainForm.controls[controlName].patchValue('0' + value);
      return;
    }
    if (value >= 60) {
      this.mainForm.controls[controlName].patchValue(
        '0' + value.substr(value.length - 1)
      );
      return;
    }
    if (value.length === 3 && +value.substr(value.length - 2) >= 60) {
      this.mainForm.controls[controlName].patchValue(
        '0' + value.substr(value.length - 1)
      );
      return;
    }
    if (value.length === 3 && +value.substr(value.length - 2) < 60) {
      this.mainForm.controls[controlName].patchValue(
        value.substr(value.length - 2)
      );
      return;
    }
  }

  onSelectDealer(event) {
    const value = event.value;
    const dealer = this.dealerOptions.find((item) => item?.name === value);
    this.mainForm.controls['dealer'].patchValue(dealer);
  }

  onSelectDealerRadio(event) {
    const value = event.value;
    if (value === 'PREFER') {
      this.getPreferDealerList();
    } else {
      this.mainForm.controls['dealerCode'].patchValue('');
      this.mainForm.controls['dealer'].patchValue('');
    }
  }
}
