<div class="towing-service">
  <div class="towing-service__header">
    <mat-icon class="medium-icon" svgIcon="ic-towing-service"></mat-icon>
    <span>{{ "tickets.towingService" | translate }}</span>
    @if (userService.isHasPermission([PERMISSIONS_CODE.ECARE_TOWING_MANAGE])) {
      <div class="towing-service__header__action">
        @if (!towingServiceData) {
        <button class="btn-link" (click)="addTowingService()">
          <mat-icon
            svgIcon="ic-add-red"
            class="small-icon"
            aria-hidden="true"
          ></mat-icon>
          {{ "common.add" | translate }}
        </button>
        } @else {
        <button class="btn-link" (click)="editTowingService()">
          <mat-icon
            svgIcon="ic-edit"
            class="small-icon"
            aria-hidden="true"
          ></mat-icon>
          {{ "common.edit" | translate }}
        </button>

        <button
          class="btn-link towing-cancel-button"
          (click)="cancelTowingService()"
        >
          <mat-icon
            svgIcon="ic-checked"
            class="small-icon"
            aria-hidden="true"
          ></mat-icon>
          {{ "common.cancel" | translate }}
        </button>
        }
      </div>
    }
  </div>

  <div class="towing-service__body">
    @if (!towingServiceData) {
    <div class="towing-service__body--empty">
      {{ "tickets.noTowingService" | translate }}
    </div>
    } @else {
    <div class="towing-service__body__confirm">
      <div class="towing-service__body__confirm--box">
        <div class="towing-service__body__confirm--box__content">
          <span>Estimated arrive at: </span>
          <span>{{
            towingServiceData?.eta | date : dateFormat.FullDate : TimeZone.UTC8
          }}</span>
        </div>
        <div *ngIf="userService.isHasPermission([PERMISSIONS_CODE.ECARE_TOWING_MANAGE])" class="towing-service__body__confirm--box__action">
          <button class="btn-link" (click)="confirmTowingService()">
            <mat-icon
              svgIcon="ic-checked-red"
              class="small-icon"
              aria-hidden="true"
            ></mat-icon>
            {{ "tickets.complete" | translate }}
          </button>
        </div>
      </div>
      <div class="towing-service__body__confirm--box__date">
        @if(towingServiceData?.status?.code === 'ACTIVE') {
          {{ ('tickets.calledOn' | translate) }} {{ towingServiceData?.creationTime | date : dateFormat.FullDate : TimeZone.UTC8 }}
        } @else if(towingServiceData?.status?.code === 'COMPLETED') {
          {{ ('tickets.completedOn' | translate) }} {{ towingServiceData?.modifyTime | date : dateFormat.FullDate : TimeZone.UTC8}}
        }
        @else {
          {{ ('tickets.cancelledOn' | translate) }} {{ towingServiceData?.modifyTime | date : dateFormat.FullDate : TimeZone.UTC8}}
        }
      </div>
    </div>
    <div class="show-more-content" [class.hidden]="!showMore">
      <div class="show-more-item">
        <h2>Dealer info</h2>
        <div class="show-more-item__icon">
          <div>
            <mat-icon matSuffix svgIcon="ic-car-towing"></mat-icon>
            {{ towingServiceData?.dealer?.displayName }}
          </div>
          <div>
            <mat-icon matSuffix svgIcon="ic-phone"></mat-icon>
            {{ towingServiceData?.dealer?.phoneNumber }}
          </div>
          <div>
            <mat-icon matSuffix svgIcon="ic-address"></mat-icon>
            {{ towingServiceData?.dealer?.addressDisplay }}
          </div>
          <div>
            <mat-icon matSuffix svgIcon="ic-towing-way"></mat-icon>
            {{ towingServiceData?.dealer?.distanceToTargetPoint + ' ' + ('common.km' | translate) + " away" }}
          </div>
        </div>
      </div>
      <div class="show-more-item">
        <h2>Towing company</h2>
        <div class="show-more-item__icon">
          <div>
            <mat-icon matSuffix svgIcon="ic-towing-company"></mat-icon>
            {{ towingServiceData?.towingCompany }}
          </div>
          <div>
            <mat-icon matSuffix svgIcon="ic-towing-info"></mat-icon>
            {{ towingServiceData?.personInChargeName }}
          </div>
          <div>
            <mat-icon matSuffix svgIcon="ic-phone"></mat-icon>
            {{ towingServiceData?.personInChargePhone }}
          </div>
        </div>
      </div>
    </div>
    <div class="edit-towing-show-more">
      <button class="btn-link" (click)="toggleShowMore()">
        <mat-icon
          svgIcon="ic-down"
          class="small-icon"
          aria-hidden="true"
          [class.rotate]="showMore"
        ></mat-icon>
        {{ showMore ? "View Less" : "View Details" }}
      </button>
    </div>
    }
  </div>
</div>
