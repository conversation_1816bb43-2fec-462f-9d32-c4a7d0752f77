@import "../../../../styles/abstracts/variables";
.section-insurance-coverage {
  display: flex;
  flex-direction: column;
  gap: 25px;
  &__header {
    display: flex;
    align-items: center;
    gap: 12px;
    span {
      font-size: 22px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
      color: #101010;
    }
  }
  &__list {
    display: flex;
    align-items: flex-start;
    align-content: flex-start;
    gap: 20px 25px;
    align-self: stretch;
    flex-wrap: wrap;
    &__item {
      display: flex;
      flex-direction: column;
      width: calc(29% - 25px);
      &:last-child {
        width: calc(42% - 25px);
      }
      &--status {
        text-transform: lowercase;
        &::first-letter {
          text-transform: uppercase;
        }
      }
      span {
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        color: #101010;
        &:nth-child(1) {
          color: #3a3a3a;
        }
        &:nth-child(2) {
          font-size: 16px;
          font-weight: 600;
          line-height: 22px;
        }
        &:nth-child(3) {
          font-size: 12px;
          font-weight: 400;
          line-height: 18px;
          color: #808080;
        }
      }
      &--bold {
        font-size: 16px !important;
        color: #101010 !important;
        font-weight: 600 !important;
        line-height: 22px;
      }
    }
  }
}
