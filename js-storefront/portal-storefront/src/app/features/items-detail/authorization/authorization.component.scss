.section-authorization {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 12px;
    flex: 0 1 0;

    &__timer-inline {
        display: inline;
        margin-left: 4px;
    }
    
    &__content {
        margin-right: auto;
        display: flex;
        flex-direction: column;

        span {
            font-size: 16px !important;
            font-style: normal;
            font-weight: 600;
            line-height: 22px;
            color: #101010;
        }

        .small-text {
            span {
                font-size: 12px !important;
                font-style: normal !important;
                font-weight: 400 !important;
                line-height: 18px !important;
            }
        }
    }

    &__confirm {
        display: flex;
        align-items: center;
        gap: 5px;

        mat-icon {
            margin: 0;
        }
    }

    .text-pending {
        color: #266EF2 !important;
    }

    .text-approved {
        color: #2ABA6C !important;
    }

    .text-rejected {
        color: #EB0A1E !important;
    }

    .text-completed,
    .text-expired {
        color: #808080 !important;
    }
}