<div class="section-dtc">
  <div class="section-header">
    <mat-icon
      svgIcon="ic-dtc"
      class="medium-icon section-header--icon"
    ></mat-icon>
    <div class="section-header--content">
      <span>{{ "vehicle.dtc.title" | translate }}</span>
    </div>
  </div>
  <div class="section-dtc__body">
    <ng-container *ngIf="data && data.length; else noData">
      @for (item of data; track $index) {
      <div class="section-dtc__body--item">
        <div class="section-dtc__body--item__content" (click)="viewDetail(item)">
          <span>{{
            (item?.diagnosticsDetails &&
              item?.diagnosticsDetails.length &&
              item?.diagnosticsDetails[0]?.sid &&
              item?.diagnosticsDetails[0]?.sid.length &&
              item?.diagnosticsDetails[0]?.sid[0]?.dtc &&
              item?.diagnosticsDetails[0]?.sid[0]?.dtc.length &&
              item?.diagnosticsDetails[0]?.sid[0]?.dtc[0]?.dtcCode) ||
              ""
          }}</span>
          <span>{{
            (item?.diagnosticsDetails &&
              item?.diagnosticsDetails.length &&
              item?.diagnosticsDetails[0]?.sid &&
              item?.diagnosticsDetails[0]?.sid.length &&
              item?.diagnosticsDetails[0]?.sid[0]?.dtc &&
              item?.diagnosticsDetails[0]?.sid[0]?.dtc.length &&
              item?.diagnosticsDetails[0]?.sid[0]?.dtc[0]?.friendlyName) ||
              ""
          }}</span>
        </div>
      </div>
      }
    </ng-container>
    <ng-template #noData>
      <div class="section-no-data">{{ "vehicle.dtc.noData" | translate }}</div>
    </ng-template>
  </div>
</div>
