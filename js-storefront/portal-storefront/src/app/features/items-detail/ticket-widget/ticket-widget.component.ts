import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  inject,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { CreateTicketComponent } from '../create-ticket/create-ticket.component';
import {
  Ticket,
  Vehicle,
} from '../../../core/interfaces/ticket-detail.interface';
import {
  ActionModal,
  PriorityTicket,
  StatusTicketDetail,
} from '../../../core/enums';
import { filter } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { UserService } from '../../../core/services/user';
import { PERMISSIONS_CODE } from '../../../core/constants';
import { TypeTicketPipe } from '../../../core/pipes';

@Component({
  selector: 'app-ticket-widget',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    TranslateModule,
    CreateTicketComponent,
    TypeTicketPipe
  ],
  templateUrl: './ticket-widget.component.html',
  styleUrls: ['./ticket-widget.component.scss'],
})
export class TicketWidgetComponent implements OnInit {
  @Input() data: Ticket[];
  @Input() vehicle: Vehicle;
  @Input() vin: string;

  @Output() refreshTicket = new EventEmitter();
  priorityTicket = PriorityTicket;
  statusTicket = StatusTicketDetail;

  dialog = inject(MatDialog);
  router = inject(Router);
  activatedRoute = inject(ActivatedRoute);

  userService = inject(UserService);

  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;

  ngOnInit() {
    this.activatedRoute.queryParams.subscribe((params) => {
      const openCreateTicket = params['openCreateTicket'];
      if (openCreateTicket) {
        this.createTicket();
        // Remove the query parameter from the URL
        this.router.navigate([], {
          relativeTo: this.activatedRoute,
          queryParams: { openCreateTicket: null },
          queryParamsHandling: 'merge', 
          replaceUrl: true,
        });
      }
    });
  }

  viewTicketDetail(id) {
    if (
      this.userService.isHasPermission([
        PERMISSIONS_CODE.ECARE_TICKET_DETAIL_VIEW,
      ])
    ) {
      this.router.navigate(['tickets/' + id]);
    }
  }

  createTicket() {
    const dialogRef = this.dialog.open(CreateTicketComponent, {
      width: '850px',
      maxHeight: '90vh',
      disableClose: true,
      autoFocus: false,
      data: {
        createFromWidget: true,
        vehicle: this.vehicle,
        vin: this.vin,
      },
    });

    dialogRef
      .afterClosed()
      .pipe(filter((result: any) => result?.action === ActionModal.Submit))
      .subscribe(() => {
        this.refreshTicket.emit();
      });
  }
}
