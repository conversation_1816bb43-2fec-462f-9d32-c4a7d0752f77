import { CommonModule } from '@angular/common';
import { Component, inject, Input, OnInit } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { OwnerWidget } from '../../../core/interfaces';
import {
  LoadingService,
  NotificationService,
  VehicleService,
} from '../../../core/services';
import { finalize, Subscription } from 'rxjs';
import { handleErrors } from '../../../core/helpers';

@Component({
  selector: 'app-owner-widget',
  standalone: true,
  imports: [CommonModule, MatIconModule, TranslateModule],
  templateUrl: './owner-widget.component.html',
  styleUrls: ['./owner-widget.component.scss'],
})
export class OwnerWidgetComponent implements OnInit {
  @Input() data: OwnerWidget;
  @Input() vin: string = '';
  mainSubscription = new Subscription();
  private vehicleService = inject(VehicleService);
  private loadingService = inject(LoadingService);
  private notificationService = inject(NotificationService);

  ngOnInit() {}

  ngOnDestroy() {
    this.mainSubscription && this.mainSubscription.unsubscribe();
  }

  onVerifyEmail() {
    this.loadingService.showLoader();
    this.mainSubscription.add(
      this.vehicleService
        .verifyEmail(this.vin)
        .pipe(finalize(() => this.loadingService.hideLoader()))
        .subscribe((res) => {
          if (res?.code == 200) {
            this.notificationService.showSuccess("vehicle.ownerWidget.verifySent")
          } else {
            this.notificationService.showError(res?.message || '')
          }
        }, error => {
          handleErrors(error, this.notificationService);
        })
    );
  }
}
