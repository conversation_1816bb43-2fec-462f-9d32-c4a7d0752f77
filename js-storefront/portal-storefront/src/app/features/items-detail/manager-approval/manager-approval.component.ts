import { CommonModule } from '@angular/common';
import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  inject,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { IconModule } from '../../../core/icon/icon.module';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatDialog } from '@angular/material/dialog';

import {
  LoadingService,
  ManagerApprovalService,
  NotificationService,
  TicketsDataService,
} from '../../../core/services';
import { RequestDetailsComponent } from './request-details/request-details.component';
import {
  ApprovalStatus,
  Ticket,
} from '../../../core/interfaces/ticket-detail.interface';
import { ResponseRequestInfo } from '../../../core/interfaces';
import { filter, interval, Subject, Subscription, takeUntil } from 'rxjs';
import {
  ActionModal,
  AuthorizationStatus,
  DateFormat,
  ManagerApprovalStatus,
  ManagerApprovalTab,
  TypeTicket,
} from '../../../core/enums';
import { DialogConfirmComponent } from '../../../core/shared';
import { handleErrors } from '../../../core/helpers';
import { ActivatedRoute } from '@angular/router';
import { AuthorizationDialogComponent } from '../authorization/modal-authorization/authorization-dialog.component';
import { UserService } from '../../../core/services/user';
import { PERMISSIONS_CODE } from '../../../core/constants';

@Component({
  selector: 'app-manager-approval',
  standalone: true,
  imports: [CommonModule, IconModule, TranslateModule],
  templateUrl: './manager-approval.component.html',
  styleUrls: ['./manager-approval.component.scss'],
  providers: [NotificationService, ManagerApprovalService],
})
export class ManagerApprovalComponent implements OnInit, OnDestroy, OnChanges {
  @Input() set ticket(value: Ticket) {
    if (value) {
      this.ticketInfo = value;
      this.getRequestInfo();
    }
  }

  @Input() approvalStatus: ApprovalStatus | null;
  @Input() verifiedDate: string | null;

  @Output() refreshApprovalStatus = new EventEmitter<void>();

  dialog = inject(MatDialog);
  translateService = inject(TranslateService);
  loadingService = inject(LoadingService);
  notificationService = inject(NotificationService);
  managerApprovalService = inject(ManagerApprovalService);
  ticketsDataService = inject(TicketsDataService);
  userService = inject(UserService);
    
  route = inject(ActivatedRoute);
  cdr = inject(ChangeDetectorRef);

  readonly ManagerApprovalStatus = ManagerApprovalStatus;
  readonly ManagerApprovalTab = ManagerApprovalTab;

  readonly TypeTicket = TypeTicket;
  readonly DateFormat = DateFormat;

  readonly AuthorizationStatus = AuthorizationStatus;
  
  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;

  subscription = new Subscription();

  isRequest = false;

  requestInfo: ResponseRequestInfo;
  ticketInfo: Ticket;
  remoteImmobilizerToggle: boolean;

  selectedTab = ManagerApprovalTab.Authorization;

  remainingTime: string = '';
  ticketId: string;

  private destroy$ = new Subject<void>();

  ngOnInit(): void {
    this.ticketId = this.route.snapshot.paramMap.get('id') || '';
    this.updateStatusAuthor();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (
      changes['approvalStatus']?.currentValue?.status?.code !==
      changes['approvalStatus']?.previousValue?.status?.code
    ) {
      this.updateStatusAuthor();
    }
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
    this.destroy$.next();
    this.destroy$.complete();
  }

  updateStatusAuthor(): void {
    if (
      this.approvalStatus?.status?.code === AuthorizationStatus.Approved ||
      this.approvalStatus?.status?.code === AuthorizationStatus.Pending
    ) {
      this.startCountdown();
    }
  }

  sendRequest(): void {
    const dialogRef = this.dialog.open(RequestDetailsComponent, {
      width: '600px',
      maxHeight: '90vh',
      disableClose: true,
      autoFocus: false,
      data: {
        ticketType: this.ticketInfo?.type,
        ticketId: this.ticketInfo?.ticketID,
      },
    });

    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe(() => {
        this.getRequestInfo();
      });
  }

  getRequestInfo(): void {
    this.loadingService.showLoader();
    this.subscription.add(
      this.managerApprovalService
        .getRequestInfo(this.ticketInfo?.ticketID)
        .subscribe(
          (response) => {
            this.loadingService.hideLoader();
            this.requestInfo = response;
            this.isRequest = !!response;
            this.ticketsDataService.managerApprovalStatus$.next(this.requestInfo);
          },
          (error) => {
            this.loadingService.hideLoader();
          }
        )
    );
  }

  actionRequest(approved: 1 | 0): void {
    const dialogRef = this.dialog.open(DialogConfirmComponent, {
      width: '530px',
      data: {
        title: this.translateService.instant(
          approved === 1
            ? 'managerApproval.approveRequest'
            : 'managerApproval.rejectRequest'
        ),
        icon: 'ic-warning',
        confirmMsg: this.translateService.instant(
          approved === 1
            ? 'managerApproval.confirmApprove'
            : 'managerApproval.confirmReject'
        ),
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.confirm'),
      },
    });

    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe(() => {
        this.loadingService.showLoader();
        this.subscription.add(
          this.managerApprovalService
            .actionOnRequest(this.ticketInfo?.ticketID, approved)
            .subscribe(
              (response) => {
                this.loadingService.hideLoader();
                this.requestInfo = response;
                this.ticketsDataService.managerApprovalStatus$.next(this.requestInfo);
                this.isRequest = true;
                if (approved === 1) {
                  this.ticketsDataService.actionApproveRequest$.next(
                    response?.action
                  );
                }
              },
              (error) => {
                this.loadingService.hideLoader();
                handleErrors(error, this.notificationService);
              }
            )
        );
      });
  }

  startCountdown() {
    
    this.destroy$.next();
    this.destroy$.complete();
    this.destroy$ = new Subject<void>();
      
    if (this.approvalStatus?.status?.code === AuthorizationStatus.Approved) {
      this.notificationService.showSuccess(
        this.translateService.instant('ticket.authorizationApprovedMess')
      );
    }
    const validTill = Math.floor(
      new Date(this.approvalStatus?.validTill).getTime() / 1000
    );

    if (isNaN(validTill)) {
      console.error('Invalid triggerDate:', this.approvalStatus?.validTill);
      return;
    }

    interval(900)
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        const now = Math.floor(Date.now() / 1000);
        const timeLeft = validTill - now;
          
         if (timeLeft > 0) {
           const minutes = Math.floor(timeLeft / 60);
           const seconds = timeLeft % 60;

           const formattedMinutes = minutes.toString().padStart(2, '0');
           const formattedSeconds = seconds.toString().padStart(2, '0');

           this.remainingTime = `${formattedMinutes}:${formattedSeconds}`;
         } else {
           this.remainingTime = '';
           this.destroy$.next();
         }
      });
  }

  onVerifyNow() {
    const dialogRef = this.dialog.open(AuthorizationDialogComponent, {
      width: '530px',
      data: {
        title: this.translateService.instant('tickets.verificationTitlePopup'),
        icon: 'ic-verify',
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.confirm'),
        ticketId: this.ticketId,
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.refreshApprovalStatus.emit();

        setTimeout(() => {
          this.approvalStatus = {
            ...this.approvalStatus,
            status: { code: 'PENDING', name: 'Pending' },
            validTill: new Date(Date.now() + 2 * 60 * 1000).toISOString(),
          };

          this.startCountdown();
          this.cdr.detectChanges();
        }, 900);
      }
    });
  }

  changeSelectedTab(tab: ManagerApprovalTab): void {
    this.selectedTab = tab;
  }
}
