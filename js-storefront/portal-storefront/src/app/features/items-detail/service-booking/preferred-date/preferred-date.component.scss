@import "../../../../../styles/abstracts/variables";

:host {
  display: flex;
  gap: 60px;
  
  mat-calendar {
    width: 100%;
  }

  .custom-calendar-preferred-date {
    ::ng-deep {
      .mdc-button__label {
        pointer-events: none;
      }
      .mat-calendar-arrow {
        display: none;
      }

      .mat-calendar-header {
        .mat-calendar-controls {
          button {
            pointer-events: none;
            width: 100%;
            font-size: 18px;
            font-weight: 600;

            &:nth-last-child(-n + 2) {
              display: none;
            }
          }
        }
      }

      .mat-calendar-table-header {
        th {
          span {
            font-weight: $fw600;
            font-size: $fs15;
            color: $text-color;
          }
        }
      }

      .mat-calendar-body-label {
        display: none;
      }

      .mat-calendar-disabled-cell {
        pointer-events: none;
        span {
          color: $text-color-2;
        }
      }

      .mat-calendar-body-cell:not(.mat-calendar-disabled-cell) {
        .mat-calendar-body-cell-content:not(.mat-calendar-body-selected) {
          background-color: $bg-color-10;
          font-weight: $fw600;
        }
      }

      .mat-calendar-body-today {
        border: 1px solid $text-color-5;
        background-color: $bg-color-10;
      }

      .mat-calendar-table-header-divider {
        &::after {
          background: unset;
        }
      }

      .mat-calendar-body-selected {
        background-color: $bg-color-11;
      }
    }
  }
}
