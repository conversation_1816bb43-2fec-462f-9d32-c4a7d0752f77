@import "../../../../../styles/abstracts/variables";

:host {
  .icon-title {
    ::ng-deep svg {
      path {
        fill: $text-color-5;
      }
    }
  }

  .title-dialog {
    margin-bottom: 35px;
  }

  .container-dialog {
    padding: 0 45px 25px;

    &__feature {
      display: flex;
      gap: 15px;

      font-weight: 600;
      font-size: 16px;

      mat-icon {
        width: 26px;
        height: 26px;
        ::ng-deep svg {
          path {
            fill: #cccccc;
          }
        }
      }
    }

    &__list {
      display: flex;
      flex-direction: column;
      gap: 5px;
      font-size: 16px;
      margin-left: 25px;
      margin-top: 5px;
    }
  }
}
