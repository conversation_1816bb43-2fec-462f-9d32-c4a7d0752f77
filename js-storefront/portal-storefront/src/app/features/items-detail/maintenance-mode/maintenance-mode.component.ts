import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { Vehicle } from '../../../core/interfaces';
import { MaintenanceModeVehicle, RemoteImmobilizerStatus, SvtStatus } from '../../../core/enums';
import { IconModule } from '../../../core/icon/icon.module';

@Component({
  selector: 'app-maintenance-mode',
  standalone: true,
  imports: [CommonModule, TranslateModule, IconModule],
  templateUrl: './maintenance-mode.component.html',
  styleUrl: './maintenance-mode.component.scss'
})
export class MaintenanceModeComponent {
  @Input() vehicle: Vehicle;

  readonly MaintenanceModeVehicle = MaintenanceModeVehicle;
  readonly RemoteImmobilizerStatus = RemoteImmobilizerStatus;
  readonly SvtStatus = SvtStatus;
}
