<mat-icon svgIcon="ic-svt" class="medium-icon"></mat-icon>
<div class="svt-content">
  <div class="svt-content__header">
    <span class="svt-content__header--title">{{
      "svt.stolenVehicleTracking" | translate
    }}</span>
    <mat-slide-toggle 
      class="custom-toggle"
      [(ngModel)]="svtToggle"
      [disabled]="
        disabledSVT ||
        isLoading ||
        (userService.isHasPermission([PERMISSIONS_CODE.CALLCENTERMANAGERGROUP]) ? 
        (managerApproval
          ? managerApproval?.status === ManagerApprovalStatus.Rejected
          : (checkApprovalStatus?.status?.code ===
              AuthorizationStatus.Rejected ||
            checkApprovalStatus?.status?.code === AuthorizationStatus.Pending ||
            checkApprovalStatus?.status?.code === AuthorizationStatus.Expired)) : 
            (
          managerApproval ? managerApproval?.status !== ManagerApprovalStatus.Approved : (
            checkApprovalStatus?.status?.code !== AuthorizationStatus.Approved
          )
        ))
      "
      (change)="changeSvtToggle($event)"
    ></mat-slide-toggle>

    @if(isLoading) {
    <div class="svt-content__header--loading">
      <mat-spinner class="custom-spinner custom-spinner--red"></mat-spinner>
      {{ "common.loading" | translate }}
    </div>
    }
  </div>
  <div class="svt-content__description">
    {{ "svt.stolenVehicleTrackingMsg" | translate }}
  </div>
</div>
