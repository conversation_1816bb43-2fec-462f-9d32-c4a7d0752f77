import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { Warning } from '../../../core/interfaces/vehicle.interface';
import { DateFormat } from '../../../core/enums';
import { environment } from '../../../../environments/environment';
import { MatRadioModule } from '@angular/material/radio';

@Component({
  selector: 'app-warning',
  standalone: true,
  imports: [CommonModule, TranslateModule, MatIconModule, MatRadioModule],
  templateUrl: './warning.component.html',
  styleUrls: ['./warning.component.scss'],
})
export class WarningComponent implements OnInit {
  @Input() data: Warning[] = [];
  @Input() onlyOneItem: boolean = false;

  @Output() warningId = new EventEmitter<string>();
  dateFormat = DateFormat;

  vehicleConditionOptions = [
    { code: true, name: 'warningMasterData.details.drivable' },
    { code: false, name: 'warningMasterData.details.undrivable' },
  ];


  ngOnInit() {}

  getFullImageUrl(imagePath: string): string {
    return `${environment.OCC_BASE_URL}${imagePath}`;
  }

  onWarningDetails(warningId) {
    this.warningId.emit(warningId)
  }
}
