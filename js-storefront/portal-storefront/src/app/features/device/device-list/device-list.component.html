<app-loading *ngIf="loadingService.isLoading"></app-loading>

<div class="header-section">
  <h2 class="title-page">
    {{ "deviceManagement.deviceManagement" | translate }}
  </h2>
  <div class="device-action">
    <div class="device-action__tabname">
      @for (item of devicesTab; track $index) {
      <span
        [class.active]="item.id === selectedTab"
        (click)="changeDeviceTab(item.id)"
        >{{ item.name }}</span
      >
      }
    </div>
    <div class="device-action__btn-action">
      @if (isLDCM) {
      <button
        *ngIf="userService.isHasPermission([PERMISSIONS_CODE.IOT_DEVICE_PAIR])"
        class="btn-tertiary"
        (click)="pairDevice()"
      >
        {{ "deviceManagement.scanToPair" | translate }}
      </button>
      <button
        *ngIf="
          userService.isHasPermission([PERMISSIONS_CODE.IOT_SIM_IMPORT_ACTIVE])
        "
        class="btn-secondary"
        (click)="importSims()"
      >
        {{ "deviceManagement.importSimStatus" | translate }}
      </button>
      <button
        *ngIf="
          userService.isHasPermission([PERMISSIONS_CODE.IOT_DEVICE_IMPORT])
        "
        class="btn-primary"
        (click)="importDevices()"
      >
        {{ "deviceManagement.importDevice" | translate }}
      </button>
      }
    </div>
  </div>
</div>

<app-widget-summary
  [widgets]="currentWidgets"
  (changeTab)="changeSummaryTab($event)"
></app-widget-summary>

<app-filter-device
  [form]="filterForm"
  [pairingStatus]="filterDevice?.pairingStatusList || []"
  [simStatus]="filterDevice?.simStatusList || []"
  [activationStatus]="filterDevice?.activationStatusList || []"
  [deviceStatus]="filterDevice?.deviceStatusList || []"
  [isLDCM]="isLDCM"
  (searchDevice)="searchDevice()"
></app-filter-device>

<div class="device-list">
  @if (rowData?.length > 0 ) {
  <app-device-table
    [rowData]="rowData"
    [colDefs]="colDefs"
    [importInfo]="importInfo"
    [importInfoSim]="importInfoSim"
    [pagingInfo]="pagingInfo"
    [isLDCM]="isLDCM"
    [isLoadingLastImport]="isLoadingLastImport"
    (onPageChange)="onPageChange($event)"
    (changeItemPerPage)="changeItemPerPage($event)"
    (exportData)="exportData()"
    (downloadFile)="downloadImportResult()"
    (downloadFileSim)="downloadImportSimResult()"
  ></app-device-table>
  } @else {
  <div class="no-data">{{ "deviceManagement.noDeviceFound" | translate }}</div>
  }
</div>
