import { CommonModule } from '@angular/common';
import { Component, EventEmitter, inject, Input, Output } from '@angular/core';
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { FormGroupComponent } from '../../../../core/shared/form-group/form-group.component';
import { DropdownFormGroupComponent } from '../../../../core/shared/dropdown-form-group/dropdown-form-group.component';
import { MatIconModule } from '@angular/material/icon';
import { ScreenSizeService } from '../../../../core/services';
import { OptionDropdown } from '../../../../core/interfaces';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-filter-device',
  templateUrl: './filter-device.component.html',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatIconModule,
    TranslateModule,
    FormGroupComponent,
    DropdownFormGroupComponent,
  ],
  providers: [
    ScreenSizeService
  ],
  standalone: true,
})
export class FilterDeviceComponent {
  @Input() form: any;
  @Input() pairingStatus: OptionDropdown[];
  @Input() simStatus: OptionDropdown[];
  @Input() activationStatus: OptionDropdown[];
  @Input() deviceStatus: OptionDropdown[];
  @Input() isLDCM: boolean;

  @Output() searchDevice = new EventEmitter();

  screenSizeService = inject(ScreenSizeService);

  isOpenFilter = false;
}
