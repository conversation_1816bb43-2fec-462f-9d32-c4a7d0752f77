import {
  Component,
  inject,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  FormGroup,
  FormControl,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { AgGridModule } from 'ag-grid-angular';
import { CommonModule, DatePipe } from '@angular/common';
import { FormGroupComponent } from '../../core/shared/form-group/form-group.component';
import { ImportResultSummaryComponent } from '../../core/shared/import-result-summary/import-result-summary.component';
import { AgGridCustomComponent } from '../../core/shared/ag-grid-custom/ag-grid-custom.component';
import { IconModule } from '../../core/icon/icon.module';
import { ActionCellRendererComponent } from './cell-renderer/action-cell-renderer.component';
import { filter, Observable, Subscription } from 'rxjs';
import { ScreenSizeService } from '../../core/services/screen-size.service';
import {
  LastImportResult,
  PagingInfo,
  VehicleModelInfo,
  VehicleModelList,
  VehicleModelListRequest,
} from '../../core/interfaces';
import { VehicleModelService } from '../../core/services/vehicle-model';
import { DeviceService, LoadingService, NotificationService } from '../../core/services';
import { LoadingComponent } from '../../layout/global/loading/loading.component';
import { DataStoreService } from '../../core/services/data-store.service';
import { MatDialog } from '@angular/material/dialog';
import { PopupAddVehicleModelComponent } from './popup-add/popup-add-vehicle-model/popup-add-vehicle-model.component';
import { ActionModal, ActionTable } from '../../core/enums';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ImportDevicesComponent } from '../device/import-devices/import-devices.component';
import { DialogConfirmComponent } from '../../core/shared';
import { handleErrors } from '../../core/helpers';
import { UserService } from '../../core/services/user';
import { PERMISSIONS_CODE } from '../../core/constants';

@Component({
  selector: 'app-vehicle-model',
  templateUrl: './vehicle-model.component.html',
  styleUrls: ['./vehicle-model.component.scss'],
  standalone: true,
  imports: [
    ReactiveFormsModule,
    FormGroupComponent,
    ImportResultSummaryComponent,
    CommonModule,
    IconModule,
    AgGridModule,
    AgGridCustomComponent,
    ActionCellRendererComponent,
    LoadingComponent,
    TranslateModule
  ],
  providers: [
    ScreenSizeService,
    VehicleModelService,
    LoadingService,
    NotificationService,
    DatePipe,
    DeviceService
  ],
})
export class VehicleModelComponent implements OnInit {
  @ViewChild('actionTemplate', { static: true })
  actionTemplate: TemplateRef<any>;

  customTemplates: { [key: string]: TemplateRef<any> } = {};

  vehicleModelFormSearch: FormGroup = new FormGroup({
    modelSalesCode: new FormControl(''),
    modelName: new FormControl(''),
  });
  vehicleModelForm: FormGroup = new FormGroup({
    modelSalesCode: new FormControl('', [Validators.required]),
    modelName: new FormControl('', [Validators.required]),
  });
  importDate: Date = new Date();
  pagingInfo: PagingInfo = {
    totalItems: 0,
    currentPage: 0,
    numberOfPage: 10,
  };
  rowData: any[] = [];
  colDefs: any[] = [];
  defaultColDef: any = {};
  isMobile$: Observable<boolean>;
  sortDes: boolean = false;
  isLoadingLastImport: boolean = false;
  importInfo: LastImportResult = {};
  mainSubscription = new Subscription();

  private datePipe = inject(DatePipe);
  private vehicleModelService = inject(VehicleModelService);
  private dataStoreService = inject(DataStoreService);
  private dialog = inject(MatDialog);
  private translateService = inject(TranslateService);
  private notificationService = inject(NotificationService);
  public loadingService = inject(LoadingService);
  public screenSizeService = inject(ScreenSizeService);
  private deviceService = inject(DeviceService);

  userService = inject(UserService);
  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;

  ngOnInit(): void {
    this.dataStoreService.setSortData({ column: null, sort: null });
    this.initializeTable();
    if (
      this.userService.isHasPermission([PERMISSIONS_CODE.IOT_MODEL_SALE_CODE_IMPORT])
    ) {
      this.getLastImport();
    }
    
    this.getVehicleList();
    this.customTemplates = {
      action: this.actionTemplate,
    };
    // this.mainSubscription.add(
    //   this.dataStoreService.sortData.subscribe((res: any) => {
    //     if (res && res.column) {
    //       this.sortDes = res.sort;
    //       this.getVehicleList();
    //     }
    //   })
    // );
  }

  ngOnDestroy(): void {
    this.mainSubscription && this.mainSubscription.unsubscribe();
    this.loadingService.hideLoader();
    this.dataStoreService.setSortData({ column: null, sort: null });
  }

  public get modelSalesCodeControlSearch(): FormControl {
    return this.vehicleModelFormSearch.get('modelSalesCode') as FormControl;
  }

  public get modelNameControlSearch(): FormControl {
    return this.vehicleModelFormSearch.get('modelName') as FormControl;
  }

  public get modelSalesCodeControl(): FormControl {
    return this.vehicleModelForm.get('modelSalesCode') as FormControl;
  }

  public get modelNameControl(): FormControl {
    return this.vehicleModelForm.get('modelName') as FormControl;
  }

  initializeTable(): void {
    this.colDefs = [
      {
        headerValueGetter: () => this.translateService.instant('vehicleModel.table.modelSalesCode'),
        field: 'modelSaleCode',
        flex: 2,
        sortable: false,
      },
      {
        headerValueGetter: () => this.translateService.instant('vehicleModel.table.modelName'),
        field: 'modelName',
        flex: 1.5,
        sortable: false,
        // headerComponent: HeaderSortComponent,
        // headerComponentParams: {
        //   headerTxt: this.translateService.instant('vehicleModel.table.modelName'),
        // },
      },
      {
        headerValueGetter: () => this.translateService.instant('vehicleModel.table.createdOn'),
        field: 'createdDate',
        flex: 1,
        sortable: false,
      },
      {
        headerValueGetter: () => this.translateService.instant('vehicleModel.table.modifiedOn'),
        field: 'modifiedDate',
        flex: 1,
        sortable: false,
      },
      {
        headerValueGetter: () => this.translateService.instant('vehicleModel.table.action'),
        field: 'actions',
        flex: 1,
        cellRenderer: ActionCellRendererComponent,
        cellRendererParams: {
          onClick: (type: string, data: VehicleModelInfo) => this.handleActionTable(type, data),
        },
        cellClass: 'action-grid action-last-grid',
        sortable: false,
      },
    ];
    this.defaultColDef = {
      resizable: false,
    };
  }

  getLastImport() {
    this.vehicleModelService.getLastImpot().subscribe((res: LastImportResult) => {
      if (res) {
        this.isLoadingLastImport = false;
        this.importInfo = res;
      }
    })
  }

  getVehicleList() {
    const dataSend: VehicleModelListRequest = {
      currentPage: this.pagingInfo.currentPage,
      pageSize: this.pagingInfo.numberOfPage,
      modelSaleCode: this.vehicleModelFormSearch.value.modelSalesCode,
      modelName: this.vehicleModelFormSearch.value.modelName,
      show: 'Page',
    };
    this.loadingService.showLoader();
    this.vehicleModelService.getVehicleModelList(dataSend).subscribe(
      (res: VehicleModelList) => {
        if (res) {
          if (res.items && res.items.length > 0) {
            this.rowData = res.items.map((item) => {
              item.createdDate = this.datePipe.transform(
                item.createdDate,
                'MM-dd-yyyy hh:mm a'
              );
              item.modifiedDate = this.datePipe.transform(
                item.modifiedDate,
                'MM-dd-yyyy hh:mm a'
              );
              return item;
            });
          }

          if (res.pagination) {
            this.pagingInfo = {
              totalItems: res.pagination.totalResults,
              currentPage: res.pagination.currentPage,
              numberOfPage: res.pagination.pageSize,
            };
          }
        }
        this.loadingService.hideLoader();
      },
      (err) => {
        this.loadingService.hideLoader();
      }
    );
  }

  handleActionTable(type: string, data: VehicleModelInfo) {
    if (type === ActionTable.Edit) {
      this.onEdit(data)
    } else {
      this.onRemove(data);
    }
  }

  onAddNewVehicleModel() {
    this.vehicleModelForm.reset();
    const dialogRef = this.dialog.open(PopupAddVehicleModelComponent, {
      width: '530px',
      data: {
        title: this.translateService.instant('vehicleModel.addSingleModel'),
        icon: 'ic-add-red',
        vehicleModelForm: this.vehicleModelForm,
        modelSalesCodeControl: this.modelSalesCodeControl,
        modelNameControl: this.modelNameControl,
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.add'),
      },
      autoFocus: true,
    });
    dialogRef.afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((result) => {
        if (result?.action === ActionModal.Submit) {
          const dataSend = {
            modelSaleCode: this.vehicleModelForm.value.modelSalesCode,
            modelName: this.vehicleModelForm.value.modelName,
          };
          this.loadingService.showLoader();
          this.vehicleModelService.addVehicleModel(dataSend).subscribe(
            (res) => {
              if (res) {
                if (res?.code === 'SUCCESS') {
                  this.notificationService.showSuccess(res?.message);
                  this.getVehicleList();
                } else {
                  this.loadingService.hideLoader();
                  this.notificationService.showError(res?.message);
                }
              }
            },
            (error) => {
              this.loadingService.hideLoader();
              handleErrors(error, this.notificationService)
            }
          );
        }
      });
  }

  onEdit(data: VehicleModelInfo) {
    this.vehicleModelForm.reset();
    this.vehicleModelForm.patchValue({
      modelName: data.modelName,
      modelSalesCode: data.modelSaleCode
    });
    const dialogRef = this.dialog.open(PopupAddVehicleModelComponent, {
      width: '530px',
      data: {
        title: this.translateService.instant('vehicleModel.editModel'),
        icon: 'ic-edit',
        vehicleModelForm: this.vehicleModelForm,
        modelSalesCodeControl: this.modelSalesCodeControl,
        modelNameControl: this.modelNameControl,
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.save'),
      },
    });
    dialogRef.afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((result) => {
        const dataSend = {
          modelSaleCode: this.vehicleModelForm.value.modelSalesCode,
          modelName: this.vehicleModelForm.value.modelName,
        };
        this.loadingService.showLoader();
        this.vehicleModelService.updateVehicleModel(dataSend, data.modelSaleCode).subscribe(
          (res) => {
            if (res) {
              if (res?.code === 'SUCCESS') {
                this.notificationService.showSuccess(res?.message);
                this.getVehicleList();
              } else {
                this.loadingService.hideLoader();
                this.notificationService.showError(res?.message);
              }
            }
          },
          (error) => {
            this.loadingService.hideLoader();
            handleErrors(error, this.notificationService)
          }
        );
      });
  }

  onRemove(data: VehicleModelInfo) {
    const dialogRef = this.dialog.open(DialogConfirmComponent, {
      width: '530px',
      data: {
        title: this.translateService.instant('vehicleModel.removeModel'),
        icon: 'ic-warning',
        confirmMsg: `${this.translateService.instant('common.remove')} <b>${this.translateService.instant('vehicleModel.model')} ${data.modelSaleCode}?</b></br> ${this.translateService.instant('vehicleModel.descriptionRemove')}`,
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.remove'),
      },
    });
    dialogRef.afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((result) => {
        const dataSend = {
          modelSaleCode: this.vehicleModelForm.value.modelSalesCode,
          modelName: this.vehicleModelForm.value.modelName,
        };
        this.loadingService.showLoader();
        this.vehicleModelService.deleteVehicleModel(data.modelSaleCode).subscribe(
          (res) => {
            if (res) {
              if (res?.code === 'SUCCESS') {
                this.notificationService.showSuccess(res?.message);
                this.getVehicleList();
              } else {
                this.loadingService.hideLoader();
                this.notificationService.showError(res?.message);
              }
            }
          },
          (error) => {
            this.loadingService.hideLoader();
            handleErrors(error, this.notificationService)
          }
        );
        this.vehicleModelForm.reset();
      });
  }

  importVehicleModel(): void {
    const dialogRef = this.dialog.open(ImportDevicesComponent, {
      width: '530px',
      data: {
        title: this.translateService.instant('vehicleModel.importTitlePopup'),
        noStep: true
      },
    });

    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((result) => {
        this.loadingService.showLoader();
        this.vehicleModelService.importVehicleModel(result?.data).subscribe(
          (res) => {
            if (res?.code === 'SUCCESS') {
              this.notificationService.showSuccess(res?.message);
              this.getVehicleList();
              this.isLoadingLastImport = true;
              setTimeout(() => {
                this.getLastImport();
              }, 3000);
            } else {
              this.loadingService.hideLoader();
              this.notificationService.showError(res?.message);
            }
          },
          (error) => {
            this.loadingService.hideLoader();
            handleErrors(error, this.notificationService);
          }
        );
      });
  }

  onSearch(): void {
    this.getVehicleList();
  }

  onPageChange(newPage: number): void {
    this.pagingInfo.currentPage = newPage;
    this.getVehicleList();
  }

  onResultsPerPageChange(event: number): void {
    this.pagingInfo = {
      ...this.pagingInfo,
      currentPage: 0,
      numberOfPage: event,
    };
    this.getVehicleList();
  }

  downloadImportResult(): void {
    this.loadingService.showLoader();
    this.deviceService
      .downloadImportResult(
        this.importInfo?.failedRecords?.code ||
          this.importInfo?.importFile?.code
      )
      .subscribe(
        (response) => {
          this.loadingService.hideLoader();

          const link = document.createElement('a');

          link.href = URL.createObjectURL(response?.blob);
          link.download =
            this.importInfo?.failedRecords?.realFileNameWithoutExt ||
            this.importInfo?.importFile?.realFileNameWithoutExt;
          link.click();
          URL.revokeObjectURL(link.href);
        },
        (error) => {
          this.loadingService.hideLoader();
          handleErrors(error, this.notificationService);
        }
      );
  }
}
