import { CommonModule, DatePipe } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { InputNumberDirective } from '../../../core/directives/input-interger.directive';
import {
  TicketsService,
  LoadingService,
  NotificationService,
  VehicleService,
} from '../../../core/services';
import {
  DateFormGroupComponent,
  FormGroupComponent,
  RadioButtonComponent,
} from '../../../core/shared';
import { LoadingComponent } from '../../../layout/global/loading/loading.component';
import { Subscription, finalize } from 'rxjs';
import { ActionModal, DateFormat } from '../../../core/enums';
import { handleErrors } from '../../../core/helpers';
import {
  MetadataTowingDropdown,
  PerferDealer,
  requestCreateTowingService,
} from '../../../core/interfaces/ticket-detail.interface';
import { ModalCreateEditComponent } from '../../items-detail/towing-service/modal-create-edit/modal-create-edit.component';
import { MatCheckboxModule } from '@angular/material/checkbox';

@Component({
  selector: 'app-modal-action-loan',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    ReactiveFormsModule,
    FormGroupComponent,
    MatRadioModule,
    MatFormFieldModule,
    MatSelectModule,
    MatIconModule,
    MatDatepickerModule,
    MatInputModule,
    MatDialogModule,
    DateFormGroupComponent,
    MatCheckboxModule,
    RadioButtonComponent
  ],
  providers: [
    provideNativeDateAdapter(),
    VehicleService,
    LoadingService,
    DatePipe,
    NotificationService,
  ],
  templateUrl: './modal-action-loan.component.html',
  styleUrls: ['./modal-action-loan.component.scss'],
})
export class ModalActionLoanComponent implements OnInit {
  data = inject(MAT_DIALOG_DATA);
  dialogRef = inject(MatDialogRef<ModalCreateEditComponent>);
  vin: string;
  mainForm!: FormGroup;
  isLoading: boolean = true;
  mainSubscription = new Subscription();
  isEdit: boolean = false;
  isWidget: boolean = false;
  isReadOnly: boolean = false;
  statusRadio = [
    {
      code: 'ACTIVE',
      name: 'Active',
    },
    {
      code: 'CLOSED',
      name: 'Closed',
    },
  ];
  private vehicleService = inject(VehicleService);
  private datePipe = inject(DatePipe);
  private notificationService = inject(NotificationService);
  private translateService = inject(TranslateService);
  public loadingService = inject(LoadingService);

  ngOnInit() {
    this.mainForm = this.data.mainForm as FormGroup;
    this.isEdit = this.data.isEdit === undefined ? false : this.data.isEdit;
    this.isWidget =
      this.data.isWidget === undefined ? false : this.data.isWidget;
    this.vin = this.data.vin;

    this.mainForm.controls['closed'].clearValidators();
    this.mainForm.controls['closed'].updateValueAndValidity();
  }

  ngOnDestroy() {
    this.mainSubscription && this.mainSubscription.unsubscribe();
  }

  public get vinControl(): FormControl {
    return this.mainForm.get('vin') as FormControl;
  }

  public get loanId(): FormControl {
    return this.mainForm.get('loanId') as FormControl;
  }

  public get endDate(): FormControl {
    return this.mainForm.get('endDate') as FormControl;
  }

  public get startDate(): FormControl {
    return this.mainForm.get('startDate') as FormControl;
  }

  public get status(): FormControl {
    return this.mainForm.get('status') as FormControl;
  }

  onSubmit() {
    const dataForm = this.mainForm.value;
    let dataSend: any;
    if (!this.isWidget) {
      dataSend = {
        id: dataForm.loanId,
        vin: this.vin,
        status: {
          code: dataForm.status,
        },
        startDate: this.datePipe.transform(
          dataForm.startDate,
          DateFormat.ShortDate
        ),
        endDate: this.datePipe.transform(
          dataForm.endDate,
          DateFormat.ShortDate
        ),
      };
    } else {
      dataSend = {
        id: dataForm.loanId,
        status: {
          code: dataForm.status,
        },
        startDate: this.datePipe.transform(
          dataForm.startDate,
          DateFormat.ShortDate
        ),
        endDate: this.datePipe.transform(
          dataForm.endDate,
          DateFormat.ShortDate
        ),
      };
    }
    if (this.data.isEdit) {
      this.updateLoan(dataSend);
    } else {
      this.createLoan(dataSend);
    }
  }

  updateLoan(dataSend) {
    this.loadingService.showLoader();
    this.mainSubscription.add(
      this.vehicleService
        .updateLoan(dataSend, this.vin)
        .pipe(finalize(() => this.mainForm.reset()))
        .subscribe(
          (res) => {
            if (res) {
              this.notificationService.showSuccess(res.message);
              this.dialogRef.close({ action: ActionModal.Submit, data: res });
            }
            this.loadingService.hideLoader();
          },
          (error) => {
            handleErrors(error, this.notificationService);
            this.loadingService.hideLoader();
            this.dialogRef.close();
          }
        )
    );
  }

  createLoan(body) {
    this.loadingService.showLoader();
    this.mainSubscription.add(
      this.vehicleService
        .enableLoan(this.isWidget, body, this.vin)
        .pipe(finalize(() => this.mainForm.reset()))
        .subscribe(
          (res) => {
            if (res) {
              this.notificationService.showSuccess(
                this.translateService.instant(res.message)
              );
              this.dialogRef.close({ action: ActionModal.Submit, data: res });
            }
            this.loadingService.hideLoader();
          },
          (error) => {
            handleErrors(error, this.notificationService);
            this.loadingService.hideLoader();
            this.dialogRef.close();
          }
        )
    );
  }

  onSelectStatusRadio(event: any) {
    const value = event.value;
    this.isReadOnly = value === 'CLOSED';
    if (this.isReadOnly) {
      this.mainForm.controls['closed'].addValidators([Validators.requiredTrue]);
      this.mainForm.controls['closed'].updateValueAndValidity();
    } else {
      this.mainForm.controls['closed'].clearValidators();
      this.mainForm.controls['closed'].updateValueAndValidity();
    }
  }

  onSelectCheck(event: any) {}

  onCancel() {
    this.mainForm.reset();
    this.dialogRef.close();
  }
}
