@import "../../../../styles/abstracts/mixins";
@import "../../../../styles/abstracts/variables";

:host ::ng-deep {
  .modal-action-form {
    position: relative;
    display: flex;
    flex-direction: column;

    .form-group {
      margin-bottom: 0;
    }

    .container-dialog {
      padding: 30px 40px 0px;
    }

    h2 {
      padding: 25px 40px 0;
      margin: 0;
      text-align: center;
    }

    form {
      display: flex;
      flex-direction: column;
      gap: 30px;
    }

    &__dealer-radio {
      display: flex;
      gap: 60px;
      .spinner {
        position: unset;
      }

    }

    app-radio-button {
      ::ng-deep {
        mat-radio-group {
          display: grid;
          grid-template-columns: auto auto;
        }
      }
    }

    &__icon {
      display: flex;
      flex-direction: column;
      gap: 10px;

      p {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 14px;
        font-weight: 400;
        margin: 0;
      }
    }

    &__actions {
      display: flex;
      justify-content: center;
      gap: 20px;
      margin-top: 30px;
      .btn {
        border-radius: 0;
        min-width: 215px;
        height: 50px;
      }
    }
  }

  .date-time {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-end;

    .form-group {
      margin-bottom: 0px;
    }
  }

  .item-hours {
    display: flex;
    position: relative;
    align-items: flex-end;

    mat-icon {
      position: absolute;
      right: 0px;
      bottom: 7px;
    }

    &__separator {
      padding-bottom: 7px;
      border-bottom: 1px solid #cccccc;
      transition: border-color 0.3s;
    }

    &__time-hh {
      max-width: 25px;
    }

    &__time-mm {
      padding-left: 5px;
    }

    &:has(.form-control:focus) {
      .form-control {
        border-bottom: 1px solid #000 !important;
      }

      .item-hours__separator {
        border-bottom: 1px solid #000 !important;
      }
    }
  }

  .item-date {
    .form-group__input {
      position: relative;

      mat-icon {
        position: absolute;
        right: 0;
        bottom: 7px;
        cursor: pointer;
      }

      .form-control {
        cursor: pointer;
      }
    }
  }

  .status-error {
    margin-top: -20px;
  }

  .custom-mat-checkbox {
    .mdc-form-field {
      position: relative;
      .mdc-checkbox {
        position: absolute;
        top: 0px;
        left: 4px;
        padding: 0;
      }
      .mdc-checkbox__native-control,
      .mat-mdc-checkbox-touch-target {
        height: 18px !important;
        width: 18px !important;
      }
      .mdc-checkbox__background {
        top: 2px;
        left: 8px;
      }
    }
    .mdc-label {
      color: var(--Primary-Black, #101010);
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      padding-left: 33px;
    }
  }
}
