<div class="tmsph-role" *ngIf="userService.isHasPermission([PERMISSIONS_CODE.TMSPHGROUP, PERMISSIONS_CODE.TMSPHMANAGERGROUP])">
    <app-dashboard-approval-ticket [index]="1"></app-dashboard-approval-ticket>
    <!-- <app-dashboard-remote-immobilization [index]="2"></app-dashboard-remote-immobilization> -->
    <app-dashboard-immobilization-mode [index]="2"></app-dashboard-immobilization-mode>
    <app-dashboard-subscriptions-activate [index]="2"></app-dashboard-subscriptions-activate>
</div>

<ng-container *ngIf="userService.isHasPermission([PERMISSIONS_CODE.CALLCENTERGROUP, PERMISSIONS_CODE.CALLCENTERMANAGERGROUP])">
    <app-dashboard-ticket [index]="1"></app-dashboard-ticket>
    <app-dashboard-immobilization-mode [index]="2"></app-dashboard-immobilization-mode>
    <app-dashboard-subscription [index]="3"></app-dashboard-subscription>
    <app-dashboard-due [index]="4"></app-dashboard-due>
    <app-dashboard-campaign-due-non-device-vehicle [index]="5"></app-dashboard-campaign-due-non-device-vehicle>
</ng-container>

<div class="helpdesk-role" 
    *ngIf="userService.isHasPermission([PERMISSIONS_CODE.DOSDHELPDESKGROUP])"
>
    <app-dashboard-vehicle-device-by-sim [index]="1"></app-dashboard-vehicle-device-by-sim>
    <app-dashboard-device [index]="2"></app-dashboard-device>
    <app-dashboard-non-device-vehicle [index]="3"></app-dashboard-non-device-vehicle>
    <app-dashboard-device-to-paired [index]="4"></app-dashboard-device-to-paired>
    <app-dashboard-sim-enabled [index]="5"></app-dashboard-sim-enabled>
</div>

<ng-container *ngIf="userService.isHasPermission([PERMISSIONS_CODE.TMTGROUP]) || userService.isHasPermission([PERMISSIONS_CODE.VLDGROUP])">
    <app-dashboard-device [index]="1"></app-dashboard-device>
    <app-dashboard-device-to-paired [index]="2"></app-dashboard-device-to-paired>
</ng-container>

<ng-container 
    *ngIf="
        userService.isHasPermission([PERMISSIONS_CODE.DOSDPROJECTGROUP]) &&
        !userService.isHasPermission([PERMISSIONS_CODE.DOSDHELPDESKGROUP])
    "
>
    <app-dashboard-ticket [index]="1"></app-dashboard-ticket>
    <app-dashboard-immobilization-mode [index]="2"></app-dashboard-immobilization-mode>
    <app-dashboard-subscription [index]="3"></app-dashboard-subscription>
    <app-dashboard-due [index]="4"></app-dashboard-due>
    <app-dashboard-approval-ticket [index]="5"></app-dashboard-approval-ticket>
    <app-dashboard-campaign-due-non-device-vehicle [index]="6"></app-dashboard-campaign-due-non-device-vehicle>
    <app-dashboard-subscriptions-activate [index]="7"></app-dashboard-subscriptions-activate>
    <!-- <app-dashboard-remote-immobilization [index]="8"></app-dashboard-remote-immobilization> -->
    <app-dashboard-vehicle-device-by-sim [index]="8"></app-dashboard-vehicle-device-by-sim>
    <app-dashboard-device [index]="9"></app-dashboard-device>
    <app-dashboard-non-device-vehicle [index]="10"></app-dashboard-non-device-vehicle>
    <app-dashboard-device-to-paired [index]="11"></app-dashboard-device-to-paired>
    <app-dashboard-device-paired [index]="12"></app-dashboard-device-paired>
    <app-dashboard-sim-enabled [index]="13"></app-dashboard-sim-enabled>
</ng-container>

<ng-container *ngIf="userService.isHasPermission([PERMISSIONS_CODE.IOTSERVICEADVISORGROUP])">
    <app-dashboard-ticket-vehicle></app-dashboard-ticket-vehicle>
</ng-container>