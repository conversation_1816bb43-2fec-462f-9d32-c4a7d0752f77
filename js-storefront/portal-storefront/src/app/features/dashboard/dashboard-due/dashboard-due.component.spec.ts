import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DashboardDueComponent } from './dashboard-due.component';

describe('DashboardDueComponent', () => {
  let component: DashboardDueComponent;
  let fixture: ComponentFixture<DashboardDueComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DashboardDueComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(DashboardDueComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
