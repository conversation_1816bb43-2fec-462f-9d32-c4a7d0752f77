import { Component, HostListener, inject, Input, OnInit } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { PERMISSIONS_CODE } from '../../../core/constants';
import { UserService } from '../../../core/services/user';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { LoadingComponent } from '../../../layout/global/loading/loading.component';
import { isInViewport } from '../../../core/helpers';
import { CurrentLocationComponent } from '../../items-detail/current-location/current-location.component';
import { IconModule } from '../../../core/icon/icon.module';

@Component({
  selector: 'app-dashboard-remote-immobilization',
  standalone: true,
  imports: [
    TranslateModule, 
    CommonModule, 
    LoadingComponent,
    CurrentLocationComponent,
    IconModule,
  ],
  providers: [TranslateService],
  templateUrl: './dashboard-remote-immobilization.component.html',
  styleUrl: './dashboard-remote-immobilization.component.scss'
})
export class DashboardRemoteImmobilizationComponent implements OnInit {
  translateService = inject(TranslateService);
  userService = inject(UserService);
  router = inject(Router);

  @Input() index;
  
  isLoaded: boolean = false;

  vehicle = {
    latitude: '10.4034969', 
    longitude: '107.0406483',
    currentAddress: 'Land Cruiser 300 · NBA 1234'
  };

  @HostListener('window:scroll', ['$event'])
  onScroll(event) {
    this.checkInView();
	}

  rowData = [];

  onActionClick(data) {
    
  }

  viewTicketDetail(id) {
    if (
      this.userService.isHasPermission([
        PERMISSIONS_CODE.ECARE_TICKET_DETAIL_VIEW,
      ])
    ) {
      this.router.navigate(['tickets/' + id]);
    }
  }

  ngOnInit(): void {
    if (this.index < 3) {
			this.isLoaded = true;
		} 

    for(let i = 0; i < 10; i++) {
      this.rowData.push(
        {
          vehicle: 'Land Cruiser 2023',
          ticketID: '10011',
          plateNo: 'NBA 1234',
          "status" : {
            "code" : "Open",
            "name" : "Open"
          },
        }
      )
    }
  }

  checkInView() {
    const box = document.querySelector(`.dashboard-remote`);

    if (box && isInViewport(box) && !this.isLoaded) {
      this.isLoaded = true;
    }
  }
}
