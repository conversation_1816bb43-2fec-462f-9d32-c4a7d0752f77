import { Component, inject, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { GlobalModule } from '../../../layout/global/global.module';
import { LoaderComponent } from '../../../layout/global/loader/loader.component';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { AuthService } from '../../../core/services/auth.service';
import { LoadingService } from '../../../core/services/loading.service';
import { NotificationService } from '../../../core/services';
import { UserService } from '../../../core/services/user';

@Component({
  selector: 'app-authentication',
  templateUrl: './authentication.component.html',
  standalone: true,
  imports: [
    CommonModule,
    GlobalModule,
    LoaderComponent,
    ReactiveFormsModule,
  ],
  providers: [NotificationService]
})
export class AuthenticationComponent {

  router = inject(Router);
  authService = inject(AuthService);
  loadingService = inject(LoadingService);
  notificationService = inject(NotificationService);
  private userService = inject(UserService);

  errorMsg: string;

  form = new FormGroup({
    otp: new FormControl('', Validators.required),
  });

  onSendOtp(): void {
    if (this.form.invalid) {
    this.errorMsg ='Please enter a valid OTP.';
      return;
    }

    this.loadingService.showLoader();

    this.authService
      .otpVerification(this.form.value.otp, this.notificationService)
      .subscribe(
        (response) => {
          this.loadingService.hideLoader();
          if (response?.code === '200') {
            this.notificationService.showSuccess('OTP verified successfully.');
            // Base on roles to redirect to the correct portal.
            this.userService.getUserRoles().subscribe((response) => {
              this.userService.setRoles(response);
              const checkUserRoles = this.userService.checkUserRoles(response?.roles || []);
              const isUserHasMultipleRoles = this.userService.checkUserMultipleRoles(checkUserRoles);
              let { returnUrl } = this.router.routerState.snapshot.root.queryParams;
              if (!isUserHasMultipleRoles) {
                returnUrl = this.userService.getRedirectUrl(response?.roles, returnUrl);
              } else {
                returnUrl = '/authorize'
              }
              this.router.navigateByUrl(returnUrl);
            });
          } else {
            this.notificationService.showError(
              response?.message || 'Failed to verify OTP.'
            );
          }
        },
        () => {
          this.loadingService.hideLoader();
          this.notificationService.showError('An error occurred while verifying OTP.');
        }
      );
  }

  resendOtp(): void {
    this.authService.executeGetOtp(this.notificationService);
  }

  returnToLogin(): void {
    this.router.navigateByUrl('/login');
  }
}
