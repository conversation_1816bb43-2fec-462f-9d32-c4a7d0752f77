<div class="formcontainer">
  <h2 class="title">Authentication</h2>

  <p>
    We have sent you an OTP code via your email. Please kindly check your
    mailbox. The OTP will be valid for 3 minutes.
  </p>

  <form [formGroup]="form" (ngSubmit)="onSendOtp()">
    <div class="form-group-custom p-b-0">
      <div class="forminput">
        <label>OTP <span>*</span></label>
        <input
          type="text"
          formControlName="otp"
          class="input-icon-container"
          placeholder="Enter text here"
        />
      </div>
    </div>
    <div class="invalid-feedback">
        {{ errorMsg }}
    </div>
    <div class="action-authen">
      <p (click)="resendOtp()">Resend OTP</p>
      <p (click)="returnToLogin()">Return to login</p>
    </div>
    <div class="submitcontainer">
      <button type="submit" class="btnsubmit" [disabled]="form.invalid">
        <span>Submit</span>
      </button>
    </div>
  </form>
</div>