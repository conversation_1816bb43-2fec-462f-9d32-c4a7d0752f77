<div class="role-picker">
  <div class="role-picker__left">
    <div class="role-picker__left--bg"></div>
    <div class="role-picker__left--logo">
      <img src="../../../../assets/images/logo-toyota.svg" />
    </div>
  </div>
  <div class="role-picker__right">
    <div class="role-picker__right--header">
      <h2>Which portal would you like to access?</h2>
      <span>Select a portal to continue from the list below.</span>
    </div>
    <div class="role-picker__right--list">
      @for (item of listOption; track $index) { @if(item?.isShow) {
      <div class="role-picker__right--list__item" [class.active]="item?.isSelected" (click)="selectedChange(item?.url, item)">
        @if (item?.isSelected) {
        <div class="role-picker__right--list__item--radio">
          <mat-icon svgIcon="ic-radio-checked"></mat-icon>
        </div>
        }
        <div class="role-picker__right--list__item--content">
          <label>{{ item?.name | translate }}</label>
          <span>{{ item?.sub | translate }}</span>
        </div>
      </div>
      } }
    </div>
    <div class="role-picker__right--action">
      <button [disabled]="!checkSelectedRole()" type="button" class="btn btn-primary" (click)="rediretToPortal(url)">select</button>
    </div>
  </div>
</div>
