import { Component, inject, OnInit } from '@angular/core';
import { UserService } from '../../../core/services/user';
import { Router } from '@angular/router';
import { MatRadioModule } from '@angular/material/radio';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { LISTOPTIONPORTAL } from '../../../core/constants';

@Component({
  selector: 'app-role-picker',
  standalone: true,
  imports: [
    MatRadioModule,
    MatIconModule,
    TranslateModule
  ],
  templateUrl: './role-picker.component.html',
  styleUrls: ['./role-picker.component.scss']
})
export class RolePickerComponent implements OnInit {
  userService = inject(UserService);
  router = inject(Router);
  userInfo: any;
  userRoles: any;
  url: string = '';
  listOption: any = LISTOPTIONPORTAL;

  ngOnInit(): void {
    this.userInfo = this.userService.loadRolesFromStorage();
    this.userRoles = this.userService.checkUserRoles(this.userInfo?.roles || []);
    this.listOption[0].isShow = this.userRoles.insurance;
    this.listOption[1].isShow = this.userRoles.claims;
    this.listOption[2].isShow = this.userRoles.iot;
  }

  selectedChange(url: string, item: any) {
    this.url = url;
    this.listOption = this.listOption.map((item: any) => {
      item.isSelected = false;
      return item;
    });
    item.isSelected = true;
  }

  rediretToPortal(url: string) {
    if (this.checkSelectedRole) {
      this.router.navigate([url]);
    }
  }

  checkSelectedRole() {
    return this.listOption.some(item => item.isSelected === true);
  }
}
