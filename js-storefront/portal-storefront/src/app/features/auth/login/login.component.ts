import { Component, inject, OnInit } from '@angular/core';
import {
  FormsModule,
  ReactiveFormsModule,
  FormBuilder,
  Validators,
} from '@angular/forms';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { AuthService } from '../../../core/services/auth.service';
import { GlobalModule } from '../../../layout/global/global.module';
import { catchError, switchMap } from 'rxjs/operators';
import { EMPTY } from 'rxjs';
import { LoaderComponent } from '../../../layout/global/loader/loader.component';
import { LoadingService } from '../../../core/services/loading.service';
import { IconModule } from '../../../core/icon/icon.module';
import { NotificationService } from '../../../core/services';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { UserService } from '../../../core/services/user';
import { PERMISSIONS_CODE } from '../../../core/constants';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    CommonModule,
    GlobalModule,
    LoaderComponent,
    TranslateModule,
    IconModule,
  ],
  providers: [NotificationService],
})
export class LoginComponent implements OnInit {
  private fb = inject(FormBuilder);
  private router = inject(Router);
  private authService = inject(AuthService);
  private loadingService = inject(LoadingService);
  private translateService = inject(TranslateService);
  private userService = inject(UserService);

  notificationService = inject(NotificationService);

  public isShowPw = false;
  public warningMessage: string | null = null;

  public loginForm = this.fb.group({
    userData: this.fb.group({
      username: ['', [Validators.required]],
      psw: ['', [Validators.required]],
    }),
  });

  ngOnInit(): void {
    this.userService.userRoles$.next(null);
    sessionStorage.removeItem('promotionTransactionSearch');
    sessionStorage.removeItem('promotionSearch');
    sessionStorage.removeItem('promotionSelectedSearch');
    sessionStorage.removeItem('promotionTab');
    sessionStorage.removeItem('receivableTab');
  }

  onLogin(): void {
    if (this.loginForm.valid) {
      this.warningMessage = null;
      this.loadingService.showLoader();

      const { username, psw } = this.loginForm.get('userData')?.value;

      this.authService
        .getToken(username, psw)
        .pipe(
          switchMap((response) => {
            localStorage.setItem('token', response.access_token);
            const expiresAt = Date.now() + response.expires_in * 1000;
            localStorage.setItem('expires_at', expiresAt.toString());

            this.showNotification(
              this.translateService.instant('login.tokenSuccess'),
              'info'
            );
            return this.authService.getMFA();
          }),
          catchError((error) => {
            this.loadingService.hideLoader();

            const errorMessage = this.getErrorMessage(error);
            this.showNotification(errorMessage, 'error');

            return EMPTY;
          })
        )
        .subscribe((response) => {
          if (response?.mfaEnable && !response?.hasEmail) {
            this.showNotification(
              this.translateService.instant('login.requiredEmail'),
              'error'
            );
            return;
          }
            if (response?.mfaEnable && response?.hasEmail) {
              this.showNotification(
                this.translateService.instant('login.mfaRequired'),
                'info'
              );
              this.router.navigateByUrl('/authentication').then(
                () => {
                  this.loadingService.hideLoader(),
                    this.authService.executeGetOtp(this.notificationService);
                },
                () => {
                  this.loadingService.hideLoader();
                  this.showNotification(
                    this.translateService.instant('login.navigationError'),
                    'error'
                  );
                }
              );
            } else {
              // Base on roles to redirect to the correct portal.
              this.userService.getUserRoles().subscribe((response) => {
                this.userService.setRoles(response);
                const checkUserRoles = this.userService.checkUserRoles(response?.roles || []);
                const isUserHasMultipleRoles = this.userService.checkUserMultipleRoles(checkUserRoles);
                let navigateUrl: any
                if (!isUserHasMultipleRoles) {
                  let { returnUrl } = this.router.routerState.snapshot.root.queryParams;
                  returnUrl = this.userService.getRedirectUrl(response?.roles, returnUrl);
                  this.showNotification(
                    this.translateService.instant('login.noMfa'),
                    'info'
                  );
                  navigateUrl = this.router.parseUrl(returnUrl);
                } else {
                  navigateUrl = this.router.parseUrl('/authorize');
                }
                
                this.router.navigateByUrl(navigateUrl).then(
                  (success) => {
                    if (!success) {
                      this.showNotification(
                        this.translateService.instant('login.wrongCredentials'),
                        'error'
                      );
                    }
                    this.loadingService.hideLoader();
                  },
                  () => {
                    this.loadingService.hideLoader();
                    this.showNotification(
                      this.translateService.instant('login.wrongCredentials'),
                      'error'
                    );
                  }
                );
              });
            }

        });
    } else {
      this.warningMessage = this.translateService.instant(
        'login.invalidFields'
      );
    }
  }

  onShowPassword(): void {
    this.isShowPw = !this.isShowPw;
  }

  navigateToResetPassword(): void {
    this.router.navigate(['/reset-password']);
    localStorage.setItem('resetPasswordStep', '1');
    localStorage.setItem('successSendMail', 'false');
  }

  private getErrorMessage(error: any): string {
    if (error.error?.error === 'invalid_client') {
      return this.translateService.instant('login.invalidClient');
    } else if (error.error?.error === 'invalid_grant') {
      return this.translateService.instant('login.invalidGrant');
    } else if (error.error?.error === 'unsupported_grant_type') {
      return this.translateService.instant('login.unsupportedGrant');
    } else {
      return this.translateService.instant('login.unexpectedError');
    }
  }

  private showNotification(
    message: string,
    type: 'success' | 'error' | 'info' | 'warning'
  ): void {
    this.notificationService.showNotification(message, type);
  }
}
