import { CommonModule } from '@angular/common';
import { Component, inject, Input, OnDestroy, OnInit } from '@angular/core';
import { IconModule } from '../../../../core/icon/icon.module';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs';
import {
  LoadingService,
  NotificationService,
  ServiceCampaignsService,
} from '../../../../core/services';
import { ItemServiceCampaign, PagingInfo } from '../../../../core/interfaces';
import { handleErrors } from '../../../../core/helpers';
import { DEFAULT_COL_DEF } from '../../../../core/constants';
import { AgGridCustomComponent } from '../../../../core/shared';

@Component({
  selector: 'app-service-campaigns',
  standalone: true,
  imports: [
    CommonModule,
    IconModule,
    TranslateModule,
    AgGridCustomComponent,
  ],
  templateUrl: './service-campaigns.component.html',
  styleUrl: './service-campaigns.component.scss',
  providers: [ServiceCampaignsService, NotificationService],
})
export class ServiceCampaignsComponent implements OnInit, OnDestroy {
  @Input() vin: string;
  serviceCampaignsService = inject(ServiceCampaignsService);
  notificationService = inject(NotificationService);
  translateService = inject(TranslateService);
  loadingService = inject(LoadingService);

  subscription = new Subscription();

  rowData: ItemServiceCampaign[];

  pagingInfo: PagingInfo = {
    totalItems: 0,
    currentPage: 0,
    numberOfPage: 10,
  };

  defaultColDef = {
    ...DEFAULT_COL_DEF,
    valueFormatter: (params) => (params.value ? params.value : '-'),
  };
  colDefs = [
    {
      headerName: this.translateService.instant('serviceCampaigns.sscName'),
      headerValueGetter: () => this.translateService.instant('serviceCampaigns.sscName'),
      flex: 2,
      autoHeight: true,
      wrapText: true,
      cellClass: 'cell-word-wrap',
      field: 'subjectSSCCategory',
    },
    {
      headerName: this.translateService.instant('serviceCampaigns.validFrom'),
      headerValueGetter: () => this.translateService.instant('serviceCampaigns.validFrom'),
      flex: 1,
      field: 'validFrom',
    },
    {
      headerName: this.translateService.instant('serviceCampaigns.validTo'),
      headerValueGetter: () => this.translateService.instant('serviceCampaigns.validTo'),
      flex: 1,
      field: 'validTo',
    },
    {
      headerName: this.translateService.instant('serviceCampaigns.status'),
      headerValueGetter: () => this.translateService.instant('serviceCampaigns.status'),
      flex: 1,
      field: 'status',
      cellRenderer: (params) => {
        return params?.value ? `<span>${params?.value?.name}</span>` : '-';
      },
    },
  ];

  ngOnInit(): void {
    this.getServiceCampaigns();
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  getServiceCampaigns(): void {
    const params = {
      vin: this.vin,
      currentPage: this.pagingInfo.currentPage,
      pageSize: this.pagingInfo.numberOfPage,
    };
    this.loadingService.showLoader();
    this.subscription.add(
      this.serviceCampaignsService.getServiceCampaigns(params).subscribe(
        (response) => {
          this.loadingService.hideLoader();
          this.rowData = response?.items;
          this.pagingInfo.totalItems = response?.pagination?.totalResults;
        },
        (error) => {
          this.loadingService.hideLoader();
          handleErrors(error, this.notificationService);
        }
      )
    );
  }

  onPageChange(value: number): void {
    this.pagingInfo.currentPage = value;
    this.getServiceCampaigns();
  }

  changeItemPerPage(item) {
    this.pagingInfo.numberOfPage = item;
    this.pagingInfo.currentPage = 0;
    this.getServiceCampaigns();
  }
}
