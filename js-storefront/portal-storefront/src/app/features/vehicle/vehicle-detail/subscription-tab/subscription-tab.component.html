<div class="subscription-tab">
  @if (rowData?.length > 0) {
  <div class="subscription-tab__title">
    <mat-icon svgIcon="ic-sub-widget"></mat-icon>
    {{ "vehicle.tab.subscriptions" | translate }}
  </div>
  <app-ag-grid-custom
    [rowData]="rowData"
    [colDefs]="colDefs"
    [defaultColDef]="defaultColDef"
    [isPaging]="true"
    [pagingInfo]="pagingInfo"
    [isShowActionExport]="false"
    (onPageChange)="onPageChange($event)"
    (changeItemPerPage)="changeItemPerPage($event)"
  >
  </app-ag-grid-custom>
  } @else {
  <div class="no-data">
    {{ "subscriptionTab.noSubscription" | translate }}
  </div>
  }
</div>
