@import '../../../../../styles/abstracts/variables';

.overview-container {
  display: flex;
  width: 100%;
  flex-wrap: wrap;
  gap: 20px;

  &__left {
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    gap: 15px;
    width: 420px;

    @media screen and (max-width: 1280px) {
      width: 100%;
    }
  }

  &__right {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 15px;
    min-width: 700px;

    @media screen and (max-width: 1280px) {
      width: 100%;
      min-width: unset;
    }
  }

  &__box {
    background-color: #fff;
    padding: 25px;
    box-shadow: 0 4px 5px 5px #00000008;

    &__ssc {
      border-bottom: 4px solid #190;
    }
  }

  &__box-haft {
    display: flex;
    align-items: stretch;
    gap: 20px;

    >* {
      width: calc(50% - 10px);
      @media screen and (max-width: 991px) {
        width: 100%;
      }
    }

    @media screen and (max-width: 991px) {
      flex-wrap: wrap;
    }
  }

  
  &--service-booking {
    position: relative;
  }

  &--maintenance-mode {
    background-color: $bg-color-8;
    padding: 10px 30px;
  }
}