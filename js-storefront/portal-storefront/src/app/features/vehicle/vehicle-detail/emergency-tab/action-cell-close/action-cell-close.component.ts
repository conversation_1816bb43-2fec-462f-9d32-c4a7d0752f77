import { CommonModule } from '@angular/common';
import { Component, EventEmitter, inject, Input, Output } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IconModule } from '../../../../../core/icon/icon.module';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { UserService } from '../../../../../core/services/user';
import { PERMISSIONS_CODE } from '../../../../../core/constants';

@Component({
  selector: 'app-action-cell-close',
  template: `
  @if(isActive && userService.isHasPermission([PERMISSIONS_CODE.ECARE_EMERGENCY_CLOSE])) {
    <div>
      <button class="btn-link" (click)="onClose()">
        <mat-icon svgIcon="ic-close-red" class="small-icon"></mat-icon>
        {{ 'common.close' | translate }}
      </button>
    </div>
  }
  `,
  standalone: true,
  imports: [IconModule, TranslateModule, CommonModule],
})
export class ActionCellCloseComponent implements ICellRendererAngularComp {
  @Input() rowIndex!: number;

  @Output() close = new EventEmitter<void>();
  userService = inject(UserService);
    
  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;
    
  params!: any;
  isActive: boolean = false;

  agInit(params: any): void {
    this.params = params;
    this.isActive = params?.data?.status && params?.data?.status?.code === 'ACTIVE' ? true : false;
    this.rowIndex = params.rowIndex;
  }

  refresh(params: any): boolean {
    return true;
  }

  onClose(): void {
    this.params.onClick(this.params.data);
  }
}
