<!-- <div class="icon-header">
  <mat-icon svgIcon="ic-dtc" class="icon-title"></mat-icon>
</div> -->

<h2 class="title-dialog">{{ "vehicle.trip.tripDetails" | translate }}</h2>
<div class="container-dialog">
  <app-ag-grid-custom [rowData]="rowData" [colDefs]="colDefs" [defaultColDef]="defaultColDef"
    [onlyTable]="true"></app-ag-grid-custom>
</div>
<mat-dialog-content class="container-dialog">
  @if (tripDetail?.staticMapUrl) {
    <!-- <div class="view-map">
      <app-current-location [onlyMap]="true" [vehicle]="vehicle"
        [flightPlanCoordinates]="flightPlanCoordinates"></app-current-location>
    </div> -->
    <div class="img-map">
      <img [src]="prefixImg + tripDetail?.staticMapUrl" />
    </div>
    
  } @else {
    <div class="no-data">
      {{ 'vehicle.trip.noTripRoute' | translate }}
    </div>
  }
  
</mat-dialog-content>