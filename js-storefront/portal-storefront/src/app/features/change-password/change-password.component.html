<div class="wrapper">
    <div class="container reset-pass-page">
        <div class="header-section">
            <h2 class="title-page">{{ 'changePassword.title' | translate }}</h2>
        </div>
        <form [formGroup]="passwordForm" (ngSubmit)="onSubmitNewPassword()">
            <div class="form-group-custom p-b-0">
                <div class="forminput">
                    <label for="currentPassword">
                        {{ 'changePassword.currentPassword' | translate }} <span class="required">*</span>
                    </label>
                    <div class="showpass">
                        <input [type]="passwordVisibility['currentPassword'] ? 'text' : 'password'" formControlName="currentPassword"
                            placeholder="Your current password" class="input-icon-container" />
                        <mat-icon (click)="togglePasswordVisibility('currentPassword')" [svgIcon]="passwordVisibility['currentPassword'] ? 'ic-eyes' : 'ic-eyes-close'">
                        </mat-icon>
                    </div>
                    <div *ngIf="passwordForm.get('currentPassword')?.invalid && passwordForm.get('currentPassword')?.touched"
                        class="error-message">
                        {{ 'changePassword.requiredField' | translate:{field: ('changePassword.currentPassword' | translate)} }}
                    </div>
                </div>
            </div>

            <div class="form-group-custom p-b-0">
                <div class="forminput">
                    <label for="newPassword">
                       {{ 'changePassword.newPassword' | translate }} <span class="required">*</span>
                    </label>
                    <div class="showpass">
                        <input [type]="passwordVisibility['newPassword'] ? 'text' : 'password'" formControlName="newPassword"
                            placeholder="Your new password" class="input-icon-container" />
                        <mat-icon (click)="togglePasswordVisibility('newPassword')" [svgIcon]="passwordVisibility['newPassword'] ? 'ic-eyes' : 'ic-eyes-close'">
                        </mat-icon>
                    </div>
                    <div *ngIf="passwordForm.get('newPassword')?.invalid && passwordForm.get('newPassword')?.touched"
                        class="error-message">
                        <span *ngIf="passwordForm.get('newPassword')?.hasError('required')">{{ 'changePassword.requiredField' | translate:{field: ('changePassword.newPassword' | translate)} }}</span>
                        <span *ngIf="passwordForm.get('newPassword')?.hasError('pattern')">{{ 'changePassword.passwordFormatError' | translate }}</span>
                    </div>
                </div>
                <div class="password-instructions">
                    <h4>{{ 'changePassword.passwordInstructions.title' | translate }}</h4>
                    <ul>
                        <li>{{ 'changePassword.passwordInstructions.instruction1' | translate }}</li>
                        <li>{{ 'changePassword.passwordInstructions.instruction2' | translate }}</li>
                        <li>{{ 'changePassword.passwordInstructions.instruction3' | translate }}</li>
                        <li>
                        {{ 'changePassword.passwordInstructions.instruction4' | translate }}
                        </li>
                        <li>{{ 'changePassword.passwordInstructions.instruction5' | translate }}</li>
                    </ul>
                </div>
            </div>

            <div class="form-group-custom p-b-0">
                <div class="forminput">
                    <label for="confirmPassword">
                        {{ 'changePassword.confirmPassword' | translate }} <span class="required">*</span>
                    </label>
                    <div class="showpass">
                        <input class="input-icon-container" [type]="passwordVisibility['confirmPassword'] ? 'text' : 'password'"
                            formControlName="confirmPassword" placeholder="Re-type new password" />
                        <mat-icon (click)="togglePasswordVisibility('confirmPassword')" [svgIcon]="passwordVisibility['confirmPassword'] ? 'ic-eyes' : 'ic-eyes-close'">
                        </mat-icon>
                    </div>
                    <div *ngIf="passwordForm.get('newPassword')?.value !== passwordForm.get('confirmPassword')?.value && passwordForm.get('confirmPassword')?.touched"
                        class="error-message">
                        {{ 'changePassword.passwordMismatch' | translate }}
                    </div>
                </div>
            </div>

            <div class="button-container">
                <button type="button" [ngClass]="{ 'disabled': passwordForm.invalid }"
                    class="btn btn-cancel btn--outline" (click)="onCancel()">
                    {{ 'common.cancel' | translate }}
                </button>
                <button type="submit" class="btn btn-submit btn-primary"
                    [ngClass]="{ 'disabled': passwordForm.invalid }">
                    {{ 'common.change' | translate }}
                </button>
            </div>
        </form>
    </div>
</div>