import { Component, inject } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { IconModule } from '../../core/icon/icon.module';
import { AuthService } from '../../core/services/auth.service';
import { NotificationService } from '../../core/services/notification.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { LoadingService } from '../../core/services/loading.service';

@Component({
  selector: 'app-change-password',
  standalone: true,
  providers: [NotificationService],
  imports: [CommonModule, ReactiveFormsModule, IconModule, TranslateModule],
  templateUrl: 'change-password.component.html',
})
export class ChangePasswordComponent {
  passwordForm: FormGroup;
  passwordVisibility: { [key: string]: boolean } = {
    currentPassword: false,
    newPassword: false,
    confirmPassword: false,
  };
  errorMessage: string = '';
  incorrectAttempts: number = 0;
  isDisabled: boolean = false;
  disableMessage: string = '';

  private authService = inject(AuthService);
  private notificationService = inject(NotificationService);
  private translate = inject(TranslateService);
  private loadingService = inject(LoadingService);

  constructor(private fb: FormBuilder) {
    this.passwordForm = this.fb.group({
      currentPassword: ['', Validators.required],
      newPassword: [
        '',
        [
          Validators.required,
          Validators.pattern(
            /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%^&*{}[\]:<>?/~+=]).{8,32}$/
          ),
        ],
      ],
      confirmPassword: ['', Validators.required],
    });
  }

  private showNotification(
    message: string,
    type: 'success' | 'error' | 'info' | 'warning'
  ): void {
    this.notificationService.showNotification(message, type);
  }

  togglePasswordVisibility(field: string): void {
    this.passwordVisibility[field] = !this.passwordVisibility[field];
  }

  onSubmitNewPassword(): void {
    if (this.isDisabled) {
      this.showNotification(this.disableMessage, 'error');
      return;
    }

    if (this.passwordForm.invalid) {
      return;
    }

    const { currentPassword, newPassword, confirmPassword } =
      this.passwordForm.value;

    if (newPassword !== confirmPassword) {
      this.showNotification(
        this.translate.instant('changePassword.passwordMismatch'),
        'error'
      );
      return;
    }

    this.loadingService.showLoader();

    this.authService.changePassword(currentPassword, newPassword).subscribe({
      next: (response) => {
        if (response)
        this.showNotification(
          this.translate.instant('changePassword.success'),
          'success'
        );
        this.passwordForm.reset();
        this.isDisabled = false;
        this.disableMessage = '';
      },
      error: (errorResponse) => {
        const error = errorResponse;
        if (errorResponse?.validNumberOfEntryPassword === false) {
          this.handleExceededAttempts();
        } else if (errorResponse?.validNumberOfEntryPassword === true) {
          this.showNotification(
            this.translate.instant('changePassword.incorrectCurrentPassword'),
            'error'
          );
        } else {
          this.showNotification(
            error?.message || this.translate.instant('changePassword.error'),
            'error'
          );
        }
      },
      complete: () => {
        this.loadingService.hideLoader();
      },
    });
  }

  private handleExceededAttempts(): void {
    this.isDisabled = true;
    this.disableMessage = this.translate.instant(
      'changePassword.threeAttemptsExceeded'
    );
    this.showNotification(this.disableMessage, 'error');

    setTimeout(() => {
      this.isDisabled = false;
      this.disableMessage = '';
    }, 10 * 60 * 1000);
  }

  onCancel(): void {
    this.passwordForm.reset();
    this.errorMessage = '';
  }
}
