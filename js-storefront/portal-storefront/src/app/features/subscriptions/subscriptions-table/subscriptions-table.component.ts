import { CommonModule } from '@angular/common';
import { Component, EventEmitter, inject, Input, OnInit, Output, TemplateRef, ViewChild } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { ColDef } from 'ag-grid-community';
import { PERMISSIONS_CODE } from '../../../core/constants';
import { ItemDevice, LastImportResult, PagingInfo } from '../../../core/interfaces';
import { DeviceActionPipe } from '../../../core/pipes';
import { UserService } from '../../../core/services/user';
import { AgGridCustomComponent } from '../../../core/shared';
import { IconCellRendererComponent } from '../../../core/shared/ag-grid-custom/icon-cell-renderer/icon-cell-renderer.component';
import { ImportResultSummaryComponent } from '../../../core/shared/import-result-summary/import-result-summary.component';


@Component({
  selector: 'app-subscriptions-table',
  templateUrl: './subscriptions-table.component.html',
  imports: [
    CommonModule,
    AgGridCustomComponent,
    IconCellRendererComponent,
    MatIconModule,
    DeviceActionPipe,
    ImportResultSummaryComponent,
  ],
  standalone: true,
})
export class SubscriptionsTableomponent implements OnInit {
  @Input() rowData: ItemDevice[];
  @Input() colDefs: ColDef[];
  @Input() importInfo: LastImportResult;
  @Input() pagingInfo: PagingInfo;

  @Input() isLoadingLastImport: boolean = false;
  @Input() isChangeItemPerPageTop = false;

  @Output() onPageChange = new EventEmitter<number>();
  @Output() onResultsPerPageChange = new EventEmitter<number>();
  @Output() changeItemPerPage = new EventEmitter<number>();
  @Output() downloadFile = new EventEmitter();

  @ViewChild('actionTemplate', { static: true })
  actionTemplate: TemplateRef<any>;

  @ViewChild('simStatusTemplate', { static: true })
  simStatusTemplate: TemplateRef<any>;

  @ViewChild('deviceStatusTemplate', { static: true })
  deviceStatusTemplate: TemplateRef<any>;

  @ViewChild('activationStatusTemplate', { static: true })
  activationStatusTemplate: TemplateRef<any>;

  customTemplates: { [key: string]: TemplateRef<any> } = {};

  userService = inject(UserService);
  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;

  defaultColDef = {
    resizable: false,
    valueFormatter: (params) => (params.value ? params.value : '-'),
    sortable: false,
    menuTabs: [],
    wrapHeaderText: true,
    autoHeaderHeight: true,
  };

  ngOnInit(): void {
    this.customTemplates = {
      action: this.actionTemplate,
      simStatus: this.simStatusTemplate,
      deviceStatus: this.deviceStatusTemplate,
      activationStatus: this.activationStatusTemplate,
    };
  }
}
