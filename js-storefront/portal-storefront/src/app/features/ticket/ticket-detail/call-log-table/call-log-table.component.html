<div class="call-log-table">
  <div class="call-log-table__header">
    <h3 class="call-log-table__title" (click)="addNewCallLog()">
      <mat-icon svgIcon="ic-log" class="medium-icon"></mat-icon>
      {{ "callLog.title" | translate }}
    </h3>
    <button *ngIf="userService.isHasPermission([PERMISSIONS_CODE.ECARE_CALL_LOG_MANAGE])" class="call-log-table__add-new" (click)="addNewCallLog()">
      <mat-icon svgIcon="ic-add-red" class="small-icon"></mat-icon>
      {{ "callLog.addNew" | translate }}
    </button>
  </div>

  <div class="call-log-table__content">
    <ng-container *ngIf="rowData && rowData.length > 0; else noData">
      <ag-grid-angular
        class="ag-theme-quartz custom-grid"
        [rowData]="rowData"
        [columnDefs]="colDefs"
        [domLayout]="'autoHeight'"
        [gridOptions]="gridOptions"
        [defaultColDef]="defaultColDef"
        [rowHeight]="rowHeight"
      />
    </ng-container>

    <ng-template #noData>
      <div class="no-data-wrapper">
        <table class="custom-table no-data-table">
          <thead>
            <tr>
              <th>{{ "callLog.dateTime" | translate }}</th>
              <th>{{ "callLog.type" | translate }}</th>
              <th>{{ "callLog.result" | translate }}</th>
              <th>{{ "callLog.comment" | translate }}</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td colspan="4" class="no-data-message">
                {{ "callLog.noData" | translate }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </ng-template>
  </div>
</div>
