@import "../../../../styles/abstracts/variables";

.ticket-detail {
  background: $bg-color-10;

  &__overview-container {
    &-content {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }
  }

  &__infor {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  &__breadcrumb {
    background: white;
    padding: 30px 30px 13px 30px;
  }

  &__content {
    padding: 30px;
    display: grid;
    grid-template-columns: 25% 75%;
    @media screen and (min-width: 1280px) and (max-width: 1440px) {
        grid-template-columns: 30% 69%;
        padding: 20px;
        gap: 15px;
    }

    gap: 20px;
    background: $bg-color-10;
  }

  &__box {
    background-color: $main-bg-color;
    padding: 25px;
    box-shadow: 0px 4px 5px 5px #00000008;
    @media screen and (min-width: 1280px) and (max-width: 1440px) {
        padding: 20px 15px;
    }
    &-underline {
        border-bottom: 2px solid #ff1744;
    }
    .section-ticket-loading {
      width: 50px;
      height: 50px;
      position: unset;
      margin: auto;
    }
  }

  &__box-haft {
    display: flex;
    gap: 20px;
    > * {
      width: 100%;
    }
  }

  &--authorization-pending {
    padding-left: 21px;
    border-left: 4px solid #266EF2;
  }
  
  &--authorization-approved {
    border-left: 4px solid #2ABA6C;
  }

  &--authorization-rejected {
    border-left: 4px solid #EB0A1E;
  }

  &--authorization-completed,
  &--authorization-expired,  &--authorization-not_authorized{
    border-left: 4px solid #808080;
  }

  &--verification {
    padding-left: 21px;
    border-left: 4px solid var(--Support-Orange, #DE9E1C);
    &-verified {
        border-left: 4px solid #2ABA6C;
    }
  }

  &--warning {
    padding-bottom: 21px;
    border-bottom: 4px solid var(--Support-Orange, #EB0A1E);
  }

  &--service-booking, &--manager-approval {
    position: relative;
  }

  &--svt {
    padding: 25px;
  }

  .vehicle-info {
    &__stat-icon {
        column-gap: 15px;
            mat-icon {
                width: 50px !important;
                height: 50px !important;
            @media screen and (min-width: 1280px) and (max-width: 1440px) {
                width: 40px !important;
                height: 40px !important;
            }
       }
    }
    &__title {
      color: #101010;
      font-size: 22px;
      margin-bottom: 8px;
    }

    &__sub-details {
      font-size: 14px;
      font-weight: 400;

      .text-capitalize {
        text-transform: capitalize;
      }

      .dot {
        font-weight: 600;
        margin: 0 10px;
      }
    }

    &__image {
      margin: 0 auto 10px;
      display: flex;
      justify-content: center;

      img {
        width: 100%;
        max-width: 300px;
        height: auto;
      }
    }

    &__status {
      display: flex;
      justify-content: center;
      margin: 10px auto 15px;

      &-disconnected {
        .vehicle-info__status-text {
            color: #808080;
            background: #f5efef;
            border: 2px solid #808080;
        }
      }
      
      &-text {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        background: #e8f5e9;
        color: #4caf50;
        border: 2px solid #2aba6c;
        border-radius: 29px;
        padding: 8px 25px 10px;
        font-size: 16px;
        font-weight: 600;

        mat-icon {
          margin: 5px 0 0 5px;
        }
      }
    }

    &__details {
      margin-bottom: 20px;

      &__title {
        font-size: 16px;
        font-weight: bold;
        margin: 5px 0;
      }

      &__sub-details {
        font-size: 14px;
        color: #666;
      }
    }

    &__stats {
      display: grid;
      grid-template-columns: repeat(3, 32%);
      column-gap: 25px;
      justify-content: start;
      margin-top: 20px;

       @media screen and (min-width: 1280px) and (max-width: 1440px) {
        column-gap: 15px;
       }
    }

    &__stat {
      display: grid;
      grid-template-columns: 30% 70%;
      column-gap: 10px;
      align-items: center;
      &:last-child {
        margin-left: -20px;
        column-gap: 5px;
      }
      &-icon {
        display: flex;
        justify-content: center;
        align-items: center;

        &--green {
          mat-icon {
            color: #4caf50;
          }
        }

        &--red {
          mat-icon {
            color: #f44336;
          }
        }
      }

      &-text {
        display: flex;
        flex-direction: column;

        p {
          font-size: 16px;
          font-weight: 600;
          margin: 0;
          &.disconnect {
            color: #808080;
          }

          &.green {
            color: #4caf50;
          }

          &.red {
            color: #f44336;
          }
        }

        small {
          font-size: 12px;
          font-weight: 400;
          color: #101010;
          &.disconnect {
            color: #808080;
          }
        }
      }
    }
  }

  .warning-occurred {
    &__title {
        display: flex;
        align-items: center;
        font-size: 22px;
        font-weight: 600;
        margin-left: -10px;

        mat-icon {
            margin-right: 12px;
        }
    }

    &__list {
      list-style: none;
      padding: 0;
      margin: 0;

      .warning-occurred__item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        .time-line {
          position: relative;
        }

        .dot {
          width: 7px;
          height: 7px;
          background-color: #333;
          border-radius: 50%;
          margin: 5px 10px 0px 0;
          position: relative;
          z-index: 1;
        }

        .line {
          width: 2px;
          height: 32px;
          background-color: #eeeeee;
          margin: 5px auto 0;
          position: absolute;
          z-index: 0;
          top: 0;
          left: 2.7px;
        }

        .time-info {
          display: flex;
          flex-direction: column;

          .text {
            font-size: 14px;
            color: #333;
          }
        }
      }

      .warning-occurred__item:last-child .line {
        display: none;
      }
    }
  }
}
