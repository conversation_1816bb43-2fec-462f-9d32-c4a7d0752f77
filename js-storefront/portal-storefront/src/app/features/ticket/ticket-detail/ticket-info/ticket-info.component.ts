import { Component, EventEmitter, Input, Output, inject } from '@angular/core';
import { Ticket } from '../../../../core/interfaces/ticket-detail.interface';
import { CommonModule } from '@angular/common';
import { IconModule } from '../../../../core/icon/icon.module';
import { MatDialog } from '@angular/material/dialog';
import { EditTicketInfoFormComponent } from './edit-ticket-info-modal/edit-ticket-info-modal.component';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { PriorityTicket, TypeTicket } from '../../../../core/enums/tickets.enum';
import { UserService } from '../../../../core/services/user';
import { PERMISSIONS_CODE } from '../../../../core/constants';

@Component({
  selector: 'app-ticket-info',
  standalone: true,
  templateUrl: './ticket-info.component.html',
  styleUrls: ['./ticket-info.component.scss'],
  imports: [CommonModule, IconModule, TranslateModule],
})
export class TicketInfoComponent {
  @Input() ticket: Ticket;
  @Output() ticketUpdated = new EventEmitter<void>();
  
  private dialog = inject(MatDialog);
  translateService = inject(TranslateService);
  userService = inject(UserService);
      
  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;
  readonly TypeTicket = TypeTicket;

  PriorityTicket = PriorityTicket;

  openEditModal(): void {
    const dialogRef = this.dialog.open(EditTicketInfoFormComponent, {
      width: '850px',
      maxHeight: '90vh',
      data: {
        title: this.translateService.instant(
          'tickets.ticketDetail.editModalInfo.title'
        ),
        ...this.ticket,
      },
    });

    dialogRef.afterClosed().subscribe((isUpdatedTicket: boolean) => {
      if (isUpdatedTicket) {
        this.ticketUpdated.emit();
      }
    });
  }
}
