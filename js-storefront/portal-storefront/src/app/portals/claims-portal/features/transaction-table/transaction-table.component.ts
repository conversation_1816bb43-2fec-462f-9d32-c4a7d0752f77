import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  inject,
  Input,
  OnInit,
  Output,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { AgGridAngular } from 'ag-grid-angular';
import { ColDef } from 'ag-grid-community';
import { TransactionTableControlsComponent } from "./transaction-table-controls/transaction-table-controls.component";
import { PagingInfo } from '../../../../core/interfaces';
import { ScreenSizeService } from '../../../../core/services';
import { PaginationComponent } from '../../../../core/shared/pagination/pagination.component';
import { UserService } from '../../../../core/services/user';
import { ROLES } from '../../../../core/constants/roles.const';

@Component({
  selector: 'app-transaction-table',
  standalone: true,
  imports: [
    CommonModule,
    AgGridAngular,
    PaginationComponent,
    TransactionTableControlsComponent
],
  providers: [ScreenSizeService],
  templateUrl: './transaction-table.component.html',
  styleUrls: ['./transaction-table.component.scss'],
})
export class TransactionTableComponent {
  @ViewChild('myGrid') grid!: AgGridAngular;
  @Input() rowData: any[];
  @Input() colDefs: ColDef[];
  @Input() defaultColDef: ColDef<any, any>;
  @Input() gridOptions: any;
  @Input() rowHeight = 52;
  @Input() isNotCreateClaim: boolean = false;
  @Input() isShowActionExport: boolean = true;

  @Input() templates: { [key: string]: TemplateRef<any> } = {};
  @Input() pagingInfo: PagingInfo;
  @Input() isChangeItemPerPageTop = false;
  @Input() isTextWrap: boolean = false;
  @Input() isVerticalScroll: boolean = false;

  @Output() onPageChange = new EventEmitter<number>();
  @Output() onResultsPerPageChange = new EventEmitter<number>();
  @Output() changeItemPerPage = new EventEmitter();
  @Output() exportData = new EventEmitter<void>();
  @Output() rowSelected = new EventEmitter();
  @Output() createClaim = new EventEmitter();
  @Output() combieClaim = new EventEmitter();
  
  screenSizeService = inject(ScreenSizeService);
  userService = inject(UserService);
  rowsSelected: any[] = [];
  totalClaim: number = 0;
  currencyIso: string = 'PHP';
  currentRoles: any;
  currentRole: any;
  ngOnInit(): void {
    this.currentRoles = JSON.parse(localStorage.getItem('roles'))?.roles || [];
    this.currentRole = this.userService.getGroupRole(this.currentRoles);
    if (this.isTextWrap) {
      this.defaultColDef.wrapText = true; // Wrap Text
      this.defaultColDef.autoHeight = true; // Adjust Cell Height to Fit Wrapped Text
      this.defaultColDef.wrapHeaderText = true; // Wrap Header Text
      this.defaultColDef.autoHeaderHeight = true; // Adjust Header Height to Fit Wrapped Text
    }
    this.gridOptions.onRowClicked = this.onRowClicked.bind(this);
  }

  onRowClicked(params: any) {
    if ((params?.event?.ctrlKey || params?.event?.shiftKey) && this.currentRole !== ROLES.CASHIERGROUP) {
      params.node.setSelected(false);
    }
  }

  changeItemPerPageTop(event): void {
    this.isChangeItemPerPageTop = !this.isChangeItemPerPageTop;
    this.changeItemPerPage.emit(event);
  }

  handleGetRowSelected(event: any) {
    this.rowsSelected = event.api.getSelectedRows();
    if (!this.isNotCreateClaim) {
      this.totalClaim = this.rowsSelected.reduce((total, item) => total + (+item?.claimAmount?.value || 0), 0);
      this.rowsSelected.forEach(element => {
        if (element?.currencyIso) {
          this.currencyIso = element.currencyIso;
        }
      });
    }

    this.rowSelected.emit(event.api.getSelectedRows());
  }
}
