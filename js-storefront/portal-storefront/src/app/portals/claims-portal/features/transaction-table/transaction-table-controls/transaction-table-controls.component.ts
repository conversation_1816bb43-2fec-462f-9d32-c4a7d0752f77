import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { TranslateModule } from '@ngx-translate/core';
import { IconModule } from '../../../../../core/icon/icon.module';
import { PaginationComponent } from '../../../../../core/shared/pagination/pagination.component';
import { TableActionComponent } from '../../../../../core/shared/table-action/table-action.component';

@Component({
  selector: 'app-transaction-table-controls',
  templateUrl: './transaction-table-controls.component.html',
  styleUrls: ['./transaction-table-controls.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IconModule,
    MatSelectModule,
    MatFormFieldModule,
    TranslateModule,
    TableActionComponent,
  ],
})
export class TransactionTableControlsComponent extends PaginationComponent {
  @Input()
  set selectedList(value: any[]) {
    this._selectedList = value || [];
    this.calculateTotalClaimableAmount();
  }
  @Input() value: any = 0;
  @Input() currencyIso: string = 'PHP';
  @Input() isNotCreateClaim: boolean = false;

  @Output() createClaim = new EventEmitter<void>();
  @Output() combieClaim = new EventEmitter<void>();
  @Output() totalClaimableAmountChange = new EventEmitter<number>();
  totalClaimableAmount: number = 0;
  private _selectedList: any[] = [];
  
  get selectedList(): any[] {
    return this._selectedList;
  }

  get isValidatedMarketing(): boolean {
    if (!this.selectedList || this.selectedList.length === 0) {
      return false;
    }
    return this.selectedList.every(
      (item) => item?.status?.code === 'VALIDATED_MARKETING'
    );
  }
  onCreateClaim() {
    this.createClaim.emit();
  }
  onCombieClaim() {
    this.combieClaim.emit();
  }

  calculateTotalClaimableAmount(): void {
    this.totalClaimableAmount = this.selectedList.reduce((sum, item) => {
      const claimableAmountValue = parseFloat(item?.claimableAmount?.value);
      return isNaN(claimableAmountValue) ? sum : sum + claimableAmountValue;
    }, 0);
    this.totalClaimableAmountChange.emit(this.totalClaimableAmount);
  }
}
