import { Component, inject, OnInit } from '@angular/core';
import { ReactiveFormsModule, FormGroup, FormControl, Validators } from '@angular/forms';
import {
  MatDialogModule,
  MAT_DIALOG_DATA,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Subscription, finalize } from 'rxjs';
import { ActionModal } from '../../../../../core/enums';
import { handleErrors } from '../../../../../core/helpers';
import {
  NotificationService,
  LoadingService,
} from '../../../../../core/services';
import { FormGroupComponent } from '../../../../../core/shared';
import { PopupOrArIssueComponent } from '../../../pages/promotions/popup-or-ar-issue/popup-or-ar-issue.component';
import { PromotionsService } from '../../../services/promotion/promotions.service';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-modal-reference-number',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatIconModule,
    TranslateModule,
    ReactiveFormsModule,
    FormGroupComponent,
  ],
  providers: [PromotionsService, NotificationService],
  templateUrl: './modal-reference-number.component.html',
  styleUrls: ['./modal-reference-number.component.scss'],
})
export class ModalReferenceNumberComponent implements OnInit {
  data = inject(MAT_DIALOG_DATA);
  private dialogRef = inject(MatDialogRef<PopupOrArIssueComponent>);
  private loadingService = inject(LoadingService);
  private promotionsService = inject(PromotionsService);
  private translateService = inject(TranslateService);
  private notificationService = inject(NotificationService);

  formUpdateDetails: FormGroup = new FormGroup({
    referenceNumber: new FormControl('', [
      Validators.required,
      Validators.maxLength(10)
    ]),
  });
  claimId: string;
  subscription = new Subscription();

  get referenceNumber() {
    return this.formUpdateDetails.get('referenceNumber') as FormControl;
  }

  ngOnInit() {
    this.claimId = this.data.claimId;
    this.referenceNumber.patchValue(this.data?.referenceNumber);
  }

  ngOnDestroy() {
    this.subscription?.unsubscribe();
  }
  onCancel() {
    this.dialogRef.close();
  }

  onConfirm() {
    this.loadingService.showLoader();
    const body = {
      referenceNumber: this.referenceNumber.value,
    };
    this.subscription.add(
      this.promotionsService
        .updateReferenceNumber(body, this.claimId)
        .pipe(finalize(() => this.loadingService.hideLoader()))
        .subscribe(
          (res) => {
            if (res.code === '200') {
              this.notificationService.showSuccess(
                this.translateService.instant(
                  'receivables.referenceNumberUpdatedSuccessfully',
                  { claimId: this.claimId }
                )
              );
            } else {
              this.notificationService.showSuccess(res.message);
            }

            this.dialogRef.close({ action: ActionModal.Submit });
          },
          (error) => {
            handleErrors(error, this.notificationService);
          }
        )
    );
  }
}
