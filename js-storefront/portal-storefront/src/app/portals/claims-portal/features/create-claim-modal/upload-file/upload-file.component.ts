import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-upload-file-claim',
  standalone: true,
  imports: [TranslateModule, CommonModule, MatIconModule, ReactiveFormsModule],
  templateUrl: './upload-file.component.html',
  styleUrls: ['./upload-file.component.scss']
})
export class UploadFileClaimComponent implements OnInit {
  @Input() control!: FormControl;
  @Input() form!: FormGroup;
  listFile = [];
  ngOnInit() {
  }

  onFileSelected(event: any): void {
    const input = event.target;
    if (!input.files?.length) {
      return;
    }
    const files = Array.from(input.files);
    this.listFile = [...this.listFile, ...files];
    this.control.patchValue(this.listFile);
  }

  clearFiles(fileInput: HTMLInputElement): void {
    fileInput.value = '';
    // this.filesSelected.emit(null);
    // this?.control?.patchValue(null);
  }

  onClickUploadFile(fileInput: HTMLInputElement) {
    this.clearFiles(fileInput);
    fileInput.click()
  }

  removeFile(item, index: number) {
    this.listFile.splice(index, 1)
    this.control.patchValue(this.listFile);
  }
}
