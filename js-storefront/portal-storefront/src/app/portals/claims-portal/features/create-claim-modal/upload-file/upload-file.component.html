<div class="upload-file-claim">
  <h2>{{ "promotions.popup.attachmentToAttached" | translate }}</h2>
  @if(listFile && listFile.length) {
    <div class="upload-file-list">
      @for (file of listFile; let i = $index; track file?.name) {
        <div class="upload-line">
          <div class="upload-file">
            <mat-icon svgIcon="ic-file-claim" class="small-icon"></mat-icon>
            <span class="upload-file-type">{{file?.name}}</span>
          </div>
          <div class="upload-file-actions">
            <div class="upload-remove" (click)="removeFile(file, i)">
              <mat-icon
                svgIcon="ic-delete"
                class="small-icon"
              ></mat-icon>
            </div>
          </div>
        </div>
      }
    </div>
  }

  <div class="form-group" [formGroup]="form">
      <div class="form-group__input">
        <div (click)="onClickUploadFile(fileInput)" class="upload-file-button">
          <mat-icon svgIcon="ic-upload-file-claim"></mat-icon>
          <span>{{'promotions.popup.uploadFile' |translate }}</span>
        </div>
    
        <input
          #fileInput
          type="file"
          [multiple]="true"
          class="hidden-file-input"
          (change)="onFileSelected($event)"
        />
      </div>
  </div>
</div>