import { CommonModule } from '@angular/common';
import { Component, inject, OnInit, ViewChild } from '@angular/core';
import { ReactiveFormsModule, FormGroup, FormControl, Validators } from '@angular/forms';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { AgGridAngular } from 'ag-grid-angular';
import { ColDef } from 'ag-grid-community';
import { catchError, finalize, tap, throwError } from 'rxjs';
import { ActionModal } from '../../../../core/enums';
import { handleErrors } from '../../../../core/helpers';
import { NotificationService, LoadingService } from '../../../../core/services';
import { FormGroupComponent } from '../../../../core/shared';
import { CellCurrencyCustomComponent } from '../../../insurance-portal/features/insurance-details/parts-and-services/cell-currency-custom/cell-currency-custom.component';
import { UploadFileClaimComponent } from './upload-file/upload-file.component';
import { PromotionsService } from '../../services/promotion/promotions.service';
import { ReceivableService } from '../../services/receivable/receivable.service';
import { DiagnosticTroubleCodeDetailComponent } from '../../../iot-portal/features/items-detail/diagnostic-trouble-code/diagnostic-trouble-code-detail/diagnostic-trouble-code-detail.component';

@Component({
  selector: 'app-create-claim-modal',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatIconModule,
    TranslateModule,
    AgGridAngular,
    ReactiveFormsModule,
    FormGroupComponent,
    UploadFileClaimComponent,
  ],
  providers: [PromotionsService, NotificationService, ReceivableService],
  templateUrl: './create-claim-modal.component.html',
  styleUrls: ['./create-claim-modal.component.scss'],
})
export class CreateClaimModalComponent implements OnInit {
  @ViewChild('myGrid') grid!: AgGridAngular;
  data = inject(MAT_DIALOG_DATA);
  dialogRef = inject(MatDialogRef<DiagnosticTroubleCodeDetailComponent>);
  translateService = inject(TranslateService);
  promotionsService = inject(PromotionsService);
  receivableService = inject(ReceivableService);
  notificationService = inject(NotificationService);
  loadingService = inject(LoadingService);

  rowData: any[] = [];
  title: string;
  rowSelected: any;
  totalClaim: any;
  currencyIso: string;
  isReceivable: boolean = false;
  createClaimForm: FormGroup = new FormGroup({
    referenceNumber: new FormControl('', [
      Validators.required,
      Validators.maxLength(10),
      Validators.minLength(10),
    ]),
    attachment: new FormControl(''),
  });
  defaultColDef: ColDef<any> = {
    resizable: false,
    sortable: false,
    menuTabs: [],
    suppressMovable: true,
  };

  gridOptions: any = {
    rowSelection: 'multiple',
    suppressRowClickSelection: true,
    stopEditingWhenCellsLoseFocus: true,
    alwaysShowVerticalScroll: true,
  };
  colDefs: any;

  get referenceNumber() {
    return this.createClaimForm.get('referenceNumber') as FormControl;
  }

  get attachment() {
    return this.createClaimForm.get('attachment') as FormControl;
  }

  ngOnInit() {
    this.title = this.data.title;
    this.rowData = this.data.rowData;
    this.isReceivable = this.data.isReceivable;
    if (this.isReceivable) {
      this.columnReceivable();
    } else {
      this.columnPromotion();
    }
  }

  ngAfterViewInit() {
    this.rowData.forEach((item) => {
      this.autoSelectModel(item.id);
    });
  }

  handleGetRowSelected(event: any) {
    this.rowSelected = event.api.getSelectedRows();
    this.totalClaim = this.rowSelected.reduce((total, item) => {
      const claimAmountValue = parseFloat(
        item?.claimableAmount?.value || item?.claimAmount?.value
      );
      return isNaN(claimAmountValue) ? total : total + claimAmountValue;
    }, 0);

    if (this.rowSelected.length > 0) {
      const currencies = this.rowSelected
        .map((item) => item?.claimableAmount?.currencyIso)
        .filter(Boolean) as string[];
      const uniqueCurrencies = [...new Set(currencies)];

      if (uniqueCurrencies.length === 1) {
        this.currencyIso = uniqueCurrencies[0] || 'PHP';
      } else if (uniqueCurrencies.length > 1) {
        this.currencyIso = 'PHP';
      } else {
        this.currencyIso = '';
      }
    } else {
      this.currencyIso = 'PHP';
    }
  }

  autoSelectModel(id: any) {
    const nodesToSelect: any[] = [];
    this.grid.api.forEachNode((node: any, index: number) => {
      if (node.data.id === id) {
        nodesToSelect.push(node);
      }
    });

    this.grid.api.setNodesSelected({
      nodes: nodesToSelect,
      newValue: true,
    });
  }

  onCancel() {
    this.dialogRef.close();
  }

  async onSubmit() {
    if (this.createClaimForm.invalid) return;
    this.loadingService.showLoader();
    try {
      let docs: string[] = [];
      let docNames: string[] = [];
      for (const file of this.attachment.value) {
        const content: any = await this.readFileAsync(file);
        docs.push(content);
        docNames.push(file.name);
      }
      if (this.data.mode === 'combine') {
        const selectedUids = this.rowSelected.map((item) => item.id).join(';');
        const body: any = {
          uids: selectedUids,
          sbRefNumber: this.referenceNumber.value,
          docs: docs,
          docsName: docNames,
        };
        this.receivableService
          .combineReceivables(body)
          .pipe(
            finalize(() => this.loadingService.hideLoader()),
            tap((res) => {
              this.notificationService.showSuccess(
                this.translateService.instant(
                  'promotions.message.combineClaimSuccess', { claimId: res.id }
                )
              );
              this.dialogRef.close({ action: ActionModal.Submit });
            }),
            catchError((err) => {
              handleErrors(err, this.notificationService);
              return throwError(() => err);
            })
          )
          .subscribe();
      } else if (this.data.mode === 'create') {
        const body: any = {
          promotionIds: this.rowSelected.map((item) => item.id),
          referenceNumber: this.referenceNumber.value,
          docs,
          docNames,
        };
        this.promotionsService
          .createClaim(body)
          .pipe(finalize(() => this.loadingService.hideLoader()))
          .subscribe(
            (res) => {
              this.notificationService.showSuccess(
                this.translateService.instant(
                  'promotions.popup.createSuccess',
                  { claimId: res.id }
                )
              );
              this.dialogRef.close({ action: ActionModal.Submit });
            },
            (err) => {
              handleErrors(err, this.notificationService);
            }
          );
      }
    } catch {
      this.loadingService.hideLoader();
    }
  }

  readFileAsync(file) {
    return new Promise((resolve, reject) => {
      const reader: any = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result.split(',')[1]);
    });
  }

  columnReceivable() {
    this.colDefs = [
      {
        headerName: '',
        headerCheckboxSelection: false,
        headerCheckboxSelectionFilteredOnly: false,
        checkboxSelection: true,
        field: 'select_all',
        sortable: false,
        wrapText: true,
        autoHeight: true,
        width: 48,
      },
      {
        headerName: this.translateService.instant('receivables.table.id'),
        headerValueGetter: () =>
          this.translateService.instant('receivables.table.id'),
        field: 'id',
        wrapText: true,
        autoHeight: true,
        flex: 1,
        cellRenderer: (params) => {
          return params?.value ? params?.value : '-';
        },
      },
      {
        headerName: this.translateService.instant(
          'receivables.table.claimableAmount'
        ),
        headerValueGetter: () =>
          this.translateService.instant('receivables.table.claimableAmount'),
        field: 'claimableAmount',
        cellRenderer: CellCurrencyCustomComponent,
        maxWidth: 200,
        minWidth: 200,
      },
    ];
  }

  columnPromotion() {
    this.colDefs = [
      {
        headerName: '',
        headerCheckboxSelection: false,
        headerCheckboxSelectionFilteredOnly: false,
        checkboxSelection: true,
        field: 'select_all',
        sortable: false,
        wrapText: true,
        autoHeight: true,
        width: 48,
      },
      {
        headerName: this.translateService.instant(
          'promotions.table.dbmOrderNumber'
        ),
        headerValueGetter: () =>
          this.translateService.instant('promotions.table.dbmOrderNumberFull'),
        field: 'dbmOrderNumber',
        wrapText: true,
        autoHeight: true,
        flex: 1,
        cellRenderer: (params) => {
          return params?.value ? params?.value : '-';
        },
      },
      {
        headerName: this.translateService.instant(
          'insuranceClaimsDetails.services.laborCosts'
        ),
        headerValueGetter: () =>
          this.translateService.instant(
            'insuranceClaimsDetails.services.laborCosts'
          ),
        field: 'claimAmount',
        cellRenderer: CellCurrencyCustomComponent,
        maxWidth: 200,
        minWidth: 200,
      },
    ];
  }
}
