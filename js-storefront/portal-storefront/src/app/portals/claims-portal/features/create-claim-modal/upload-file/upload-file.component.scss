@import "../../../../../../styles/abstracts/variables";

.upload-file-claim {
  display: flex;
  flex-direction: column;
  gap: 15px;

  h2 {
    margin: 0;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
  }

  .upload-file-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
    max-height: 235px;
    overflow: auto;

    .upload-line {
      display: flex;
      gap: 15px;

      // flex-wrap: wrap;
      .upload-file {
        margin-right: auto;
        font-weight: 400;
        min-width: 53px;
      }

      .upload-file-name {
        font-weight: 400;
      }

      .upload-date {
        color: $text-color-4;
        font-weight: 400;
      }

      .upload-file-actions {
        flex-shrink: 0;
        display: flex;
        gap: 15px;
        font-weight: 600;
      }
    }

    .small-icon {
      position: relative;
      top: 2px;
      margin-right: 5px;
    }
  }
  .hidden-file-input {
    display: none;
  }
}


.upload-download,
.upload-remove {
  cursor: pointer;
}

.upload-file-button {
  display: flex;
  align-items: flex-end;
  gap: 5px;
  span {
    color: var(--Primary-Red, #EB0A1E);
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px;
    cursor: pointer;
  }
  mat-icon {
    cursor: pointer;
  }
}
