import { CommonModule } from '@angular/common';
import { Component, EventEmitter, inject, Input, Output } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatIcon } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-upload-file',
  standalone: true,
  imports: [CommonModule, MatSelectModule, MatIcon, ReactiveFormsModule, TranslateModule],
  templateUrl: './upload-file.component.html',
  styleUrl: './upload-file.component.scss'
})
export class UploadFileComponent {
  @Input() label: string;
  @Input() multiple: boolean = false; // Allow multiple file selection
  @Input() accept: string = ''; // Accept file types (e.g., '.jpg,.png,.pdf')
  @Input() control!: FormControl;
  @Input() placeholder: string = '';
  @Input() required: boolean = false;
  @Input() isLabelHidden: boolean = true;
  @Input() disabled: boolean = false;

  @Output() filesSelected = new EventEmitter<FileList>();
  @Output() handleInvalidFile = new EventEmitter<void>();

  fileNames: string = '';

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (!input.files?.length) {
      return;
    }
    const files = Array.from(input.files);
    const invalidFiles = files.filter((file) => !this.isValidFileType(file));
    if (invalidFiles.length > 0) {
      this.handleInvalidFile.emit();
      this.clearFiles(input);
    } else {
      this.fileNames = files.map((file) => file.name).join(', ');
      this.filesSelected.emit(input.files);
    }
  }

  clearFiles(fileInput: HTMLInputElement): void {
    fileInput.value = '';
    this.fileNames = '';
    // this.filesSelected.emit(null);
    this?.control?.patchValue(null);
  }

  isValidFileType(file: File): boolean {
    const acceptedTypes = this.accept.split(',').map((type) => type.trim());
    return acceptedTypes.some(
      (type) => file?.type?.includes(type) || file?.name?.endsWith(type)
    );
  }

  onClickUploadFile(fileInput: HTMLInputElement) {
    this.clearFiles(fileInput);
    fileInput.click()
  }
}
