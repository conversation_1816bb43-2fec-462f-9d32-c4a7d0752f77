import { Routes } from '@angular/router';
import { ContainerViewComponent } from '../../layout/global/container-view/container-view.component';
import { CLAIM_ROLES, CLAIM_ROLES_CASHIER } from '../../core/constants/roles.const';
import { PromotionsComponent } from './pages/promotions/promotions.component';
import { RebateComponent } from './pages/rebate/rebate.component';
import { ReceivablesComponent } from './pages/receivables/receivables.component';
import { ReportsComponent } from './pages/reports/reports.component';
import { PromotionDetailComponent } from './pages/promotions/promotion-detail/promotion-detail.component';
import { TransactionDetailsComponent } from './pages/promotions/transactions-tab/transaction-details/transaction-details.component';
import { AddReceivableComponent } from './pages/receivables/add-receivable/add-receivable.component';
import { RebateClaimDetailComponent } from './pages/rebate/rebate-claims/rebate-claim-detail/rebate-claim-detail.component';
import { ReceivableDetailsComponent } from './pages/receivables/receivables-transaction-tab/receivable-details/receivable-details.component';
import { ReceivableClaimDetailsComponent } from './pages/receivables/receivable-claim-tab/receivable-claim-details/receivable-claim-details.component';
import { PermissionPortalClaimGuard } from './services/permission-portal-claim.guard.service';

export const CLAIM_PORTAL_ROUTES: Routes = [
  {
    path: '',
    component: ContainerViewComponent,
    children: [
      {
        path: 'promotions', 
        component: PromotionsComponent,
        data: { roles: CLAIM_ROLES },
      }, {
        path: 'promotions/claim-detail/:id', 
        component: PromotionDetailComponent,
        data: { roles: CLAIM_ROLES },
      }, {
        path: 'promotions/promotions-transactions/:id', 
        component: TransactionDetailsComponent,
        data: { roles: CLAIM_ROLES },
      }, {
        path: 'rebate',
        component: RebateComponent,
        data: { roles: CLAIM_ROLES }
      }, {
        path: 'rebate/claim-detail/:id',
        component: RebateClaimDetailComponent,
        data: { roles: CLAIM_ROLES }
      }, {
        path: 'receivables',
        component: ReceivablesComponent,
        data: { roles: CLAIM_ROLES }
      }, {
        path: 'receivables/new-receivable',
        component: AddReceivableComponent,
        data: { roles: CLAIM_ROLES_CASHIER },
        canActivate: [PermissionPortalClaimGuard]
      }, {
        path: 'receivables/receivables-transactions/:id', 
        component: ReceivableDetailsComponent,
        data: { roles: CLAIM_ROLES },
      },
      {
        path: 'receivables/receivables-claim/:id', 
        component: ReceivableClaimDetailsComponent,
        data: { roles: CLAIM_ROLES },
      },{
        path: 'reports',
        component: ReportsComponent,
        data: { roles: CLAIM_ROLES }
      }, {
        path: '**',
        redirectTo: 'promotions'
      }
    ]
  }
];
