import { Component, inject, <PERSON><PERSON><PERSON><PERSON>, OnInit, signal } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { BreadcrumbWithLabelComponent } from '../../../../../core/shared/breadcrumb-with-label/breadcrumb-with-label.component';
import { MatIconModule } from '@angular/material/icon';
import { CommonModule } from '@angular/common';
import { MatAccordion, MatExpansionModule } from '@angular/material/expansion';
import { MatDialog } from '@angular/material/dialog';
import { NotificationService, LoadingService, ScreenSizeService } from '../../../../../core/services';
import { PopupApprovePromotionClaimComponent } from '../popup-approve-promotion-claim/popup-approve-promotion-claim.component';
import { PopupRejectPromotionClaimComponent } from '../popup-reject-promotion-claim/popup-reject-promotion-claim.component';
import { Form<PERSON>roup, FormControl, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { PromotionClaimDetail } from '../../../../../core/interfaces/promotions.interface';

import { Documents } from '../../../../insurance-portal/interfaces';
import { downloadFile } from '../../../../../core/helpers/function-common.helper';
import { FileListComponent } from '../../../features/file-list/file-list.component';
import { PromotionsService } from '../../../services/promotion/promotions.service';
import { PromotionClaimDetailAction, PromotionClaimStatus } from '../../../enum';
import { ROLES } from '../../../../../core/constants/roles.const';
import { UserService } from '../../../../../core/services/user';
import { UploadFileComponent } from '../../../features/upload-file/upload-file.component';
import { filter } from 'rxjs';
import { ActionModal } from '../../../../../core/enums';
import { handleErrors } from '../../../../../core/helpers';
import { ModalReferenceNumberComponent } from '../../../features/modal/modal-reference-number/modal-reference-number.component';

@Component({
  selector: 'app-promotion-detail',
  standalone: true,
  imports: [
    CommonModule, 
    MatIconModule, 
    TranslateModule,
    BreadcrumbWithLabelComponent,
    MatExpansionModule,
    FileListComponent,
    UploadFileComponent,
  ],
  providers: [
    ScreenSizeService,
    LoadingService,
    NotificationService,
    PromotionsService
  ],
  templateUrl: './promotion-detail.component.html',
  styleUrl: './promotion-detail.component.scss'
})
export class PromotionDetailComponent implements OnInit, OnDestroy  {

  readonly PromotionClaimStatus = PromotionClaimStatus;
  readonly PromotionClaimDetailAction = PromotionClaimDetailAction

  dialog = inject(MatDialog);
  translateService = inject(TranslateService);
  notificationService = inject(NotificationService);
  loadingService = inject(LoadingService);
  public screenSizeService = inject(ScreenSizeService);
  route = inject(ActivatedRoute);
  router = inject(Router);

  promotionService = inject(PromotionsService)
  userService = inject(UserService)

  isMarketingTeam: boolean = false
  isTreasury: boolean = false
  isCashier: boolean = false

  rejectPromotionClaimForm: FormGroup = new FormGroup({
    rejectReason: new FormControl('', [Validators.required]),
    rejectNote: new FormControl(''),
    remarks: new FormControl(''),
  });

  public get rejectReasonControl(): FormControl {
    return this.rejectPromotionClaimForm.get('rejectReason') as FormControl;
  }

  public get remarksControl(): FormControl {
    return this.rejectPromotionClaimForm.get('remarks') as FormControl;
  }

  public get rejectNoteControl(): FormControl {
    return this.rejectPromotionClaimForm.get('rejectNote') as FormControl;
  }
  
  promotionId: string
  totalRelatedTransactions: number = 0

  promotionClaimDetail: PromotionClaimDetail

  transactionListExpandStatus: any = []

  statusPromo: string

  attachedDocumentList: any = []

  attachedDeletedDocumentList: any = []

  relatedTransactionList: any = []

  relatedTransactionDeletedList: any = []

  mapKeyTransactionList: any = [
    "status",
    "dealerName",
    "customerName",
    "customerId",
    "customerType",
    "customerGroup",
    "billingDocument",
    "invoiceCreationDate",
    "postBillingApprovalDate",
    "vehicleModel",
    "modelYear",
    "mileage",
    "billTo",
    "discount",
    "split",
    "claimAmount",
    "orderType",
    "jobType",
    "releaseDate",
    "variant",
    "materialNumber",
    "materialDescription",
    "customerAdvisor",
    "vehicleGuid",
    "vin",
    "reservationDate",
    "documentNo",
    "deliveryDocNo",
    "forDeliveryDate",
    "orderDate",
    "vehicleDeliveryDate",
    "serviceBillingAmount",
    "orderReason",
    "reservationDocNo",
    "reservationApprovalDate",
    "paymentMethod",
    "quantity",
    "cxPromotionCode",
    "srp",
    "orderStatus",
    "dbmOrderNumber",
    "packageId"
  ]

  step = signal(0);

  setExpanded(index: number, check: boolean) {
    // this.transactionListExpandStatus[index].check = check
    this.step.set(index);

    if(!check) {
      this.step.set(-1)
    }
  }

  onApprovePromotionClaim() {
    const dialogRef = this.dialog.open(PopupApprovePromotionClaimComponent, {
      width: '530px',
      data: {
        title: this.translateService.instant('promotionDetail.approveClaim'),
        content: this.translateService.instant('promotionDetail.approveContent'),
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.approve'),
      },
        // autoFocus: true,
      });
      dialogRef.afterClosed()
        .pipe(filter((result) => result?.action === ActionModal.Submit))
        .subscribe((result) => {
          if (result?.action === ActionModal.Submit) {
            this.loadingService.showLoader();
            this.promotionService.approvePromotionClaim(this.promotionId).subscribe(
              (res) => {
                if (res) {
                  if (res?.code === '200') {
                    this.notificationService.showSuccess('promotionDetail.approvedClaimMessage');
                    this.getPromotionClaimDetail(false);
                  } else {
                    this.loadingService.hideLoader();
                    this.notificationService.showError(res?.message);
                  }
                }
              },
              (error) => {
                this.loadingService.hideLoader();
                handleErrors(error, this.notificationService)
              }
            )
          }
        });
  }

  returnValue(value: any) {
    return (value !== null && value !== undefined && value !== "") ? value.toString().trim() : ''
  }

  onRejectPromotionClaim() {
    this.rejectPromotionClaimForm.reset()

    const dialogRef = this.dialog.open(PopupRejectPromotionClaimComponent, {
      width: '850px',
      data: {
        title: this.translateService.instant('promotionDetail.rejectClaim'),
        rejectPromotionClaimForm: this.rejectPromotionClaimForm,
        rejectReasonControl: this.rejectReasonControl,
        remarksControl: this.remarksControl,
        rejectNoteControl: this.rejectNoteControl,
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('promotionDetail.reject'),
      },
        // autoFocus: true,
      });
      dialogRef.afterClosed()
        .pipe(filter((result) => result?.action === ActionModal.Submit))
        .subscribe((result) => {

          if (result?.action === ActionModal.Submit) {
            const dataSend = {
              reason: this.rejectPromotionClaimForm.value.rejectReason !== 'OTHERS' ? this.rejectPromotionClaimForm.value.rejectReason : this.rejectPromotionClaimForm.value.rejectNote.toString().trim(),
              remarks: this.returnValue(this.rejectPromotionClaimForm.value.remarks),
            };

            this.loadingService.showLoader();
            
            this.promotionService.rejectPromotionClaim(this.promotionId, dataSend).subscribe(
              (res) => {
                if (res) {
                  if (res?.code === '200') {
                    this.notificationService.showSuccess('promotionDetail.rejectClaimMessage');
                    this.getPromotionClaimDetail(false);
                  } else {
                    this.loadingService.hideLoader();
                    this.notificationService.showError(res?.message);
                  }
                }
              },
              (error) => {
                this.loadingService.hideLoader();
                handleErrors(error, this.notificationService)
              }
            ).add(() => {
              this.rejectPromotionClaimForm.reset()
            });
          }
        });
  }

  onAppealPromotionClaim() {
    const dialogRef = this.dialog.open(PopupApprovePromotionClaimComponent, {
      width: '530px',
      data: {
        title: this.translateService.instant('promotionDetail.appealClaim'),
        content: this.translateService.instant('promotionDetail.appealContent'),
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.appeal'),
      },
        // autoFocus: true,
      });
      dialogRef.afterClosed()
        .pipe(filter((result) => result?.action === ActionModal.Submit))
        .subscribe((result) => {
          if (result?.action === ActionModal.Submit) {
            this.loadingService.showLoader();
            this.promotionService.appealPromotionClaim(this.promotionId).subscribe(
              (res) => {
                if (res) {
                  if (res?.code === '200') {
                    this.notificationService.showSuccess('promotionDetail.appealedClaimMessage');
                    this.getPromotionClaimDetail(false);
                  } else {
                    this.loadingService.hideLoader();
                    this.notificationService.showError(res?.message);
                  }
                }
              },
              (error) => {
                this.loadingService.hideLoader();
                handleErrors(error, this.notificationService)
              }
            )
          }
        });
  }

  ngOnInit(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id !== null) {
      this.promotionId = id;
      this.loadingService.showLoader();
      this.getPromotionClaimDetail(false)

      if(this.userService.isHasPermission([ROLES.MARKETINGGROUP]) || this.userService.isHasPermission([ROLES.CRDEMPLOYEEGROUP])) {
        this.isMarketingTeam = true  
      }
      if(this.userService.isHasPermission([ROLES.TREASURYGROUP])) {
        this.isTreasury = true  
      }
      if(this.userService.isHasPermission([ROLES.CASHIERGROUP])) {
        this.isCashier = true  
      }
    } else {
      console.error('Device ID is null');
    }
  }

  ngOnDestroy(): void {
    
  }

  getPromotionClaimDetail(isRefresh?: boolean) {
    this.promotionService.getPromotionClaimDetail(this.promotionId).subscribe(
      (data: PromotionClaimDetail) => {
        if(data !== undefined) {

          let transactionListExpandStatus: any = []

          if(data?.promotionTransactions !== undefined) {
            data.promotionTransactions.forEach((itm: any, index: number) => {
              transactionListExpandStatus.push({
                expand: false
              })
            })
          }

          this.transactionListExpandStatus = transactionListExpandStatus

          this.statusPromo = data?.status.code

          this.promotionClaimDetail = data

          this.attachedDocumentList = data?.documents !== undefined ? data?.documents : []

          if(!isRefresh) {
            this.relatedTransactionList = data?.promotionTransactions !== undefined ? data?.promotionTransactions : []

            this.totalRelatedTransactions = data?.promotionTransactions !== undefined ? data?.promotionTransactions?.length : 0
          }
        }
      }
    ).add(() => {
      this.loadingService.hideLoader();
    })
  }

  onRemoveAttachedDocument(event: number) {
    this.removeAttachedDocument(event)
  }

  removeAttachedDocument(index: number) {

    const dialogRef = this.dialog.open(PopupApprovePromotionClaimComponent, {
      width: '600px',
      data: {
        title: this.translateService.instant('promotionDetail.removeAttachmentDocument'),
        content: this.translateService.instant('promotionDetail.removeAttachmentDocumentContent'),
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.confirm'),
        isRemove: true
      },
        // autoFocus: true,
    });

    dialogRef.afterClosed()
    .pipe(filter((result) => result?.action === ActionModal.Submit))
    .subscribe((result) => {
      if (result?.action === ActionModal.Submit) {
        let attachedDocumentList = [...this.attachedDocumentList]
        let attachedDeletedDocumentList = []
        attachedDeletedDocumentList.push(attachedDocumentList[index].code)
        this.attachedDeletedDocumentList = attachedDeletedDocumentList
      
        this.updateRejectPromotionClaim(
          this.promotionId,
          PromotionClaimDetailAction.REMOVE_ATTACHMENT, 
          this.attachedDeletedDocumentList,
          true
        )
      }
    })
  }

  removeTransactionDocument(index: number) {

    const dialogRef = this.dialog.open(PopupApprovePromotionClaimComponent, {
      width: '600px',
      data: {
        title: this.translateService.instant('promotionDetail.removeTransaction'),
        content: this.translateService.instant('promotionDetail.removeTransactionContent'),
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.confirm'),
        isRemove: true
      },
        // autoFocus: true,
    });

    dialogRef.afterClosed()
    .pipe(filter((result) => result?.action === ActionModal.Submit))
    .subscribe((result) => {
      if (result?.action === ActionModal.Submit) {
        let relatedTransactionList = [...this.relatedTransactionList]
        let relatedTransactionDeletedList = []
        relatedTransactionDeletedList.push(relatedTransactionList[index].id)
        this.relatedTransactionDeletedList = relatedTransactionDeletedList
        
        this.updateRejectPromotionClaim(
          this.promotionId,
          PromotionClaimDetailAction.REMOVE_TRANSACTION,
          this.relatedTransactionDeletedList,
          false
        )
      }
    });
  }

  convertObjectEntries(object: any) {
    return Object.entries(object)
  }

  onDownloadFile(event: Documents) {
    if (event.code) {
      downloadFile(event.realFileName, event.downloadUrl);
    } else {
      const a = document.createElement('a');
      a.style.display = "none";
      a.href = event.downloadUrl;
      a.download = event.realFileName;
      document.body.appendChild(a);
      a.click();
      a.remove()
    }
  }

  async onFileSelected(e: any) {
    const files = e;
    const base64Array = await this.convertFileListToBase64Array(files);
    const fileNameArray = this.convertFileListToNameArray(files)

    let dataSend = {
      uploadingDocs: base64Array,
      uploadingDocNames: fileNameArray
    }
  
    this.updateRejectPromotionClaim(
      this.promotionId,
      PromotionClaimDetailAction.UPLOAD_ATTACHMENT, 
      dataSend,
      true
    )
  }

  convertFileListToNameArray(fileList: any) {
    const fileName = Array.from(fileList).map((file: any) => file.name)
    return fileName
  }

  convertFileListToBase64Array(fileList: any) {
    const promises = Array.from(fileList).map((file: any) => {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = e => resolve(e.target.result.slice(e.target.result.toString().indexOf(',') + 1)); // full base64 string with prefix
        reader.onerror = e => reject(e);
        reader.readAsDataURL(file);
      });
    });
  
    return Promise.all(promises);
  }

  updateRejectPromotionClaim(id: string, type: string, data: any, isRefresh: boolean) {
    this.loadingService.showLoader();
    this.promotionService.updatedRejectPromotionClaim(
      id,
      type, 
      data
    ).subscribe(
      (res) => {
        if (res) {
          switch(type) {
            case PromotionClaimDetailAction.REMOVE_ATTACHMENT :
              this.notificationService.showSuccess('promotionDetail.removeAttachmentDocumentSuccess')
              break
            case PromotionClaimDetailAction.REMOVE_TRANSACTION:
              this.notificationService.showSuccess('promotionDetail.removeTransactionSuccess')
              break
          }
          this.getPromotionClaimDetail(isRefresh);
        }
      },
      (error) => {
        this.loadingService.hideLoader();
        handleErrors(error, this.notificationService)
      }
    )
  }

  onEditDetails(referenceNumber: string) {
    const dialogRef = this.dialog.open(ModalReferenceNumberComponent, {
      width: '900px',
      data: {
        claimId: this.promotionId,
        referenceNumber: referenceNumber ?? '',
        title: 'receivables.popup.updateDetails',
        cancelBtn: 'common.cancel',
        submitBtn: 'common.update',
      },
    });
    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((result) => {
        if (result) {
          this.getPromotionClaimDetail();
        }
      });
  }
}
