<div class="promotion-container">
  <div class="promotion-content">
    <form [formGroup]="filterPromotionForm">
      <app-dropdown-form-group
        [label]="'promotions.promoType' | translate"
        [control]="promoType"
        [options]="filterPromotion"
        (changeOption)="onChangePromoType($event)"
      ></app-dropdown-form-group>
      @if (promoType.value !=='FUTURE_PROMOTION') {
      <app-form-group
        [label]="'promotions.promotionName' | translate"
        [control]="promotionName"
        [placeholder]="'common.enterTosearch' | translate"
        (onEnter)="onEnterPromotionName($event)"
      ></app-form-group>
      }
      @if (promoType.value ==='FUTURE_PROMOTION') {
            <app-dropdown-form-group
        [label]="'promotions.filter.make' | translate"
        [control]="make"
        [options]="filterPromotionTransaction?.makes"
        [placeholder]="'common.select' | translate"
        (changeOption)="onChangeMake($event)"
      ></app-dropdown-form-group>
            <app-dropdown-form-group
        [label]="'promotions.filter.vehicleModel' | translate"
        [control]="vehicleModel"
        [options]="vehicleModelList"
        [placeholder]="'common.select' | translate"
      ></app-dropdown-form-group>
            <app-dropdown-form-group
        [label]="'promotions.filter.month' | translate"
        [control]="month"
        [options]="filterPromotionTransaction?.months"
        [placeholder]="'common.select' | translate"
      ></app-dropdown-form-group>
            <app-dropdown-form-group
        [label]="'promotions.filter.year' | translate"
        [control]="year"
        [options]="filterPromotionTransaction?.years"
        [placeholder]="'common.select' | translate"
      ></app-dropdown-form-group>
      <div class="btn-free-pms">
        <button type="submit" class="btn-primary" (click)="filterPromotionForm.invalid ? false : search()" [disabled]="filterPromotionForm.invalid">
          {{ "common.search" | translate }}
        </button>
      </div>
      }
    </form>
    @if (promoType.value !=='FUTURE_PROMOTION') {
    <div
      class="promotion-content--list"
      (scroll)="onScrollPromotionList($event)"
    >
      @for (item of promotionList; track $index) {
      <div class="mat-radio-container" [title]="item?.name">
        <mat-radio-button
          class="custom-radio-button"
          [value]="item?.code"
          [checked]="item?.code === this.promotionSelected?.code"
          (change)="onSelectPromotionRadio(item)"
        >
          {{ item?.name }}
        </mat-radio-button>
      </div>
      } @if (isLoadingPromotion && !firstTimeLoad) {
      <div class="promotion-content--loading">
        <mat-spinner class="custom-spinner"></mat-spinner>
      </div>
      }
    </div>
    }
  </div>
  <div
    class="transaction-content"
    [ngStyle]="{ 'align-self': !promotionSelected && !hasDatafreePMS ? 'flex-start' : 'stretch' }"
  >
    @if(!promotionSelected && !hasDatafreePMS) {
    <div class="no-data">
      {{ "promotions.pleaseSelectPromotion" | translate }}
    </div>
    } @else {
    <h2>{{ promotionSelected?.name }}</h2>

    <app-filter-transaction
      [form]="filterTransactionForm"
      [statusList]="transactionStatus"
      (search)="search()"
    >
    </app-filter-transaction>

    <app-transaction-table
      [rowData]="rowData"
      [colDefs]="colDefs"
      [isShowActionExport]="false"
      [defaultColDef]="defaultColDef"
      [gridOptions]="gridOptions"
      [pagingInfo]="pagingInfo"
      (onPageChange)="onPageChange($event)"
      (changeItemPerPage)="changeItemPerPage($event)"
      (rowSelected)="onRowSelected($event)"
      (createClaim)="createClaim()"
    >
    </app-transaction-table>
    }
  </div>
</div>
