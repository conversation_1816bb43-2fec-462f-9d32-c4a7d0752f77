@if (((status === promotionStatus.INITIAL_VALIDATION || status ===
promotionStatus.VALIDATION_APPEAL) && 
(currentRole === ROLES.MARKETINGGROUP || currentRole === ROLES.CRDEMPLOYEEGROUP)
)) {
<div class="action-buttons-cell-renderer-claim">
  <button class="edit-button" (click)="onApprove()">
    <mat-icon svgIcon="ic-checked-red" class="small-icon"></mat-icon>
    <span>{{ "common.approve" | translate }}</span>
  </button>
  <button class="cancel-button" (click)="onReject()">
    <mat-icon svgIcon="ic-cancel-red" class="small-icon"></mat-icon>
    <span>{{ "common.reject" | translate }}</span>
  </button>
</div>
} @if ((status === promotionStatus.CLAIM_VALIDATED && (currentRole === ROLES.MARKETINGGROUP))) {
<div class="action-buttons-cell-renderer-claim">
  <button class="edit-button" (click)="onGenerateExcel()">
    <mat-icon svgIcon="ic-generate-excel-red" class="small-icon"></mat-icon>
    <span>{{ "common.generateExcel" | translate }}</span>
  </button>
</div>
} @if ((status === promotionStatus.FOR_FUND_TRANSFER && currentRole ===
ROLES.CASHIERGROUP) || (status === promotionStatus.OR_AR_ISSUED && currentRole
=== ROLES.TREASURYGROUP) ) {
<div class="action-buttons-cell-renderer-claim">
  <button class="edit-button" (click)="onAddNumber()">
    <mat-icon [svgIcon]="currentRole === ROLES.TREASURYGROUP ? 'ic-checked-red' : 'ic-generate-excel-red'" class="small-icon"></mat-icon>
    <span>{{(currentRole === ROLES.TREASURYGROUP ? "common.addORARNumberTreasury" : "common.addORARNumber") | translate }}</span>
  </button>
</div>
} @if ((status === promotionStatus.REJECTED && currentRole ===
ROLES.CASHIERGROUP)) {
<div class="action-buttons-cell-renderer-claim">
  <button class="edit-button" (click)="onAppeal()">
    <mat-icon svgIcon="ic-appeal-red" class="small-icon"></mat-icon>
    <span>{{ "common.appeal" | translate }}</span>
  </button>
</div>
}
