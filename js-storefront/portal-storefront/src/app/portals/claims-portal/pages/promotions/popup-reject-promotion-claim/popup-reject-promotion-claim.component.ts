import { Component, inject, OnD<PERSON>roy, OnInit } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { ActionModal } from '../../../../../core/enums';
import { DropdownFormGroupComponent, FormGroupComponent } from '../../../../../core/shared';
import { ScreenSizeService, LoadingService, NotificationService } from '../../../../../core/services';
import { PromotionsService } from '../../../services/promotion/promotions.service';
import { handleErrors } from '../../../../../core/helpers';


@Component({
  selector: 'app-popup-reject-promotion-claim',
  standalone: true,
  imports: [
    MatDialogModule,
    MatIconModule,
    TranslateModule,
    ReactiveFormsModule,
    FormGroupComponent,
    DropdownFormGroupComponent,
  ],
  providers: [
    ScreenSizeService,
    LoadingService,
    NotificationService,
    PromotionsService
  ],
  templateUrl: './popup-reject-promotion-claim.component.html',
  styleUrls: ['./popup-reject-promotion-claim.component.scss'],
})
export class PopupRejectPromotionClaimComponent implements OnInit, OnDestroy {
  data = inject(MAT_DIALOG_DATA);
  dialogRef = inject(MatDialogRef<PopupRejectPromotionClaimComponent>);

  promotionService = inject(PromotionsService)
  notificationService = inject(NotificationService);

  dataReasonOption: any = []

  onCancel() {
    this.dialogRef.close();
  }

  onConfirm() {
    this.dialogRef.close({ action: ActionModal.Submit });
  }

  onChangeDataType(e: any) {

  }

  ngOnInit(): void {
    this.getRejectOptionList()
  }

  ngOnDestroy() {

  }

  getRejectOptionList() {
    this.promotionService.getRejectOptionList().subscribe(
      (data: any) => {
        if(data?.enums !== undefined) {
          if(data?.enums?.promotionClaimRejectReason !== undefined) {
            this.dataReasonOption = data?.enums?.promotionClaimRejectReason
          }
        }
      },
      (error) => {
        handleErrors(error, this.notificationService);
      }
    )
  }

  disableSubmit() {
    if(this.data?.rejectPromotionClaimForm !== undefined) {
      if(this.data?.rejectPromotionClaimForm?.value?.rejectReason === 'OTHERS') {
        let value = this.data?.rejectPromotionClaimForm?.value?.rejectNote
        return !(value !== null && value !== undefined && value.toString().trim() !== "")
      }
      else {
        return !this.data.rejectPromotionClaimForm.valid
      }
    }
    else {
      return true
    }
  }
}
