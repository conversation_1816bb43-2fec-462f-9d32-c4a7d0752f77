import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import {
  LoadingService,
  NotificationService,
} from '../../../../../core/services';
import {
  DropdownFormGroupComponent,
  FormGroupComponent,
} from '../../../../../core/shared';
import { MatDialog } from '@angular/material/dialog';
import { filter, finalize, Subscription } from 'rxjs';
import { PERMISSIONS_CODE, ROLES } from '../../../../../core/constants';
import { ActionModal, DeviceSummaryTab } from '../../../../../core/enums';
import { PagingInfo, OptionDropdown } from '../../../../../core/interfaces';
import { UserService } from '../../../../../core/services/user';
import { Router } from '@angular/router';
import { MatRadioModule } from '@angular/material/radio';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { FilterTransactionComponent } from './filter-transaction/filter-transaction.component';
import {
  PromotionTransactionFilter,
  TransactionItem,
} from '../../../../../core/interfaces/promotions.interface';
import { TransactionTableComponent } from '../../../features/transaction-table/transaction-table.component';
import { CreateClaimModalComponent } from '../../../features/create-claim-modal/create-claim-modal.component';
import { PromotionsTab } from '../../../enum';
import { PromotionsService } from '../../../services/promotion/promotions.service';
import { RouterLinkCellRendererComponent } from '../../../../../core/shared/router-link-cell-renderer/router-link-cell-renderer.component';
import { CustomHeaderTicketComponent } from '../../../../iot-portal/features/ticket/custom-header-ticket/custom-header-ticket.component';

@Component({
  selector: 'app-transactions-tab',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    MatIconModule,
    FormGroupComponent,
    DropdownFormGroupComponent,
    MatRadioModule,
    MatProgressSpinnerModule,
    FilterTransactionComponent,
    TransactionTableComponent,
  ],
  providers: [PromotionsService, NotificationService],
  standalone: true,
  templateUrl: './transactions-tab.component.html',
  styleUrls: ['./transactions-tab.component.scss'],
})
export class TransactionsTabComponent {
  router = inject(Router);
  loadingService = inject(LoadingService);
  translateService = inject(TranslateService);
  dialog = inject(MatDialog);
  promotionsService = inject(PromotionsService);
  notificationService = inject(NotificationService);
  userService = inject(UserService);

  readonly PromotionsTab = PromotionsTab;
  readonly DeviceSummaryTab = DeviceSummaryTab;
  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;

  selectedSummaryTab: DeviceSummaryTab;
  currentWidgets: any[] = [
    {
      id: '' as PromotionsTab,
      count: 0,
      description: '',
      icon: '',
    },
  ];
  filterPromotionForm = new FormGroup({
    promotionName: new FormControl(''),
    promoType: new FormControl(''),
    make: new FormControl('', [Validators.required]),
    vehicleModel: new FormControl('', [Validators.required]),
    month: new FormControl('', [Validators.required]),
    year: new FormControl('', [Validators.required]),
  });

  filterTransactionForm = new FormGroup({
    billingDocumentNo: new FormControl(),
    vin: new FormControl(),
    status: new FormControl(),
  });
  transactionStatus: OptionDropdown[];
  promotionList: OptionDropdown[] = [];
  defaultColDef = {
    resizable: false,
    sortable: false,
    menuTabs: [],
    wrapHeaderText: true,
    autoHeaderHeight: true,
  };
  gridOptions: any = {
    rowSelection: 'multiple',
    suppressRowClickSelection: true,
  };
  colDefs: any;
  pagingInfo: PagingInfo = {
    totalItems: 0,
    currentPage: 0,
    numberOfPage: 10,
  };

  pagingPromotionInfo: PagingInfo = {
    totalItems: 0,
    currentPage: 0,
    numberOfPage: 20,
  };

  numberOfPageDefault = 10;
  filterPromotion: OptionDropdown[];
  isLoadingPromotion: boolean = false;
  isFreePMS: boolean = false;
  firstTimeLoad: boolean = true;
  promotionSelected: OptionDropdown;
  rowData: TransactionItem[];
  rowSelected: any;
  filterPromotionTransaction: PromotionTransactionFilter;
  vehicleModelList: OptionDropdown[] = [];
  hasDatafreePMS: boolean = false;
  subscription = new Subscription();
  subscriptionTransaction: any;
  currentRoles: any;
  currentRole: any;
  ROLES = ROLES;
  perviousPromotionTransactionSearch: any = sessionStorage.getItem(
    'promotionTransactionSearch'
  )
    ? JSON.parse(sessionStorage.getItem('promotionTransactionSearch'))
    : '';
  perviousPromotionSearch: any = sessionStorage.getItem('promotionSearch')
    ? JSON.parse(sessionStorage.getItem('promotionSearch'))
    : '';
  promotionSelectedSearch: any = sessionStorage.getItem(
    'promotionSelectedSearch'
  )
    ? JSON.parse(sessionStorage.getItem('promotionSelectedSearch'))
    : '';
  ngOnInit(): void {
    this.currentRoles = JSON.parse(localStorage.getItem('roles'))?.roles || [];
    this.currentRole = this.userService.getGroupRole(this.currentRoles);
    if (this.currentRole === ROLES.CRDEMPLOYEEGROUP) {
      this.promoType.disable();
    }
    this.getColumnInit();
    this.initData();
  }

  ngOnDestroy(): void {
    this.subscription && this.subscription.unsubscribe();
    this.subscriptionTransaction && this.subscriptionTransaction.unsubscribe();
  }

  get promoType() {
    return this.filterPromotionForm.get('promoType') as FormControl;
  }

  get promotionName() {
    return this.filterPromotionForm.get('promotionName') as FormControl;
  }

  get make() {
    return this.filterPromotionForm.get('make') as FormControl;
  }

  get vehicleModel() {
    return this.filterPromotionForm.get('vehicleModel') as FormControl;
  }

  get month() {
    return this.filterPromotionForm.get('month') as FormControl;
  }

  get year() {
    return this.filterPromotionForm.get('year') as FormControl;
  }

  viewPromotionDetail(id: string) {
    this.router.navigate(['/claims/promotions/promotions-transactions', id]);
  }

  initData() {
    this.filterPromotionForm.patchValue({
      make: this.perviousPromotionTransactionSearch?.make || '',
      month: this.perviousPromotionTransactionSearch?.month || '',
      promotionName: this.perviousPromotionSearch?.promotionName || '',
      promoType: this.perviousPromotionSearch?.promotionType || '',
      year: this.perviousPromotionTransactionSearch?.year || '',
    });
    if (this.make?.value === 'TOYOTA') {
      this.filterPromotionForm.controls['vehicleModel'].patchValue(
        this.perviousPromotionTransactionSearch?.vehicleModel || ''
      );
    } else {
      this.filterPromotionForm.controls['vehicleModel'].patchValue(
        this.perviousPromotionTransactionSearch?.vehicleModelLexus || ''
      );
    }

    this.filterTransactionForm.patchValue({
      billingDocumentNo:
        this.perviousPromotionTransactionSearch?.billingDocumentNo || '',
      status: this.perviousPromotionTransactionSearch?.status || '',
      vin: this.perviousPromotionTransactionSearch?.vin || '',
    });
    this.isFreePMS = this.promoType.value === 'FUTURE_PROMOTION';
    if (!this.isFreePMS) {
      this.getPromotionFilter();
      this.promotionSelectedSearch;
      this.promotionSelectedSearch &&
        this.onSelectPromotionRadio(this.promotionSelectedSearch);
    } else {
      this.getPromotionFilter();
      this.getPromotionTransactionFilter();
      this.search();
    }
  }

  getPromotionList(): void {
    this.isLoadingPromotion = true;
    const params: any = {
      page: this.pagingPromotionInfo?.currentPage,
      size: this.pagingPromotionInfo?.numberOfPage,
      promotionName: this.promotionName?.value,
      promotionType: this.promoType?.value,
    };
    sessionStorage.setItem('promotionSearch', JSON.stringify(params));
    this.firstTimeLoad && this.loadingService.showLoader();
    this.promotionsService
      .getPromotionsList(params)
      .pipe(
        finalize(() => {
          this.isLoadingPromotion = false;
          this.firstTimeLoad && this.loadingService.hideLoader();
          this.firstTimeLoad = false;
        })
      )
      .subscribe({
        next: (response: any) => {
          this.promotionList = [
            ...this.promotionList,
            ...(response?.items || []),
          ];
          this.pagingPromotionInfo.currentPage += 1;
          this.pagingPromotionInfo.totalItems =
            response?.pagination?.totalResults;
        },
        error: () => {},
      });
  }

  getTransactionList(): void {
    this.subscriptionTransaction && this.subscriptionTransaction.unsubscribe();
    let params: any = {
      currentPage: this.pagingInfo?.currentPage,
      pageSize: this.pagingInfo?.numberOfPage,
      billingDocumentNo:
        this.filterTransactionForm?.value?.billingDocumentNo || '',
      vin: this.filterTransactionForm?.value?.vin || '',
      status: this.filterTransactionForm?.value?.status || '',
    };
    if (this.isFreePMS) {
      params = {
        ...params,
        make: this.filterPromotionForm?.value?.make || '',
        month: this.filterPromotionForm?.value?.month || '',
        year: this.filterPromotionForm?.value?.year || '',
        promotionType: this.filterPromotionForm?.value?.promoType || '',
      };
      if (this.make?.value === 'TOYOTA') {
        params.vehicleModel =
          this.filterPromotionForm?.value?.vehicleModel || '';
      } else {
        params.vehicleModelLexus =
          this.filterPromotionForm?.value?.vehicleModel || '';
      }
    } else {
      params.promotionCode = this.promotionSelected?.code;
    }
    sessionStorage.setItem(
      'promotionTransactionSearch',
      JSON.stringify(params)
    );

    this.loadingService.showLoader();
    this.subscriptionTransaction = this.promotionsService
      .getTransactionList(params)
      .pipe(
        finalize(() => {
          this.loadingService.hideLoader();
        })
      )
      .subscribe({
        next: (response: any) => {
          this.rowData = response?.items || [];
          this.pagingInfo = {
            totalItems: response?.pagination?.totalResults,
            currentPage: response?.pagination?.currentPage,
            numberOfPage: response?.pagination?.pageSize,
          };
          if (this.isFreePMS) {
            this.hasDatafreePMS = true;
          }
        },
        error: () => {
          this.rowData = [];
          this.pagingInfo = {
            totalItems: 0,
            currentPage: 0,
            numberOfPage: 10,
          };
        },
      });
  }

  onPageChange(value: number): void {
    this.pagingInfo.currentPage = value;
    this.getTransactionList();
  }

  search(): void {
    this.pagingInfo.totalItems = 0;
    this.pagingInfo.currentPage = 0;
    !this.transactionStatus && this.getTransactionFilter();
    this.getTransactionList();
  }

  changeItemPerPage(item) {
    this.pagingInfo.numberOfPage = item;
    this.pagingInfo.currentPage = 0;
    this.getTransactionList();
  }

  getPromotionTransactionFilter(): void {
    this.loadingService.showLoader();
    this.subscription.add(
      this.promotionsService
        .getPromotionTransactionFilter()
        .pipe(finalize(() => this.loadingService.hideLoader()))
        .subscribe(
          (response) => {
            this.filterPromotionTransaction = response;
            if (this.make.value) {
              this.onChangeMake();
            }
          },
          (error) => {}
        )
    );
  }

  getPromotionFilter(): void {
    this.loadingService.showLoader();
    this.subscription.add(
      this.promotionsService.getPromotionsFilter().subscribe(
        (response) => {
          this.filterPromotion = response;
          if (!this.perviousPromotionSearch?.promotionType) {
            if (this.filterPromotion && this.filterPromotion.length > 0) {
              if (this.currentRole === ROLES.CRDEMPLOYEEGROUP) {
                this.promoType.patchValue('VOUCHER');
              } else {
                this.promoType.patchValue(this.filterPromotion[0].code);
              }
            }
          }
          this.getPromotionList();
        },
        (error) => {
          this.getPromotionList();
        }
      )
    );
  }

  getTransactionFilter(): void {
    this.loadingService.showLoader();
    this.subscription.add(
      this.promotionsService.getTransactionFilter().subscribe(
        (response) => {
          this.transactionStatus = response;
          this.transactionStatus.unshift({
            code: '',
            name: this.translateService.instant('common.all'),
          });
          if (!this.perviousPromotionTransactionSearch?.status) {
            this.filterTransactionForm.controls['status'].patchValue(
              this.transactionStatus && this.transactionStatus.length && this.transactionStatus[0]?.code || ''
            );
          }
        },
        (error) => {}
      )
    );
  }

  onChangeMake(event = null) {
    this.vehicleModelList =
      this.make?.value === 'TOYOTA'
        ? this.filterPromotionTransaction?.vehicleModels
        : this.filterPromotionTransaction?.vehicleModelsLexus;
  }

  onChangePromoType(event) {
    this.resetDataPromotion();
    this.isFreePMS = this.promoType.value === 'FUTURE_PROMOTION';
    if (this.isFreePMS) {
      this.getPromotionTransactionFilter();
    }
    this.filterTransactionForm.patchValue({
      billingDocumentNo: '',
      status: '',
      vin: '',
    });
    this.promotionSelected = null;
  }

  onEnterPromotionName(event) {
    this.resetDataPromotion();
  }

  resetDataPromotion() {
    this.pagingPromotionInfo.currentPage = 0;
    this.pagingPromotionInfo.totalItems = 0;
    this.promotionList = [];
    this.promotionSelected = null;
    this.hasDatafreePMS = false;
    this.getPromotionList();
  }
  onSelectPromotionRadio(data) {
    sessionStorage.setItem('promotionSelectedSearch', JSON.stringify(data));
    if (data?.code === this.promotionSelected?.code) return;
    this.promotionSelected = data;
    this.pagingInfo = {
      totalItems: 0,
      currentPage: 0,
      numberOfPage: 10,
    };
    !this.transactionStatus && this.getTransactionFilter();
    this.getTransactionList();
  }

  onScrollPromotionList(e) {
    const scrolled = e.target.scrollTop;
    const height = e.target.offsetHeight;
    const scrollHeight = e.target.scrollHeight;
    if (height + scrolled + 1 >= scrollHeight && !this.isLoadingPromotion) {
      if (
        this.promotionList &&
        this.promotionList.length < this.pagingPromotionInfo.totalItems &&
        this.pagingPromotionInfo.currentPage * 10 <=
          this.pagingPromotionInfo.totalItems
      ) {
        this.getPromotionList();
      }
    }
  }

  createClaim() {
    const dialogRef = this.dialog.open(CreateClaimModalComponent, {
      width: '1100px',
      data: {
        title: this.translateService.instant(
          'promotions.popup.titleCreateClaim'
        ),
        rowData: this.rowSelected,
        mode: 'create',
      },
    });

    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((res) => {
        if (res) {
          this.getTransactionList();
        }
      });
  }

  onRowSelected(event: any) {
    this.rowSelected = event;
  }

  exportData(): void {
    // this.loadingService.showLoader();
    // this.deviceService.exportDevice(this.selectedTab).subscribe((response) => {
    //   this.loadingService.hideLoader();
    //   const link = document.createElement('a');
    //   link.href = URL.createObjectURL(response?.blob);
    //   link.download = response?.filename;
    //   link.click();
    //   URL.revokeObjectURL(link.href);
    // });
  }

  getColumnInit() {
    const checkbox = {
      headerName: '',
      headerCheckboxSelection: true,
      headerCheckboxSelectionFilteredOnly: true,
      checkboxSelection: true,
      field: 'select_all',
      sortable: false,
      wrapText: true,
      autoHeight: true,
      width: 48,
    };
    const temp = [
      {
        headerName: this.translateService.instant(
          'promotions.table.dbmOrderNumber'
        ),
        headerValueGetter: () =>
          this.translateService.instant('promotions.table.dbmOrderNumber'),
        field: 'dbmOrderNumber',
        wrapText: true,
        autoHeight: true,
        flex: 1,
        headerComponent: CustomHeaderTicketComponent,
        headerComponentParams: {
          secondHeader: this.translateService.instant(
            'promotions.table.number'
          ),
        },
        cellRenderer: RouterLinkCellRendererComponent,
        cellRendererParams: {
          linkBuilder: (data: any) => `/claims/promotions/promotions-transactions/${data?.id}`,
          displayValue: (params) => {
            return params?.id ? params?.id : '-';
          },
        }
      },
      {
        headerName: this.translateService.instant('promotions.table.status'),
        headerValueGetter: () =>
          this.translateService.instant('promotions.table.status'),
        field: 'status',
        flex: 1,
        autoHeaderHeight: true,
        wrapText: true,
        autoHeight: true,
        cellRenderer: (params) => {
          return params?.value?.name ? params?.value?.name : '-';
        },
      },
      {
        headerName: this.translateService.instant('promotions.table.dealer'),
        headerValueGetter: () =>
          this.translateService.instant('promotions.table.dealer'),
        field: 'dealerName',
        flex: 1,
        wrapText: true,
        autoHeight: true,
        cellRenderer: (params) => {
          return params?.value ? params?.value : '-';
        },
      },
      {
        headerName: this.translateService.instant('promotions.table.claimId'),
        headerValueGetter: () =>
          this.translateService.instant('promotions.table.claimId'),
        field: 'claimId',
        flex: 1,
        wrapText: true,
        autoHeight: true,
        cellRenderer: (params) => {
          return params?.value ? params?.value : '-';
        },
      },
      {
        headerName: this.translateService.instant(
          'promotions.table.customerName'
        ),
        headerValueGetter: () =>
          this.translateService.instant('promotions.table.customerName'),
        field: 'customerName',
        flex: 1,
        autoHeight: true,
        wrapText: true,
        headerComponent: CustomHeaderTicketComponent,
        headerComponentParams: {
          secondHeader: this.translateService.instant(
            'promotions.table.customerId'
          ),
        },
        cellRenderer: (params) => {
          return `<p>${params?.data?.customerName || '-'}</p><p>${
            params?.data?.customerId || '-'
          }</p>`;
        },
      },
      {
        headerName: this.translateService.instant(
          'promotions.table.customerType'
        ),
        headerValueGetter: () =>
          this.translateService.instant('promotions.table.customerType'),
        field: 'customerType',
        flex: 1,
        autoHeight: true,
        wrapText: true,
        headerComponent: CustomHeaderTicketComponent,
        headerComponentParams: {
          secondHeader: this.translateService.instant(
            'promotions.table.customerGroup'
          ),
        },
        cellRenderer: (params) => {
          return `<p>${params?.data?.customerType || '-'}</p><p>${
            params?.data?.customerGroup || '-'
          }</p>`;
        },
      },
    ];

    if (this.currentRole === ROLES.CASHIERGROUP) {
      this.gridOptions = {
        ...this.gridOptions,
        hideDisabledCheckboxes: true,
        isRowSelectable: (rowNode) =>
          rowNode?.data &&
          rowNode?.data?.status &&
          rowNode?.data?.status &&
          rowNode?.data?.status?.code
            ? rowNode?.data?.status?.code === 'NEW' || rowNode?.data?.status?.code === 'RETURN_TO_DEALER'
            : false,
      };
      this.colDefs = [checkbox, ...temp];
    } else {
      this.colDefs = temp;
    }
  }
}
