import { Component, EventEmitter, inject, Input, OnInit, Output } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { ROLES } from '../../../../../../../core/constants/roles.const';
import { IconModule } from '../../../../../../../core/icon/icon.module';
import { UserService } from '../../../../../../../core/services/user';
import { PromotionClaimStatus, PromotionClaimAction } from '../../../../../enum';

@Component({
  selector: 'app-cell-action-promotion-claim',
  standalone: true,
  imports: [IconModule, TranslateModule],
  templateUrl: './cell-action-promotion-claim.component.html',
  styleUrls: ['./cell-action-promotion-claim.component.scss'],
})
export class CellActionPromotionClaimComponent  implements ICellRendererAngularComp {
  @Output() edit = new EventEmitter<void>();
  @Output() cancel = new EventEmitter<void>();

  private userService = inject(UserService);

  params!: any;
  status: string = '';
  promotionStatus = PromotionClaimStatus;
  currentRoles: any;
  currentRole: any;
  ROLES = ROLES
  agInit(params: any): void {
    this.params = params;
    this.currentRoles = JSON.parse(localStorage.getItem('roles'))?.roles || [];
    this.currentRole = this.userService.getGroupRole(this.currentRoles);
    this.status = params?.value?.code || '';
  }

  refresh(params: any): boolean {
    return true;
  }

  onApprove(): void {
    this.params.onClick(PromotionClaimAction.approve, this.params.data);
  }

  onReject(): void {
    this.params.onClick(PromotionClaimAction.reject, this.params.data);
  }

  onAddNumber(): void {
    this.params.onClick(PromotionClaimAction.addORARNumber, this.params.data);
  }

  onGenerateExcel(): void {
    this.params.onClick(PromotionClaimAction.generateExcel, this.params.data);
  }

  onAppeal(): void {
    this.params.onClick(PromotionClaimAction.appeal, this.params.data);
  }
}

