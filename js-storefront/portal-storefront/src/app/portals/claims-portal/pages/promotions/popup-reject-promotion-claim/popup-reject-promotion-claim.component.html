<div class="popup-reject-container">
  <h2 class="title">{{ data?.title }}</h2>

  <div class="container">
      <form
          [formGroup]="data.rejectPromotionClaimForm"
      >
      <div class="add-vehicle-model-section-form-group">
          <app-dropdown-form-group
              [label]="'promotionDetail.rejectReason' | translate"
              [control]="data.rejectReasonControl"
              [options]="dataReasonOption"
              class="vehicle-model-section__input"
              (changeOption)="onChangeDataType($event)"
              [required]="true"
          ></app-dropdown-form-group>

          @if(data.rejectPromotionClaimForm.value.rejectReason === 'OTHERS') {
            <app-form-group
              [label]="'' | translate"
              [control]="data.rejectNoteControl"
              controlId="rejectNote"
              class="vehicle-model-section__input"
              [isTextArea]="true"
              [required]="false"
            ></app-form-group>
          }

          <app-form-group
              [label]="'promotionDetail.remarks' | translate"
              [control]="data.remarksControl"
              controlId="remarks"
              class="vehicle-model-section__input"
              [isTextArea]="true"
              [required]="false"
          ></app-form-group>
      </div>
    </form>
  </div>

  <div class="action-dialog">
    <button class="btn-quaternary" (click)="onCancel()">
      {{ data?.cancelBtn }}
    </button>

    <button class="btn-primary btn-confirm" (click)="onConfirm()" [disabled]="disableSubmit()">
      {{ data?.submitBtn }}
    </button>
  </div>
</div>
