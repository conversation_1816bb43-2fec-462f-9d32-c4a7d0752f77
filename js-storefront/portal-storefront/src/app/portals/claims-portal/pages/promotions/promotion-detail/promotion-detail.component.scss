:host {
    min-height: calc(100vh - 73px);
    display: block;

    .tabs-group {
      ::ng-deep .mat-mdc-tab-body-wrapper {
        min-height: calc(100vh - 285px);
      }
    }
  
    .container-details {

        &__header {
            align-items: center;
        }

        &__body {
            padding: 30px;
            // display: flex;
            // justify-content: space-between;
            // align-items: end;

            &__promotion {
                padding: 15px;
                background-color: #f5f5f5;
                .details-section-header {
                    width: 100%;
                    display: flex;
                    align-items: flex-start;
                    gap: 15px;
                    .section-header {
                        margin-right: auto;
                        flex: 1;
                    }
                }

                .section-header--content span {
                    font-size: 18px;
                    line-height: 24px;
                }

                .section-body--content span {
                    color: #101010;
                    font-size: 16px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 22px; 
                }

                .top-detail-section {
                    display: flex;
                    gap: 5px;

                    .details, .attached-document {
                        display: flex;
                        padding: 25px;
                        flex-direction: column;
                        align-items: flex-start;
                        gap: 25px;
                        flex: 1 0 0;
                        align-self: stretch;
                        background-color: #ffffff;
                    }

                    .details {
                        .section-body {
                            display: flex;
                            align-items: flex-start;
                            align-content: flex-start;
                            gap: 20px;
                            align-self: stretch;
                            flex-wrap: wrap;

                            .detail-column-left, .detail-column-right {
                                width: calc(50% - 10px);
                                display: flex;
                                flex-direction: column;
                                gap: 25px;

                                .detail-item {
                                    display: flex;
                                    flex-direction: column;
                                    .title {
                                        color: #3A3A3A;
                                        font-size: 14px;
                                        font-style: normal;
                                        font-weight: 400;
                                        line-height: 14px; 
                                    }
                                    .content {
                                        color: #101010;
                                        font-size: 16px;
                                        font-style: normal;
                                        font-weight: 600;
                                        line-height: normal;
                                    }
                                }
                            }
                        }
                    }

                    .attached-document {
                        .section-body {
                            width: 100%;

                            .upload-file-promo {
                                margin-bottom: 25px;
                            }
                        }
                    }
                }

                .bottom-detail-section {
                    display: flex;
                    gap: 5px;
                    margin-top: 15px;

                    .details {
                        display: flex;
                        padding: 25px;
                        flex-direction: column;
                        align-items: flex-start;
                        gap: 30px;
                        flex: 1 0 0;
                        align-self: stretch;
                        background-color: #ffffff;

                        .section-body {
                            width: 100%;
                        }
                    }
                }
            }
        }
    }

    .promotion-details {
        .container-details__actions {
            button {
                width: 150px!important;
            }
        }
    }
}