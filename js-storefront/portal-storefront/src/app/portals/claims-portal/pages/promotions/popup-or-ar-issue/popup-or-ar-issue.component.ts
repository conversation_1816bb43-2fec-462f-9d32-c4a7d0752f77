import { Component, inject, OnInit } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ActionModal } from '../../../../../core/enums';
import { FormGroupComponent } from '../../../../../core/shared';
import {
  LoadingService,
  NotificationService,
} from '../../../../../core/services';
import { finalize, Subscription } from 'rxjs';
import { handleErrors } from '../../../../../core/helpers';
import { PromotionsService } from '../../../services/promotion/promotions.service';

@Component({
  selector: 'app-popup-or-ar-issue',
  standalone: true,
  imports: [
    MatDialogModule,
    MatIconModule,
    TranslateModule,
    ReactiveFormsModule,
    FormGroupComponent,
  ],
  providers: [PromotionsService, NotificationService],
  templateUrl: './popup-or-ar-issue.component.html',
  styleUrls: ['./popup-or-ar-issue.component.scss'],
})
export class PopupOrArIssueComponent {
  data = inject(MAT_DIALOG_DATA);
  private dialogRef = inject(MatDialogRef<PopupOrArIssueComponent>);
  private loadingService = inject(LoadingService);
  private promotionsService = inject(PromotionsService);
  private translateService = inject(TranslateService);
  private notificationService = inject(NotificationService);

  mainForm: FormGroup;
  claimId: string;
  subscription = new Subscription();

  get numberORAR() {
    return this.mainForm.get('numberORAR') as FormControl;
  }
  ngOnInit() {
    this.claimId = this.data.claimId;
    this.mainForm = this.data.form;
  }

  ngOnDestroy() {
    this.subscription?.unsubscribe();
  }
  onCancel() {
    this.dialogRef.close();
  }

  onConfirm() {
    this.loadingService.showLoader();
    const body = {
      numberORAR: this.numberORAR.value,
    };
    this.subscription.add(
      this.promotionsService
        .addORARNumber(body, this.claimId)
        .pipe(finalize(() => this.loadingService.hideLoader()))
        .subscribe(
          (res) => {
            this.notificationService.showSuccess(
              this.translateService.instant(
                'promotions.message.addORARNumberSuccess',
                { claimId: this.claimId }
              )
            );
            this.dialogRef.close({ action: ActionModal.Submit });
          },
          (error) => {
            handleErrors(error, this.notificationService);
          }
        )
    );
  }
}
