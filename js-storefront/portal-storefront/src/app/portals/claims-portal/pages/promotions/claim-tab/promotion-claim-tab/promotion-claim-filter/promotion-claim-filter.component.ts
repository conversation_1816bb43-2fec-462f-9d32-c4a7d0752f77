import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { OptionDropdown } from '../../../../../../../core/interfaces';
import { ScreenSizeService } from '../../../../../../../core/services';
import { FormGroupComponent, DropdownFormGroupComponent } from '../../../../../../../core/shared';

@Component({
  selector: 'app-promotion-claim-filter',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatIconModule,
    TranslateModule,
    FormGroupComponent,
    DropdownFormGroupComponent,
  ],
  providers: [ScreenSizeService],
  standalone: true,
  templateUrl: './promotion-claim-filter.component.html',
  styleUrls: ['./promotion-claim-filter.component.scss'],
})
export class PromotionClaimFilterComponent  {
  @Input() form: FormGroup;
  @Input() statusList: OptionDropdown[];
  @Input() dealerList: OptionDropdown[];
  @Input() monthList: OptionDropdown[];
  @Input() yearList: OptionDropdown[];
  @Input() creationMonthList: OptionDropdown[];
  @Input() creationYearList: OptionDropdown[];
  @Input() hiddenDealer: boolean = false;

  @Output() search = new EventEmitter();

  isYearValid: boolean = true
  isCreationYearValid: boolean = true

  get id() {
    return this.form.get('id') as FormControl;
  }

  get status() {
    return this.form.get('status') as FormControl;
  }

  get month() {
    return this.form.get('month') as FormControl;
  }

  get year() {
    return this.form.get('year') as FormControl;
  }

  get creationMonth() {
    return this.form.get('creationMonth') as FormControl;
  }

  get creationYear() {
    return this.form.get('creationYear') as FormControl;
  }

  onSubmit() {
    
    let formValue = this.form.getRawValue()

    if(formValue.month !== '') {
      this.isYearValid = formValue.year === '' ? false : true
    }
    else {
      this.isYearValid = true
    }

    if(formValue.creationMonth !== '') {
      this.isCreationYearValid = formValue.creationYear === '' ? false : true
    }
    else {
      this.isCreationYearValid = true
    }

    if(this.isYearValid && this.isCreationYearValid) {
      this.search.emit()
    }
  }
}
