import { Component, inject, OnInit } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { ActionModal } from '../../../../../core/enums';


@Component({
  selector: 'app-popup-approve-promotion-claim',
  standalone: true,
  imports: [
    MatDialogModule,
    MatIconModule,
    TranslateModule,
    ReactiveFormsModule
  ],
  templateUrl: './popup-approve-promotion-claim.component.html',
  styleUrls: ['./popup-approve-promotion-claim.component.scss'],
})
export class PopupApprovePromotionClaimComponent {
  data = inject(MAT_DIALOG_DATA);
  dialogRef = inject(MatDialogRef<PopupApprovePromotionClaimComponent>);

  onCancel() {
    this.dialogRef.close();
  }

  onConfirm() {
    this.dialogRef.close({ action: ActionModal.Submit });
  }
}
