import { CommonModule } from '@angular/common';
import { Component, inject, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { DropdownFormGroupComponent, DateFormGroupComponent, FormGroupComponent } from '../../../../core/shared';
import { LoadingService, NotificationService, ReportService } from '../../../../core/services';
import { finalize, Subscription } from 'rxjs';
import { ReportDataType } from '../../../../core/enums/report.enum';
import { handleErrors } from '../../../../core/helpers';
import { OptionDropdown } from '../../../../core/interfaces';
import { PromotionClaimFilter } from '../../../../core/interfaces/promotions.interface';
import { PromotionsService } from '../../services/promotion/promotions.service';
import { UserService } from '../../../../core/services/user';
import { ROLES } from '../../../../core/constants/roles.const';

@Component({
  selector: 'app-reports',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    ReactiveFormsModule,
    DropdownFormGroupComponent,
    DateFormGroupComponent,
    FormGroupComponent,
  ],
  providers: [NotificationService, ReportService, PromotionsService],
  templateUrl: './reports.component.html',
  styleUrl: './reports.component.scss'
})
export class ReportsComponent implements OnInit, OnDestroy {
  loadingService = inject(LoadingService);
  translateService = inject(TranslateService);
  reportService = inject(ReportService)
  notificationService = inject(NotificationService);
  promotionsService = inject(PromotionsService);
  userService = inject(UserService)

  subscription = new Subscription();

  readonly ReportDataType = ReportDataType;

  form = new FormGroup({
    dataType: new FormControl(ReportDataType.PROMOTION_TRANSACTION.toString()),

    //Dealer
    month: new FormControl('', {
      nonNullable: true,
    }),
    year: new FormControl('', {
      nonNullable: true,
    }),

    //Dealer Promotion Transaction
    promoType: new FormControl('', {
      nonNullable: true,
    }),

    //Dealer Receivable Transaction
    receivableType: new FormControl('', {
      nonNullable: true,
    }),

    //Marketing Receivable Transaction
    dealer: new FormControl('', {
      nonNullable: true,
    }),

  });

  dataTypeOptionDealer: OptionDropdown[] = [
    {
      code: ReportDataType.PROMOTION_TRANSACTION,
      name: 'Promotion Transaction',
    },
    {
      code: ReportDataType.REBATE_TRANSACTION,
      name: 'Rebate Transaction',
    },
    {
      code: ReportDataType.RECEIVABLE_TRANSACTION,
      name: 'Receivable',
    },
  ];

  dataPromotionTypeOption: OptionDropdown[] = [
    {
      code: 'CASH_DISCOUNT',
      name: 'reports.cashDiscount'
    },
    {
      code: 'DISCOUNT',
      name: 'reports.discount'
    },
    {
      code: 'FREE_INSURANCE',
      name: 'reports.freeInsurance'
    },
    {
      code: 'FUTURE_PROMOTION',
      name: 'reports.freePMS'
    },
    {
      code: 'FREE_BIES',
      name: 'reports.freebies'
    },
    {
      code: 'VOUCHER',
      name: 'reports.voucher'
    },
  ]

  dataReceivableTypeOption:  OptionDropdown[] = [
    {
      code: 'RETURNED_UNIT',
      name: 'reports.returnedUnit'
    },
    {
      code: 'EXTERNAL_AFTER_SALE_REBATE_PART_SERVICE',
      name: 'reports.externalRebaseService'
    },
    {
      code: 'EXTERNAL_AFTER_SALE_REBATE_FLEET_CLAIM',
      name: 'reports.externalRebaseFleet'
    },
    {
      code: 'GOODWILL_FREE_PERIODIC',
      name: 'reports.goodwillFree'
    },
    {
      code: 'GOODWILL_UNIT_REPLACEMENT',
      name: 'reports.goodwillUnit'
    },
    {
      code: 'INTERNAL_AFTER_SALE_REBATE',
      name: 'reports.internalAfterRebate'
    },
    {
      code: 'RENTAL_CAR',
      name: 'reports.rentalCar'
    },
  ]

  dataMonthOption:  OptionDropdown[] = [
    {
      code: '',
      name: 'manageSub.paymentStatus.all'
    },
    {
      code: '01',
      name: '01'
    },
    {
      code: '02',
      name: '02'
    },
    {
      code: '03',
      name: '03'
    },
    {
      code: '04',
      name: '04'
    },
    {
      code: '05',
      name: '05'
    },
    {
      code: '06',
      name: '06'
    },
    {
      code: '07',
      name: '07'
    },
    {
      code: '08',
      name: '08'
    },
    {
      code: '09',
      name: '09'
    },
    {
      code: '10',
      name: '10'
    },
    {
      code: '11',
      name: '11'
    },
    {
      code: '12',
      name: '12'
    },
  ]

  dataYearOption: OptionDropdown[] = []

  dataDealerOption: OptionDropdown[] = []

  defaultOption: OptionDropdown[] = [
    {
      code: '',
      name: 'manageSub.paymentStatus.all'
    }
  ]

  showDealerField: boolean = false

  dataYearPromoOption: OptionDropdown[] = []

  dataYearRebateReceiveOption: OptionDropdown[] = []

  isCRDEmployee: boolean = false

  checkFormValid() {
    let type = this.form.getRawValue().dataType
    switch(type) {
      case ReportDataType.PROMOTION_TRANSACTION:
        return (this.form.getRawValue().promoType === '' || this.form.getRawValue().year === '') ? true : false
      case ReportDataType.REBATE_TRANSACTION:
        return this.form.getRawValue().year === '' ? true : false
      case ReportDataType.RECEIVABLE_TRANSACTION:
        return (this.form.getRawValue().receivableType === '' || this.form.getRawValue().year === '') ? true : false
      default:
        return false
    }
  }

  ngOnInit(): void {
    this.generatePromoYearOption()
    this.generateRebateReceiveYearOption()
    this.generatePromoTypeOption()

    if(this.userService.isHasPermission([ROLES.MARKETINGGROUP])) {
      this.showDealerField = true
      this.getPromotionClaimFilter()
    }

    if(this.userService.isHasPermission([ROLES.CRDEMPLOYEEGROUP])) {
      this.isCRDEmployee = true
      this.showDealerField = true
      this.getPromotionClaimFilter()
      if(this.isCRDEmployee) {
        this.form.patchValue({
          promoType: 'VOUCHER'
        })
        this.form.controls['dataType'].disable()
        this.form.controls['promoType'].disable()
      }
    }
    
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  generateRebateReceiveYearOption() {
    let currentYear = new Date().getFullYear();
    for (let year = currentYear; year >= 2019; year--) {
      this.dataYearRebateReceiveOption.push({
        code: year.toString(),
        name: year.toString()
      });
    }
  }

  generatePromoYearOption() {
    this.reportService.getYearOp().subscribe(
      (data: any) => {
        let dataList = data.length > 0 && data !== undefined ? data : []
        let dataYearOp = []
        dataList.forEach((year: any) => {
          dataYearOp.push({
            code: year.toString(),
            name: year.toString()
          });
        })
        dataYearOp = dataYearOp.sort((a, b) => b.code - a.code)
        this.dataYearPromoOption = dataYearOp

        if(this.dataYearPromoOption.length > 0) {
          this.dataYearOption = this.dataYearPromoOption
          if(this.dataYearOption.length > 0) {
            this.form.patchValue({
              year: this.dataYearOption[0].code
            })
          }
        }
      },
      error => {
        this.loadingService.hideLoader();
        handleErrors(error, this.notificationService)
      }
    )
  }
  onChangeDataType(e) {
    let dataType = e.value
    this.form.reset()
    this.form.patchValue({
      dataType: dataType
    })

    switch(dataType) {
      case ReportDataType.PROMOTION_TRANSACTION:
        this.dataYearOption = this.dataYearPromoOption
        if(this.dataYearOption.length > 0) {
          this.form.patchValue({
            year: this.dataYearOption[0].code
          })
        }
        break 
      default:
        this.dataYearOption = this.dataYearRebateReceiveOption
        if(this.dataYearOption.length > 0) {
          this.form.patchValue({
            year: this.dataYearOption[0].code
          })
        }
        break 
    }
  }

  onChangeOption(e, field) {
    let formValue = e.value
    this.form.controls[field].setValue(formValue)
  }

  generateReport() {
    let dataSend: any = {}
    let dataForm = this.form.getRawValue()
    let dataType = dataForm.dataType

    this.loadingService.showLoader();

    switch(dataType) {
      case ReportDataType.PROMOTION_TRANSACTION:
        dataSend = {
          dataType: dataForm.dataType,
          month: dataForm.month,
          year: dataForm.year,
          promotionType: dataForm.promoType
        }

        if(this.showDealerField) {
          dataSend.dealer = dataForm.dealer
        }

        this.generateReportTransaction(dataSend, 'promo-report')
        break
      case ReportDataType.RECEIVABLE_TRANSACTION:
        dataSend = {
          dataType: dataForm.dataType,
          month: dataForm.month,
          year: dataForm.year,
          receivableType: dataForm.receivableType
        }

        if(this.showDealerField) {
          dataSend.dealer = dataForm.dealer
        }

        this.generateReportTransaction(dataSend, 'receivable-report')
        break
      case ReportDataType.REBATE_TRANSACTION:
        dataSend = {
          dataType: dataForm.dataType,
          month: dataForm.month,
          year: dataForm.year,
        }

        if(this.showDealerField) {
          dataSend.dealer = dataForm.dealer
        }

        this.generateReportTransaction(dataSend, 'rebase-report')
        break
    }
  }

  generateReportTransaction(body: any, fileName: string) {
    this.reportService.generateReportTransaction(body).subscribe(
      (data: any) => {
        this.generateFileZip(data, fileName)
      },
      error => {
        this.loadingService.hideLoader();
        handleErrors(error, this.notificationService)
      }
    )
  }

  generateFileZip(data: any, filename: string) {
    const url = window.URL.createObjectURL(data);
    let exportDate = new Date().toLocaleString('en-US', { 
      month: '2-digit', day: '2-digit', year: 'numeric', 
      hour: '2-digit', minute: '2-digit', second: '2-digit', 
      hour12: false 
    }).replace(/\D/g, '')

    // Create an anchor element
    const a = document.createElement('a');
    a.href = url;
    a.download = filename + '-' + exportDate + '.zip'; // Specify the filename here
    document.body.appendChild(a);

    // Trigger the download
    a.click();

    // Clean up
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);

    this.loadingService.hideLoader();
    this.notificationService.showSuccess('reports.exportSuccesfully');
  }

   getPromotionClaimFilter(): void {
      this.loadingService.showLoader();
      this.subscription.add(
        this.promotionsService
          .getPromotionClaimFilter()
          .pipe(finalize(() => this.loadingService.hideLoader()))
          .subscribe(
            (response: PromotionClaimFilter) => {
              if(response?.dealer.length > 0 && response !== undefined) {
                let dealerList = response?.dealer.sort((a, b) => a.name.localeCompare(b.name))
                let dataDealerOption = [...this.defaultOption, ...dealerList]
                this.dataDealerOption = dataDealerOption
              }
            },
            error => {
              this.loadingService.hideLoader();
              handleErrors(error, this.notificationService)
            }
          )
      );
    }
  
  generatePromoTypeOption() {
    this.loadingService.showLoader();
    this.subscription.add(
      this.reportService
        .getPromoTypeList()
        .subscribe(
          (response) => {
            if(response && response.length > 0) {
              this.dataPromotionTypeOption = response

              if(this.isCRDEmployee) {
                this.form.patchValue({
                  promoType: 'VOUCHER'
                })
              }
            }
            this.loadingService.hideLoader();
          },
          (error) => {
            this.loadingService.hideLoader();
          }
        )
    );
  }
}