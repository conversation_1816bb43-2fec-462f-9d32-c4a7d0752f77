<div class="form-content" [class.edit]="isEdit">
    <div class="btn-block">
        <button
            class="btn-quaternary"
            (click)="onCancel()"
        >
            {{ "common.cancel" | translate }}
        </button>
    
        <button
            class="btn-primary btn-confirm"
            (click)="onSubmit()"
            [disabled]="form.invalid"
        >
            @if (isEdit) {
                {{ "common.update" | translate }}
            }
            @else {
                {{ "common.add" | translate }}
            }
        </button>
    </div>
    
    <form [formGroup]="form" [class.form-change]="selectedType">
        <div class="type-item" *ngIf="!isEdit">
            <app-dropdown-form-group
                [label]="'createTicket.type' | translate"
                [control]="form.controls.type"
                [options]="typeOption"
                [required]="true"
                (changeOption)="onChangeType($event)"
            ></app-dropdown-form-group>
        </div>
    
        <ng-container [ngSwitch]="selectedType">
            <div class="form-item" 
                *ngIf="selectedType"
            >
                <label>
                    <ng-container [ngSwitch]="selectedType">
                        <ng-container 
                            *ngSwitchCase="ReceivableType.EXTERNAL_AFTER_SALE_REBATE_PART_SERVICE"
                        >
                            <ng-container [ngTemplateOutlet]="debitMemoLabel"></ng-container>
                        </ng-container>
    
                        <ng-container 
                            *ngSwitchCase="ReceivableType.EXTERNAL_AFTER_SALE_REBATE_FLEET_CLAIM"
                        >
                            <ng-container [ngTemplateOutlet]="debitMemoLabel"></ng-container>
                        </ng-container>
    
                        <ng-container 
                            *ngSwitchCase="ReceivableType.INTERNAL_AFTER_SALE_REBATE"
                        >
                            <ng-container [ngTemplateOutlet]="debitMemoLabel"></ng-container>
                        </ng-container>
    
                        <ng-container 
                            *ngSwitchCase="ReceivableType.RETURNED_UNIT"
                        >
                            {{ 'newReceivable.formField.amountOfUnit' | translate }}
                            <span class="required">*</span>
                        </ng-container>
    
                        <ng-container *ngSwitchDefault>
                            {{ 'newReceivable.formField.totalAmount' | translate }}
                            <span class="required">*</span>
                        </ng-container>
                    </ng-container>
                </label>
                
                <div class="input-require">
                    <mat-form-field class="custom-input">
                        <span matTextPrefix>₱</span>
                        <input 
                            type="number" 
                            matInput 
                            required 
                            formControlName="amount"
                        >
                    </mat-form-field>
                    <div 
                        *ngIf="form.controls?.amount.hasError('required') && form.controls?.amount.touched" 
                        class="form-group__error"
                    >
                        {{ 'validation.fillInData' | translate }}
                    </div>
                </div>
            </div>
    
            <div class="form-item" 
                *ngIf="
                    selectedType === ReceivableType.EXTERNAL_AFTER_SALE_REBATE_PART_SERVICE ||
                    selectedType === ReceivableType.EXTERNAL_AFTER_SALE_REBATE_FLEET_CLAIM
                "
            >
                <label>
                    {{ 'newReceivable.formField.repairOrderNumber' | translate }}
                </label>
                <mat-form-field class="custom-input">
                    <input 
                        type="text" 
                        matInput 
                        formControlName="repairOrderNumber"
                    >
                </mat-form-field>
            </div>
    
            <div class="form-item" 
                *ngIf="
                    selectedType === ReceivableType.INTERNAL_AFTER_SALE_REBATE
                "
            >
                <label>
                    {{ 'newReceivable.formField.promoDescription' | translate }}
                </label>
                <mat-form-field class="custom-input">
                    <input 
                        type="text" 
                        matInput 
                        formControlName="promoDescription"
                    >
                </mat-form-field>
            </div>
    
            <div class="form-item" 
                *ngIf="
                    selectedType === ReceivableType.EXTERNAL_AFTER_SALE_REBATE_PART_SERVICE ||
                    selectedType === ReceivableType.EXTERNAL_AFTER_SALE_REBATE_FLEET_CLAIM
                "
            >
                <app-date-form-group
                    [label]="'newReceivable.formField.repairOrderDate' | translate"
                    [control]="form.controls.repairOrderDate"
                    controlId="repairOrderDate"
                    [placeholder]="'MM/DD/YYYY'"
                ></app-date-form-group>
            </div>
    
            <div class="form-item" 
                *ngIf="
                    selectedType === ReceivableType.EXTERNAL_AFTER_SALE_REBATE_PART_SERVICE ||
                    selectedType === ReceivableType.EXTERNAL_AFTER_SALE_REBATE_FLEET_CLAIM ||
                    selectedType === ReceivableType.GOODWILL_FREE_PERIODIC ||
                    selectedType === ReceivableType.GOODWILL_UNIT_REPLACEMENT ||
                    selectedType === ReceivableType.RENTAL_CAR
                "
            >
                <app-custom-autocomplete-input
                    [label]="'common.customer' | translate"
                    [suggestions]="customerOption"
                    [control]="form.controls.customer"
                    [controlSearch]="form.controls.searchCustomer"
                    [isLoading]="isLoadingCustomer"
                    (onScrolled)="onScrollCustomer()"
                    (onSearch)="onCustomerSearch($event)"
                >
                </app-custom-autocomplete-input>
            </div>
    
            <div class="form-item" 
                *ngIf="
                    selectedType === ReceivableType.RETURNED_UNIT || 
                    selectedType === ReceivableType.EXTERNAL_AFTER_SALE_REBATE_PART_SERVICE ||
                    selectedType === ReceivableType.GOODWILL_FREE_PERIODIC ||
                    selectedType === ReceivableType.GOODWILL_UNIT_REPLACEMENT ||
                    selectedType === ReceivableType.RENTAL_CAR
                "
            >
                <app-custom-autocomplete-input
                    [label]="'common.model' | translate"
                    [suggestions]="modelOption"
                    [control]="form.controls.model"
                    [controlSearch]="form.controls.searchModel"
                    [isLoading]="isLoadingModel"
                    (onScrolled)="onScrollModel()"
                    (onSearch)="onModelSearch($event)"
                >
                </app-custom-autocomplete-input>
            </div>
    
            <div class="form-item"
                *ngIf="
                    selectedType === ReceivableType.GOODWILL_FREE_PERIODIC || 
                    selectedType === ReceivableType.GOODWILL_UNIT_REPLACEMENT ||
                    selectedType === ReceivableType.RENTAL_CAR
                "
            >
                <label>
                    {{ 'newReceivable.formField.plateNumber' | translate }}
                </label>
                <mat-form-field class="custom-input">
                    <input 
                        type="text" 
                        matInput 
                        formControlName="plateNumber"
                    >
                </mat-form-field>
            </div>
    
            <div class="form-item"
                *ngIf="selectedType === ReceivableType.EXTERNAL_AFTER_SALE_REBATE_PART_SERVICE"
            >
                <label>
                    {{ 'newReceivable.formField.roAmount' | translate }}
                </label>
                <mat-form-field class="custom-input">
                    <span matTextPrefix>₱</span>
                    <input 
                        type="number" 
                        matInput 
                        formControlName="roAmount"
                    >
                </mat-form-field>
            </div>
    
            <div class="form-item"
                *ngIf="
                    selectedType === ReceivableType.RETURNED_UNIT ||
                    selectedType === ReceivableType.GOODWILL_FREE_PERIODIC ||
                    selectedType === ReceivableType.GOODWILL_UNIT_REPLACEMENT ||
                    selectedType === ReceivableType.RENTAL_CAR
                "
            >
                <label>
                    {{ 'newReceivable.formField.csNumber' | translate }}
                </label>
                <mat-form-field class="custom-input">
                    <input 
                        type="text" 
                        matInput 
                        formControlName="csNumber"
                    >
                </mat-form-field>
            </div>
    
            <div class="form-item" 
                *ngIf="
                    selectedType === ReceivableType.RETURNED_UNIT ||
                    selectedType === ReceivableType.EXTERNAL_AFTER_SALE_REBATE_FLEET_CLAIM
                "
            >
                <app-date-form-group
                    [label]="'newReceivable.formField.invoiceDate' | translate"
                    [control]="form.controls.invoiceDate"
                    controlId="invoiceDate"
                    [placeholder]="'MM/DD/YYYY'"
                ></app-date-form-group>
            </div>
    
            <div class="form-item" 
                *ngIf="
                    selectedType === ReceivableType.GOODWILL_FREE_PERIODIC ||
                    selectedType === ReceivableType.GOODWILL_UNIT_REPLACEMENT ||
                    selectedType === ReceivableType.RENTAL_CAR
                "
            >
                <app-date-form-group
                    [label]="'newReceivable.formField.dateOfClaimSubmissionByDealer' | translate"
                    [control]="form.controls.dateOfClaimSubmissionByDealer"
                    controlId="dateOfClaimSubmissionByDealer"
                    [placeholder]="'MM/DD/YYYY'"
                ></app-date-form-group>
            </div>
    
            <div class="form-item"
                *ngIf="
                    selectedType === ReceivableType.GOODWILL_FREE_PERIODIC ||
                    selectedType === ReceivableType.GOODWILL_UNIT_REPLACEMENT ||
                    selectedType === ReceivableType.RENTAL_CAR
                "
            >
                <label>
                    {{ 'newReceivable.formField.DMReferenceNumber' | translate }}
                </label>
                <mat-form-field class="custom-input">
                    <input 
                        type="text" 
                        matInput 
                        formControlName="DMReferenceNumber"
                    >
                </mat-form-field>
            </div>
    
            <div class="form-item"
                *ngIf="
                    selectedType === ReceivableType.GOODWILL_FREE_PERIODIC ||
                    selectedType === ReceivableType.GOODWILL_UNIT_REPLACEMENT ||
                    selectedType === ReceivableType.RENTAL_CAR
                "
            >
                <label>
                    {{ 'newReceivable.formField.RFPReferenceNumber' | translate }}
                </label>
                <mat-form-field class="custom-input">
                    <input 
                        type="text" 
                        matInput 
                        formControlName="RFPReferenceNumber"
                    >
                </mat-form-field>
            </div>
    
            <div class="form-item"
                *ngIf="
                    selectedType === ReceivableType.GOODWILL_FREE_PERIODIC ||
                    selectedType === ReceivableType.GOODWILL_UNIT_REPLACEMENT ||
                    selectedType === ReceivableType.RENTAL_CAR
                "
            >
                <label>
                    {{ 'newReceivable.formField.descriptionOfRFP' | translate }}
                </label>
                <mat-form-field class="custom-input">
                    <input 
                        type="text" 
                        matInput 
                        formControlName="descriptionOfRFP"
                    >
                </mat-form-field>
            </div>
    
            <div class="form-item"
                *ngIf="
                    selectedType === ReceivableType.EXTERNAL_AFTER_SALE_REBATE_PART_SERVICE ||
                    selectedType === ReceivableType.EXTERNAL_AFTER_SALE_REBATE_FLEET_CLAIM
                "
            >
                <label>
                    {{ 'newReceivable.formField.serviceBillingNumber' | translate }}
                </label>
                <mat-form-field class="custom-input">
                    <input 
                        type="text" 
                        matInput 
                        formControlName="serviceBillingNumber"
                    >
                </mat-form-field>
            </div>
    
            <div class="form-item"
                *ngIf="
                    selectedType === ReceivableType.EXTERNAL_AFTER_SALE_REBATE_PART_SERVICE ||
                    selectedType === ReceivableType.EXTERNAL_AFTER_SALE_REBATE_FLEET_CLAIM
                "
            >
                <label>
                    {{ 'newReceivable.formField.serviceBillingAmount' | translate }}
                    <span class="required">*</span>
                </label>
    
                <div class="input-require">
                    <mat-form-field class="custom-input">
                        <span matTextPrefix>₱</span>
                        <input 
                            type="number" 
                            matInput 
                            required 
                            formControlName="serviceBillingAmount"
                        >
                    </mat-form-field>
                    <div 
                        *ngIf="
                            form.controls?.serviceBillingAmount.hasError('required') && 
                            form.controls?.serviceBillingAmount.touched
                        " 
                        class="form-group__error"
                    >
                        {{ 'validation.fillInData' | translate }}
                    </div>
                </div>
            </div>
    
            <div class="form-item"
                *ngIf="selectedType === ReceivableType.RETURNED_UNIT"
            >
                <app-dropdown-form-group
                    [label]="'newReceivable.formField.reasonForCancellation' | translate"
                    [control]="form.controls.reason"
                    [options]="reasonOption"
                    (changeOption)="onChangeReason($event)"
                ></app-dropdown-form-group>
            </div>
    
            <div class="form-item" 
                *ngIf="selectedType === ReceivableType.RETURNED_UNIT"
            >
                <app-date-form-group
                    [label]="'newReceivable.formField.invoiceCancellationDate' | translate"
                    [control]="form.controls.invoiceCancellationDate"
                    controlId="invoiceCancellationDate"
                    [placeholder]="'MM/DD/YYYY'"
                ></app-date-form-group>
            </div>
    
            <div class="form-item" 
                *ngIf="selectedType === ReceivableType.RETURNED_UNIT"
            >
                <app-date-form-group
                    [label]="'newReceivable.formField.deallocationDate' | translate"
                    [control]="form.controls.deallocationDate"
                    controlId="deallocationDate"
                    [placeholder]="'MM/DD/YYYY'"
                ></app-date-form-group>
            </div>
    
            <div class="form-item" 
                *ngIf="selectedType === ReceivableType.RETURNED_UNIT"
            >
                <app-date-form-group
                    [label]="'newReceivable.formField.returnedDate' | translate"
                    [control]="form.controls.returnedDate"
                    controlId="returnedDate"
                    [placeholder]="'MM/DD/YYYY'"
                ></app-date-form-group>
            </div>
    
            <div class="form-item" 
                *ngIf="selectedType === ReceivableType.RETURNED_UNIT"
            >
                <app-date-form-group
                    [label]="'newReceivable.formField.receivedDate' | translate"
                    [control]="form.controls.receivedDate"
                    controlId="receivedDate"
                    [placeholder]="'MM/DD/YYYY'"
                ></app-date-form-group>
            </div>
    
            <div class="form-item" 
                *ngIf="selectedType === ReceivableType.RETURNED_UNIT"
            >
                <label>
                    {{ 'newReceivable.formField.debitMemo' | translate }}
                </label>
                <mat-form-field class="custom-input">
                    <input 
                        type="text" 
                        matInput 
                        formControlName="debitMemo"
                    >
                </mat-form-field>
            </div>
    
            <div class="form-item"
            *ngIf="selectedType === ReceivableType.RETURNED_UNIT"
            >
                <label>
                    {{ 'newReceivable.formField.vehicleSalesInvoice' | translate }}
                </label>
                <mat-form-field class="custom-input">
                    <input 
                        type="text" 
                        matInput 
                        formControlName="vehicleSalesInvoice"
                    >
                </mat-form-field>
            </div>
    
            <div class="form-item large" 
                *ngIf="selectedType === ReceivableType.RETURNED_UNIT"
            >
                <label>
                    {{ 'newReceivable.formField.vehicleDeliveryNote' | translate }}
                </label>
                <mat-form-field class="custom-input">
                    <input 
                        type="text" 
                        matInput 
                        formControlName="vehicleDeliveryNote"
                    >
                </mat-form-field>
            </div>
    
            <div class="form-item full" 
                *ngIf="selectedType && !isEdit"
            >
                <app-upload-file
                    [label]="'newReceivable.formField.attachedDocuments' | translate"
                    [multiple]="true"
                    [placeholder]="'uploadFile.selectFile' | translate"
                    [isLabelHidden]="false"
                    [control]="form.controls.attachment"
                    (filesSelected)="onFileSelected($event)"
                ></app-upload-file>
                <div style="margin-top: 10px;">
                    <app-file-list
                    [fileList]="selectedfiles"
                    (handleDeleteBasedOnIndex)="removeFile($event)"
                    (handleDownload)="onDownloadFile($event)"
                    >
                    </app-file-list>
                </div>
            </div>
        </ng-container>
    
        <ng-template #debitMemoLabel>
            {{ 'newReceivable.formField.debitMemoAmount' | translate }}
             <span class="required">*</span>
        </ng-template>
    </form>
</div>