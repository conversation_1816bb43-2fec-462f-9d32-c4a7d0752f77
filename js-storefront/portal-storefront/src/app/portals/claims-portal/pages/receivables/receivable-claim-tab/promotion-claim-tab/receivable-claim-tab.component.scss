@import "../../../../../../../styles/abstracts/mixins";
@import "../../../../../../../styles/abstracts/variables";

.promotion-container {
  padding: 25px 16px 16px;
  background-color: #f5f5f5;
  display: flex;
  gap: 25px;

  @include md {
    padding: 70px 30px 40px;
  }

  .no-data {
    font-size: $fs18;
    font-weight: $fw600;
    text-align: center;
    padding: 20px;
  }

  .promotion-claim-content {
    flex: 1;
    display: flex;
    padding: 25px;
    gap: 30px;
    flex-direction: column;
    align-items: flex-start;
    background: var(--Primary-White, #fff);
    box-shadow: 0px 4px 5px 5px rgba(0, 0, 0, 0.03);

    .no-data {
      width: 100%;
      color: var(--Primary-Mid-Grey, #808080);
      text-align: center;
      font-family: "Toyota Type";
      font-size: 20px;
      font-style: normal;
      font-weight: 600;
      line-height: 26px;
    }

    h2 {
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 26px;
      font-style: normal;
      font-weight: 600;
      line-height: 32px;
      margin: 0;
    }
    app-transaction-table {
      width: 100%;
    }
  }
}

:host ::ng-deep {
  .promotion-container {
    .mdc-form-field {
      width: 100%;
    }

    .mdc-label {
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      cursor: pointer;
    }

    .mat-ripple {
      display: none !important;
    }

    .mdc-radio__background {
      &:before {
        background-color: transparent !important;
      }
    }

    .mat-mdc-radio-button
      .mdc-radio:hover
      .mdc-radio__native-control:enabled
      + .mdc-radio__background
      .mdc-radio__inner-circle {
      background-color: #eb0a1e !important;
    }

    .mat-mdc-radio-button
      .mdc-radio:hover
      .mdc-radio__native-control:enabled
      + .mdc-radio__background
      .mdc-radio__inner-circle {
      border-color: #eb0a1e !important;
    }

    .mat-mdc-radio-button
      .mdc-radio
      .mdc-radio__native-control:enabled
      + .mdc-radio__background
      .mdc-radio__inner-circle {
      border-color: #eb0a1e !important;
    }

    .custom-radio-button {
      font-size: 16px;
      font-weight: 400;
      color: $text-color;

      .mat-mdc-radio .mdc-radio__background .mdc-radio__outer-circle {
        border-color: $text-color !important;
      }

      &.mat-mdc-radio-checked .mdc-radio__background .mdc-radio__outer-circle {
        border-color: $text-color !important;
      }

      .mat-radio-inner-circle {
        background-color: transparent;
      }
    }
    .ag-checkbox-input-wrapper:focus-within,
    .ag-checkbox-input-wrapper:active {
      box-shadow: unset !important;
    }

    .ag-checked {
      &::after {
        color: #101010 !important;
      }
    }
    .ag-cell-value {
      p {
        margin: 0;
      }
    }
  }
}
