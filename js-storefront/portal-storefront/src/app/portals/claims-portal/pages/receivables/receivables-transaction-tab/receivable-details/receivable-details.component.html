<app-loading *ngIf="loadingService.isLoading"></app-loading>

<div class="promotion-details container-details">
  <div class="container-details__header p-b-0">
    <app-breadcrumb-with-label
      [breadcrumbs]="['receivables.title', 'receivables.details.title']"
      [breadcrumbLinks]="[
        '/claims/receivables',
        '/receivables-transactions/' + receivableId
      ]"
      [label]="receivableId"
    >
    </app-breadcrumb-with-label>
    @if (currentRole === ROLES.MARKETINGGROUP && (statusCode === PromotionClaimStatus.INITIAL_VALIDATION || statusCode === PromotionClaimStatus.VALIDATION_APPEAL)) {
      <div class="container-details__actions">
        <button class="btn-third" (click)="handleActionTable(PromotionClaimAction.reject, dataDetail)">{{ "promotionDetail.reject" | translate }}</button>
        <button class="btn-primary" (click)="handleActionTable(PromotionClaimAction.approve, dataDetail)">{{ "promotionDetail.approve" | translate }}</button>
      </div>
    }
    @if (currentRole === ROLES.CASHIERGROUP && (statusCode === PromotionClaimStatus.REJECTED)) {
      <div class="container-details__actions">
        <button class="btn-primary" (click)="handleActionTable(PromotionClaimAction.appeal, dataDetail)" [disabled]="isEdit">{{ "promotionDetail.appeal" | translate }}</button>
      </div>
    }

  </div>
  <div class="container-details__body">
    <div class="container-details__body__promotion">
      <div class="top-detail-section">
        <div class="details">
          <div class="section-header">
            <mat-icon
              svgIcon="ic-bp-est-infor"
              class="medium-icon section-header--icon"
            ></mat-icon>
            <div class="section-header--content">
              <span>{{ "receivables.details.briefInfo" | translate }}</span>
            </div>
          </div>
          <div class="section-body">
            <div class="detail-column-left">
              <div class="detail-item">
                <div>
                  <span class="title">
                    {{ "receivables.briefInfo.type" | translate }}
                  </span>
                </div>
                <div>
                  <span class="content">
                    {{ dataDetail?.type?.name || "-" }}
                  </span>
                </div>
              </div>
              @if(dataDetail?.status?.code === ReceivableStatus.REJECTED) {
                <div class="detail-item">
                  <div>
                    <span class="title">
                      {{ "receivables.briefInfo.rejectReason" | translate }}
                    </span>
                  </div>
                  <div>
                    <span class="content">
                      {{ dataDetail?.rejectReason || "-" }}
                    </span>
                  </div>
                </div>
                } @else {
                <div class="detail-item">
                  <div>
                    <span class="title">
                      {{ "receivables.briefInfo.dealer" | translate }}
                    </span>
                  </div>
                  <div>
                    <span class="content">
                      {{ dataDetail?.dealer?.displayName || "-" }}
                    </span>
                  </div>
                </div>
              }
              
            </div>
            <div class="detail-column-right">
              <div class="detail-item">
                <div>
                  <span class="title">
                    {{ "receivables.briefInfo.status" | translate }}
                  </span>
                </div>
                <div>
                  <span class="content">
                    {{ dataDetail?.status?.name || "-" }}
                  </span>
                </div>
              </div>
              <div class="detail-item">
                <div>
                  <span class="title">
                    {{ "receivables.briefInfo.remarks" | translate }}
                  </span>
                </div>
                <div>
                  <span class="content">
                    {{ dataDetail?.remarks || "-" }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="attached-document">
          <div class="section-header">
            <mat-icon
              svgIcon="ic-document"
              class="medium-icon section-header--icon"
            ></mat-icon>
            <div class="section-header--content">
              <span>{{ "promotionDetail.attachedDocuments" | translate }}</span>
            </div>
          </div>
          <div class="section-body">
            <div class="section-body--content">
              @if (currentRole === ROLES.CASHIERGROUP && (statusCode === PromotionClaimStatus.REJECTED)) {
                <div class="section-body-upload-file">
                  <app-upload-file
                  [multiple]="true"
                  [placeholder]="'uploadFile.selectFile' | translate"
                  [isLabelHidden]="true"
                  (filesSelected)="onFileSelected($event)"
                  ></app-upload-file>
                </div>
              }
              @if(dataDetail && dataDetail?.documents && dataDetail?.documents?.length > 0) {
                <div class="attached-document-list">
                  <app-file-list
                    [fileList]="dataDetail?.documents"
                    [isHiddenRemove]="true"
                    [checkCode]="true"
                    (handleDeleteBasedOnIndex)="removeFile($event)"
                    (handleDownload)="onDownloadFile($event)"
                  ></app-file-list>
                </div>
                } @else {
                  @if (!(currentRole === ROLES.CASHIERGROUP && (statusCode === PromotionClaimStatus.REJECTED))) {
                    <span>{{
                      "receivables.details.noAttachedDocs" | translate
                    }}</span>
                  }
              }
            </div>
          </div>
        </div>
      </div>
      <div class="bottom-detail-section">
        <div class="details">
          <div class="section-header--detail-section">
            <div class="section-header--detail-section__left-title">
              <mat-icon
                svgIcon="ic-bp-est-infor"
                class="medium-icon section-header--icon"
              ></mat-icon>
              <div class="section-header--content">
                <span>{{ "receivables.details.details" | translate }}</span>
              </div>
            </div>
            
            @if (currentRole === ROLES.CASHIERGROUP && (statusCode === PromotionClaimStatus.REJECTED)) {
              <div class="section-header--detail-section__right-title" (click)="onEdit()" *ngIf="!isEdit">
                <mat-icon
                  svgIcon="ic-edit"
                  class="small-icon section-header--icon"
                ></mat-icon>
                <div class="section-header--content">
                  <span>{{ "receivables.details.editDetails" | translate }}</span>
                </div>
              </div>
            }
          </div>

          <div class="detail-box">
            @if (isEdit) {
              <app-receivable-form 
                [isEdit]="true"
                [dataDetail]="dataDetail"
                (handleSubmit)="onUpdate($event)"
                (handleCancel)="onCancel()"
                [files]="files"
              ></app-receivable-form>
            }
            @else {
              <div class="detail-content">
                <div class="detail-row">
                  @for (data of mapData; track $index) {
                  <div [class]="{ 'currency-with-symbol': data?.isAmount, 'detail-element': true }">
                    <div class="detail-label">
                      {{ data?.label }}
                    </div>
                    <div class="detail-value">{{ data?.value }}</div>
                  </div>
                  }
                </div>
              </div>
            }
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
