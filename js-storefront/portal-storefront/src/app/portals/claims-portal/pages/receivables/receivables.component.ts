import { CommonModule, DatePipe } from '@angular/common';
import { Component, inject } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { DateFormat } from '../../../../core/enums';
import { WidgetSummaryComponent } from '../../../../core/shared';
import { ReceivablesTransactionTabComponent } from './receivables-transaction-tab/receivables-transaction-tab.component';
import { CellCurrencyCustomComponent } from '../../../insurance-portal/features/insurance-details/parts-and-services/cell-currency-custom/cell-currency-custom.component';
import { PromotionsTab } from '../../enum';
import { MatDialog } from '@angular/material/dialog';
import { LoadingService } from '../../../../core/services';
import { PROMOTION_CLAIM_WIDGETS } from '../../constants/promotion.const';
import { Subscription } from 'rxjs';
import { ReceivableService } from '../../services/receivable/receivable.service';
import { UserService } from '../../../../core/services/user';
import { ROLES } from '../../../../core/constants/roles.const';
import { RECEIVABLE_CLAIM_WIDGETS, RECEIVABLE_WIDGETS } from '../../constants/receivable.const';
import { ReceivableClaimTabComponent } from './receivable-claim-tab/promotion-claim-tab/receivable-claim-tab.component';
import { CellActionReceivableClaimComponent } from './receivable-claim-tab/promotion-claim-tab/cell-action-receivable-claim/cell-action-receivable-claim.component';

@Component({
  selector: 'app-receivables',
  imports: [
    CommonModule,
    TranslateModule,
    ReceivablesTransactionTabComponent,
    WidgetSummaryComponent,
    ReceivableClaimTabComponent,
  ],
  providers: [ReceivableService, DatePipe],
  standalone: true,
  templateUrl: './receivables.component.html',
  styleUrl: './receivables.component.scss',
})
export class ReceivablesComponent {
  private route = inject(ActivatedRoute);
  private router = inject(Router);
  private loadingService = inject(LoadingService);
  private receivableService = inject(ReceivableService);
  private translateService = inject(TranslateService);
  private datePipe = inject(DatePipe);
  private dialog = inject(MatDialog);
  private userService = inject(UserService);

  readonly PromotionsTab = PromotionsTab;
  promotionTab = [
    { id: PromotionsTab.TRANSACTIONS, name: 'promotions.tab.transactions' },
    { id: PromotionsTab.CLAIMS, name: 'promotions.tab.claims' },
  ];

  currentWidgets: any[] = [
    {
      id: '' as PromotionsTab,
      count: 0,
      description: '',
      icon: '',
    },
  ];

  isRefresh: boolean = false;
  refreshByStatus: string = '';
  currentRoles: any;
  currentRole: any;
  subscription = new Subscription();
  isDisplayDealer: boolean = false;
  ROLES = ROLES;

  selectedTab: PromotionsTab;
  perviousReceivableTab: any = sessionStorage.getItem('receivableTab')
  ? JSON.parse(sessionStorage.getItem('receivableTab'))
  : '';
  ngOnInit(): void {
    const tab = this.perviousReceivableTab;
    switch (tab) {
      case PromotionsTab.CLAIMS:
        this.selectedTab = PromotionsTab.CLAIMS;
        break;
      default:
        this.selectedTab = PromotionsTab.TRANSACTIONS;
        break;
    }
    this.currentRoles = JSON.parse(localStorage.getItem('roles'))?.roles || [];
    this.currentRole = this.userService.getGroupRole(this.currentRoles);
    this.isDisplayDealer = this.currentRole === ROLES.MARKETINGGROUP;
    this.getPromotionClaimWidgets();
  }

  changeTab(id: PromotionsTab) {
    if (this.selectedTab === id) return;
    this.selectedTab = id;
    sessionStorage.setItem('receivableTab', JSON.stringify(this.selectedTab));
    this.getPromotionClaimWidgets();
    
  }

  getPromotionClaimWidgets(): void {
    this.loadingService.showLoader();
    this.subscription.add(
      this.receivableService
        .getReceivableWidget(this.selectedTab === PromotionsTab.CLAIMS)
        .subscribe((response: any) => {
          const descriptionWidget = this.selectedTab === PromotionsTab.TRANSACTIONS ? RECEIVABLE_WIDGETS : RECEIVABLE_CLAIM_WIDGETS;
          const widgetOrderMap = descriptionWidget.reduce(
            (acc, widget, index) => {
              acc[widget.id] = index;
              return acc;
            },
            {} as Record<string, number>
          );

          this.currentWidgets = response?.widgets?.map((item) => {
              const { description, icon, status } =
                descriptionWidget.find((widget) => widget.id === item.type) ||
                {};
              return {
                id: item?.type,
                count: item?.count,
                description,
                icon,
                status
              };
            })
            .sort(
              (a, b) =>
                (widgetOrderMap[a.id] ?? Infinity) -
                (widgetOrderMap[b.id] ?? Infinity)
            );
        })
    );
  }

  onChangeTab(e) {
    this.refreshByStatus = e.status;
  }

  addReceivable() {
    this.router.navigate(['claims/receivables/new-receivable']);
  }
}
