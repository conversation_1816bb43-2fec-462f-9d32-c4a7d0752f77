<div class="header-section">
    <div class="title-page">
      <h2>
        {{ "receivables.title" | translate }}
      </h2>
      @if (selectedTab === PromotionsTab.TRANSACTIONS && (currentRole === ROLES.CASHIERGROUP)) {
        <button
          type="button"
          class="btn btn-primary"
          (click)="addReceivable()"
        >
          {{ "receivables.newReceivable" | translate }}
        </button>
      }

    </div>

    <div class="device-action">
      <div class="device-action__tabname">
        @for (item of promotionTab; track $index) {
        <span
          [class.active]="item.id === selectedTab"
          (click)="changeTab(item.id)"
          >{{ item.name | translate }}</span
        >
        }
      </div>
    </div>
    <div class="promotion-widget">
      <app-widget-summary [widgets]="currentWidgets" (changeTabFullData)="onChangeTab($event)"></app-widget-summary>
    </div>
  </div>
  
  @if (selectedTab === PromotionsTab.TRANSACTIONS) {
      <app-receivables-transaction-tab [isDisplayDealer]="isDisplayDealer" [(refreshByStatus)]="refreshByStatus"></app-receivables-transaction-tab>
  } @else if (selectedTab === PromotionsTab.CLAIMS) {
      <app-receivable-claim-tab [(refreshByStatus)]="refreshByStatus" [isDisplayDealer]="isDisplayDealer"></app-receivable-claim-tab>
  }
  