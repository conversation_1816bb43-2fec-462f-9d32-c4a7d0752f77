import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  Output,
} from '@angular/core';
import {
  FormsModule,
  ReactiveFormsModule,
  FormGroup,
  FormControl,
} from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { OptionDropdown } from '../../../../../../core/interfaces';
import {
  ScreenSizeService
} from '../../../../../../core/services';
import {
  FormGroupComponent,
  DropdownFormGroupComponent,
} from '../../../../../../core/shared';

@Component({
  selector: 'app-receivables-transaction-filter',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatIconModule,
    TranslateModule,
    DropdownFormGroupComponent,
  ],
  providers: [ScreenSizeService],
  standalone: true,
  templateUrl: './receivables-transaction-filter.component.html',
  styleUrls: ['./receivables-transaction-filter.component.scss'],
})
export class ReceivablesTransactionFilterComponent {
  @Input() form: FormGroup;
  @Input() statusList: OptionDropdown[];
  @Input() typeList: OptionDropdown[];
  @Input() dealerList: OptionDropdown[];
  @Input() isDisplayDealer: boolean = false;

  @Output() search = new EventEmitter();

  get type() {
    return this.form.get('type') as FormControl;
  }

  get status() {
    return this.form.get('status') as FormControl;
  }

  get dealer() {
    return this.form.get('dealer') as FormControl;
  }
}
