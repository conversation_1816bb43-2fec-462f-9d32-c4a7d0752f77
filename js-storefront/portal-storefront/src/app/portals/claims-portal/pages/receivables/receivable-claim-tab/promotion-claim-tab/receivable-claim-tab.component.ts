import { CommonModule, DatePipe } from '@angular/common';
import {
  Component,
  EventEmitter,
  inject,
  Input,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import * as _ from 'lodash-es';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatRadioModule } from '@angular/material/radio';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Router } from '@angular/router';
import { ReceivableClaimFilterComponent } from './receivable-claim-filter/receivable-claim-filter.component';
import { MatDialog } from '@angular/material/dialog';
import { Subscription, filter, finalize } from 'rxjs';
import { PERMISSIONS_CODE } from '../../../../../../core/constants';
import {
  ActionModal,
  DateFormat,
  DeviceSummaryTab,
} from '../../../../../../core/enums';
import { OptionDropdown, PagingInfo } from '../../../../../../core/interfaces';
import {
  NotificationService,
  LoadingService,
} from '../../../../../../core/services';
import { UserService } from '../../../../../../core/services/user';
import {
  PromotionClaimAction,
  PromotionClaimStatus,
  PromotionsTab,
} from '../../../../enum';
import { TransactionTableComponent } from '../../../../features/transaction-table/transaction-table.component';
import { PromotionClaimFilter } from '../../../../interfaces/promotions.interface';
import { ReceivableClaimStatusFilter } from '../../../../interfaces/receivable.interface';
import { PromotionsService } from '../../../../services/promotion/promotions.service';
import { CellCurrencyCustomComponent } from '../../../../../insurance-portal/features/insurance-details/parts-and-services/cell-currency-custom/cell-currency-custom.component';
import { CellActionReceivableClaimComponent } from './cell-action-receivable-claim/cell-action-receivable-claim.component';
import { ReceivableService } from '../../../../services/receivable/receivable.service';
import { ModalReceivableRejectComponent } from '../../modal/modal-receivable-reject/modal-receivable-reject.component';
import { DialogConfirmComponent } from '../../../../../../core/shared';
import { handleErrors } from '../../../../../../core/helpers';
import { GenerateExcelService } from '../../../../services/claims/generate-excel-cas.service';
import { ModalReceivableApproveComponent } from '../../modal/modal-receivable-approve/modal-receivable-approve.component';
import { ROLES } from '../../../../../../core/constants/roles.const';
import { reject } from 'lodash-es';
import { RouterLinkCellRendererComponent } from '../../../../../../core/shared/router-link-cell-renderer/router-link-cell-renderer.component';
import { DateTimeHelper } from '../../../../../../core/helpers/date-time.helper';

@Component({
  selector: 'app-receivable-claim-tab',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    MatIconModule,
    MatRadioModule,
    MatProgressSpinnerModule,
    ReceivableClaimFilterComponent,
    TransactionTableComponent,
  ],
  providers: [
    ReceivableService,
    NotificationService,
    DatePipe,
    GenerateExcelService,
    DateTimeHelper
  ],
  standalone: true,
  templateUrl: './receivable-claim-tab.component.html',
  styleUrls: ['./receivable-claim-tab.component.scss'],
})
export class ReceivableClaimTabComponent {
  @Input() refreshByStatus: string;
  @Input() isDisplayDealer: boolean = false;

  @Output() refreshByStatusChange = new EventEmitter<string>();

  router = inject(Router);
  loadingService = inject(LoadingService);
  translateService = inject(TranslateService);
  dialog = inject(MatDialog);
  receivableService = inject(ReceivableService);
  notificationService = inject(NotificationService);
  userService = inject(UserService);
  datePipe = inject(DatePipe);
  generateExcelService = inject(GenerateExcelService);
  dateTimeHelper = inject(DateTimeHelper);

  readonly PromotionsTab = PromotionsTab;
  readonly DeviceSummaryTab = DeviceSummaryTab;
  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;

  selectedSummaryTab: DeviceSummaryTab;

  filterClaimForm = new FormGroup({
    id: new FormControl(),
    status: new FormControl(),
    month: new FormControl(),
    year: new FormControl(),
    creationMonth: new FormControl(),
    creationYear: new FormControl(),
  });
  rejectForm = new FormGroup({
    rejectReason: new FormControl('', [Validators.required]),
    reason: new FormControl(''),
    remarks: new FormControl(''),
  });
  approveForm = new FormGroup({
    glCode: new FormControl('', Validators.required),
    costCenter: new FormControl('', Validators.required),
    internalOrderNo: new FormControl('', Validators.required),
    profitCenter: new FormControl('', Validators.required),
  });
  claimStatus: OptionDropdown[];
  promotionList: OptionDropdown[] = [];
  defaultColDef = {
    resizable: false,
    sortable: false,
    menuTabs: [],
    wrapHeaderText: true,
    autoHeaderHeight: true,
  };
  gridOptions: any = {
    rowSelection: 'multiple',
    suppressRowClickSelection: true,
  };

  pagingInfo: PagingInfo = {
    totalItems: 0,
    currentPage: 0,
    numberOfPage: 10,
  };

  pagingPromotionInfo: PagingInfo = {
    totalItems: 0,
    currentPage: 0,
    numberOfPage: 20,
  };

  numberOfPageDefault = 10;
  filterPromotion: OptionDropdown[];
  isLoadingPromotion: boolean = false;
  firstTimeLoad: boolean = true;
  promotionSelected: OptionDropdown;
  rowData: any[] = [];
  rowSelected: any;
  subscription = new Subscription();
  creationYears: OptionDropdown[];
  months: OptionDropdown[];
  statusList: OptionDropdown[];
  years: OptionDropdown[];
  dealer: OptionDropdown[];
  filterReceivableClaimStatus: OptionDropdown[];
  colDefs: any;
  reasonList: OptionDropdown[] = [];
  currentRoles: any;
  currentRole: any;
  ROLES = ROLES;
  rowsSelected: any;
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['refreshByStatus'] && changes['refreshByStatus'].currentValue) {
      this.getClaimList(changes['refreshByStatus'].currentValue);
      this.filterClaimForm.controls['status'].patchValue(
        changes['refreshByStatus'].currentValue
      );
      setTimeout(() => {
        this.refreshByStatusChange.emit('');
      }, 100);
    }
  }
  ngOnInit(): void {
    this.currentRoles = JSON.parse(localStorage.getItem('roles'))?.roles || [];
    this.currentRole = this.userService.getGroupRole(this.currentRoles);
    if (this.currentRole === ROLES.MARKETINGGROUP) {
      this.gridOptions = {
        ...this.gridOptions,
        hideDisabledCheckboxes: true,
        isRowSelectable: (rowNode) =>
          rowNode?.data &&
          rowNode?.data?.status &&
          rowNode?.data?.status &&
          rowNode?.data?.status?.code
            ? rowNode?.data?.status?.code ===
              PromotionClaimStatus.CLAIM_VALIDATED
            : false,
      };
    }
    if (this.isDisplayDealer) {
      this.getColumnHaveDealer();
    } else {
      this.getColumnNotHaveDealer();
    }
    this.getReceivableClaimMetaData();
  }

  ngOnDestroy(): void {
    this.subscription && this.subscription.unsubscribe();
  }

  viewDetail(id: string) {
    this.router.navigate(['/claims/receivables/receivables-claim', id]);
  }

  searchClaimList() {
    this.pagingInfo.totalItems = 0;
    this.pagingInfo.currentPage = 0;
    this.getClaimList();
  }
  getClaimList(status: string = ''): void {
    const data = this.filterClaimForm?.value;
    let params: any = {
      currentPage: this.pagingInfo?.currentPage,
      pageSize: this.pagingInfo?.numberOfPage,
      status: status || data?.status || '',
    };
    if (!this.isDisplayDealer) {
    } else {
      params.dealer = data?.id || '';
    }

    this.loadingService.showLoader();
    this.receivableService
      .getClaimList(params)
      .pipe(
        finalize(() => {
          this.loadingService.hideLoader();
        })
      )
      .subscribe({
        next: (response: any) => {
          this.rowData = response?.items || [];
          this.pagingInfo.totalItems = response?.pagination?.totalResults;
        },
        error: () => {
          this.rowData = [];
          this.pagingInfo = {
            totalItems: 0,
            currentPage: 0,
            numberOfPage: 10,
          };
        },
      });
  }

  onPageChange(value: number): void {
    this.pagingInfo.currentPage = value;
    this.getClaimList();
  }

  searchDevice(): void {
    this.pagingInfo.currentPage = 0;
  }

  changeItemPerPage(item) {
    this.pagingInfo.numberOfPage = item;
    this.pagingInfo.currentPage = 0;
    this.getClaimList();
  }

  getReceivableClaimMetaData(): void {
    this.loadingService.showLoader();
    this.subscription.add(
      this.receivableService.getReceivableClaimMetaData().subscribe(
        (response) => {
          this.dealer =
            _.orderBy(response?.dealers, ['displayName'], ['asc'])
            .map((item) => {
              return {
                code: item?.name || '',
                name: item?.displayName || '',
              };
            }) || [];
          this.statusList = response?.enums?.receivableClaimStatus || [];
          this.dealer?.unshift({
            code: '',
            name: this.translateService.instant('common.all'),
          });
          this.statusList?.unshift({
            code: '',
            name: this.translateService.instant('common.all'),
          });
          this.filterClaimForm.patchValue({
            id: this.dealer && this.dealer.length && this.dealer[0]?.code || '',
            status: this.statusList && this.statusList.length && this.statusList[0]?.code || '',
          });
          this.getClaimList();
        },
        (error) => {
          this.getClaimList();
        }
      )
    );
  }

  getColumnHaveDealer() {
    this.colDefs = [
      {
        headerName: '',
        headerCheckboxSelection: true,
        headerCheckboxSelectionFilteredOnly: true,
        checkboxSelection: true,
        field: 'select_all',
        sortable: false,
        wrapText: true,
        autoHeight: true,
        width: 48,
      },
      {
        headerName: this.translateService.instant('promotions.table.id'),
        headerValueGetter: () =>
          this.translateService.instant('promotions.table.id'),
        field: 'id',
        wrapText: true,
        autoHeight: true,
        flex: 1,
        cellRenderer: RouterLinkCellRendererComponent,
        cellRendererParams: {
          linkBuilder: (data: any) => `/claims/receivables/receivables-claim/${data?.id}`,
          displayName: (params) => {
            return params?.id ? params?.id : '-';
          },
        }
      },
      {
        headerName: this.translateService.instant('promotions.table.status'),
        headerValueGetter: () =>
          this.translateService.instant('promotions.table.status'),
        field: 'status',
        flex: 1,
        minWidth: 150,
        autoHeaderHeight: true,
        wrapText: true,
        autoHeight: true,
        cellRenderer: (params) => {
          return params?.value?.name ? params?.value?.name : '-';
        },
      },
      {
        headerName: this.translateService.instant(
          'promotions.table.claimableAmount'
        ),
        headerValueGetter: () =>
          this.translateService.instant('promotions.table.claimableAmount'),
        field: 'claimableAmount',
        headerClass: 'header-center',
        cellClass: 'currency-with-symbol cell-right',
        flex: 1,
        wrapText: true,
        autoHeight: true,
        cellRenderer: CellCurrencyCustomComponent,
      },
      {
        headerName: this.translateService.instant('promotions.table.dealer'),
        headerValueGetter: () =>
          this.translateService.instant('promotions.table.dealer'),
        field: 'dealer',
        flex: 1,
        wrapText: true,
        autoHeight: true,
        cellRenderer: (params) => {
          return params?.value?.displayName ? params?.value?.displayName : '-';
        },
      },
      {
        headerName: this.translateService.instant(
          'promotions.table.referenceNumber'
        ),
        headerValueGetter: () =>
          this.translateService.instant('promotions.table.referenceNumber'),
        field: 'referenceNumber',
        flex: 1,
        wrapText: true,
        autoHeight: true,
        cellRenderer: (params) => {
          return params?.value ? params?.value : '-';
        },
      },
      {
        headerName: this.translateService.instant(
          'promotions.table.createdDate'
        ),
        headerValueGetter: () =>
          this.translateService.instant('promotions.table.createdDate'),
        field: 'createDate',
        flex: 1,
        autoHeight: true,
        wrapText: true,
        cellRenderer: (params) => {
          return params?.value
            ? this.dateTimeHelper.convertUTCtoDisplayDate(params?.value, DateFormat.FullDate)
            : '-';
        },
      },
      {
        headerName: this.translateService.instant(
          'promotions.table.lastUpdatedDate'
        ),
        headerValueGetter: () =>
          this.translateService.instant('promotions.table.lastUpdatedDate'),
        field: 'updatedDate',
        flex: 1,
        autoHeight: true,
        wrapText: true,
        cellRenderer: (params) => {
          return params?.value
            ? this.dateTimeHelper.convertUTCtoDisplayDate(params?.value, DateFormat.FullDate)
            : '-';
        },
      },
      {
        headerName: this.translateService.instant(
          'promotions.table.lastUpdatedBy'
        ),
        headerValueGetter: () =>
          this.translateService.instant('promotions.table.lastUpdatedBy'),
        field: 'updatedBy',
        flex: 1,
        wrapText: true,
        autoHeight: true,
        cellRenderer: (params) => {
          return params?.value?.name ? params?.value?.name : '-';
        },
      },
      {
        headerName: this.translateService.instant('promotions.table.action'),
        headerValueGetter: () =>
          this.translateService.instant('promotions.table.action'),
        field: 'status',
        flex: 1,
        wrapText: true,
        autoHeight: true,
        cellRenderer: CellActionReceivableClaimComponent,
        cellRendererParams: {
          onClick: (type: string, data: any) =>
            this.handleActionTable(type, data),
        },
      },
    ];
  }

  getColumnNotHaveDealer() {
    this.colDefs = [
      {
        headerName: this.translateService.instant('promotions.table.id'),
        headerValueGetter: () =>
          this.translateService.instant('promotions.table.id'),
        field: 'id',
        wrapText: true,
        autoHeight: true,
        flex: 1,
        cellRenderer: RouterLinkCellRendererComponent,
        cellRendererParams: {
          linkBuilder: (data: any) => `/claims/receivables/receivables-claim/${data?.id}`,
          displayName: (params) => {
            return params?.id ? params?.id : '-';
          },
        }
      },
      {
        headerName: this.translateService.instant('promotions.table.status'),
        headerValueGetter: () =>
          this.translateService.instant('promotions.table.status'),
        field: 'status',
        flex: 1,
        minWidth: 150,
        autoHeaderHeight: true,
        wrapText: true,
        autoHeight: true,
        cellRenderer: (params) => {
          return params?.value?.name ? params?.value?.name : '-';
        },
      },
      {
        headerName: this.translateService.instant(
          'promotions.table.claimableAmount'
        ),
        headerValueGetter: () =>
          this.translateService.instant('promotions.table.claimableAmount'),
        field: 'claimableAmount',
        headerClass: 'header-center',
        cellClass: 'currency-with-symbol cell-right',
        flex: 1,
        wrapText: true,
        autoHeight: true,
        cellRenderer: CellCurrencyCustomComponent,
      },
      {
        headerName: this.translateService.instant(
          'promotions.table.referenceNumber'
        ),
        headerValueGetter: () =>
          this.translateService.instant('promotions.table.referenceNumber'),
        field: 'referenceNumber',
        flex: 1,
        wrapText: true,
        autoHeight: true,
        cellRenderer: (params) => {
          return params?.value ? params?.value : '-';
        },
      },
      {
        headerName: this.translateService.instant(
          'promotions.table.createdDate'
        ),
        headerValueGetter: () =>
          this.translateService.instant('promotions.table.createdDate'),
        field: 'createDate',
        flex: 1,
        autoHeight: true,
        wrapText: true,
        cellRenderer: (params) => {
          return params?.value
            ? this.dateTimeHelper.convertUTCtoDisplayDate(params?.value, DateFormat.FullDate)
            : '-';
        },
      },
      {
        headerName: this.translateService.instant(
          'promotions.table.lastUpdatedDate'
        ),
        headerValueGetter: () =>
          this.translateService.instant('promotions.table.lastUpdatedDate'),
        field: 'updatedDate',
        flex: 1,
        autoHeight: true,
        wrapText: true,
        cellRenderer: (params) => {
          return params?.value
            ? this.dateTimeHelper.convertUTCtoDisplayDate(params?.value, DateFormat.FullDate)
            : '-';
        },
      },
      {
        headerName: this.translateService.instant(
          'promotions.table.lastUpdatedBy'
        ),
        headerValueGetter: () =>
          this.translateService.instant('promotions.table.lastUpdatedBy'),
        field: 'updatedBy',
        flex: 1,
        wrapText: true,
        autoHeight: true,
        cellRenderer: (params) => {
          return params?.value?.name ? params?.value?.name : '-';
        },
      },
      {
        headerName: this.translateService.instant('promotions.table.action'),
        headerValueGetter: () =>
          this.translateService.instant('promotions.table.action'),
        field: 'status',
        flex: 1,
        wrapText: true,
        autoHeight: true,
        cellRenderer: CellActionReceivableClaimComponent,
        cellRendererParams: {
          onClick: (type: string, data: any) =>
            this.handleActionTable(type, data),
        },
      },
    ];
  }

  handleActionTable(type: string, data: any) {
    switch (type) {
      case PromotionClaimAction.addORARNumber:
        // this.openAddARNumberModal(data);
        break;
      case PromotionClaimAction.reject:
        this.getListReason(data);
        break;

      case PromotionClaimAction.generateExcel:
        this.openGenerateExcelModal(data);
        break;

      case PromotionClaimAction.approve:
        this.openApproveModal(data);
        break;

      case PromotionClaimAction.appeal:
        this.onAppealModal(data);
        break;
    }
  }

  openGenerateExcelModal(data, isMulti = false) {
    const dialogRef = this.dialog.open(DialogConfirmComponent, {
      width: '530px',
      data: {
        title: this.translateService.instant(
          'receivables.popup.generateExcelForCAS'
        ),
        icon: 'ic-warning',
        confirmMsg: this.translateService.instant(isMulti ? 'receivables.popup.generateExcelForCASMulti' : 
          'receivables.popup.generateExcelForCASSingle'
        ),
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.generate'),
      },
    });
    let body;
    if (isMulti) {
      body = data.map((item) => item?.id);
    } else {
      body = [data?.id];
    }
    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((result) => {
        if (result) {
          this.subscription.add(
            this.receivableService.exportExcelReceivables(body).subscribe(
              (res) => {
                if (res.code === '200') {
                  this.generateExcelService.downloadExcelByURL(res?.message);
                  if (isMulti) {
                    this.notificationService.showSuccess(
                      this.translateService.instant(
                        'receivables.message.exportMultiSuccess'
                      )
                    );
                  } else {
                    this.notificationService.showSuccess(
                      this.translateService.instant(
                        'receivables.message.exportSuccess',
                        { claimId: data?.id }
                      )
                    );
                  }
                } else {
                  this.notificationService.showError(res?.message);
                }
                this.getClaimList();
              },
              (err) => {
                handleErrors(err, this.notificationService);
              }
            )
          );
        }
      });
  }

  getListReason(data) {
    this.loadingService.showLoader();
    this.subscription.add(
      this.receivableService
        .getReceivableClaimMetaData()
        .pipe(finalize(() => this.loadingService.hideLoader()))
        .subscribe(
          (response) => {
            this.reasonList = response?.enums?.claimRejectReason;
            this.openRejectModal(data);
          },
          (err) => {
            this.openRejectModal(data);
          }
        )
    );
  }

  openRejectModal(data) {
    this.rejectForm.reset();
    const dialogRef = this.dialog.open(ModalReceivableRejectComponent, {
      width: '530px',
      data: {
        form: this.rejectForm,
        reasonList: this.reasonList,
        claimCode: data?.id,
        mode: 'claim',
        rejectReason: this.rejectForm.controls['rejectReason'],
        reason: this.rejectForm.controls['reason'],
        remarks: this.rejectForm.controls['remarks'],
        title: 'receivables.popup.rejectClaim',
        cancelBtn: 'common.cancel',
        submitBtn: 'common.reject',
      },
    });
    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((result) => {
        if (result) {
          this.getClaimList();
        }
      });
  }

  openApproveModal(data) {
    this.approveForm.reset();
    const dialogRef = this.dialog.open(ModalReceivableApproveComponent, {
      width: '850px',
      data: {
        form: this.approveForm,
        claimCode: data?.id,
        glCode: this.approveForm.controls['glCode'],
        costCenter: this.approveForm.controls['costCenter'],
        internalOrderNo: this.approveForm.controls['internalOrderNo'],
        profitCenter: this.approveForm.controls['profitCenter'],
        title: 'receivables.popup.approveClaim',
        cancelBtn: 'common.cancel',
        submitBtn: 'common.approve',
      },
    });
    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((result) => {
        if (result) {
          this.getClaimList();
        }
      });
  }

  onAppealModal(data) {
    const dialogRef = this.dialog.open(DialogConfirmComponent, {
      width: '530px',
      data: {
        title: this.translateService.instant('promotionDetail.appealClaim'),
        icon: 'ic-warning',
        confirmMsg: this.translateService.instant(
          'promotionDetail.appealContent'
        ),
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.appeal'),
      },
      // autoFocus: true,
    });
    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((result) => {
        if (result?.action === ActionModal.Submit) {
          this.loadingService.showLoader();
          this.receivableService
            .appealReceivableClaim({}, data?.id)
            .pipe(finalize(() => this.loadingService.hideLoader()))
            .subscribe(
              (res) => {
                if (res) {
                  if (res?.code === '200') {
                    this.notificationService.showSuccess(res?.message);
                    this.getClaimList();
                  } else {
                    this.notificationService.showError(res?.message);
                  }
                }
              },
              (error) => {
                handleErrors(error, this.notificationService);
              }
            );
        }
      });
  }

  onRowSelected(event) {
    this.rowsSelected = event;
  }
  exportData(): void {
    // this.loadingService.showLoader();
    // this.deviceService.exportDevice(this.selectedTab).subscribe((response) => {
    //   this.loadingService.hideLoader();
    //   const link = document.createElement('a');
    //   link.href = URL.createObjectURL(response?.blob);
    //   link.download = response?.filename;
    //   link.click();
    //   URL.revokeObjectURL(link.href);
    // });
  }
}
