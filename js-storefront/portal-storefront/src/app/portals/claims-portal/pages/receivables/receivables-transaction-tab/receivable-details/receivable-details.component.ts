import { CommonModule, C<PERSON><PERSON>cyPipe, DatePipe } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import {
  ScreenSizeService,
  LoadingService,
  NotificationService,
} from '../../../../../../core/services';
import { BreadcrumbWithLabelComponent } from '../../../../../../core/shared/breadcrumb-with-label/breadcrumb-with-label.component';
import { FileListComponent } from '../../../../features/file-list/file-list.component';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { downloadFile } from '../../../../../../core/helpers/function-common.helper';
import { Documents } from '../../../../../insurance-portal/interfaces';

import { filter, finalize, Subscription } from 'rxjs';
import { ReceivableService } from '../../../../services/receivable/receivable.service';
import { ReceivableClaimDetail } from '../../../../interfaces/receivable.interface';
import { handleErrors } from '../../../../../../core/helpers';
import { UploadFileComponent } from '../../../../../insurance-portal/features/upload-file/upload-file.component';
import { ReceivableFormComponent } from '../../add-receivable/receivable-form/receivable-form.component';
import {
  ReceivableStatus,
  ReceivableType,
} from '../../../../enum/receivable.enum';
import {
  ActionModal,
  DateFormat,
  TimeZone,
} from '../../../../../../core/enums';
import { UserService } from '../../../../../../core/services/user';
import { OptionDropdown } from '../../../../../../core/interfaces';
import { PromotionClaimAction, PromotionClaimStatus } from '../../../../enum';
import { DialogConfirmComponent } from '../../../../../../core/shared';
import { ModalReceivableRejectComponent } from '../../modal/modal-receivable-reject/modal-receivable-reject.component';
import { PopupApprovePromotionClaimComponent } from '../../../promotions/popup-approve-promotion-claim/popup-approve-promotion-claim.component';
import { LoadingComponent } from "../../../../../../layout/global/loading/loading.component";
import { DateTimeHelper } from '../../../../../../core/helpers/date-time.helper';
import moment from 'moment';
import { ROLES } from '../../../../../../core/constants';

@Component({
  selector: 'app-receivable-details',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    TranslateModule,
    TranslateModule,
    BreadcrumbWithLabelComponent,
    MatExpansionModule,
    FileListComponent,
    UploadFileComponent,
    ReceivableFormComponent,
    LoadingComponent
],
  providers: [
    ScreenSizeService,
    LoadingService,
    NotificationService,
    ReceivableService,
    CurrencyPipe,
    DatePipe,
    UserService,
    DateTimeHelper
  ],
  templateUrl: './receivable-details.component.html',
  styleUrls: ['./receivable-details.component.scss'],
})
export class ReceivableDetailsComponent implements OnInit {
  dialog = inject(MatDialog);
  translateService = inject(TranslateService);
  notificationService = inject(NotificationService);
  loadingService = inject(LoadingService);
  route = inject(ActivatedRoute);
  router = inject(Router);
  currencyPipe = inject(CurrencyPipe);
  datePipe = inject(DatePipe);
  userService = inject(UserService);
  dateTimeHelper = inject(DateTimeHelper);

  receivableService = inject(ReceivableService);

  readonly ReceivableType = ReceivableType;
  readonly ReceivableStatus = ReceivableStatus;

  subscription = new Subscription();

  mapData: any;
  keyName: any;
  rowSize: number = 5;
  isEdit: boolean = false;
  files: any;

  currentRoles: any;
  currentRole: any;
  statusCode: string;
  PromotionClaimStatus = PromotionClaimStatus;
  ROLES = ROLES;
  PromotionClaimAction = PromotionClaimAction;
  rejectReceivableForm: FormGroup = new FormGroup({
    rejectReason: new FormControl('', [Validators.required]),
    reason: new FormControl(''),
    remarks: new FormControl(''),
  });

  public get rejectReasonControl(): FormControl {
    return this.rejectReceivableForm.get('rejectReason') as FormControl;
  }

  public get remarksControl(): FormControl {
    return this.rejectReceivableForm.get('remarks') as FormControl;
  }

  receivableId: string;

  dataDetail: ReceivableClaimDetail;
  reasonList: OptionDropdown[];
  attachments: Documents[] = [];

  ngOnInit(): void {
    const id = this.route.snapshot.paramMap.get('id');
    const isEdit = this.route.snapshot.queryParamMap.get('edit');

    if (isEdit === 'true') this.isEdit = true;
    else this.isEdit = false;

    this.currentRoles = JSON.parse(localStorage.getItem('roles'))?.roles || [];
    this.currentRole = this.userService.getGroupRole(this.currentRoles);
    this.keyName = this.translateService.instant(
      'receivables.details.detailsSection'
    );
    if (id !== null) {
      this.receivableId = id;
      this.getDetail();
    }
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  // setExpanded(index: number, check: boolean) {
  //   this.transactionListExpandStatus[index].check = check
  // }

  getDetail() {
    this.loadingService.showLoader();
    this.receivableService.getReceivableDetail(this.receivableId)
    .pipe(finalize(() => this.loadingService.hideLoader()))
    .subscribe(
      (data: ReceivableClaimDetail) => {
        this.dataDetail = data;
        this.dataDetail.detail = {
          ...this.dataDetail.detail,
          searchCustomer: this.dataDetail?.detail?.customer || '',
          searchModel: this.dataDetail?.detail?.model || ''
        }
        this.statusCode = data?.status?.code;
        this.mapDataDetails();
        this.isEdit = false;
      },
      (error) => {
        handleErrors(error, this.notificationService);
      }
    );
  }

  convertObjectEntries(object: any) {
    return Object.entries(object);
  }

  onDownloadFile(event: Documents) {
    if (event.code) {
      downloadFile(event.realFileName, event.downloadUrl);
    } else {
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = event.downloadUrl;
      a.download = event.realFileName;
      document.body.appendChild(a);
      a.click();
      a.remove();
    }
  }

  mapDataDetails(): void {
    const {
      type: { code },
    } = this.dataDetail;

    switch (code) {
      case ReceivableType.RETURNED_UNIT: {
        this.mapData = [
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.model'
            ),
            value: this.dataDetail?.detail?.model?.name || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.csNumber'
            ),
            value: this.dataDetail?.detail?.csNumber || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.amountOfUnit'
            ),
            value: this.dataDetail?.detail?.amountUnit ? this.currencyPipe.transform(
              this.dataDetail?.detail?.amountUnit?.value,
              this.dataDetail?.detail?.amountUnit?.currencyIso
            )
            : '-',
            isAmount: true,
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.invoiceDate'
            ),
            value: this.dataDetail?.detail?.invoiceDate
              ? this.datePipe.transform(
                  this.dataDetail?.detail?.invoiceDate,
                  DateFormat.ShortDate,
                  TimeZone.UTC8
                )
              : '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.reasonForCancellation'
            ),
            value: this.dataDetail?.detail?.reason || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.invoiceCancellationDate'
            ),
            value: this.dataDetail?.detail?.invoiceCancellationDate
              ? this.datePipe.transform(
                  this.dataDetail?.detail?.invoiceCancellationDate,
                  DateFormat.ShortDate,
                  TimeZone.UTC8
                )
              : '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.deallocationDate'
            ),
            value: this.dataDetail?.detail?.deallocationDate
              ? this.datePipe.transform(
                  this.dataDetail?.detail?.deallocationDate,
                  DateFormat.ShortDate,
                  TimeZone.UTC8
                )
              : '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.returnedDate'
            ),
            value: this.dataDetail?.detail?.returnedDate
              ? this.datePipe.transform(
                  this.dataDetail?.detail?.returnedDate,
                  DateFormat.ShortDate,
                  TimeZone.UTC8
                )
              : '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.receivedDate'
            ),
            value: this.dataDetail?.detail?.receivedDate
              ? this.datePipe.transform(
                  this.dataDetail?.detail?.receivedDate,
                  DateFormat.ShortDate,
                  TimeZone.UTC8
                )
              : '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.debitMemo'
            ),
            value: this.dataDetail?.detail?.debitMemo || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.vehicleSalesInvoice'
            ),
            value: this.dataDetail?.detail?.vehicleSaleInvoice || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.vehicleDeliveryNote'
            ),
            value: this.dataDetail?.detail?.vehicleDeliveryNote || '-',
          },
        ];
        break;
      }
      case ReceivableType.EXTERNAL_AFTER_SALE_REBATE_PART_SERVICE: {
        this.mapData = [
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.repairOrderNo'
            ),
            value: this.dataDetail?.detail?.repairOrderNo || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.repairOrderDate'
            ),
            value: this.dataDetail?.detail?.repairOrderDate
              ? this.datePipe.transform(
                  this.dataDetail?.detail?.repairOrderDate,
                  DateFormat.ShortDate,
                  TimeZone.UTC8
                )
              : '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.customerName'
            ),
            value: this.dataDetail?.detail?.customer?.displayName || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.model'
            ),
            value: this.dataDetail?.detail?.model?.name || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.roAmount'
            ),
            value: this.dataDetail?.detail?.roAmountToReverse ? this.currencyPipe.transform(
              this.dataDetail?.detail?.roAmountToReverse,
              'PHP'
            ) : '-',
            isAmount: true,
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.serviceBillingNo'
            ),
            value: this.dataDetail?.detail?.serviceBillingNo || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.serviceBillingAmount'
            ),
            value: this.dataDetail?.detail?.serviceBillingAmountToReverse ? this.currencyPipe.transform(
              this.dataDetail?.detail?.serviceBillingAmountToReverse,
              'PHP'
            ) : '-',
            isAmount: true,
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.debitMemoAmount'
            ),
            value: this.dataDetail?.detail?.debitMemoAmountToReverse ? this.currencyPipe.transform(
              this.dataDetail?.detail?.debitMemoAmountToReverse,
              'PHP'
            )
            : '-',
            isAmount: true,
          },
        ];
        break;
      }
      case ReceivableType.EXTERNAL_AFTER_SALE_REBATE_FLEET_CLAIM: {
        this.mapData = [
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.repairOrderNo'
            ),
            value: this.dataDetail?.detail?.repairOrderNo || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.repairOrderDate'
            ),
            value: this.dataDetail?.detail?.repairOrderDate
              ? this.datePipe.transform(
                  this.dataDetail?.detail?.repairOrderDate,
                  DateFormat.ShortDate,
                  TimeZone.UTC8
                )
              : '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.customerName'
            ),
            value: this.dataDetail?.detail?.customer?.displayName || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.invoiceDate'
            ),
            value: this.dataDetail?.detail?.invoiceDate
              ? this.datePipe.transform(
                  this.dataDetail?.detail?.invoiceDate,
                  DateFormat.ShortDate,
                  TimeZone.UTC8
                )
              : '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.serviceBillingNo'
            ),
            value: this.dataDetail?.detail?.serviceBillingNo || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.serviceBillingAmount'
            ),
            value:
              this.dataDetail?.detail?.serviceBillingAmountToReverse ? this.currencyPipe.transform(
                this.dataDetail?.detail?.serviceBillingAmountToReverse,
                'PHP'
              ) : '-',
              isAmount: true,
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.debitMemoAmount'
            ),
            value: this.dataDetail?.detail?.debitMemoAmountToReverse ? this.currencyPipe.transform(
              this.dataDetail?.detail?.debitMemoAmountToReverse,
              'PHP'
            )
            : '-',
            isAmount: true,
          },
        ];
        break;
      }
      case ReceivableType.INTERNAL_AFTER_SALE_REBATE: {
        this.mapData = [
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.promoDescriptionParticulars'
            ),
            value: this.dataDetail?.detail?.promoDescription || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.debitMemoAmount'
            ),
            value: this.dataDetail?.detail?.debitMemoAmount ? this.currencyPipe.transform(
              this.dataDetail?.detail?.debitMemoAmount?.value,
              this.dataDetail?.detail?.debitMemoAmount?.currencyIso
            )
            : '-',
            isAmount: true,
          },
        ];
        break;
      }

      default: {
        this.mapData = [
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.model'
            ),
            value: this.dataDetail?.detail?.model?.name || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.plateNumber'
            ),
            value: this.dataDetail?.detail?.plateNumber || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.csNumber'
            ),
            value: this.dataDetail?.detail?.csNumber || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.customerName'
            ),
            value: this.dataDetail?.detail?.customer?.displayName || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.dateOfClaimSubmissionByDealer'
            ),
            value: this.dataDetail?.detail?.submissionDate
              ? this.datePipe.transform(
                  this.dataDetail?.detail?.submissionDate,
                  DateFormat.ShortDate,
                  TimeZone.UTC8
                )
              : '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.dmReferenceNumber'
            ),
            value: this.dataDetail?.detail?.dmNumber || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.rfpReferenceNumber'
            ),
            value: this.dataDetail?.detail?.rfpNumber || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.descriptionOfRFP'
            ),
            value: this.dataDetail?.detail?.rfpDescription || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.totalAmount'
            ),
            value: this.dataDetail?.detail?.totalAmount ? this.currencyPipe.transform(
              this.dataDetail?.detail?.totalAmount?.value,
              this.dataDetail?.detail?.totalAmount?.currencyIso
            )
            : '-',
            isAmount: true,
          },
        ];
        break;
      }
    }
  }

  handleActionTable(type: string, data: any) {
    switch (type) {
      case PromotionClaimAction.reject:
        this.getListReason(data);
        break;

      case PromotionClaimAction.approve:
        this.openApproveModal(data);
        break;

      case PromotionClaimAction.appeal:
        this.onAppealModal(data);
        break;
    }
  }

  getListReason(data) {
    this.loadingService.showLoader();
    this.subscription.add(
      this.receivableService
        .getReceivableMetaData()
        .pipe(finalize(() => this.loadingService.hideLoader()))
        .subscribe(
          (response) => {
            this.reasonList = response?.enums?.receivableRejectReason;
            this.openRejectModal(data);
          },
          (err) => {
            this.openRejectModal(data);
          }
        )
    );
  }

  openApproveModal(data) {
    const dialogRef = this.dialog.open(DialogConfirmComponent, {
      width: '530px',
      data: {
        title: this.translateService.instant(
          'receivables.popup.approveReceivable'
        ),
        icon: 'ic-warning',
        confirmMsg: this.translateService.instant(
          'receivables.popup.approveMsgReceivable'
        ),
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.approve'),
      },
    });
    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((result) => {
        if (result) {
          this.subscription.add(
            this.receivableService
              .approveReceivableTransaction(data?.id)
              .subscribe(
                (res) => {
                  if (res.code === '200') {
                    this.notificationService.showSuccess(
                      this.translateService.instant(
                        'receivables.message.approveTransactionSuccess'
                      )
                    );
                  }
                  this.getDetail();
                },
                (error) => {
                  handleErrors(error, this.notificationService);
                }
              )
          );
        }
      });
  }

  openRejectModal(data: any) {
    this.rejectReceivableForm.reset();
    const dialogRef = this.dialog.open(ModalReceivableRejectComponent, {
      width: '530px',
      data: {
        form: this.rejectReceivableForm,
        reasonList: this.reasonList,
        receivableCode: data?.id,
        mode: 'transaction',
        rejectReason: this.rejectReceivableForm.controls['rejectReason'],
        reason: this.rejectReceivableForm.controls['reason'],
        remarks: this.rejectReceivableForm.controls['remarks'],
        title: 'receivables.popup.rejectReceivable',
        cancelBtn: 'common.cancel',
        submitBtn: 'common.reject',
      },
    });
    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((result) => {
        if (result) {
          this.getDetail();
        }
      });
  }

  onAppealModal(data) {
    const dialogRef = this.dialog.open(DialogConfirmComponent, {
      width: '530px',
      data: {
        title: this.translateService.instant('receivables.appealClaim'),
        icon: 'ic-warning',
        confirmMsg: this.translateService.instant(
          'receivables.appealContent'
        ),
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.appeal'),
      },
      // autoFocus: true,
    });
    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((result) => {
        if (result?.action === ActionModal.Submit) {
          this.loadingService.showLoader();
          this.receivableService
            .appealReceivableTransaction(data?.id)
            .pipe(finalize(() => this.loadingService.hideLoader()))
            .subscribe(
              (res) => {
                if (res) {
                  if (res?.code === '200') {
                    this.notificationService.showSuccess('receivables.appealedClaimMessage');
                    this.getDetail();
                  } else {
                    this.notificationService.showError(res?.message);
                  }
                }
              },
              (error) => {
                handleErrors(error, this.notificationService);
              }
            );
        }
      });
  }

  async onUpdate (e) {
    let type = this.dataDetail.type.code
    const data = this.mapDataUpdate(this.dataDetail?.type?.code, e);
    this.dataDetail.detail = {
      ...this.dataDetail?.detail,
      ...data,
      searchCustomer: e?.value?.searchCustomer || '',
      searchModel: e?.value?.searchModel || '',
    }
    let dataDetail: ReceivableClaimDetail = this.dataDetail
    let dataSend = {}
    let attachment: any;
    if (this.attachments && this.attachments.length) {
      attachment = await this.handleFileUpload(this.attachments);
    }
    switch(type) {
      case ReceivableType.GOODWILL_FREE_PERIODIC:
      case ReceivableType.GOODWILL_UNIT_REPLACEMENT:
      case ReceivableType.RENTAL_CAR:
        dataSend = {
          receivableType: type, //Fixed value: GOODWILL_FREE_PERIODIC/RENTAL_CAR/GOODWILL_UNIT_REPLACEMENT
          dealer: dataDetail?.dealer?.name !== undefined ? dataDetail?.dealer?.name : null, // can be null
          docs: attachment?.docs || [],
          docsName: attachment?.docNames || [],
          dealerName: dataDetail?.dealer?.displayName !== undefined ? dataDetail?.dealer?.displayName : null, // can be null
          productCode: dataDetail?.detail?.model?.code, // Code for the product, can be null
          plateNumber: dataDetail?.detail?.plateNumber,
          csNumber: dataDetail?.detail?.csNumber,
          customerUid: dataDetail?.detail?.customer?.uid,
          submissionDate: dataDetail?.detail?.submissionDate,
          dmNumber: dataDetail?.detail?.dmNumber,
          rfpNumber: dataDetail?.detail?.rfpNumber,
          rfpDescription: dataDetail?.detail?.rfpDescription,
          totalAmount: dataDetail?.detail?.totalAmountToReverse
        }
        break
      case ReceivableType.EXTERNAL_AFTER_SALE_REBATE_FLEET_CLAIM:
        dataSend = {
          receivableType: type, //Fixed value: GOODWILL_FREE_PERIODIC/RENTAL_CAR/GOODWILL_UNIT_REPLACEMENT
          dealer: dataDetail?.dealer?.name !== undefined ? dataDetail?.dealer?.name : null, // can be null
          docs: attachment?.docs || [],
          docsName: attachment?.docNames || [],
          dealerName: dataDetail?.dealer?.displayName !== undefined ? dataDetail?.dealer?.displayName : null, // can be null
          repairOrderNo: dataDetail?.detail?.repairOrderNo,
          repairOrderDate: dataDetail?.detail?.repairOrderDate,
          customerUid: dataDetail?.detail?.customer?.uid,
          invoiceDate: dataDetail?.detail?.invoiceDate,
          serviceBillingNo: dataDetail?.detail?.serviceBillingNo,
          serviceBillingAmount: dataDetail?.detail?.serviceBillingAmountToReverse,
          debitMemoAmount: dataDetail?.detail?.debitMemoAmountToReserve
        }
        break
      case ReceivableType.RETURNED_UNIT:
        dataSend = {
          receivableType: type, //Fixed value: GOODWILL_FREE_PERIODIC/RENTAL_CAR/GOODWILL_UNIT_REPLACEMENT
          dealer: dataDetail?.dealer?.name !== undefined ? dataDetail?.dealer?.name : null, // can be null
          docs: attachment?.docs || [],
          docsName: attachment?.docNames || [],
          dealerName: dataDetail?.dealer?.displayName !== undefined ? dataDetail?.dealer?.displayName : null, // can be null
          customerUid: dataDetail?.detail?.customer?.uid,
          csNumber: dataDetail?.detail?.csNumber,
          amountUnit: dataDetail?.detail?.amountUnit?.value,
          productCode: dataDetail?.detail?.model?.code, // can be null
          invoiceDate: dataDetail?.detail?.invoiceDate,
          receivedDate: dataDetail?.detail?.receivedDate,
          invoiceCancellationDate: dataDetail?.detail?.invoiceCancellationDate,
          deallocationDate: dataDetail?.detail?.deallocationDate,
          returnedDate: dataDetail?.detail?.returnedDate,
          reason: dataDetail?.detail?.reasonCode, // reason code
          vehicleSaleInvoice: dataDetail?.detail?.vehicleSaleInvoice,
          vehicleDeliveryNote: dataDetail?.detail?.vehicleDeliveryNote,
          debitMemo: dataDetail?.detail?.debitMemo,
        }
        break
      case ReceivableType.EXTERNAL_AFTER_SALE_REBATE_PART_SERVICE:
        dataSend = {
          receivableType: type, //Fixed value: GOODWILL_FREE_PERIODIC/RENTAL_CAR/GOODWILL_UNIT_REPLACEMENT
          dealer: dataDetail?.dealer?.name !== undefined ? dataDetail?.dealer?.name : null, // can be null
          docs: attachment?.docs || [],
          docsName: attachment?.docNames || [],
          dealerName: dataDetail?.dealer?.displayName !== undefined ? dataDetail?.dealer?.displayName : null, // can be null
          repairOrderNo: dataDetail?.detail?.repairOrderNo,
          repairOrderDate: dataDetail?.detail?.repairOrderDate,
          customerUid: dataDetail?.detail?.customer?.uid,
          serviceBillingNo: dataDetail?.detail?.serviceBillingNo,
          serviceBillingAmount: dataDetail?.detail?.serviceBillingAmountToReverse,
          productCode: dataDetail?.detail?.model?.code, // can be null
          roAmount: dataDetail?.detail?.roAmountToReverse,
          debitMemoAmount: dataDetail?.detail?.debitMemoAmountToReserve
        }
        break
      case ReceivableType.INTERNAL_AFTER_SALE_REBATE:
        dataSend = {
          receivableType: type, //Fixed value: GOODWILL_FREE_PERIODIC/RENTAL_CAR/GOODWILL_UNIT_REPLACEMENT
          dealer: dataDetail?.dealer?.name !== undefined ? dataDetail?.dealer?.name : null, // can be null
          docs: attachment?.docs || [],
          docsName: attachment?.docNames || [],
          dealerName: dataDetail?.dealer?.displayName !== undefined ? dataDetail?.dealer?.displayName : null, // can be null
          debitMemoAmount:  dataDetail?.detail?.debitMemoAmountToReserve,
          promoDescription: dataDetail?.detail?.promoDescription
        }
        break
    }

    dataSend = Object.fromEntries(
      Object.entries(dataSend).filter(([_, v]) => v !== undefined && v !== null)
    );

    this.loadingService.showLoader();
    this.receivableService
      .appealReceivableClaimTransaction(dataSend, this.dataDetail?.id, type)
      .pipe(finalize(() => this.loadingService.hideLoader()))
      .subscribe(
        (res) => {
          if (res) {
            if (res?.code === '200') {
              this.notificationService.showSuccess('receivables.message.receivableUpdateSuccess');
              this.getDetail();
            } else {
              this.notificationService.showError(res?.message);
            }
          }
        },
        (error) => {
          handleErrors(error, this.notificationService);
        }
      );
    }

  onEdit() {
    // this.router.navigate(
    //   ['/claims/receivables/receivables-transactions/' + this.receivableId],
    //   {
    //     queryParams: { edit: true },
    //   }
    // );

    this.isEdit = true;
  }

  checkLimitFiles(event: any) {
    let invalid: boolean = false;
    if (event && event.length > 0) {
      for (let index = 0; index < event.length; index++) {
        const element = event[index];
        if (element.size > 5 * 1024 * 1024) {
          invalid = true;
          return invalid;
        }
      }
    }
    return invalid;
  }

  onFileSelected(e: any) {
    const invalidLimit: boolean = this.checkLimitFiles(e);
    if (invalidLimit) {
      this.notificationService.showError(this.translateService.instant('uploadFile.invalidLimit5MB'));
      return;
    }
    const mapFiles = [];
    const mapAttachment = []
    Array.from(e).forEach((item: any) => {
      const temp = {
        creationTime: new Date(),
        realFileName: item?.name,
        downloadUrl: URL.createObjectURL(item)
      }
      mapFiles.push(temp);
      mapAttachment.push(item);
    })
    if (!this.dataDetail?.documents) {
      this.dataDetail.documents = [];
    }
    this.dataDetail.documents = [...this.dataDetail.documents, ...mapFiles];;
    this.attachments = [...this.attachments, ...mapFiles];
  }

  removeFile(index) {
    const dialogRef = this.dialog.open(PopupApprovePromotionClaimComponent, {
      width: '600px',
      data: {
        title: this.translateService.instant('promotionDetail.removeAttachmentDocument'),
        content: this.translateService.instant('promotionDetail.removeAttachmentDocumentContent'),
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.confirm'),
        isRemove: true
      },
        // autoFocus: true,
    });

    dialogRef.afterClosed()
    .pipe(filter((result) => result?.action === ActionModal.Submit))
    .subscribe((result) => {
      if (result?.action === ActionModal.Submit) {
        const find = this.dataDetail.documents.find((item, i) => {
          return i === index;
        })
        this.attachments = this.attachments.filter(item => item.creationTime !== find.creationTime);
        this.dataDetail.documents.splice(index, 1);
        this.notificationService.showSuccess('receivables.attachmentRemoveSuccess');
      }
    })
  }

  onCancel() {
    this.isEdit = false;
  }

  /**
   * 
   * @param date this function to convert date to UTC format before send to BE
   * @returns string of date in UTC format
   */
  convertDate(date: string) {
    return date ? moment.utc(date).format(DateFormat.UTC) : '';
  }

  mapDataUpdate(type: string, form) {
    const data = form.value;
    let body: any = {};
    switch (type) {
      case ReceivableType.RENTAL_CAR:
      case ReceivableType.GOODWILL_FREE_PERIODIC:
      case ReceivableType.GOODWILL_UNIT_REPLACEMENT:
        body = {
          customer: data?.searchCustomer || '',
          csNumber: data?.csNumber || '',
          plateNumber: data?.plateNumber || '',
          model: data?.searchModel || '',
          productCode: data?.searchModel || '',
          submissionDate: this.convertDate(data?.dateOfClaimSubmissionByDealer),
          dmNumber: data?.DMReferenceNumber || '',
          rfpNumber: data?.RFPReferenceNumber || '',
          rfpDescription: data?.descriptionOfRFP || '',
          totalAmountToReverse: data?.amount || '',
          totalAmount: {
            value: data?.amount || '',
          },
        };
        break;

      case ReceivableType.RETURNED_UNIT:
        body = {
          customer: data?.searchCustomer || '',
          csNumber: data?.csNumber || '',
          amountUnitToReverse: data?.amount || '',
          amountUnit: {
            value: data?.amount || '',
          },
          model: data?.searchModel || '',
          productCode: data?.searchModel || '',
          invoiceDate: this.convertDate(data?.invoiceDate),
          receivedDate: this.convertDate(data?.receivedDate),
          invoiceCancellationDate: this.convertDate(data?.invoiceCancellationDate),
          deallocationDate: this.convertDate(data?.deallocationDate),
          returnedDate: this.convertDate(data?.returnedDate),
          reason: data?.reasonName || '',
          reasonCode: data?.reason || '',
          vehicleSaleInvoice: data?.vehicleSalesInvoice || '',
          vehicleDeliveryNote: data?.vehicleDeliveryNote || '',
          debitMemo: data?.debitMemo || '',
        };
        break;

      case ReceivableType.INTERNAL_AFTER_SALE_REBATE:
        body = {
          debitMemoAmountToReserve: data?.amount || '',
          promoDescription: data?.promoDescription || '',
        };
        break;

      case ReceivableType.EXTERNAL_AFTER_SALE_REBATE_PART_SERVICE:
        body = {
          receivableType: data?.type || '',
          repairOrderNo: data?.repairOrderNumber || '',
          repairOrderDate: this.convertDate(data?.repairOrderDate),
          customer: data?.searchCustomer || '',
          serviceBillingNo: data?.serviceBillingNumber || '',
          serviceBillingAmountToReverse: data?.serviceBillingAmount || '',
          debitMemoAmountToReserve: data?.amount || '',
          model: data?.searchModel || '',
          productCode: data?.searchModel || '',
          roAmountToReverse: data?.roAmount || '',
        };
        break;

      case ReceivableType.EXTERNAL_AFTER_SALE_REBATE_FLEET_CLAIM:
        body = {
          receivableType: data?.type || '',
          repairOrderNo: data?.repairOrderNumber || '',
          repairOrderDate: this.convertDate(data?.repairOrderDate),
          customer: data?.searchCustomer || '',
          invoiceDate: this.convertDate(data?.invoiceDate),
          serviceBillingNo: data?.serviceBillingNumber || '',
          serviceBillingAmountToReverse: data?.serviceBillingAmount || '',
          debitMemoAmountToReserve: data?.amount || '',
        };
        break;
    }
    return body;
  }

  readFileAsync(file) {
    return new Promise(async(resolve, reject) => {
      const response = await fetch(file);
      const blob = await response.blob();
      const reader: any = new FileReader();
      reader.readAsDataURL(blob);
      reader.onload = () => resolve(reader.result.split(',')[1]);
    });
  }

  async handleFileUpload(attachments) {
    try {
      const tempAttachment = attachments;
      let params: any;
      let tempFiles: string[] = [];
      let tempFileName: string[] = [];

      for (const file of tempAttachment) {
        const content: any = await this.readFileAsync(file?.downloadUrl);
        tempFiles.push(content);
        tempFileName.push(file?.realFileName);
      }
      params = {
        docs: tempFiles,
        docNames: tempFileName,
      };
      return params;
    } catch (err) {
      return {};
    }
  }
}
