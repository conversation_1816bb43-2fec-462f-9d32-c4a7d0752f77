:host {
    min-height: calc(100vh - 73px);
    display: block;

    .tabs-group {
      ::ng-deep .mat-mdc-tab-body-wrapper {
        min-height: calc(100vh - 285px);
      }
    }
  
    .container-details {

        &__header {
            align-items: center;
        }

        &__body {
            padding: 30px;
            // display: flex;
            // justify-content: space-between;
            // align-items: end;

            &__promotion {
                padding: 15px;
                background-color: #f5f5f5;

                .section-header--content span {
                    font-size: 18px;
                    line-height: 24px;
                }

                .section-header--detail-section {
                    display: flex;
                    justify-content: space-between;
                    width: 100%;
                    &__left-title {
                        display: flex;
                        align-items: center;
                        gap: 12px;
                    }

                    &__right-title {
                        display: flex;
                        align-items: center;
                        gap: 5px;
                        cursor: pointer;

                        span {
                            color: #eb0a1e;
                            font-size: 14px;
                        } 
                    }

                }
               
                .section-body-upload-file {
                    margin-bottom: 10px;
                }           

                .section-body--content span {
                    color: #101010;
                    font-size: 16px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 22px;
                }

                .top-detail-section {
                    display: flex;
                    gap: 15px;

                    .details, .attached-document {
                        display: flex;
                        padding: 25px;
                        flex-direction: column;
                        align-items: flex-start;
                        gap: 25px;
                        flex: 1 0 0;
                        align-self: stretch;
                        background-color: #ffffff;
                    }

                    .details {
                        .section-body {
                            display: grid;
                            grid-template-columns: repeat(2, 1fr);
                            width: 100%;
                            gap: 20px;

                            .detail-column-left, .detail-column-right {
                                display: flex;
                                flex-direction: column;
                                gap: 25px;

                                .detail-item {
                                    display: flex;
                                    flex-direction: column;
                                    .title {
                                        color: #3A3A3A;
                                        font-size: 14px;
                                        font-style: normal;
                                        font-weight: 400;
                                        line-height: 14px; 
                                    }
                                    .content {
                                        color: #101010;
                                        font-size: 16px;
                                        font-style: normal;
                                        font-weight: 600;
                                        line-height: normal;
                                    }
                                }
                            }
                        }
                    }

                    .attached-document {
                        .section-body {
                            width: 100%;
                            display: flex;
                            flex-direction: column;
                            gap: 10px;
                        }
                    }
                }

                .bottom-detail-section {
                    display: flex;
                    gap: 5px;
                    margin-top: 15px;

                    .details {
                        display: flex;
                        padding: 25px;
                        flex-direction: column;
                        align-items: flex-start;
                        gap: 30px;
                        flex: 1 0 0;
                        align-self: stretch;
                        background-color: #ffffff;

                        .section-body {
                            width: 100%;
                        }

                        .detail-box {
                            padding: 0;
                            width: 100%;

                            .detail-content {
                                .detail-row {
                                    width: 100%;
                                    display: grid;
                                    grid-template-columns: repeat(5, 1fr);
                                    gap: 20px;
                                }
                            }

                            app-add-receivable {
                                padding: 0;
                                min-height: 0;
                            }
                        }
                    }
                }
            }
        }
    }

    .currency-with-symbol {
        .detail-value {
            display: inline-block;
            &::first-letter {
                margin-right: 3px;
            }
        }
    }
}