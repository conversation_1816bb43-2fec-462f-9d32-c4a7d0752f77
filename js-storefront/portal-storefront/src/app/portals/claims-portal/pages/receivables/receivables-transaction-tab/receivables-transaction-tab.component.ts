import { CommonModule, DatePipe } from '@angular/common';
import {
  Component,
  EventEmitter,
  inject,
  Input,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import {
  ReactiveFormsModule,
  FormGroup,
  FormControl,
  Validators,
} from '@angular/forms';
import * as _ from 'lodash-es';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatRadioModule } from '@angular/material/radio';
import { Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Subscription, finalize, filter } from 'rxjs';
import { PERMISSIONS_CODE, ROLES } from '../../../../../core/constants';
import {
  DeviceSummaryTab,
  ActionModal,
  DateFormat,
  ReceivableType,
} from '../../../../../core/enums';
import { OptionDropdown, PagingInfo } from '../../../../../core/interfaces';
import {
  NotificationService,
  LoadingService,
} from '../../../../../core/services';
import { UserService } from '../../../../../core/services/user';
import { CreateClaimModalComponent } from '../../../features/create-claim-modal/create-claim-modal.component';
import { TransactionTableComponent } from '../../../features/transaction-table/transaction-table.component';
import { ReceivablesTransactionFilterComponent } from './receivables-transaction-filter/receivables-transaction-filter.component';
import {
  PromotionClaimAction,
  PromotionsTab,
} from '../../../enum/promotion.enum';
import { ReceivableService } from '../../../services/receivable/receivable.service';
import { ReceivableClaimDetail, ReceivableTransactionItem } from '../../../interfaces/receivable.interface';
import { ModalReceivableRejectComponent } from '../modal/modal-receivable-reject/modal-receivable-reject.component';
import { CellActionReceivableClaimComponent } from '../receivable-claim-tab/promotion-claim-tab/cell-action-receivable-claim/cell-action-receivable-claim.component';
import { DialogConfirmComponent } from '../../../../../core/shared';
import { handleErrors } from '../../../../../core/helpers';
import { reject } from 'lodash-es';
import { RouterLinkCellRendererComponent } from '../../../../../core/shared/router-link-cell-renderer/router-link-cell-renderer.component';
import { DateTimeHelper } from '../../../../../core/helpers/date-time.helper';

@Component({
  selector: 'app-receivables-transaction-tab',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    MatIconModule,
    MatRadioModule,
    MatProgressSpinnerModule,
    ReceivablesTransactionFilterComponent,
    TransactionTableComponent,
  ],
  providers: [ReceivableService, NotificationService, DatePipe, DateTimeHelper],
  standalone: true,
  templateUrl: './receivables-transaction-tab.component.html',
  styleUrls: ['./receivables-transaction-tab.component.scss'],
})
export class ReceivablesTransactionTabComponent {
  @Input() refreshByStatus: string;
  @Input() isDisplayDealer: boolean = false;

  @Output() refreshByStatusChange = new EventEmitter<string>();

  router = inject(Router);
  loadingService = inject(LoadingService);
  translateService = inject(TranslateService);
  dialog = inject(MatDialog);
  receivableService = inject(ReceivableService);
  notificationService = inject(NotificationService);
  userService = inject(UserService);
  datePipe = inject(DatePipe);
  dateTimeHelper = inject(DateTimeHelper)

  readonly PromotionsTab = PromotionsTab;
  readonly DeviceSummaryTab = DeviceSummaryTab;
  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;

  selectedSummaryTab: DeviceSummaryTab;

  filterTransactionForm = new FormGroup({
    dealer: new FormControl(),
    type: new FormControl(),
    status: new FormControl(),
  });
  rejectForm = new FormGroup({
    rejectReason: new FormControl('', Validators.required),
    reason: new FormControl(''),
    remarks: new FormControl(''),
  });
  promotionList: OptionDropdown[] = [];
  defaultColDef = {
    resizable: false,
    sortable: false,
    menuTabs: [],
    wrapHeaderText: true,
    autoHeaderHeight: true
  };
  gridOptions: any = {
    rowSelection: 'multiple',
    suppressRowClickSelection: true,
  };

  colDefs: any;
  pagingInfo: PagingInfo = {
    totalItems: 0,
    currentPage: 0,
    numberOfPage: 10,
  };

  numberOfPageDefault = 10;
  filterType: OptionDropdown[];
  filterStatus: OptionDropdown[];
  filterDealer: OptionDropdown[];
  promotionSelected: OptionDropdown;
  rowData: ReceivableTransactionItem[];
  selectedTransactionItems: ReceivableTransactionItem[] = [];
  rowSelected: any;
  subscription = new Subscription();
  subscriptionTransaction: any;
  currentRoles: any;
  currentRole: any;
  reasonList: OptionDropdown[] = [];

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['refreshByStatus'] && changes['refreshByStatus'].currentValue) {
      this.filterTransactionForm.controls['status'].patchValue(
        changes['refreshByStatus'].currentValue
      );
      this.getTransactionList();
      setTimeout(() => {
        this.refreshByStatusChange.emit('');
      }, 100);
    }
  }

  ngOnInit(): void {
    this.currentRoles = JSON.parse(localStorage.getItem('roles'))?.roles || [];
    this.currentRole = this.userService.getGroupRole(this.currentRoles);
    if (this.currentRole === ROLES.CASHIERGROUP) {
      this.gridOptions = {
        ...this.gridOptions,
        hideDisabledCheckboxes: true,
        isRowSelectable: (rowNode) =>
          rowNode?.data &&
          rowNode?.data?.status &&
          rowNode?.data?.status &&
          rowNode?.data?.status?.code
            ? rowNode?.data?.status?.code === 'VALIDATED_MARKETING'
            : false,
      };
    }
    if (this.isDisplayDealer) {
      this.getColumnHaveDealer();
    } else if (this.currentRole === ROLES.CASHIERGROUP) {
      this.getColumnNotHaveDealer();
    } else {
      this.getColumnNotHaveDealerNotCheckbox();
    }
    this.getTransactionFilter();
  }

  ngOnDestroy(): void {
    this.subscription && this.subscription.unsubscribe();
  }

  viewPromotionDetail(id: string) {
    this.router.navigate(['/claims/receivables/receivables-transactions', id]);
  }

  searchTransaction() {
    this.pagingInfo.totalItems = 0;
    this.pagingInfo.currentPage = 0;
    this.getTransactionList();
  }

  getTransactionList(): void {
    const params: any = {
      currentPage: this.pagingInfo?.currentPage,
      pageSize: this.pagingInfo?.numberOfPage,
      type: this.filterTransactionForm?.value?.type || '',
      status: this.filterTransactionForm?.value?.status || '',
      dealer: this.filterTransactionForm?.value?.dealer || '',
    };

    this.loadingService.showLoader();
    this.subscription.add(
      this.receivableService
        .getTransactionList(params)
        .pipe(
          finalize(() => {
            this.loadingService.hideLoader();
          })
        )
        .subscribe({
          next: (response: any) => {
            this.rowData = response?.items || [];
            this.pagingInfo.totalItems = response?.pagination?.totalResults;
          },
          error: () => {},
        })
    );
  }

  onPageChange(value: number): void {
    this.pagingInfo.currentPage = value;
    this.getTransactionList();
  }

  changeItemPerPage(item) {
    this.pagingInfo.numberOfPage = item;
    this.pagingInfo.currentPage = 0;
    this.getTransactionList();
  }
  getTransactionFilter(): void {
    this.loadingService.showLoader();
    this.subscription.add(
      this.receivableService
        .getReceivableMetaData()
        .pipe(finalize(() => this.loadingService.hideLoader()))
        .subscribe(
          (response) => {
            this.filterStatus = response?.enums?.receivableStatus || [];
            this.filterType = response?.enums?.receivableType || [];
            this.reasonList = response?.enums?.receivableRejectReason || [];
            this.filterDealer = _.orderBy(response?.dealers, ['displayName'], ['asc'])?.map((item) => {
              return {
                code: item?.name || '',
                name: item?.displayName || '',
              };
            });
            this.filterStatus.unshift({
              code: '',
              name: this.translateService.instant('common.all'),
            });
            this.filterDealer.unshift({
              code: '',
              name: this.translateService.instant('common.all'),
            });
            this.filterTransactionForm.patchValue({
              type: this.filterType && this.filterType.length && this.filterType[0]?.code || '',
              status: this.filterStatus && this.filterStatus.length && this.filterStatus[0]?.code || '',
              dealer: this.filterDealer && this.filterDealer.length &&  this.filterDealer[0]?.code || '',
            });
            this.getTransactionList();
          },
          (error) => {}
        )
    );
  }

  combieClaim() {
    const dialogRef = this.dialog.open(CreateClaimModalComponent, {
      width: '1100px',
      data: {
        title: this.translateService.instant(
          'promotions.popup.titleCreateClaim'
        ),
        isReceivable: true,
        rowData: this.rowSelected,
        mode: 'combine',
      },
    });

    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((res) => {
        if (res) {
          this.getTransactionList();
        }
      });
  }


  onRowSelected(event: any) {
    this.rowSelected = event;
  }

  exportData(): void {
    // this.loadingService.showLoader();
    // this.deviceService.exportDevice(this.selectedTab).subscribe((response) => {
    //   this.loadingService.hideLoader();
    //   const link = document.createElement('a');
    //   link.href = URL.createObjectURL(response?.blob);
    //   link.download = response?.filename;
    //   link.click();
    //   URL.revokeObjectURL(link.href);
    // });
  }

  getColumnHaveDealer() {
    this.colDefs = [
      {
        headerName: this.translateService.instant('receivables.table.id'),
        headerValueGetter: () =>
          this.translateService.instant('receivables.table.id'),
        field: 'id',
        wrapText: true,
        autoHeight: true,
        flex: 1,
        cellRenderer: RouterLinkCellRendererComponent,
        cellRendererParams: {
          linkBuilder: (data: any) => `/claims/receivables/receivables-transactions/${data?.id}`,
          displayName: (params) => {
            return params?.id ? params?.id : '-';
          },
        }
      },
      {
        headerName: this.translateService.instant('receivables.table.claimId'),
        headerValueGetter: () =>
          this.translateService.instant('receivables.table.claimId'),
        field: 'claimId',
        flex: 1,
        autoHeaderHeight: true,
        wrapText: true,
        autoHeight: true,
        cellRenderer: (params) => {
          return params?.value ? params?.value : '-';
        },
      },
      {
        headerName: this.translateService.instant('receivables.table.status'),
        headerValueGetter: () =>
          this.translateService.instant('receivables.table.status'),
        field: 'status',
        flex: 1,
        wrapText: true,
        autoHeight: true,
        cellRenderer: (params) => {
          return params?.value?.name ? params?.value?.name : '-';
        },
      },
      {
        headerName: this.translateService.instant('receivables.table.type'),
        headerValueGetter: () =>
          this.translateService.instant('receivables.table.type'),
        field: 'type',
        flex: 1,
        wrapText: true,
        autoHeight: true,
        cellRenderer: (params) => {
          return params?.value?.name ? params?.value?.name : '-';
        },
      },
      {
        headerName: this.translateService.instant('promotions.table.dealer'),
        headerValueGetter: () =>
          this.translateService.instant('promotions.table.dealer'),
        field: 'dealer',
        flex: 1,
        wrapText: true,
        autoHeight: true,
        cellRenderer: (params) => {
          return params?.value?.displayName ? params?.value?.displayName : '-';
        },
      },
      {
        headerName: this.translateService.instant(
          'receivables.table.createdDate'
        ),
        headerValueGetter: () =>
          this.translateService.instant('receivables.table.createdDate'),
        field: 'createDate',
        flex: 1,
        autoHeight: true,
        wrapText: true,
        cellRenderer: (params) => {
          return params?.value
            ? this.dateTimeHelper.convertUTCtoDisplayDate(params?.value, DateFormat.FullDate)
            : '-';
        },
      },
      {
        headerName: this.translateService.instant(
          'receivables.table.lastUpdatedDate'
        ),
        headerValueGetter: () =>
          this.translateService.instant('receivables.table.lastUpdatedDate'),
        field: 'updatedDate',
        flex: 1,
        autoHeight: true,
        wrapText: true,
        cellRenderer: (params) => {
          return params?.value
            ? this.dateTimeHelper.convertUTCtoDisplayDate(params?.value, DateFormat.FullDate)
            : '-';
        },
      },
      {
        headerName: this.translateService.instant(
          'receivables.table.lastUpdatedBy'
        ),
        headerValueGetter: () =>
          this.translateService.instant('receivables.table.lastUpdatedBy'),
        field: 'updatedBy',
        flex: 1,
        wrapText: true,
        autoHeight: true,
        cellRenderer: (params) => {
          return params?.value?.name ? params?.value?.name : '-';
        },
      },
      {
        headerName: this.translateService.instant('receivables.table.action'),
        headerValueGetter: () =>
          this.translateService.instant('receivables.table.action'),
        field: 'status',
        flex: 1,
        wrapText: true,
        autoHeight: true,
        cellRenderer: CellActionReceivableClaimComponent,
        cellRendererParams: {
          onClick: (type: string, data: any) =>
            this.handleActionTable(type, data),
        },
      },
    ];
  }

  getColumnNotHaveDealer() {
    this.colDefs = [
      {
        headerName: '',
        headerCheckboxSelection: true,
        headerCheckboxSelectionFilteredOnly: true,
        checkboxSelection: true,
        field: 'select_all',
        sortable: false,
        wrapText: true,
        autoHeight: true,
        width: 48,
      },
      {
        headerName: this.translateService.instant('receivables.table.id'),
        headerValueGetter: () =>
          this.translateService.instant('receivables.table.id'),
        field: 'id',
        wrapText: true,
        autoHeight: true,
        flex: 1,
        cellRenderer: RouterLinkCellRendererComponent,
        cellRendererParams: {
          linkBuilder: (data: any) => `/claims/receivables/receivables-transactions/${data?.id}`,
          displayName: (params) => {
            return params?.id ? params?.id : '-';
          },
        }
      },
      {
        headerName: this.translateService.instant('receivables.table.claimId'),
        headerValueGetter: () =>
          this.translateService.instant('receivables.table.claimId'),
        field: 'claimId',
        flex: 1,
        autoHeaderHeight: true,
        wrapText: true,
        autoHeight: true,
        cellRenderer: (params) => {
          return params?.value ? params?.value : '-';
        },
      },
      {
        headerName: this.translateService.instant('receivables.table.status'),
        headerValueGetter: () =>
          this.translateService.instant('receivables.table.status'),
        field: 'status',
        flex: 1,
        wrapText: true,
        autoHeight: true,
        cellRenderer: (params) => {
          return params?.value?.name ? params?.value?.name : '-';
        },
      },
      {
        headerName: this.translateService.instant('receivables.table.type'),
        headerValueGetter: () =>
          this.translateService.instant('receivables.table.type'),
        field: 'type',
        flex: 1,
        wrapText: true,
        autoHeight: true,
        cellRenderer: (params) => {
          return params?.value?.name ? params?.value?.name : '-';
        },
      },
      {
        headerName: this.translateService.instant(
          'receivables.table.createdDate'
        ),
        headerValueGetter: () =>
          this.translateService.instant('receivables.table.createdDate'),
        field: 'createDate',
        flex: 1,
        autoHeight: true,
        wrapText: true,
        cellRenderer: (params) => {
          return params?.value
            ? this.dateTimeHelper.convertUTCtoDisplayDate(params?.value, DateFormat.FullDate)
            : '-';
        },
      },
      {
        headerName: this.translateService.instant(
          'receivables.table.lastUpdatedDate'
        ),
        headerValueGetter: () =>
          this.translateService.instant('receivables.table.lastUpdatedDate'),
        field: 'updatedDate',
        flex: 1,
        autoHeight: true,
        wrapText: true,
        cellRenderer: (params) => {
          return params?.value
            ? this.dateTimeHelper.convertUTCtoDisplayDate(params?.value, DateFormat.FullDate)
            : '-';
        },
      },
      {
        headerName: this.translateService.instant(
          'receivables.table.lastUpdatedBy'
        ),
        headerValueGetter: () =>
          this.translateService.instant('receivables.table.lastUpdatedBy'),
        field: 'updatedBy',
        flex: 1,
        wrapText: true,
        autoHeight: true,
        cellRenderer: (params) => {
          return params?.value?.name ? params?.value?.name : '-';
        },
      },
      {
        headerName: this.translateService.instant('receivables.table.action'),
        headerValueGetter: () =>
          this.translateService.instant('receivables.table.action'),
        field: 'status',
        flex: 1,
        wrapText: true,
        autoHeight: true,
        cellRenderer: CellActionReceivableClaimComponent,
        cellRendererParams: {
          onClick: (type: string, data: any) =>
            this.handleActionTable(type, data),
        },
      },
    ];
  }

  getColumnNotHaveDealerNotCheckbox() {
    this.colDefs = [
      {
        headerName: this.translateService.instant('receivables.table.id'),
        headerValueGetter: () =>
          this.translateService.instant('receivables.table.id'),
        field: 'id',
        wrapText: true,
        autoHeight: true,
        flex: 1,
        cellRenderer: RouterLinkCellRendererComponent,
        cellRendererParams: {
          linkBuilder: (data: any) => `/claims/receivables/receivables-transactions/${data?.id}`,
          displayName: (params) => {
            return params?.id ? params?.id : '-';
          },
        }
      },
      {
        headerName: this.translateService.instant('receivables.table.claimId'),
        headerValueGetter: () =>
          this.translateService.instant('receivables.table.claimId'),
        field: 'claimId',
        flex: 1,
        autoHeaderHeight: true,
        wrapText: true,
        autoHeight: true,
        cellRenderer: (params) => {
          return params?.value ? params?.value : '-';
        },
      },
      {
        headerName: this.translateService.instant('receivables.table.status'),
        headerValueGetter: () =>
          this.translateService.instant('receivables.table.status'),
        field: 'status',
        flex: 1,
        wrapText: true,
        autoHeight: true,
        cellRenderer: (params) => {
          return params?.value?.name ? params?.value?.name : '-';
        },
      },
      {
        headerName: this.translateService.instant('receivables.table.type'),
        headerValueGetter: () =>
          this.translateService.instant('receivables.table.type'),
        field: 'type',
        flex: 1,
        wrapText: true,
        autoHeight: true,
        cellRenderer: (params) => {
          return params?.value?.name ? params?.value?.name : '-';
        },
      },
      {
        headerName: this.translateService.instant(
          'receivables.table.createdDate'
        ),
        headerValueGetter: () =>
          this.translateService.instant('receivables.table.createdDate'),
        field: 'createDate',
        flex: 1,
        autoHeight: true,
        wrapText: true,
        cellRenderer: (params) => {
          return params?.value
            ? this.dateTimeHelper.convertUTCtoDisplayDate(params?.value, DateFormat.FullDate)
            : '-';
        },
      },
      {
        headerName: this.translateService.instant(
          'receivables.table.lastUpdatedDate'
        ),
        headerValueGetter: () =>
          this.translateService.instant('receivables.table.lastUpdatedDate'),
        field: 'updatedDate',
        flex: 1,
        autoHeight: true,
        wrapText: true,
        cellRenderer: (params) => {
          return params?.value
            ? this.dateTimeHelper.convertUTCtoDisplayDate(params?.value, DateFormat.FullDate)
            : '-';
        },
      },
      {
        headerName: this.translateService.instant(
          'receivables.table.lastUpdatedBy'
        ),
        headerValueGetter: () =>
          this.translateService.instant('receivables.table.lastUpdatedBy'),
        field: 'updatedBy',
        flex: 1,
        wrapText: true,
        autoHeight: true,
        cellRenderer: (params) => {
          return params?.value?.name ? params?.value?.name : '-';
        },
      },
      {
        headerName: this.translateService.instant('receivables.table.action'),
        headerValueGetter: () =>
          this.translateService.instant('receivables.table.action'),
        field: 'status',
        flex: 1,
        wrapText: true,
        autoHeight: true,
        cellRenderer: CellActionReceivableClaimComponent,
        cellRendererParams: {
          onClick: (type: string, data: any) =>
            this.handleActionTable(type, data),
        },
      },
    ];
  }
  handleActionTable(type: string, data: any) {
    switch (type) {
      case PromotionClaimAction.appeal:
        this.onAppealModal(data);
        break;
      case PromotionClaimAction.reject:
        this.openRejectModal(data);
        break;
      case PromotionClaimAction.approve:
        this.openApproveModal(data);
        break;
    }
  }

  openApproveModal(data) {
    const dialogRef = this.dialog.open(DialogConfirmComponent, {
      width: '530px',
      data: {
        claimCode: data?.id,
        title: this.translateService.instant(
          'receivables.popup.approveReceivable'
        ),
        icon: 'ic-warning',
        confirmMsg: this.translateService.instant(
          'receivables.popup.approveMsgReceivable'
        ),
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.approve'),
      },
    });
    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((result) => {
        if (result) {
          this.subscription.add(
            this.receivableService
              .approveReceivableTransaction(data?.id)
              .subscribe(
                (res) => {
                  if (res.code === '200') {
                    this.notificationService.showSuccess(
                      this.translateService.instant(
                        'receivables.message.approveTransactionSuccess'
                      )
                    );
                  }
                  this.getTransactionList();
                },
                (error) => {
                  handleErrors(error, this.notificationService);
                }
              )
          );
        }
      });
  }

  openRejectModal(data: any) {
    this.rejectForm.reset();
    const dialogRef = this.dialog.open(ModalReceivableRejectComponent, {
      width: '530px',
      data: {
        form: this.rejectForm,
        reasonList: this.reasonList,
        receivableCode: data?.id,
        mode: 'transaction',
        rejectReason: this.rejectForm.controls['rejectReason'],
        reason: this.rejectForm.controls['reason'],
        remarks: this.rejectForm.controls['remarks'],
        title: 'receivables.popup.rejectReceivable',
        cancelBtn: 'common.cancel',
        submitBtn: 'common.reject',
      },
    });
    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((result) => {
        if (result) {
          this.getTransactionList();
        }
      });
  }

  onAppealModal(data) {
    const dialogRef = this.dialog.open(DialogConfirmComponent, {
      width: '530px',
      data: {
        title: this.translateService.instant('receivables.appealClaim'),
        icon: 'ic-warning',
        confirmMsg: this.translateService.instant(
          'receivables.appealContent'
        ),
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.appeal'),
      },
      // autoFocus: true,
    });
    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((result) => {
        if (result?.action === ActionModal.Submit) {
          this.loadingService.showLoader();
          this.receivableService
            .appealReceivableTransaction(data?.id)
            .pipe(finalize(() => this.loadingService.hideLoader()))
            .subscribe(
              (res) => {
                if (res) {
                  if (res?.code === '200') {
                    this.notificationService.showSuccess('receivables.appealedClaimMessage');
                    this.getTransactionList();
                  } else {
                    this.notificationService.showError(res?.message);
                  }
                }
              },
              (error) => {
                handleErrors(error, this.notificationService);
              }
            );
        }
      });
    }
}
