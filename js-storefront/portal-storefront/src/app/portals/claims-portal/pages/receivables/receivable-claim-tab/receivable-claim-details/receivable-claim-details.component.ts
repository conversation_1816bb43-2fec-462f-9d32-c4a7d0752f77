import { CommonModule, CurrencyPipe, DatePipe } from '@angular/common';
import { Component, inject, OnInit, signal } from '@angular/core';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import {
  ScreenSizeService,
  LoadingService,
  NotificationService,
  DeviceService,
} from '../../../../../../core/services';
import { BreadcrumbWithLabelComponent } from '../../../../../../core/shared/breadcrumb-with-label/breadcrumb-with-label.component';
import { FileListComponent } from '../../../../features/file-list/file-list.component';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { downloadFile } from '../../../../../../core/helpers/function-common.helper';
import { Documents } from '../../../../../insurance-portal/interfaces';
import { PopupApprovePromotionClaimComponent } from '../../../promotions/popup-approve-promotion-claim/popup-approve-promotion-claim.component';
import { PopupRejectPromotionClaimComponent } from '../../../promotions/popup-reject-promotion-claim/popup-reject-promotion-claim.component';
import { filter, finalize, Subscription } from 'rxjs';
import { ReceivableService } from '../../../../services/receivable/receivable.service';
import { ReceivableClaimDetail } from '../../../../interfaces/receivable.interface';
import { PromotionClaimAction, PromotionClaimStatus } from '../../../../enum';
import { ROLES } from '../../../../../../core/constants/roles.const';
import { UserService } from '../../../../../../core/services/user';
import { OptionDropdown } from '../../../../../../core/interfaces';
import {
  ActionModal,
  DateFormat,
  ReceivableType,
  TimeZone,
} from '../../../../../../core/enums';
import { handleErrors } from '../../../../../../core/helpers';
import { DialogConfirmComponent } from '../../../../../../core/shared';
import { ModalReceivableApproveComponent } from '../../modal/modal-receivable-approve/modal-receivable-approve.component';
import { ModalReceivableRejectComponent } from '../../modal/modal-receivable-reject/modal-receivable-reject.component';
import { reject } from 'lodash-es';
import { UploadFileComponent } from '../../../../../insurance-portal/features/upload-file/upload-file.component';
import { ModalReferenceNumberComponent } from '../../../../features/modal/modal-reference-number/modal-reference-number.component';

@Component({
  selector: 'app-receivable-claim-details',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    TranslateModule,
    TranslateModule,
    BreadcrumbWithLabelComponent,
    MatExpansionModule,
    FileListComponent,
    UploadFileComponent
  ],
  providers: [
    ScreenSizeService,
    LoadingService,
    NotificationService,
    DeviceService,
    ReceivableService,
    UserService,
    CurrencyPipe,
    DatePipe,
  ],
  templateUrl: './receivable-claim-details.component.html',
  styleUrls: ['./receivable-claim-details.component.scss'],
})
export class ReceivableClaimDetailsComponent implements OnInit {
  dialog = inject(MatDialog);
  translateService = inject(TranslateService);
  notificationService = inject(NotificationService);
  loadingService = inject(LoadingService);
  deviceService = inject(DeviceService);
  route = inject(ActivatedRoute);
  router = inject(Router);
  userService = inject(UserService);
  receivableService = inject(ReceivableService);
  datePipe = inject(DatePipe);
  currencyPipe = inject(CurrencyPipe);

  subscription = new Subscription();

  currentRoles: any;
  currentRole: any;
  statusCode: string;
  PromotionClaimStatus = PromotionClaimStatus;
  ROLES = ROLES;
  PromotionClaimAction = PromotionClaimAction;
  reasonList: OptionDropdown[];
  rejectForm: FormGroup = new FormGroup({
    rejectReason: new FormControl('', [Validators.required]),
    reason: new FormControl(''),
    remarks: new FormControl(''),
  });
  approveForm = new FormGroup({
    glCode: new FormControl('', Validators.required),
    costCenter: new FormControl('', Validators.required),
    internalOrderNo: new FormControl('', Validators.required),
    profitCenter: new FormControl('', Validators.required),
  });

  receivableId: string;
  step = signal(0);
  dataDetail: ReceivableClaimDetail;
  receivables: any[] = [];
  attachments: Documents[] = [];

  ngOnInit(): void {
    this.currentRoles = JSON.parse(localStorage.getItem('roles'))?.roles || [];
    this.currentRole = this.userService.getGroupRole(this.currentRoles);
    const id = this.route.snapshot.paramMap.get('id');
    // this.keyName = this.translateService.instant(
    //   'receivables.details.detailsSection'
    // );
    if (id !== null) {
      this.receivableId = id;
      this.getDetail();
    } else {
      console.error('Device ID is null');
    }
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  setExpanded(index: number, check: boolean) {
    // this.transactionListExpandStatus[index].check = check
    this.step.set(index);
    if(!check) {
      this.step.set(-1)
    }
  }

  getDetail() {
    this.receivableService
      .getReceivableClaimDetail(this.receivableId)
      .subscribe((data: ReceivableClaimDetail) => {
        this.dataDetail = data;
        this.receivables = data?.receivables;
        this.statusCode = data?.status?.code;
        if(this.receivables && this.receivables.length > 0) {
          this.receivables = this.receivables.map((item) => {
            item.mapData = this.mapDataDetails(item);
            return item;
          });
        }
      });
  }

  mapDataDetails(data) {
    const {
      type: { code },
    } = data;
    let mapData = [];
    switch (code) {
      case ReceivableType.RETURNED_UNIT: {
        mapData = [
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.model'
            ),
            value: data?.detail?.model?.name || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.csNumber'
            ),
            value: data?.detail?.csNumber || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.amountOfUnit'
            ),
            value: data?.detail?.amountUnit ? this.currencyPipe.transform(
              data?.detail?.amountUnit?.value,
              data?.detail?.amountUnit?.currencyIso
            )
            : '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.invoiceDate'
            ),
            value: data?.detail?.invoiceDate
              ? this.datePipe.transform(
                  data?.detail?.invoiceDate,
                  DateFormat.ShortDate,
                  TimeZone.UTC8
                )
              : '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.reasonForCancellation'
            ),
            value: data?.detail?.reason || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.invoiceCancellationDate'
            ),
            value: data?.detail?.invoiceCancellationDate
              ? this.datePipe.transform(
                  data?.detail?.invoiceCancellationDate,
                  DateFormat.ShortDate,
                  TimeZone.UTC8
                )
              : '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.deallocationDate'
            ),
            value: data?.detail?.deallocationDate
              ? this.datePipe.transform(
                  data?.detail?.deallocationDate,
                  DateFormat.ShortDate,
                  TimeZone.UTC8
                )
              : '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.returnedDate'
            ),
            value: data?.detail?.returnedDate
              ? this.datePipe.transform(
                  data?.detail?.returnedDate,
                  DateFormat.ShortDate,
                  TimeZone.UTC8
                )
              : '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.receivedDate'
            ),
            value: data?.detail?.receivedDate
              ? this.datePipe.transform(
                  data?.detail?.receivedDate,
                  DateFormat.ShortDate,
                  TimeZone.UTC8
                )
              : '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.debitMemo'
            ),
            value: data?.detail?.debitMemo || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.csr'
            ),
            value: data?.detail?.csr || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.vehicleSalesInvoice'
            ),
            value: data?.detail?.vehicleSaleInvoice || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.vehicleDeliveryNote'
            ),
            value: data?.detail?.vehicleDeliveryNote || '-',
          },
        ];
        break;
      }
      case ReceivableType.EXTERNAL_AFTER_SALE_REBATE_PART_SERVICE: {
        mapData = [
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.repairOrderNo'
            ),
            value: data?.detail?.repairOrderNo || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.repairOrderDate'
            ),
            value: data?.detail?.repairOrderDate
              ? this.datePipe.transform(
                  data?.detail?.repairOrderDate,
                  DateFormat.ShortDate,
                  TimeZone.UTC8
                )
              : '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.customerName'
            ),
            value: data?.detail?.customer?.displayName || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.model'
            ),
            value: data?.detail?.model?.name || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.roAmount'
            ),
            value: data?.detail?.roAmountToReverse || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.serviceBillingNo'
            ),
            value: data?.detail?.serviceBillingNo || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.serviceBillingAmount'
            ),
            value:
              data?.detail?.serviceBillingAmountToReverse || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.debitMemoAmount'
            ),
            value: data?.detail?.debitMemoAmount ? this.currencyPipe.transform(
              data?.detail?.debitMemoAmount?.value,
              data?.detail?.debitMemoAmount?.currencyIso
            )
            : '-'
          },
        ];
        break;
      }
      case ReceivableType.EXTERNAL_AFTER_SALE_REBATE_FLEET_CLAIM: {
        mapData = [
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.repairOrderNo'
            ),
            value: data?.detail?.repairOrderNo || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.repairOrderDate'
            ),
            value: data?.detail?.repairOrderDate
              ? this.datePipe.transform(
                  data?.detail?.repairOrderDate,
                  DateFormat.ShortDate,
                  TimeZone.UTC8
                )
              : '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.customerName'
            ),
            value: data?.detail?.customer?.displayName || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.invoiceDate'
            ),
            value: data?.detail?.invoiceDate
              ? this.datePipe.transform(
                  data?.detail?.invoiceDate,
                  DateFormat.ShortDate,
                  TimeZone.UTC8
                )
              : '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.serviceBillingNo'
            ),
            value: data?.detail?.serviceBillingNo || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.serviceBillingAmount'
            ),
            value:
              data?.detail?.serviceBillingAmountToReverse || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.debitMemoAmount'
            ),
            value: data?.detail?.debitMemoAmount ? this.currencyPipe.transform(
              data?.detail?.debitMemoAmount?.value,
              data?.detail?.debitMemoAmount?.currencyIso
            )
            : '-'
          },
        ];
        break;
      }
      case ReceivableType.INTERNAL_AFTER_SALE_REBATE: {
        mapData = [
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.promoDescriptionParticulars'
            ),
            value: data?.detail?.promoDescription || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.debitMemoAmount'
            ),
            value: data?.detail?.debitMemoAmount ? this.currencyPipe.transform(
              data?.detail?.debitMemoAmount?.value,
              data?.detail?.debitMemoAmount?.currencyIso
            )
            : '-'
          },
        ];
        break;
      }

      default: {
        mapData = [
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.model'
            ),
            value: data?.detail?.model?.name || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.plateNumber'
            ),
            value: data?.detail?.plateNumber || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.csNumber'
            ),
            value: data?.detail?.csNumber || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.customerName'
            ),
            value: data?.detail?.customer?.displayName || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.dateOfClaimSubmissionByDealer'
            ),
            value: data?.detail?.submissionDate
              ? this.datePipe.transform(
                  data?.detail?.submissionDate,
                  DateFormat.ShortDate,
                  TimeZone.UTC8
                )
              : '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.dmReferenceNumber'
            ),
            value: data?.detail?.dmNumber || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.rfpReferenceNumber'
            ),
            value: data?.detail?.rfpNumber || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.descriptionOfRFP'
            ),
            value: data?.detail?.rfpDescription || '-',
          },
          {
            label: this.translateService.instant(
              'receivables.details.detailsSection.totalAmount'
            ),
            value: data?.detail?.totalAmount ? this.currencyPipe.transform(
              data?.detail?.totalAmount?.value,
              data?.detail?.totalAmount?.currencyIso
            )
            : '-'
          },
        ];
        break;
      }
    }
    return mapData;
  }

  onDownloadFile(event: Documents) {
    if (event.code) {
      downloadFile(event.realFileName, event.downloadUrl);
    } else {
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = event.downloadUrl;
      a.download = event.realFileName;
      document.body.appendChild(a);
      a.click();
      a.remove();
    }
  }

  removeTransactionDocument(item: any) {
      const dialogRef = this.dialog.open(DialogConfirmComponent, {
        width: '600px',
        data: {
          title: this.translateService.instant('promotionDetail.removeTransaction'),
          icon: 'ic-warning',
          confirmMsg: this.translateService.instant(
            'promotionDetail.removeTransactionContent'
          ),
          cancelBtn: this.translateService.instant('common.cancel'),
          submitBtn: this.translateService.instant('common.confirm'),
        },
          // autoFocus: true,
      });
  
      dialogRef.afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((result) => {
        if (result) {
          this.loadingService.showLoader();
          this.subscription.add(this.receivableService.removeRelateReceivable(this.receivableId, item?.id)
          .pipe(finalize(() => this.loadingService.hideLoader()))
          .subscribe(res => {
            if (res) {
              if (res?.code === '200') {
                this.notificationService.showSuccess(this.translateService.instant('receivables.removeTransaction'))
              } else {
                this.notificationService.showSuccess(res?.message)
              }
              this.getDetail();
            }
          }, err => {
            handleErrors(err, this.notificationService);
          }))
        }
      });
    }

  handleActionTable(type: string, data: any) {
    switch (type) {
      case PromotionClaimAction.reject:
        this.getListReason(data);
        break;

      case PromotionClaimAction.approve:
        this.openApproveModal(data);
        break;

      case PromotionClaimAction.appeal:
        this.onAppealModal(data);
        break;
    }
  }

  getListReason(data) {
    this.loadingService.showLoader();
    this.subscription.add(
      this.receivableService
        .getReceivableClaimMetaData()
        .pipe(finalize(() => this.loadingService.hideLoader()))
        .subscribe(
          (response) => {
            this.reasonList = response?.enums?.claimRejectReason;
            this.openRejectModal(data);
          },
          (err) => {
            this.openRejectModal(data);
          }
        )
    );
  }

  openRejectModal(data) {
    this.rejectForm.reset();
    const dialogRef = this.dialog.open(ModalReceivableRejectComponent, {
      width: '530px',
      data: {
        form: this.rejectForm,
        reasonList: this.reasonList,
        claimCode: data?.id,
        mode: 'claim',
        rejectReason: this.rejectForm.controls['rejectReason'],
        reason: this.rejectForm.controls['reason'],
        remarks: this.rejectForm.controls['remarks'],
        title: 'receivables.popup.rejectClaim',
        cancelBtn: 'common.cancel',
        submitBtn: 'common.reject',
      },
    });
    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((result) => {
        if (result) {
          this.getDetail();
        }
      });
  }

  openApproveModal(data) {
    this.approveForm.reset();
    const dialogRef = this.dialog.open(ModalReceivableApproveComponent, {
      width: '850px',
      data: {
        form: this.approveForm,
        claimCode: data?.id,
        glCode: this.approveForm.controls['glCode'],
        costCenter: this.approveForm.controls['costCenter'],
        internalOrderNo: this.approveForm.controls['internalOrderNo'],
        profitCenter: this.approveForm.controls['profitCenter'],
        title: 'receivables.popup.approveClaim',
        cancelBtn: 'common.cancel',
        submitBtn: 'common.approve',
      },
    });
    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((result) => {
        if (result) {
          this.getDetail();
        }
      });
  }

  onAppealModal(data) {
    const dialogRef = this.dialog.open(DialogConfirmComponent, {
      width: '530px',
      data: {
        title: this.translateService.instant('promotionDetail.appealClaim'),
        icon: 'ic-warning',
        confirmMsg: this.translateService.instant(
          'promotionDetail.appealContent'
        ),
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.appeal'),
      },
      // autoFocus: true,
    });
    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe(async (result) => {
        if (result?.action === ActionModal.Submit) {
          let attachment: any;
          if (this.attachments && this.attachments.length) {
            attachment = await this.handleFileUpload(this.attachments);
          }
          const dataSend = {
            referenceNumber: this.dataDetail?.referenceNumber,
            docs: attachment?.docs,
            docsName:  attachment?.docNames
          }
          this.loadingService.showLoader();
          this.receivableService
            .appealReceivableClaim(dataSend, data?.id)
            .pipe(finalize(() => this.loadingService.hideLoader()))
            .subscribe(
              (res) => {
                if (res) {
                  if (res?.code === '200') {
                    this.notificationService.showSuccess(res?.message);
                    this.getDetail();
                  } else {
                    this.notificationService.showError(res?.message);
                  }
                }
              },
              (error) => {
                handleErrors(error, this.notificationService);
              }
            );
        }
      });
  }

  checkLimitFiles(event: any) {
    let invalid: boolean = false;
    if (event && event.length > 0) {
      for (let index = 0; index < event.length; index++) {
        const element = event[index];
        if (element.size > 5 * 1024 * 1024) {
          invalid = true;
          return invalid;
        }
      }
    }
    return invalid;
  }

  onFileSelected(e: any) {
    const invalidLimit: boolean = this.checkLimitFiles(e);
    if (invalidLimit) {
      this.notificationService.showError(this.translateService.instant('uploadFile.invalidLimit5MB'));
      return;
    }
    const mapFiles = [];
    const mapAttachment = []
    Array.from(e).forEach((item: any) => {
      const temp = {
        creationTime: new Date(),
        realFileName: item?.name,
        downloadUrl: URL.createObjectURL(item)
      }
      mapFiles.push(temp);
      mapAttachment.push(item);
    })
    if (!this.dataDetail?.documents) {
      this.dataDetail.documents = [];
    }

    this.dataDetail.documents = [...this.dataDetail.documents, ...mapFiles];
    this.attachments = [...this.attachments, ...mapFiles];
  }

  removeFile(index) {
    this.removeAttachedDocument(index);
  }

  removeAttachedDocument(index: number) {
    const dialogRef = this.dialog.open(PopupApprovePromotionClaimComponent, {
      width: '600px',
      data: {
        title: this.translateService.instant('promotionDetail.removeAttachmentDocument'),
        content: this.translateService.instant('promotionDetail.removeAttachmentDocumentContent'),
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.confirm'),
        isRemove: true
      },
        // autoFocus: true,
    });

    dialogRef.afterClosed()
    .pipe(filter((result) => result?.action === ActionModal.Submit))
    .subscribe((result) => {
      if (result) {
        const find = this.dataDetail.documents.find((item, i) => {
          return i === index;
        })
        this.attachments = this.attachments.filter(item => item.creationTime !== find.creationTime);
        this.dataDetail.documents.splice(index, 1);
        this.notificationService.showSuccess('receivables.attachmentRemoveSuccess');
      }
    })
  }

  readFileAsync(file) {
    return new Promise(async(resolve, reject) => {
      const response = await fetch(file);
      const blob = await response.blob();
      const reader: any = new FileReader();
      reader.readAsDataURL(blob);
      reader.onload = () => resolve(reader.result.split(',')[1]);
    });
  }

  async handleFileUpload(attachments) {
    try {
      const tempAttachment = attachments;
      let params: any;
      let tempFiles: string[] = [];
      let tempFileName: string[] = [];

      for (const file of tempAttachment) {
        const content: any = await this.readFileAsync(file?.downloadUrl);
        tempFiles.push(content);
        tempFileName.push(file?.realFileName);
      }
      params = {
        docs: tempFiles,
        docNames: tempFileName,
      };
      return params;
    } catch (err) {
      console.log(err);
      return {};
    }
  }

  onEditDetails(referenceNumber: string) {
    const dialogRef = this.dialog.open(ModalReferenceNumberComponent, {
        width: '900px',
        data: {
          claimId: this.receivableId,
          referenceNumber: referenceNumber ?? '',
          title: 'receivables.popup.updateDetails',
          cancelBtn: 'common.cancel',
          submitBtn: 'common.update',
        },
      });
    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((result) => {
        if (result) {
          this.getDetail();
        }
      });
  }
}
