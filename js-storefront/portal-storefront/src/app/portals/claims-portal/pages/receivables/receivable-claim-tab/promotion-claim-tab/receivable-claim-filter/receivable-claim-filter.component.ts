import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { OptionDropdown } from '../../../../../../../core/interfaces';
import { ScreenSizeService } from '../../../../../../../core/services';
import { FormGroupComponent, DropdownFormGroupComponent } from '../../../../../../../core/shared';

@Component({
  selector: 'app-receivable-claim-filter',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatIconModule,
    TranslateModule,
    FormGroupComponent,
    DropdownFormGroupComponent,
  ],
  providers: [ScreenSizeService],
  standalone: true,
  templateUrl: './receivable-claim-filter.component.html',
  styleUrls: ['./receivable-claim-filter.component.scss'],
})
export class ReceivableClaimFilterComponent  {
  @Input() form: FormGroup;
  @Input() isDisplayDealer: boolean = false;
  @Input() statusList: OptionDropdown[];
  @Input() dealerList: OptionDropdown[];

  @Output() search = new EventEmitter();

  get id() {
    return this.form.get('id') as FormControl;
  }

  get status() {
    return this.form.get('status') as FormControl;
  }
}
