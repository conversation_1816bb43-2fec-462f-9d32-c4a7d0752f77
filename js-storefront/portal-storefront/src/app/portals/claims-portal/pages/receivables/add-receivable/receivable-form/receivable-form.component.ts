import { ChangeDetectorRef, Component, EventEmitter, inject, Input, OnInit, Output, SimpleChanges } from '@angular/core';
import { ReceivableService } from '../../../../services/receivable/receivable.service';
import { ReceivableType } from '../../../../../../core/enums';
import { ReactiveFormsModule, Validators } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import { IconModule } from '../../../../../../core/icon/icon.module';
import { DropdownFormGroupComponent, DateFormGroupComponent } from '../../../../../../core/shared';
import { UploadFileComponent } from '../../../../features/upload-file/upload-file.component';
import { CommonModule, DatePipe } from '@angular/common';
import { Router } from '@angular/router';
import { OptionDropdown, PagingInfo } from '../../../../../../core/interfaces';
import { CustomAutocompleteInputComponent } from '../../../../../../core/shared/custom-autocomplete-input/custom-autocomplete-input.component';
import { FileListComponent } from '../../../../features/file-list/file-list.component';
import { Documents } from '../../../../../insurance-portal/interfaces';
import { ReceivableClaimDetail } from '../../../../interfaces/receivable.interface';
import { DateTimeHelper } from '../../../../../../core/helpers/date-time.helper';

@Component({
  selector: 'app-receivable-form',
  standalone: true,
  imports: [
    CommonModule,
    DropdownFormGroupComponent,
    TranslateModule,
    CustomAutocompleteInputComponent,
    IconModule,
    ReactiveFormsModule,
    MatInputModule,
    DateFormGroupComponent,
    UploadFileComponent,
    FileListComponent
  ],
  providers: [
    ReceivableService,
    DateTimeHelper,
    DatePipe
  ],
  templateUrl: './receivable-form.component.html',
  styleUrl: './receivable-form.component.scss'
})
export class ReceivableFormComponent implements OnInit {
  receivableService = inject(ReceivableService);
  router = inject(Router);
  cd = inject(ChangeDetectorRef)
  selectedType: string = '';

  @Output() handleCancel = new EventEmitter();
  @Output() handleSubmit = new EventEmitter();

  @Input() isEdit: boolean = false;
  @Input() files: any;
  @Input() dataDetail: ReceivableClaimDetail;

  typeOption = [];
  modelOption = [];
  reasonOption = [];
  customerOption = [];

  isLoadingModel = false;
  isLoadingCustomer = false;
  isFinalModelData = false;
  isFinalCustomerData = false;
  txtSearchModel: string = '';
  txtSearchCustomer: string = '';
  pagingModelInfo: PagingInfo = {
    totalItems: 0,
    currentPage: 0,
    numberOfPage: 10,
  };
  pagingCustomerInfo: PagingInfo = {
    totalItems: 0,
    currentPage: 0,
    numberOfPage: 10,
  };
  subscriptionModel: any;
  subscriptionCustomer: any;

  form = this.receivableService.form;
  selectedfiles: Documents[] = [];
  ReceivableType = ReceivableType;
  dateTimeHelper = inject(DateTimeHelper);

  ngOnChanges(changes: SimpleChanges): void {
    if(changes['dataDetail'] && changes['dataDetail']?.currentValue) {
      this.initForm(this.dataDetail?.type?.code);
      if (this.dataDetail?.detail?.customer && this.dataDetail?.detail?.customer?.uid) {
        this.onCustomerSearch(this.dataDetail?.detail?.customer?.displayName);
      } else {
        this.getCustomerData();
      }
      if (this.dataDetail?.detail?.model && this.dataDetail?.detail?.model?.code) {
        this.onModelSearch(this.dataDetail?.detail?.model?.name);
      } else {
        this.getModelData();
      }
      const dataFill = this.fillDataToForm(this.dataDetail?.type, this.dataDetail?.detail);
      this.form.patchValue(dataFill);
    }
  }

  ngOnInit(): void {
    this.receivableService.getReceivableMetaData().subscribe(
      (res: any) => {
        this.typeOption = res?.enums?.receivableType;
        this.reasonOption = res?.cancelReasons;
      }
    )
    if (!this.isEdit) {
      this.getCustomerData();
      this.getModelData();
    }
  }

  onChangeType(e) {
    this.initForm(e.value);
  }

  initForm(value: string) {
    this.selectedType = value;
    this.form.reset();
    this.form.get('type')?.setValue(value);
    this.selectedfiles = [];
    switch (value) {
      case ReceivableType.RETURNED_UNIT:
      case ReceivableType.GOODWILL_FREE_PERIODIC:
      case ReceivableType.GOODWILL_UNIT_REPLACEMENT:
      case ReceivableType.INTERNAL_AFTER_SALE_REBATE:
      case ReceivableType.RENTAL_CAR:
        this.removeValidator('serviceBillingAmount');

        break;

      case ReceivableType.EXTERNAL_AFTER_SALE_REBATE_PART_SERVICE:
      case ReceivableType.EXTERNAL_AFTER_SALE_REBATE_FLEET_CLAIM:
        this.setValidator('serviceBillingAmount');

        break;

      default:
        break;
    }
  }

  removeValidator(field) {
    this.form.get(field).clearValidators();
    this.form.get(field).updateValueAndValidity();
  }

  setValidator(field) {
    this.form.get(field).setValidators(Validators.required);
    this.form.get(field).updateValueAndValidity();
  }

  onCancel() {
    if (this.isEdit) {
      this.handleCancel.emit();
    }
    else this.router.navigate(['claims/receivables']);
  }

  onSubmit() {
    this.handleSubmit.emit(this.form);
  }

  onFileSelected(e: any) {
    const mapFiles = [];
    const mapAttachment = []
    Array.from(e).forEach((item: any) => {
      const temp = {
        creationTime: new Date(),
        realFileName: item?.name,
        downloadUrl: URL.createObjectURL(item)
      }
      mapFiles.push(temp);
      mapAttachment.push(item);
    })

    this.selectedfiles = [...this.selectedfiles, ...mapFiles];;
    this.form.controls['attachment'].patchValue([...this.selectedfiles]);
  }

  removeFile(index) {
    const tempAttachment = this.form.controls['attachment'].value;
    tempAttachment.splice(index, 1);
    this.form.controls['attachment'].patchValue(tempAttachment);
    this.selectedfiles.splice(index, 1);
  }

  onDownloadFile(event: Documents) {
    const a = document.createElement('a');
    a.style.display = "none";
    a.href = event.downloadUrl;
    a.download = event.realFileName;
    document.body.appendChild(a);
    a.click();
    a.remove()
  }

  onModelSearch(value) {
    this.isFinalModelData = false;
    this.pagingModelInfo.currentPage = 0;
    this.modelOption = [];
    this.txtSearchModel = value;
    this.getModelData(value);
  }

  onCustomerSearch(value) {
    this.isFinalCustomerData = false;
    this.pagingCustomerInfo.currentPage = 0;
    this.customerOption = [];
    this.txtSearchCustomer = value;
    this.getCustomerData(value);
  }

  onScrollModel() {
    if (!this.isFinalModelData) {
      this.getModelData(this.txtSearchModel);
    }
  }

  onScrollCustomer() {
    if (!this.isFinalCustomerData) {
      this.getModelData(this.txtSearchCustomer);
    }
  }

  getModelData(value: string = '') {
    this.subscriptionModel && this.subscriptionModel.unsubscribe();
    const body = {
      page: this.pagingModelInfo.currentPage,
      text: value
    }
    this.isLoadingModel = true;
    this.subscriptionModel = this.receivableService.getReceivableModelData(body).subscribe(res => {
      if (res) {
        this.pagingModelInfo.currentPage += 1;
        this.modelOption = [...this.modelOption, ...res];
        this.isLoadingModel = false;
        if (res.length === 0) {
          this.isFinalModelData = true;
        }

        if (this.modelOption && this.modelOption.length < 1) {
          this.form.controls['model'].patchValue('');
        }
      }
    }, err => {
      this.isLoadingModel = false;
    })
  }

  getCustomerData(value: string = '') {
    this.subscriptionCustomer && this.subscriptionCustomer.unsubscribe();
    const body = {
      page: this.pagingCustomerInfo.currentPage,
      text: value
    }
    this.isLoadingCustomer = true;
    this.subscriptionCustomer = this.receivableService.getReceivableCustomerData(body).subscribe((res: OptionDropdown[]) => {
      if (res) {
        this.pagingCustomerInfo.currentPage += 1;
        this.customerOption = [...this.customerOption, ...res];
        this.isLoadingCustomer = false;
        if (res.length === 0) {
          this.isFinalCustomerData = true;
        }
        if (this.customerOption && this.customerOption.length < 1) {
          this.form.controls['customer'].patchValue('');
        }
      }
    }, err => {
      this.isLoadingCustomer = false;
    })
  }

  convertDate(date: string) {
    return date ? new Date(this.dateTimeHelper.convertUTCtoDisplayDate(date)) : null;
  }

  fillDataToForm(type: any, data) {
    let body: any;
    switch (type?.code) {
      case ReceivableType.RENTAL_CAR:
      case ReceivableType.GOODWILL_FREE_PERIODIC:
      case ReceivableType.GOODWILL_UNIT_REPLACEMENT:
        body = {
          type: type?.code,
          customer: data?.customer?.uid || '',
          searchCustomer: data?.searchCustomer || '',
          csNumber: data?.csNumber || '',
          plateNumber: data?.plateNumber || '',
          model: data?.model?.code || '',
          searchModel: data?.searchModel || '',
          dateOfClaimSubmissionByDealer: this.convertDate(data?.submissionDate),
          DMReferenceNumber: data?.dmNumber || '',
          RFPReferenceNumber: data?.rfpNumber || '',
          descriptionOfRFP: data?.rfpDescription || '',
          amount: data?.totalAmountToReverse || '',
        };
        break;

      case ReceivableType.RETURNED_UNIT:
        body = {
          type: type?.code,
          customer: data?.customer?.uid || '',
          searchCustomer: data?.searchCustomer || '',
          csNumber: data?.csNumber || '',
          amount: data?.amountUnitToReverse || '',
          model: data?.model?.code || '',
          searchModel: data?.searchModel || '',
          invoiceDate: this.convertDate(data?.invoiceDate),
          receivedDate: this.convertDate(data?.receivedDate),
          invoiceCancellationDate: this.convertDate(data?.invoiceCancellationDate),
          deallocationDate: this.convertDate(data?.deallocationDate),
          returnedDate: this.convertDate(data?.returnedDate),
          reason: data?.reasonCode || '',
          vehicleSalesInvoice: data?.vehicleSaleInvoice || '',
          vehicleDeliveryNote: data?.vehicleDeliveryNote || '',
          debitMemo: data?.debitMemo || '',
          csr: data?.csr || '',
        };
        break;

      case ReceivableType.INTERNAL_AFTER_SALE_REBATE:
        body = {
          type: type?.code,
          amount: data.debitMemoAmountToReserve || '',
          promoDescription: data?.promoDescription || '',
        };
        break;

      case ReceivableType.EXTERNAL_AFTER_SALE_REBATE_PART_SERVICE:
        body = {
          type: type?.code,
          repairOrderNumber: data?.repairOrderNo || '',
          repairOrderDate: this.convertDate(data?.repairOrderDate),
          customer: data?.customer?.uid || '',
          searchCustomer: data?.searchCustomer || '',
          serviceBillingNumber: data?.serviceBillingNo || '',
          serviceBillingAmount: data?.serviceBillingAmountToReverse || '',
          amount: data?.debitMemoAmountToReverse  || '',
          model: data?.model?.code || '',
          searchModel: data?.searchModel || '',
          roAmount: data?.roAmountToReverse || '',
        };
        break;

      case ReceivableType.EXTERNAL_AFTER_SALE_REBATE_FLEET_CLAIM:
        body = {
          type: type?.code,
          repairOrderNumber: data?.repairOrderNo || '',
          repairOrderDate: this.convertDate(data?.repairOrderDate),
          customer: data?.customer?.uid || '',
          searchCustomer: data?.searchCustomer || '',
          invoiceDate: this.convertDate(data?.invoiceDate),
          serviceBillingNumber: data?.serviceBillingNo || '',
          serviceBillingAmount: data?.serviceBillingAmountToReverse || '',
          amount: data?.debitMemoAmountToReverse || '',
        };
        break;
      }
      return body;
  }

  onChangeReason(e) {
    const find = this.reasonOption?.find(item => item.code === e?.value);
    this.form.controls['reasonName'].patchValue(find?.name || '');
  }
}
