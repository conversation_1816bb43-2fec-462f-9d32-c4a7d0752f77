import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import { BreadcrumbWithLabelComponent } from '../../../../../core/shared/breadcrumb-with-label/breadcrumb-with-label.component';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { IconModule } from '../../../../../core/icon/icon.module';
import { ReceivableService } from '../../../services/receivable/receivable.service';
import { ReceivableFormComponent } from './receivable-form/receivable-form.component';
import {
  LoadingService,
  NotificationService,
} from '../../../../../core/services';
import { ReceivableType } from '../../../../../core/enums';
import { FormGroup } from '@angular/forms';
import { handleErrors } from '../../../../../core/helpers';
import moment from 'moment';
import { Router } from '@angular/router';
import { L } from '@angular/cdk/keycodes';
@Component({
  selector: 'app-add-receivable',
  standalone: true,
  imports: [
    CommonModule,
    BreadcrumbWithLabelComponent,
    TranslateModule,
    IconModule,
    ReceivableFormComponent,
  ],
  providers: [ReceivableService, NotificationService],
  templateUrl: './add-receivable.component.html',
  styleUrl: './add-receivable.component.scss',
})
export class AddReceivableComponent {
  receivableService = inject(ReceivableService);
  router = inject(Router);
  loadingService = inject(LoadingService);
  translateService = inject(TranslateService);
  notificationService = inject(NotificationService);

  form: FormGroup;

  onSubmit(e) {
    this.form = e;
    this.handleAPI(this.form.value.type);
  }

  async handleAPI(type: string) {
    const data = this.form.value;
    let attachment: any;
    if (data.attachment) {
      attachment = await this.handleFileUpload(data.attachment);
    }
    let body: any = {};
    switch (type) {
      case ReceivableType.RENTAL_CAR:
      case ReceivableType.GOODWILL_FREE_PERIODIC:
      case ReceivableType.GOODWILL_UNIT_REPLACEMENT:
        body = {
          receivableType: data.type || '',
          dealer: '',
          actionSuffix: 'create',
          docs: attachment?.docs || [],
          docsName: attachment?.docNames || [],
          status: '',
          dealerName: '',
          customerUid: data?.customer || '',
          csNumber: data?.csNumber || '',
          plateNumber: data?.plateNumber || '',
          productCode: data?.model || '',
          submissionDate: data?.dateOfClaimSubmissionByDealer
            ? moment(data?.dateOfClaimSubmissionByDealer).format()
            : '',
          dmNumber: data?.DMReferenceNumber || '',
          rfpNumber: data?.RFPReferenceNumber || '',
          rfpDescription: data?.descriptionOfRFP || '',
          totalAmount: data?.amount || '',
        };
        this.addReceivable(body, type);
        break;

      case ReceivableType.RETURNED_UNIT:
        body = {
          receivableType: data.type || '',
          dealer: '',
          actionSuffix: 'create',
          docs: attachment?.docs || [],
          docsName: attachment?.docNames || [],
          status: '',
          dealerName: '',
          customerUid: data?.customer || '',
          csNumber: data?.csNumber || '',
          amountUnit: data?.amount || '',
          productCode: data?.model || '',
          invoiceDate: data?.invoiceDate
            ? moment(data?.invoiceDate).format()
            : '',
          receivedDate: data?.receivedDate
            ? moment(data?.receivedDate).format()
            : '',
          invoiceCancellationDate: data?.invoiceCancellationDate
            ? moment(data?.invoiceCancellationDate).format()
            : '',
          deallocationDate: data?.deallocationDate
            ? moment(data?.deallocationDate).format()
            : '',
          returnedDate: data?.returnedDate
            ? moment(data?.returnedDate).format()
            : '',
          reason: data?.reason || '',
          vehicleSaleInvoice: data?.vehicleSalesInvoice || '',
          vehicleDeliveryNote: data?.vehicleDeliveryNote || '',
          debitMemo: data?.debitMemo || '',
          csr: data?.csr || '',
        };
        this.addReceivable(body, type);
        break;

      case ReceivableType.INTERNAL_AFTER_SALE_REBATE:
        body = {
          receivableType: data.type || '',
          dealer: '',
          actionSuffix: 'create',
          docs: attachment?.docs || [],
          docsName: attachment?.docNames || [],
          status: '',
          dealerName: '',
          debitMemoAmount: data.amount || '',
          promoDescription: data.promoDescription || '',
        };
        this.addReceivable(body, type);
        break;

      case ReceivableType.EXTERNAL_AFTER_SALE_REBATE_PART_SERVICE:
        body = {
          receivableType: data?.type || '',
          dealer: '',
          actionSuffix: 'create',
          docs: attachment?.docs || [],
          docsName: attachment?.docNames || [],
          status: '',
          dealerName: '',
          repairOrderNo: data?.repairOrderNumber || '',
          repairOrderDate: data?.repairOrderDate
            ? moment(data?.repairOrderDate).format()
            : '',
          customerUid: data?.customer || '',
          serviceBillingNo: data?.serviceBillingNumber || '',
          serviceBillingAmount: data?.serviceBillingAmount || '',
          debitMemoAmount: data?.amount || '',
          productCode: data?.model || '',
          roAmount: data?.roAmount || '',
        };
        this.addReceivable(body, type);
        break;

      case ReceivableType.EXTERNAL_AFTER_SALE_REBATE_FLEET_CLAIM:
        body = {
          receivableType: data?.type || '',
          dealer: '',
          actionSuffix: 'create',
          docs: attachment?.docs || [],
          docsName: attachment?.docNames || [],
          status: '',
          dealerName: '',
          repairOrderNo: data?.repairOrderNumber || '',
          repairOrderDate: data?.repairOrderDate
            ? moment(data?.repairOrderDate).format()
            : '',
          customerUid: data?.customer || '',
          invoiceDate: data?.invoiceDate
            ? moment(data?.invoiceDate).format()
            : '',
          serviceBillingNo: data?.serviceBillingNumber || '',
          serviceBillingAmount: data?.serviceBillingAmount || '',
          debitMemoAmount: data?.amount || '',
        };
        this.addReceivable(body, type);
        break;
    }
    return body;
  }

  readFileAsync(file) {
    return new Promise(async(resolve, reject) => {
      const response = await fetch(file);
      const blob = await response.blob();
      const reader: any = new FileReader();
      reader.readAsDataURL(blob);
      reader.onload = () => resolve(reader.result.split(',')[1]);
    });
  }

  async handleFileUpload(attachments) {
    try {
      const tempAttachment = attachments;
      let params: any;
      let tempFiles: string[] = [];
      let tempFileName: string[] = [];

      for (const file of tempAttachment) {
        const content: any = await this.readFileAsync(file?.downloadUrl);
        tempFiles.push(content);
        tempFileName.push(file?.realFileName);
      }
      params = {
        docs: tempFiles,
        docNames: tempFileName,
      };
      return params;
    } catch (err) {
      console.log(err);
      return {};
    }
  }

  addReceivable(body, type) {
    this.loadingService.showLoader();
    this.receivableService.addReceivable(body, type).subscribe(
      (res) => {
        this.notificationService.showSuccess(
          this.translateService.instant('New Receivable created')
        );
        this.router.navigate(['/claims/receivables']);
        this.loadingService.hideLoader();
      },
      (err) => {
        handleErrors(err, this.notificationService);
        this.loadingService.hideLoader();
      }
    );
  }
}
