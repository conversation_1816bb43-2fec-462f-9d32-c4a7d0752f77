<div class="promotion-details container-details">
  <div class="container-details__header p-b-0">
    <app-breadcrumb-with-label
      [breadcrumbs]="['receivables.titleClaim', 'receivables.details.titleClaim']"
      [breadcrumbLinks]="[
        '/claims/receivables',
        '/receivables-claim/' + receivableId
      ]"
      [label]="receivableId"
    >
    </app-breadcrumb-with-label>
    @if (currentRole === ROLES.MARKETINGGROUP && (statusCode ===
    PromotionClaimStatus.INITIAL_VALIDATION || statusCode ===
    PromotionClaimStatus.VALIDATION_APPEAL)) {
    <div class="container-details__actions">
      <button class="btn-third" (click)="handleActionTable(PromotionClaimAction.reject, dataDetail)">
        {{ "promotionDetail.reject" | translate }}
      </button>
      <button class="btn-primary" (click)="handleActionTable(PromotionClaimAction.approve, dataDetail)">
        {{ "promotionDetail.approve" | translate }}
      </button>
    </div>
    } @if (currentRole === ROLES.CASHIERGROUP && (statusCode ===
    PromotionClaimStatus.REJECTED)) {
    <div class="container-details__actions">
      <button
        class="btn-primary"
        (click)="handleActionTable(PromotionClaimAction.appeal, dataDetail)"
      >
        {{ "promotionDetail.appeal" | translate }}
      </button>
    </div>
    }
  </div>
  <div class="container-details__body">
    <div class="container-details__body__promotion">
      <div class="top-detail-section">
        <div class="details">
          <div class="details-section-header">
            <div class="section-header">
              <mat-icon
                svgIcon="ic-bp-est-infor"
                class="medium-icon section-header--icon"
              ></mat-icon>
              <div class="section-header--content">
                <span>{{ "receivables.details.briefInfo" | translate }}</span>
              </div>
            </div>
            @if(currentRole === ROLES.CASHIERGROUP && statusCode === PromotionClaimStatus.REJECTED) {
                <button class="btn-link" (click)="onEditDetails(dataDetail?.referenceNumber)">
                    <mat-icon svgIcon="ic-edit" class="small-icon" aria-hidden="true"></mat-icon>
                    {{ "common.edit" | translate }}
                </button>
            }
          </div>
         
          <div class="section-body">
            <div class="detail-column-left">
              <div class="detail-item">
                <div>
                  <span class="title">
                    {{ "receivables.claimDetails.status" | translate }}
                  </span>
                </div>
                <div>
                  <span class="content">
                    {{ dataDetail?.status?.name || "-" }}
                  </span>
                </div>
              </div>
              <div class="detail-item">
                <div>
                  <span class="title">
                    {{ "receivables.claimDetails.orArNumber" | translate }}
                  </span>
                </div>
                <div>
                  <span class="content">
                    {{ dataDetail?.orNumber || "-" }}
                  </span>
                </div>
              </div>

              <div class="detail-item">
                <div>
                  <span class="title">
                    {{ "receivables.briefInfo.rejectReason" | translate }}
                  </span>
                </div>
                <div>
                  <span class="content">
                    {{ dataDetail?.rejectReason || "-" }}
                  </span>
                </div>
              </div>
            </div>
            <div class="detail-column-right">
              <div class="detail-item">
                <div>
                  <span class="title">
                    {{ "receivables.claimDetails.referenceNumber" | translate }}
                  </span>
                </div>
                <div>
                  <span class="content">
                    {{ dataDetail?.referenceNumber || "-" }}
                  </span>
                </div>
              </div>
              <div class="detail-item">
                <div>
                  <span class="title">
                    {{ "receivables.claimDetails.claimableAmount" | translate }}
                  </span>
                </div>
                <div>
                  <span class="content">
                    {{
                      (dataDetail?.claimableAmount?.value
                        | currency
                          : dataDetail?.claimableAmount
                              ?.currencyIso) || "-"
                    }}
                  </span>
                </div>
              </div>

              <div class="detail-item">
                <div>
                  <span class="title">
                    {{ "receivables.briefInfo.remarks" | translate }}
                  </span>
                </div>
                <div>
                  <span class="content">
                    {{ dataDetail?.remarks || "-" }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="attached-document">
          <div class="section-header">
            <mat-icon
              svgIcon="ic-document"
              class="medium-icon section-header--icon"
            ></mat-icon>
            <div class="section-header--content">
              <span>{{ "promotionDetail.attachedDocuments" | translate }}</span>
            </div>
          </div>
          <div class="section-body">
            <div class="section-body--content">
              @if (currentRole === ROLES.CASHIERGROUP && (statusCode === PromotionClaimStatus.REJECTED)) {
                <div class="section-body-upload-file">
                  <app-upload-file
                  [multiple]="true"
                  [placeholder]="'uploadFile.selectFile' | translate"
                  [isLabelHidden]="true"
                  (filesSelected)="onFileSelected($event)"
                  ></app-upload-file>
                </div>
              }
              @if(dataDetail && dataDetail?.documents && dataDetail?.documents?.length > 0) {
                <div class="attached-document-list">
                  <app-file-list
                    [fileList]="dataDetail?.documents"
                    [isHiddenRemove]="true"
                    [checkCode]="true"
                    (handleDeleteBasedOnIndex)="removeFile($event)"
                    (handleDownload)="onDownloadFile($event)"
                  ></app-file-list>
                </div>
                } @else {
                  @if (!(currentRole === ROLES.CASHIERGROUP && (statusCode === PromotionClaimStatus.REJECTED))) {
                  <span>{{
                    "receivables.details.noAttachedDocs" | translate
                  }}</span>
                  }
              }
            </div>
          </div>
        </div>
      </div>
      <div class="bottom-detail-section">
        <div class="details">
          <div class="section-header--detail-section">
            <div class="section-header--detail-section__left-title">
              <mat-icon
                svgIcon="ic-promotion"
                class="medium-icon section-header--icon"
              ></mat-icon>
              <div class="section-header--content">
                <span>{{
                  "receivables.claimDetails.relatedReceivables" | translate
                }} {{receivables && receivables.length ? '(' + receivables.length + ')' : ''}}</span>
              </div>
            </div>
          </div>
          <div class="section-body">
            @if(receivables && receivables.length > 0) {
                <mat-accordion class="example-headers-align">
                    @for (item of receivables; track $index) {
                        <mat-expansion-panel 
                            [expanded]="step() === $index" 
                            (opened)="setExpanded($index, true)" 

                            hideToggle
                        >
                            <mat-expansion-panel-header>
                                <mat-panel-title>
                                    {{item?.id ?? ''}}
                                </mat-panel-title>
                                <div class="accordion-action-button">
                                @if(currentRole === ROLES.CASHIERGROUP && statusCode === PromotionClaimStatus.REJECTED) {
                                  <div class="remove-button" 
                                      (click)="$event.stopPropagation(); removeTransactionDocument(item)"
                                  >
                                      <mat-icon
                                          svgIcon="ic-trash-1"
                                          class="small-icon section-header--icon"
                                      >
                                      </mat-icon>
                                      <span>
                                          {{'common.remove' | translate}}
                                      </span>
                                  </div>
                                }
                                <mat-icon
                                  svgIcon="ic-arrow-down"
                                  class="small-icon section-header--icon toggle-accordion-icon"
                                  ></mat-icon>
                                </div>
                            </mat-expansion-panel-header>
                            <div class="detail-box">
                              <div class="detail-content">
                                <div class="detail-row">
                                  @for (data of item?.mapData; track $index) {
                                  <div class="detail-element">
                                    <div class="detail-label">
                                      {{ data?.label }}
                                    </div>
                                    <div class="detail-value">{{ data?.value }}</div>
                                  </div>
                                  }
                                </div>
                              </div>
                              <div class="hide-info" (click)="setExpanded($index, false)">
                                <img src="assets/images/icons/redeyecloseic.svg" class="small-icon section-header--icon">
                                <span>{{ "promotionDetail.hideInfo" | translate }}</span>
                              </div>
                            </div>
                        </mat-expansion-panel>
                    }
                </mat-accordion>
            }
        </div>
        </div>
      </div>
    </div>
  </div>
</div>
