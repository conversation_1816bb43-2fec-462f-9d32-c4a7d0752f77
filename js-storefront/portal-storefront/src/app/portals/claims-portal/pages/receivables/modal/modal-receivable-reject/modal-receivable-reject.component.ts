import { Component, inject, OnInit } from '@angular/core';
import { FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatDialogModule, MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs';
import { ActionModal } from '../../../../../../core/enums';
import { handleErrors } from '../../../../../../core/helpers';
import { OptionDropdown } from '../../../../../../core/interfaces';
import { NotificationService, LoadingService } from '../../../../../../core/services';
import { DropdownFormGroupComponent, FormGroupComponent } from '../../../../../../core/shared';
import { ReceivableService } from '../../../../services/receivable/receivable.service';
import { ModalCloseComponent } from '../../../../../iot-portal/features/vehicle/vehicle-detail/emergency-tab/modal-close/modal-close.component';

@Component({
  selector: 'app-modal-receivable-reject',
  standalone: true,
  imports: [
    MatDialogModule,
    MatIconModule,
    TranslateModule,
    ReactiveFormsModule,
    DropdownFormGroupComponent,
    FormGroupComponent,
  ],
  providers: [NotificationService, ReceivableService],
  templateUrl: './modal-receivable-reject.component.html',
  styleUrls: ['./modal-receivable-reject.component.scss'],
})
export class ModalReceivableRejectComponent implements OnInit {
  data = inject(MAT_DIALOG_DATA);
  dialogRef = inject(MatDialogRef<ModalCloseComponent>);
  loadingService = inject(LoadingService);
  notificationService = inject(NotificationService);
  translateService = inject(TranslateService);
  receivableService = inject(ReceivableService);
  mainSubscription = new Subscription();
  reasonList: OptionDropdown[] = [];
  form: FormGroup;
  ngOnInit() {
    this.form = this.data?.form;
    this.reasonList = (this.data?.reasonList && this.data.reasonList.filter((item) => item.code !== '')) || [];
  }

  ngOnDestroy() {
    this.mainSubscription && this.mainSubscription.unsubscribe();
  }

  onChangeRejectReason(e) {
    if (this.form.get('rejectReason')?.value === 'OTHERS') {
      this.form.controls['reason'].setValidators([Validators.required]);
      this.form.controls['reason'].updateValueAndValidity();
    } else {
      this.form.controls['reason'].clearValidators();
      this.form.controls['reason'].updateValueAndValidity();
    }
  } 

  onCancel() {
    this.dialogRef.close();
  }

  onConfirm() {
    if (this.data.mode === 'transaction') { 
      this.rejectReceivableTransaction();
    } else if (this.data.mode === 'claim') { 
      this.rejectReceivableClaim();
    }
  }

  rejectReceivableTransaction() {
    const formData = this.data?.form?.value;
    const params = {
      receivableCode: this.data.receivableCode,
      reason: formData.rejectReason !== 'OTHERS' ? (formData.rejectReason && this.data.reasonList.find((item) => item.code === formData.rejectReason)?.name || '') : formData.reason,
      remark: formData.remarks,
    };
    this.loadingService.showLoader();
    this.mainSubscription.add(
      this.receivableService.rejectReceivableTransaction(params, this.data.receivableCode).subscribe(
        (res) => {
          if (res.code === '200') {
            this.notificationService.showSuccess(
              this.translateService.instant(
                'receivables.message.rejectTransactionSuccess'
              )
            );
            this.data.form.reset();
            this.dialogRef.close({ action: ActionModal.Submit });
          } else {
            this.notificationService.showError(res?.message);
          }
          this.loadingService.hideLoader();
        },
        (error) => {
          this.loadingService.hideLoader();
          handleErrors(error, this.notificationService);
        }
      )
    );
  }

  rejectReceivableClaim() {
    const formData = this.data?.form?.value;
    const params = {
      reason: formData.rejectReason !== 'OTHERS' ? (formData.rejectReason && this.data.reasonList.find((item) => item.code === formData.rejectReason)?.name || '') : formData.reason,
      remarks: formData.remarks,
    };
    this.loadingService.showLoader();
    this.mainSubscription.add(
      this.receivableService.rejectReceivableClaim(params, this.data.claimCode).subscribe(
        (res) => {
          if (res.code === '200') {
            this.notificationService.showSuccess(
              this.translateService.instant(
                'receivables.message.rejectSuccess'
              )
            );
            this.data.form.reset();
            this.dialogRef.close({ action: ActionModal.Submit });
          } else {
            this.notificationService.showError(res?.message);
          }
          this.loadingService.hideLoader();
        },
        (error) => {
          this.loadingService.hideLoader();
          handleErrors(error, this.notificationService);
        }
      )
    );
  }
}
