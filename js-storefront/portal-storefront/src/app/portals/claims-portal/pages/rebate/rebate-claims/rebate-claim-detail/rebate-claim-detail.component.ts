import { Component, inject, <PERSON><PERSON><PERSON><PERSON>, OnInit, signal } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { BreadcrumbWithLabelComponent } from '../../../../../../core/shared/breadcrumb-with-label/breadcrumb-with-label.component';
import { MatIconModule } from '@angular/material/icon';
import { CommonModule } from '@angular/common';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatDialog } from '@angular/material/dialog';
import { NotificationService, LoadingService, ScreenSizeService } from '../../../../../../core/services';
import { ActivatedRoute, Router } from '@angular/router';

import { PromotionsService } from '../../../../services/promotion/promotions.service';
import { ROLES } from '../../../../../../core/constants/roles.const';
import { UserService } from '../../../../../../core/services/user';
import { handleErrors } from '../../../../../../core/helpers';
import { REBATE_CLAIM_STATUS } from '../../../../constants/rebate.constant';
import { RebateService } from '../../../../services/rebate/rebate.service';
import { RebateClaimDetail } from '../../../../interfaces/rebate.interface';
import { CurrencyComponent } from "../../../../../../core/shared/currency/currency.component";
import { error } from 'console';
import { G } from '@angular/cdk/keycodes';
import { GenerateExcelService } from '../../../../services/claims/generate-excel-cas.service';
import { LoadingComponent } from "../../../../../../layout/global/loading/loading.component";

@Component({
  selector: 'app-rebate-claim-detail',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    TranslateModule,
    BreadcrumbWithLabelComponent,
    MatExpansionModule,
    CurrencyComponent,
    LoadingComponent
],
  providers: [
    ScreenSizeService,
    LoadingService,
    NotificationService,
    PromotionsService
  ],
  templateUrl: './rebate-claim-detail.component.html',
  styleUrl: './rebate-claim-detail.component.scss'
})
export class RebateClaimDetailComponent implements OnInit {

  readonly REBATE_CLAIM_STATUS = REBATE_CLAIM_STATUS;

  dialog = inject(MatDialog);
  translateService = inject(TranslateService);
  notificationService = inject(NotificationService);
  loadingService = inject(LoadingService);
  public screenSizeService = inject(ScreenSizeService);
  route = inject(ActivatedRoute);
  router = inject(Router);
  generateExcelCasService = inject(GenerateExcelService);

  rebateService = inject(RebateService)
  userService = inject(UserService)

  isMarketingTeam: boolean = false

  claimId: string
  totalRelatedTransactions: number = 0

  rebateClaimDetail: RebateClaimDetail

  transactionListExpandStatus: any = []

  claimStatus: string

  relatedTransactionList: any = []

  relatedTransactionDeletedList: any = []

  step = signal(0);

  setExpanded(index: number, check: boolean) {
    this.step.set(index);
  }

  ngOnInit(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id !== null) {
      this.claimId = id;
      this.loadingService.showLoader();
      this.getRebateClaimDetail(false)

      if(this.userService.isHasPermission([ROLES.MARKETINGGROUP])) {
        this.isMarketingTeam = true
      }
    } else {
      console.error('Device ID is null');
    }
  }

  getRebateClaimDetail(isRefresh?: boolean) {
    this.rebateService.getRebateClaimDetail(this.claimId).subscribe(
      (data: RebateClaimDetail) => {
        if(data !== undefined) {
          let transactionListExpandStatus: any = []
          if(data?.promoTransactions !== undefined) {
            data.promoTransactions.forEach((itm: any, index: number) => {
              transactionListExpandStatus.push({
                expand: false
              })
            })
          }
          this.transactionListExpandStatus = transactionListExpandStatus
          this.claimStatus = data?.status.code
          this.rebateClaimDetail = data
          if(!isRefresh) {
            this.relatedTransactionList = data?.promoTransactions !== undefined
              ? data?.promoTransactions.map(item => {
                return this.handlePromoTransactionData(item)
              })
              : []
            this.totalRelatedTransactions = data?.promoTransactions !== undefined ? data?.promoTransactions?.length : 0
          }
        }
      }
    , error => {
      handleErrors(error, this.notificationService);
      this.loadingService.hideLoader();
    }).add(() => {
      this.loadingService.hideLoader();
    })
  }

  onGeneralExcelCAS() {
    this.generateExcelCasService.openGenerateExcelDialog([this.claimId], () => this.generateExcelCasService.generateExcelForClaims([this.claimId]))
      .subscribe((response) => {
        if (response?.code === '200') {
          this.notificationService.showSuccess(
            this.translateService.instant('rebate.claims.generateExcelSuccessfully', {
              claimId: this.claimId,
            }),
          );
          this.generateExcelCasService.downloadExcelByURL(response?.message);
        }
      },
      (error) => {
        this.notificationService.showError('common.generalError')
      });
  }

  handlePromoTransactionData(promoTransaction) {
    const uiList = [
      { code: 'status', value: '-' },
      { code: 'dealerName', value: '-' },
      { code: 'customerName', value: '-' },
      { code: 'customerId', value: '-' },
      { code: 'customerType', value: '-' },
      { code: 'customerGroup', value: '-' },
      { code: 'billingDocument', value: '-' },
      { code: 'invoiceCreationDate', value: '-' },
      { code: 'releaseDate', value: '-' },
      { code: 'postBillingApprovalDate', value: '-' },
      { code: 'vehicleModel', value: '-' },
      { code: 'variant', value: '-' },
      { code: 'modelYear', value: '-' },
      { code: 'mileage', value: '-' },
      { code: 'billTo', value: '-' },
      { code: 'discount', value: '-' },
      { code: 'split', value: '-' },
      { code: 'claimAmount', value: '-' },
      { code: 'orderType', value: '-' },
      { code: 'jobType', value: '-' },
      { code: 'materialNumber', value: '-' },
      { code: 'materialDescription', value: '-' },
      { code: 'customerAdvisor', value: '-' },
      { code: 'vehicleGuid', value: '-' },
      { code: 'vin', value: '-' },
      { code: 'reservationDate', value: '-' },
      { code: 'documentNo', value: '-' },
      { code: 'deliveryDocNo', value: '-' },
      { code: 'forDeliveryDate', value: '-' },
      { code: 'orderDate', value: '-' },
      { code: 'vehicleDeliveryDate', value: '-' },
      { code: 'serviceBillingAmount', value: '-' },
      { code: 'packageId', value: '-' },
      { code: 'orderReason', value: '-' },
      { code: 'reservationDocNo', value: '-' },
      { code: 'reservationApprovalDate', value: '-' },
      { code: 'paymentMethod', value: '-' },
      { code: 'quantity', value: '-' },
      { code: 'cxPromotionCode', value: '-' },
      { code: 'srp', value: '-' },
      { code: 'orderStatus', value: '-' },
      { code: 'dbmOrderNumber', value: '-' },
    ]
    // Base on key in object promoTransaction, update the value in uiList
    uiList.forEach((item) => {
      if (promoTransaction[item.code] !== undefined) {
        item.value = promoTransaction[item.code]
      }
    })
    return uiList;
  }
}
