
import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { ActivatedRoute } from '@angular/router';
import { WidgetSummaryComponent } from '../../../../core/shared';
import { RebateTab } from '../../enum';
import { debug } from 'console';
import { ItemWidget } from '../../../../core/interfaces';
import { RebateClaimsComponent } from "./rebate-claims/rebate-claims.component";
import { RebateChecklistComponent } from "./rebate-checklist/rebate-checklist.component";
import { UserService } from '../../../../core/services/user';
import { ROLES } from '../../../../core/constants/roles.const';

@Component({
  selector: 'app-rebate',
  imports: [
    CommonModule,
    TranslateModule,
    RebateClaimsComponent,
    RebateChecklistComponent
],
  providers: [],
  standalone: true,
  templateUrl: './rebate.component.html',
  styleUrl: './rebate.component.scss'
})
export class RebateComponent {
  private route = inject(ActivatedRoute);
  private userService = inject(UserService);

  isHiddenChecklist = this.userService.isHasPermission([ROLES.CASHIERGROUP, ROLES.TREASURYGROUP]);

  readonly RebateTab = RebateTab;
  mainTabs = [
    { id: RebateTab.CHECKLIST, name: 'rebate.tab.checklist', isHidden: this.isHiddenChecklist },
    { id: RebateTab.CLAIMS, name: 'rebate.tab.claims', isHidden: false },
  ];

  selectedTab: RebateTab;
  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      const tab = params['tab'];
      switch (tab) {
        case RebateTab.CLAIMS:
          this.selectedTab = RebateTab.CLAIMS;
          break;
        case RebateTab.CHECKLIST:
          this.selectedTab = RebateTab.CHECKLIST;
          break;
        default:
          if (this.isHiddenChecklist) {
            this.selectedTab = RebateTab.CLAIMS;
          } else {
            this.selectedTab = RebateTab.CHECKLIST;
          }
      }
    });
  }

  changeMainTab(id: RebateTab) {
    this.selectedTab = id;
  }
}
