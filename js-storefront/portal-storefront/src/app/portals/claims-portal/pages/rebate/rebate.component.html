<div class="header-section">
  <h2 class="title-page">
    {{ "rebate.title" | translate }}
  </h2>
  <div class="device-action">
    <div class="device-action__tabname">
      @for (item of mainTabs; track $index) {
        @if (!item.isHidden) {
          <span
            [class.active]="item.id === selectedTab"
            (click)="changeMainTab(item.id)"
            >{{ item.name | translate }}</span
          >
        }
      }
    </div>
  </div>
</div>

@if (selectedTab === RebateTab.CHECKLIST) {
  <app-rebate-checklist></app-rebate-checklist>
} @else if (selectedTab === RebateTab.CLAIMS) {
  <app-rebate-claims></app-rebate-claims>
}
