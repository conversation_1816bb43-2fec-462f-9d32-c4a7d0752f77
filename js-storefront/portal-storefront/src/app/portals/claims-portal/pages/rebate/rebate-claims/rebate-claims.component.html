<app-loading *ngIf="loadingService.isLoading"></app-loading>

<div class="claims-wrapper">
  <div class="claims-widget">
    <app-widget-summary
      [widgets]="currentWidgets"
      (changeTab)="changeSummaryTab($event)"
    ></app-widget-summary>
  </div>
  <div class="claims-container">
    <form [formGroup]="filterForm">
      <div class="filter-form">
        <div class="filter-form-properties">
          <app-dropdown-form-group *ngIf="isMarketingRole"
            [label]="'rebate.filter.dealer' | translate"
            [control]="filterForm.controls.dealer"
            [options]="dealers"
          ></app-dropdown-form-group>
          <app-dropdown-form-group
            [label]="'rebate.filter.status' | translate"
            [control]="filterForm.controls.status"
            [options]="status"
          ></app-dropdown-form-group>
        </div>
        <div class="filter-form-search">
          <button type="submit" class="btn-tertiary search" (click)="searchRebateClaims()">
            {{ "common.search" | translate }}
          </button>
        </div>
      </div>
    </form>
    <div class="device-list">
      @if (rowData?.length > 0 ) {
        <div class="generate-excel-multiple" *ngIf="isMarketingRole">
          <button
            class="btn-primary"
            (click)="onGenerateExcel()"
          >
            {{'rebate.claims.grid.generateExcelForCAS' | translate}}
          </button>
        </div>
        <app-ag-grid-custom
          #rebateClaimsGrid
          class="ticket-table"
          [class.isMarketingRole]="isMarketingRole"
          [rowData]="rowData"
          [colDefs]="colDefs"
          [defaultColDef]="defaultColDef"
          [isPaging]="true"
          [pagingInfo]="pagingInfo"
          [isTextWrap]="true"
          [gridOptions]="gridOptions"
          [isShowActionExport]="false"
          (onPageChange)="onPageChange($event)"
          (changeItemPerPage)="onResultsPerPageChange($event)"
        >
        </app-ag-grid-custom>
      } @else {
        <div class="no-data">{{ "rebate.claims.noData" | translate }}</div>
      }
    </div>
  </div>
</div>