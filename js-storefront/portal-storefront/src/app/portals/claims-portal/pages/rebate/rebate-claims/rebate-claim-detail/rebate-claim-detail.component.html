<app-loading *ngIf="loadingService.isLoading"></app-loading>

<div class="promotion-details container-details">
    <div class="container-details__header p-b-0">
      <app-breadcrumb-with-label
        [breadcrumbs]="['rebate.claims.title', 'rebate.claims.details']"
        [breadcrumbLinks]="['/claims/rebate?tab=CLAIMS', '/claims/rebate/claim-detail/' + (claimId || '')]"
        [label]="claimId"
      >
      </app-breadcrumb-with-label>
      <div class="container-details__actions">
        @if(isMarketingTeam && claimStatus === REBATE_CLAIM_STATUS.MARKETING_VALIDATED)
        {
            <button class="btn-primary" (click)="onGeneralExcelCAS()">{{ "common.generateExcel" | translate }}</button>
        }
      </div>
    </div>
    <div class="container-details__body">
        <div class="container-details__body__promotion">
            <div class="top-detail-section">
                <div class="details">
                    <div class="section-header">
                        <mat-icon
                            svgIcon="ic-bp-est-infor"
                            class="medium-icon section-header--icon"
                        ></mat-icon>
                        <div class="section-header--content">
                            <span>{{ "promotionDetail.detail" | translate }}</span>
                        </div>
                    </div>
                    <div class="section-body">
                        <div class="detail-item">
                            <div>
                                <span class="title">
                                    {{ "promotionDetail.status" | translate }}
                                </span>
                            </div>
                            <div>
                                <span class="content">
                                    {{(rebateClaimDetail?.status?.name ?? '-') || '-'}}
                                </span>
                            </div>
                        </div>
                        <div class="detail-item">
                            <div>
                                <span class="title">
                                    {{ "promotionDetail.referenceNumber" | translate }}
                                </span>
                            </div>
                            <div>
                                <span class="content">
                                    {{(rebateClaimDetail?.referenceNumber ?? '-') || '-'}}
                                </span>
                            </div>
                        </div>
                        <div class="detail-item">
                            <div>
                                <span class="title">
                                    {{ "rebate.claims.requiredDocument" | translate }}
                                </span>
                            </div>
                            <div>
                                <span class="content">
                                    {{(rebateClaimDetail?.requiredDocument) || '-'}}
                                </span>
                            </div>
                        </div>
                        <div class="detail-item">
                            <div>
                                <span class="title">
                                    {{ "promotionDetail.orNumber" | translate }}
                                </span>
                            </div>
                            <div>
                                <span class="content">
                                    {{(rebateClaimDetail?.orNumber ?? '-') || '-'}}
                                </span>
                            </div>
                        </div>
                        <div class="detail-item">
                            <div>
                                <span class="title">
                                    {{ "promotionDetail.claimableAmount" | translate }}
                                </span>
                            </div>
                            <div>
                                <span class="content">
                                    <app-currency
                                        [currencyIso]="rebateClaimDetail?.claimableAmount?.currencyIso"
                                        [amount]="rebateClaimDetail?.claimableAmount?.value"
                                    ></app-currency>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bottom-detail-section">
                <div class="details">
                    <div class="section-header">
                        <mat-icon
                            svgIcon="ic-transaction"
                            class="medium-icon section-header--icon"
                        ></mat-icon>
                        <div class="section-header--content">
                            <span>{{ "promotionDetail.relatedTransactions" | translate }} ({{totalRelatedTransactions}})</span>
                        </div>
                    </div>
                    <div class="section-body">
                        @if(relatedTransactionList.length > 0) {
                            <mat-accordion class="example-headers-align">
                                @for (item of relatedTransactionList; track $index) {
                                    <mat-expansion-panel 
                                        [expanded]="step() === $index" 
                                        (opened)="setExpanded($index, true)" 
                                        hideToggle
                                    >
                                        <mat-expansion-panel-header>
                                            <mat-panel-title>
                                                <!-- item[41]?.value is dbmOrderNumber -->
                                                {{'promotionDetail.promotrans' | translate}}-{{item[41]?.value ?? ''}}
                                            </mat-panel-title>
                                            <div class="accordion-action-button">
                                                <mat-icon
                                                    svgIcon="ic-arrow-down"
                                                    class="small-icon section-header--icon toggle-accordion-icon"
                                                ></mat-icon>
                                            </div>
                                        </mat-expansion-panel-header>
                                        <div class="accordion-container">
                                            <div class="accordion-list">
                                                @for(trans of item; track $index) {
                                                    <div class="accordion-item">
                                                        <div>
                                                            <span class="title">{{('promotionDetail.' + trans.code) | translate}}</span>
                                                        </div>
                                                        <div>
                                                            <span class="content">
                                                                @if (trans.code === 'status'
                                                                    || trans.code === 'customerType'
                                                                ) {
                                                                    {{ trans.value?.name || '-' }}
                                                                } @else if (trans.code === 'serviceBillingAmount'
                                                                    || trans.code === 'claimAmount'
                                                                ) {
                                                                    <app-currency
                                                                        [amount] = "trans.value?.value"
                                                                        [currencyIso] = "trans.value?.currencyIso"
                                                                    ></app-currency>
                                                                } @else {
                                                                    {{ trans.value || '-' }}
                                                                }
                                                            </span>
                                                        </div>
                                                    </div>
                                                }
                                            </div>
                                            <div class="hide-info" (click)="setExpanded($index, false)">
                                                <img src="assets/images/icons/redeyecloseic.svg" class="small-icon section-header--icon">
                                                <span>{{ "promotionDetail.hideInfo" | translate }}</span>
                                            </div>
                                        </div>
                                    </mat-expansion-panel>
                                }
                            </mat-accordion>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>