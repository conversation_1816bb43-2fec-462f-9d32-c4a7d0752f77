export const ID_WIDGET_CLAIM_VALIDATED_CLAIMS = 'marketingValidated';
export const ID_WIDGET_RFP_PROCESSING_CLAIMS = 'rfpProcessing';
export const ID_WIDGET_FOR_INITIAL_VALIDATION_CLAIMS = 'forInitialValidationClaims';
export const ID_WIDGET_APPROVED_CLAIMS = 'approvedClaims';
export const ID_WIDGET_PUSH_MODEL_TRANSACTIONS = 'PUSH_MODEL';
export const ID_WIDGET_GENERAL_MODEL_TRANSACTIONS = 'GENERAL';

export const REBATE_CLAIMS_DEALER_WIDGETS = [
  {
    id: ID_WIDGET_CLAIM_VALIDATED_CLAIMS,
    description: 'rebate.claimValidatedClaims',
    icon: 'ic-info-1',
    count: 0,
  }, {
    id: ID_WIDGET_RFP_PROCESSING_CLAIMS,
    description: 'rebate.rfpProcessingClaims',
    icon: 'ic-info-2',
    count: 0,
  }
];

export const REBATE_CLAIMS_OTHER_WIDGETS = [
  {
    id: ID_WIDGET_FOR_INITIAL_VALIDATION_CLAIMS,
    description: 'rebate.forInitialValidationClaims',
    icon: 'ic-file-claim',
    count: 0,
  }, {
    id: ID_WIDGET_APPROVED_CLAIMS,
    description: 'rebate.approvedClaims',
    icon: 'ic-info-1',
    count: 0,
  }
];

export const REBATE_CHECKLIST_WIDGETS = [
  {
    id: ID_WIDGET_PUSH_MODEL_TRANSACTIONS,
    description: 'rebate.pushModelTransactions',
    icon: 'ic-transaction',
    count: 0,
  }, {
    id: ID_WIDGET_GENERAL_MODEL_TRANSACTIONS,
    description: 'rebate.generalModelTransactions',
    icon: 'ic-transaction',
    count: 0,
  }
];

export const REBATE_CLAIM_STATUS = {
  MARKETING_VALIDATED: 'MARKETING_VALIDATED',
  RFP_PROCESSING: 'RFP_PROCESSING',
  MARKETING_RETURNED: 'MARKETING_RETURNED',
  ACCOUNTING_POSTED: 'ACCOUNTING_POSTED',
  FOR_FUN_TRANSFER: 'FOR_FUN_TRANSFER',
  OR_AR_ISSUED: 'OR_AR_ISSUED',
  OR_AR_RECEIVED: 'OR_AR_RECEIVED',
  CANCELLED: 'CANCELLED',
}

export const REBATE_CLAIM_TYPE = {
  PUSH_MODEL: 'PUSH_MODEL',
  GENERAL: 'GENERAL',
}
