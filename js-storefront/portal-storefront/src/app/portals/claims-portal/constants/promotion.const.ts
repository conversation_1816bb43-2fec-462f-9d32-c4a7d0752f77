import { PromotionClaimStatus, PromotionClaimSummaryTab, PromotionTransactionSummaryTab } from "../enum";
export const PROMOTION_CLAIM_WIDGETS = [
  {
    id: PromotionClaimSummaryTab.initialValidation,
    description: 'promotions.summary.forInitialValidationClaims',
    status: PromotionClaimStatus.INITIAL_VALIDATION,
    icon: 'ic-claim-for-initial-validation-widget',
  },
  {
    id: PromotionClaimSummaryTab.approved,
    description: 'promotions.summary.approvedClaims',
    status: PromotionClaimStatus.CLAIM_VALIDATED,
    icon: 'ic-claim-approved-widget',
  }
];

export const PROMOTION_TRANSACTION_WIDGETS = [
  {
    id: PromotionTransactionSummaryTab.new,
    description: 'promotions.summary.new',
    status: PromotionClaimStatus.INITIAL_VALIDATION,
    icon: 'ic-claim-for-initial-validation-widget',
  },
  {
    id: PromotionTransactionSummaryTab.submitted,
    description: 'promotions.summary.submitted',
    status: PromotionClaimStatus.CLAIM_VALIDATED,
    icon: 'ic-promotion-submitted',
  }
];