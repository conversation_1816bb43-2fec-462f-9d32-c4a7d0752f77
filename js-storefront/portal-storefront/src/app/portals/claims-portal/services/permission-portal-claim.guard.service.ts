import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, Router } from '@angular/router';
import { UserService } from '../../../core/services/user';

@Injectable({
  providedIn: 'root'
})
export class PermissionPortalClaimGuard implements CanActivate {

  constructor(private userService: UserService, private router: Router) {
   
  }

  canActivate(route: ActivatedRouteSnapshot): boolean {
    const requiredPermission = route.data['roles'];
    const currentRoles = JSON.parse(localStorage.getItem('roles'))?.roles || [];
    const currentRole = this.userService.getGroupRole(currentRoles);
    if (requiredPermission.includes(currentRole)) {
      return true;
    } else {
      // Chuyển hướng hoặc thông báo
      this.router.navigate(['/claims']);
      return false;
    }
  }
}