import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { filter, Observable } from 'rxjs';
import { OptionDropdown, WidgetResponse } from '../../interfaces';
import {
  PromotionClaimFilter,
  PromotionClaimItem,
  PromotionList,
  PromotionTransactionFilter,
  TransactionItem,
} from '../../interfaces/promotions.interface';
import { environment } from '../../../../../environments/environment';
import { ReceivableClaimStatusFilter } from '../../interfaces/receivable.interface';
import { ResponseCommon } from '../../../../core/interfaces';
import { PromotionClaimDetailAction } from '../../enum';

@Injectable()
export class PromotionsService {
  constructor(private http: HttpClient) {}

  prefixEnv = environment.OCC_BASE_URL + environment.OCC_PREFIX;
  readonly PromotionClaimDetailAction = PromotionClaimDetailAction

  appealPromotionClaim(id: string) {
    let data: any = {}
    const path = `${this.prefixEnv}portal/claim/promotion-claims/${id}/appeal`;
    return this.http.post<any>(path, data);
  }

  approvePromotionClaim(id: string) {
    let data: any = {}
    const path = `${this.prefixEnv}portal/claim/action/${id}/approve`;
    return this.http.post<any>(path, data);
  }

  updatedRejectPromotionClaim(id: string, type: string, data: any) {
    let dataSend: any = {}
    switch(type) {
      case PromotionClaimDetailAction.REMOVE_ATTACHMENT:
        dataSend = {
          deletingDocCodes: data
        }
        break
      case PromotionClaimDetailAction.UPLOAD_ATTACHMENT:
        dataSend = {
          uploadingDocs: data.uploadingDocs,
          uploadingDocNames: data.uploadingDocNames
        }
        break
      case PromotionClaimDetailAction.REMOVE_TRANSACTION:
        dataSend = {
          deletingPromotionTransactionIds: data
        }
    }

    const path = `${this.prefixEnv}portal/claim/promotion-claims/${id}/rejected-update`;
    return this.http.put<any>(path, dataSend);
  }

  rejectPromotionClaim(id: string, data: any) {
    const path = `${this.prefixEnv}portal/claim/action/${id}/reject`;
    return this.http.post<any>(path, data);
  }

  getRejectOptionList() {
    const path = `${this.prefixEnv}portal/claim/promotion-claims/metadata`;
    return this.http.get(path);
  }

  getPromotionClaimDetail(id: string) {
    const path = `${this.prefixEnv}portal/claim/promotion-claims/${id}`;
    return this.http.get(path);
  }

  getPromotionWidget(isClaims: boolean): Observable<{ widgets: WidgetResponse[] }> {
    let path = `${this.prefixEnv}portal/claim/promotion-claims/widget`;
    if (!isClaims) {
      path = `${this.prefixEnv}portal/claim/promotion-transactions/widget`;
    }
    return this.http.get<{ widgets: WidgetResponse[] }>(path);
  }

  getPromotionTransactionFilter(): Observable<PromotionTransactionFilter> {
    const path = `${this.prefixEnv}portal/claim/promotion-transactions/promotions/filters`;
    return this.http.get<PromotionTransactionFilter>(path);
  }

  getClaimList(params: {
    id: string;
    status: string;
    month: string;
    year: string;
    creationMonth: string;
    creationYear: string;
    currentPage: number;
    pageSize: number;
  }): Observable<PromotionList<PromotionClaimItem>> {
    let path = `${this.prefixEnv}portal/claim/promotion-claims/claims`;
    return this.http.get<PromotionList<PromotionClaimItem>>(path, { params });
  }

  getPromotionsList(params: {
    promotionName: string;
    promotionType: string;
    page: number;
    size: number;
  }): Observable<PromotionList<OptionDropdown>> {
    const path = `${this.prefixEnv}portal/claim/promotion-transactions/promotions`;
    return this.http.get<PromotionList<OptionDropdown>>(path, { params });
  }

  getTransactionList(params: {
    billingDocumentNo: string;
    promotionType: string;
    vin: string;
    status: string;
    currentPage: number;
    pageSize: number;
  }): Observable<PromotionList<TransactionItem>> {
    const path = `${this.prefixEnv}portal/claim/promotion-transactions/`;
    return this.http.get<PromotionList<TransactionItem>>(path, { params });
  }

  getPromotionsFilter(): Observable<OptionDropdown[]> {
    const path = `${this.prefixEnv}portal/claim/promotion-transactions/promotions/promotion-types`;
    return this.http.get<OptionDropdown[]>(path);
  }

  getTransactionFilter(): Observable<OptionDropdown[]> {
    const path = `${this.prefixEnv}portal/claim/promotion-transactions/promotion-transaction-statuses`;
    return this.http.get<OptionDropdown[]>(path);
  }

  getPromotionClaimFilter(): Observable<PromotionClaimFilter> {
    const path = `${this.prefixEnv}portal/claim/promotion-claims/filter `;
    return this.http.get<PromotionClaimFilter>(path);
  }

  getTransactionDetails(claimId: string): Observable<TransactionItem> {
    const path = `${this.prefixEnv}portal/claim/promotion-transactions/details/${claimId}`;
    return this.http.get<TransactionItem>(path);
  }

  createClaim(body: {
    promotionIds: string;
    referenceNumber: string;
    docs: string;
    docNames: string;
  }): Observable<any> {
    const path = `${this.prefixEnv}portal/claim/promotion-claims/`;
    return this.http.post<any>(path, body);
  }

  addORARNumber(
    body: {
      numberORAR: string;
    },
    claimId
  ): Observable<any> {
    const path = `${this.prefixEnv}portal/claim/promotion-claims/${claimId}/addORAR`;
    return this.http.put<any>(path, body);
  }

  exportExcelReceivables(body): Observable<ResponseCommon> {
    const path = `${this.prefixEnv}portal/claim/action/export-excel`;
    return this.http.post<ResponseCommon>(path, body);
  }

  confirmORAR(body): Observable<any> {
    const path = `${this.prefixEnv}portal/claim/promotion-claims/confirmORAR`;
    return this.http.put<any>(path, body);
  }

  updateReferenceNumber(body, claimCode): Observable<ResponseCommon> {
    const path = `${this.prefixEnv}portal/claim/action/${claimCode}/updateReferenceNumber`;
    return this.http.post<ResponseCommon>(path, body);
  }
}
