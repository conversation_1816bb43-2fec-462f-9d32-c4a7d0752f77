export enum PromotionsTab {
  TRANSACTIONS = 'TRANSACTIONS',
  CLAIMS = 'CLAIMS',
}

export enum PromotionClaimSummaryTab {
  //LDCM
  initialValidation = 'initialValidation',
  approved = 'approved',
}

export enum PromotionTransactionSummaryTab {
  //LDCM
  new = 'new',
  submitted = 'submitted',
}

export enum PromotionClaimDetailAction {
  UPLOAD_ATTACHMENT = 'uploadAttachment',
  REMOVE_ATTACHMENT = 'removeAttachment',
  REMOVE_TRANSACTION = 'removeTransaction'
}

export enum PromotionClaimStatus {
  SUBMITTED = 'SUBMITTED',
  INITIAL_VALIDATION = 'INITIAL_VALIDATION',
  VALIDATION_APPEAL = 'VALIDATION_APPEAL',
  CLAIM_VALIDATED = 'CLAIM_VALIDATED',
  REJECTED = 'REJECTED',
  RFP_PROCESSING = 'RFP_PROCESSING',
  RETURNED = 'RETURNED',
  POSTED = 'POSTED',
  FOR_FUND_TRANSFER = 'FOR_FUND_TRANSFER',
  OR_AR_ISSUED = 'OR_AR_ISSUED',
  OR_AR_RECEIVED = 'OR_AR_RECEIVED',
  CANCELLED = 'CANCELLED',
}

export enum PromotionClaimAction {
  addORARNumber = 'addORARNumber',
  generateExcel = 'generateExcel',
  approve = 'approve',
  reject = 'reject',
  appeal = 'appeal',
}
