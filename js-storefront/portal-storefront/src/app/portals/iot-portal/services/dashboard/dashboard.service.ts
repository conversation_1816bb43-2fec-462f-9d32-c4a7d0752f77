import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import {
  ItemChart,
  ItemSubscription,
  NonOperationalDevice,
  PMSDashboardItem,
  ServiceBookingDashboard,
  SpecialServiceCampaignDueItem,
  Vehicle,
} from '../../interfaces';
import { ChartType, VehicleMode } from '../../enums';
import { LoadingService } from '../../../../core/services';
import { environment } from '../../../../../environments/environment';
import { SectionList, WidgetResponse } from '../../../../core/interfaces';
import { removeNullParams } from '../../../../core/helpers';

@Injectable()
export class DashboardService {
  http = inject(HttpClient);
  loadingService = inject(LoadingService);

  prefixEnv = environment.OCC_BASE_URL + environment.OCC_PREFIX;

  fetchChartData(chartType: ChartType, onComplete: (items: any) => void): void {
    this.loadingService.startLoading();
    this.getItemChart(chartType).subscribe(
      (response) => {
        this.loadingService.stopLoading();
        onComplete(response?.items);
      },
      () => {
        this.loadingService.stopLoading();
      }
    );
  }

  getItemChart(type: ChartType): Observable<{
    items: ItemChart[];
    type: string;
  }> {
    const path = `${this.prefixEnv}iot-portal/e-care/dashboard/chart`;
    return this.http.get<{
      items: ItemChart[];
      type: string;
    }>(path, {
      params: {
        type,
      },
    });
  }

  getSpecialServiceCampaignDue(params: {
    currentPage?: number;
    pageSize?: number;
  }): Observable<SectionList<SpecialServiceCampaignDueItem>> {
    const path = `${this.prefixEnv}iot-portal/e-care/dashboard/table/ssc-due`;
    return this.http.get<SectionList<SpecialServiceCampaignDueItem>>(path, {
      params,
    });
  }

  getVehicleWithSos(params: {
    currentPage?: number;
    pageSize?: number;
  }): Observable<SectionList<Vehicle>> {
    const path = `${this.prefixEnv}iot-portal/e-care/dashboard/table/sos`;
    return this.http.get<SectionList<Vehicle>>(path, {
      params,
    });
  }

  getNonOperationalDevice(params: {
    currentPage?: number;
    pageSize?: number;
  }): Observable<SectionList<NonOperationalDevice>> {
    const path = `${this.prefixEnv}iot-portal/`;
    return of({
      pagination: {
        currentPage: 0,
        pageSize: 10,
        totalPages: 1,
        totalResults: 20,
      },
      items: [
        {
          make: 'Land Cruiser 300',
          plateNo: 'NBA 1234',
          status: 'Active',
        },
      ],
    });
    return this.http.get<SectionList<SpecialServiceCampaignDueItem>>(path, {
      params,
    });
  }

  getVehicle(
    params: {
      currentPage?: number;
      pageSize?: number;
    },
    mode: VehicleMode
  ): Observable<SectionList<Vehicle>> {
    const path = `${this.prefixEnv}iot-portal/e-care/dashboard/vehicle`;

    return this.http.get<SectionList<Vehicle>>(path, {
      params: {
        ...params,
        mode,
      },
    });
  }

  getSubscriptionToActive(params: {
    currentPage?: number;
    pageSize?: number;
    status?: string;
  }): Observable<SectionList<ItemSubscription[]>> {
    const path = `${this.prefixEnv}iot-portal/e-care/dashboard/table/subscriptions-to-active`;

    return this.http.get<SectionList<ItemSubscription[]>>(path, {
      params: removeNullParams(params),
    });
  }

  getSubscriptions(params: {
    currentPage?: number;
    pageSize?: number;
    status?: string;
  }): Observable<SectionList<ItemSubscription[]>> {
    const path = `${this.prefixEnv}iot-portal/e-care/dashboard/table/subscriptions`;
    return this.http.get<SectionList<ItemSubscription[]>>(path, {
      params: removeNullParams(params),
    });
  }

  getDeviceByStatus(params: {
    currentPage?: number;
    pageSize?: number;
    deviceType?: string; // GDCM/LDCM
  }): Observable<SectionList<any>> {
    const path = `${this.prefixEnv}iot-portal/e-care/dashboard/table/device-by-type`;
    return this.http.get<SectionList<any>>(path, {
      params,
    });
  }

  getNonOperationalVehicle(params: {
    currentPage?: number;
    pageSize?: number;
  }): Observable<SectionList<any>> {
    const path = `${this.prefixEnv}iot-portal/e-care/dashboard/table/non-operational`;
    return this.http.get<SectionList<any>>(path, {
      params,
    });
  }

  getDeviceByStatusThisMonth(params: {
    currentPage?: number;
    pageSize?: number;
    deviceType?: string; // GDCM/LDCM
  }): Observable<SectionList<any>> {
    const path = `${this.prefixEnv}iot-portal/e-care/dashboard/table/device-pair-this-month`;
    return this.http.get<SectionList<any>>(path, {
      params,
    });
  }

  getServiceBooking(params: {
    currentPage?: number;
    pageSize?: number;
  }): Observable<SectionList<ServiceBookingDashboard[]>> {
    const path = `${this.prefixEnv}iot-portal/e-care/dashboard/table/service-booking`;
    return this.http.get<SectionList<any>>(path, {
      params,
    });
  }

  getPMS(params: {
    currentPage?: number;
    pageSize?: number;
  }): Observable<SectionList<PMSDashboardItem[]>> {
    const path = `${this.prefixEnv}iot-portal/e-care/dashboard/table/pms-due`;
    return this.http.get<SectionList<any>>(path, {
      params,
    });
  }

  getWidget(): Observable<WidgetResponse[]> {
    const path = `${this.prefixEnv}iot-portal/e-care/dashboard/widget`;

    return this.http.get<WidgetResponse[]>(path);
  }
}
