import { inject, Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import {map, Observable, of} from 'rxjs';
import {
    ApprovalStatus,
  CallLog, CallLogMetadata, CallLogResponse,
  DealerDetails,
  DropdownDetails,
  InsuranceDetails,
  MetadataTowingDropdown,
  PerferDealer,
  RequestCallLog,
  requestCreateTowingService,
  Ticket,
  TicketDetail,
  TowingServiceDetails,
  Vehicle,
} from '../../interfaces/ticket-detail.interface';
import { DTCWidget } from '../../interfaces';
import { environment } from '../../../../../environments/environment';
import { ResponseCommon } from '../../../../core/interfaces';

@Injectable()
export class TicketsDetailService {
  #http = inject(HttpClient);

  prefixEnv = `${environment.OCC_BASE_URL}${environment.OCC_PREFIX}`;

  // Fetches ticket details (fake data for now)
  getTicketDetail(ticketId: string): Observable<TicketDetail> {
    const path = `${this.prefixEnv}iot-portal/tickets/${ticketId}`;
    return this.#http.get<TicketDetail>(path);
  }

  // Updates ticket details (fake response for now)
  updateTicketDetail(ticket: Ticket): Observable<Ticket> {
    const path = `${this.prefixEnv}iot-portal/tickets/${ticket.ticketID}`;

    // Fake response
    const updatedTicket = { ...ticket };
    return of(updatedTicket);

    // Uncomment when API is ready
    // return this.#http.put<Ticket>(path, ticket);
  }

  getDealerList(vin: string): Observable<MetadataTowingDropdown> {
    const path = `${this.prefixEnv}iot-portal/towing/metadata`;
    return this.#http.get<MetadataTowingDropdown>(path, { params: { vin } });
  }

  getPreferDealerList(vin: string): Observable<PerferDealer> {
    const path = `${this.prefixEnv}iot-portal/towing/prefer-dealer`;
    return this.#http.get<PerferDealer>(path, { params: { vin } });
  }

  getTowingServiceDetails(vin: string): Observable<TowingServiceDetails> {
    const path = `${this.prefixEnv}iot-portal/towing/latest`;
    return this.#http.get<TowingServiceDetails>(path, { params: { vin } });
  }

  createTowingService(
    body: requestCreateTowingService
  ): Observable<TowingServiceDetails> {
    const path = `${this.prefixEnv}iot-portal/towing`;
    return this.#http.post<TowingServiceDetails>(path, body);
  }

  updateTowingService(
    body: requestCreateTowingService,
    id: string
  ): Observable<TowingServiceDetails> {
    const path = `${this.prefixEnv}iot-portal/towing/${id}`;
    return this.#http.put<TowingServiceDetails>(path, body);
  }

  updateStatusTowingService(body: {
    id: string;
    status: string;
  }): Observable<ResponseCommon> {
    const path = `${this.prefixEnv}iot-portal/towing/updateStatus`;
    return this.#http.post<ResponseCommon>(path, body);
  }

  getInsuranceDetails(vin: string): Observable<InsuranceDetails> {
    const path = `${this.prefixEnv}iot-portal/e-care/vehicle/insurance-info`;
    return this.#http.get<InsuranceDetails>(path, { params: { vin } });
  }

  verifyTicket(id: string) {
    const path = `${this.prefixEnv}iot-portal/tickets/${id}/verify`;
    return this.#http.put<{
      isVerified: boolean;
      verifiedBy: string;
      verifiedDate: string;
    }>(path, {});
  }

  getCallLogList(
    ticket: string,
    pageSize: number = 5,
    currentPage: number = 0
  ): Observable<CallLogResponse> {
    const path = `${this.prefixEnv}iot-portal/${ticket}/call-logs`;
    const params = new HttpParams()
      .set('pageSize', pageSize.toString())
      .set('currentPage', currentPage.toString());

    return this.#http.get<CallLogResponse>(path, { params });
  }

  getCallLogMetadata(ticket: string): Observable<CallLogMetadata> {
    const path = `${this.prefixEnv}iot-portal/${ticket}/call-logs/metadata`;
    return this.#http.get<CallLogMetadata>(path);
  }

  createCallLog(
    ticket: string,
    body: RequestCallLog
  ): Observable<RequestCallLog> {
    const path = `${this.prefixEnv}iot-portal/${ticket}/call-logs`;
    return this.#http.post<RequestCallLog>(path, body);
  }

  updateCallLog(
    ticket: string,
    id: string,
    body: RequestCallLog
  ): Observable<RequestCallLog> {
    const path = `${this.prefixEnv}iot-portal/${ticket}/call-logs/${id}`;
    return this.#http.put<RequestCallLog>(path, body);
  }

  deleteCallLog(ticket: string, id: string): Observable<ResponseCommon> {
    const path = `${this.prefixEnv}iot-portal/${ticket}/call-logs/${id}`;
    return this.#http
      .delete<ResponseCommon>(path, { observe: 'response' })
      .pipe(
        map((res: any) => {
          if (res.status === 204) {
            const dataRes: ResponseCommon = {
              code: 'SUCCESS',
              message: 'callLog.successMessages.deleteSuccess',
            };
            return dataRes;
          }
          return res;
        })
      );
  }

  getApprovalStatus(ticketId: string): Observable<any> {
    const path = `${this.prefixEnv}iot-portal/tickets/${ticketId}/approvalStatus`;
    return this.#http.get<ApprovalStatus>(path);
  }

  generateToken(
    ticketId: string,
    method: 'EMAIL' | 'DIGITAL_TOKEN'
  ): Observable<any> {
    const path = `${this.prefixEnv}iot-portal/tickets/${ticketId}/generateToken/${method}`;
    return this.#http.post<any>(path, {});
  }

  refreshCurrentLocation(id: string): Observable<Vehicle> {
    const path = `${this.prefixEnv}iot-portal/tickets/${id}/refresh`;
    return this.#http.get<Vehicle>(path)
  }

  getWarningListForTicket(ticket: string, pageSize): Observable<any> {
    const path = `${this.prefixEnv}iot-portal/e-care/notifications?pageSize=${pageSize}&ticketId=${ticket}`;
    return this.#http.get<any>(path);
  }

  getDTCWidget(id: string): Observable<DTCWidget[]> {
    const path = `${this.prefixEnv}iot-portal/tickets/dtc-warning/${id}`;
    return this.#http.get<DTCWidget[]>(path);
  }
}
