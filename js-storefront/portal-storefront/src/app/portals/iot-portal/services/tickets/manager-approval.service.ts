import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ResponseRequestInfo } from '../../interfaces';
import { ManagerApprovalAction } from '../../enums';
import { environment } from '../../../../../environments/environment';

@Injectable()
export class ManagerApprovalService {
  #http = inject(HttpClient);

  prefixEnv = environment.OCC_BASE_URL + environment.OCC_PREFIX;

  createRequest(
    ticketId: string,
    payload: {
      action: ManagerApprovalAction;
      comment: string;
      assignee: string;
    }
  ): Observable<ResponseRequestInfo> {
    const path = `${this.prefixEnv}iot-portal/tickets/${ticketId}/request-manager-approval`;
    return this.#http.post<ResponseRequestInfo>(path, payload);
  }

  getRequestInfo(ticketId: string): Observable<ResponseRequestInfo> {
    const path = `${this.prefixEnv}iot-portal/tickets/${ticketId}/request-manager-approval`;
    return this.#http.get<ResponseRequestInfo>(path);
  }

  // approve (1) /reject (0) request manager
  actionOnRequest(
    ticketId: string,
    approved: 1 | 0
  ): Observable<ResponseRequestInfo> {
    const path = `${this.prefixEnv}iot-portal/tickets/${ticketId}/request-manager-approval`;
    return this.#http.put<ResponseRequestInfo>(
      path,
      {},
      {
        params: {
          approved,
        },
      }
    );
  }
}
