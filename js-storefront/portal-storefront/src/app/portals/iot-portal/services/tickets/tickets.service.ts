import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import {
  Assignee,
  FilterTickets,
  PayloadGetTicketList,
  ResponseTicketList,
  UpdateTicketPayload,
  VehiclePlateInfo,
} from '../../interfaces';
import { removeNullParams } from '../../../../core/helpers';
import { environment } from '../../../../../environments/environment';
import { WidgetResponse } from '../../../../core/interfaces';

@Injectable()
export class TicketsService {
  #http = inject(HttpClient);

  prefixEnv = environment.OCC_BASE_URL + environment.OCC_PREFIX;

  getTicketList(params: PayloadGetTicketList): Observable<ResponseTicketList> {
    const path = `${this.prefixEnv}iot-portal/tickets`;
    return this.#http.get<ResponseTicketList>(path, {
      params: removeNullParams({ ...params }),
    });
  }

  getWidgetTicket(): Observable<{ widgets: WidgetResponse[] }> {
    const path = `${this.prefixEnv}iot-portal/tickets/widget`;

    return this.#http.get<{
      widgets: WidgetResponse[];
    }>(path);
  }

  getFilter(): Observable<FilterTickets> {
    const path = `${this.prefixEnv}iot-portal/tickets/filter`;
    return this.#http.get<FilterTickets>(path);
  }

  getAssignee(searchText: string, groups: string): Observable<{ assignees: Assignee[] }> {
    const path = `${this.prefixEnv}iot-portal/tickets/assignees`;
    return this.#http.get<{ assignees: Assignee[] }>(path, {
      params: removeNullParams({
        name: searchText,
        limit: 10,
        groups
      }),
    });
  }

  updateTicketInfo(
    ticketId: string,
    payload: UpdateTicketPayload
  ): Observable<any> {
    const path = `${this.prefixEnv}iot-portal/tickets/${ticketId}`;
    return this.#http.put(path, payload);
  }

  createTicket(payload: {
    title: string;
    type: {
      code: string;
    };
    vin: string;
    description?: string;
    priority?: {
      code: string;
    };
    isDrivable: boolean;
    assignee?: {
      username: string;
    };
  }): Observable<{ ticketId: string }> {
    const path = `${this.prefixEnv}iot-portal/tickets`;
    return this.#http.post<{ ticketId: string }>(path, removeNullParams(payload));
  }

  getVehiclePlate(
    plateNumber: string
  ): Observable<VehiclePlateInfo[]> {
    const path = `${this.prefixEnv}iot-portal/e-care/vehicle/search`;
    return this.#http.get<VehiclePlateInfo[]>(path, { params: { plateNumber }});
  }
}
