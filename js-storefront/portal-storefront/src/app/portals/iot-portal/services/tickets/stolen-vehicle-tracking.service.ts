import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { SVTStatus } from '../../enums';
import { environment } from '../../../../../environments/environment';

@Injectable()
export class StolenVehicleTrackingService {
  #http = inject(HttpClient);

  prefixEnv = environment.OCC_BASE_URL + environment.OCC_PREFIX;

  getSVTStatus(vin: string): Observable<{
    type: string;
    message: string;
    result: boolean;
    svtStatus: SVTStatus;
  }> {
    const path = `${this.prefixEnv}iot-portal/e-care/vehicle/getSVTStatus`;
    return this.#http.get<{
      type: string;
      message: string;
      result: boolean;
      svtStatus: SVTStatus;
    }>(path, {
      params: { vin },
    });
  }

  updateSVT(
    vin: string,
    isSVT: 0 | 1
  ): Observable<{
    type: string;
    deviceType: string;
    result: string;
  }> {
    const path = `${this.prefixEnv}iot-portal/e-care/vehicle/updateSVT`;
    return this.#http.post<{
      type: string;
      deviceType: string;
      result: string;
    }>(path, {
      vin,
      isSVT,
    });
  }
}
