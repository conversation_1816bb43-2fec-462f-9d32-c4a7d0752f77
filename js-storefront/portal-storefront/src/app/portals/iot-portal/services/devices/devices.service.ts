import { HttpClient, HttpResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { catchError, filter, map, Observable } from 'rxjs';
import {
  FilterDevice,
  ItemDevice,
  PayloadGetDevice,
  ResponseDeviceList,
  ResponseEnableSim,
} from '../../interfaces/devices.interface';

import {  DeviceManagementTab } from '../../enums';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { environment } from '../../../../../environments/environment';
import { removeNullParams } from '../../../../core/helpers';
import { ActionModal } from '../../../../core/enums';
import { LoadingService, NotificationService } from '../../../../core/services';
import { DialogConfirmComponent } from '../../../../core/shared';
import { ExtendedResponseCommon, LastImportResult, ResponseCommon, WidgetResponse } from '../../../../core/interfaces';

@Injectable()
export class DeviceService {
  #http = inject(HttpClient);
  loadingService = inject(LoadingService);
  dialog = inject(MatDialog);
  notificationService = inject(NotificationService);
  translateService = inject(TranslateService);

  prefixEnv = environment.OCC_BASE_URL + environment.OCC_PREFIX;

  getDeviceList(
    params: PayloadGetDevice,
    deviceTab: DeviceManagementTab
  ): Observable<ResponseDeviceList> {
    const tab = deviceTab === DeviceManagementTab.LDCM ? 'ldcm' : 'gdcm';
    const path = `${this.prefixEnv}iot-portal/devices/${tab}`;
    return this.#http.get<ResponseDeviceList>(path, {
      params: removeNullParams({ ...params }),
    });
  }

  getDevicesFilter(): Observable<FilterDevice> {
    const path = `${this.prefixEnv}iot-portal/devices/filter`;
    return this.#http.get<FilterDevice>(path);
  }

  exportDevice(
    deviceTab: DeviceManagementTab
  ): Observable<{ blob: Blob; filename: string }> {
    const tab = deviceTab === DeviceManagementTab.LDCM ? 'ldcm' : 'gdcm';
    const path = `${this.prefixEnv}iot-portal/devices/${tab}/export`;
    return this.#http
      .get(path, {
        responseType: 'blob',
        observe: 'response',
      })
      .pipe(
        map((response: HttpResponse<Blob>) => {
          const contentDisposition = response.headers.get(
            'Content-Disposition'
          );
          let filename = 'devices.zip';
          if (contentDisposition) {
            const matches = /filename=([^;]+)/.exec(contentDisposition);
            if (matches != null && matches[1]) {
              filename =
                matches && matches[1] ? decodeURIComponent(matches[1]) : 'file';
            }
          }
          return { blob: response.body as Blob, filename };
        })
      );
  }

  deviceWidgets(
    deviceTab: DeviceManagementTab
  ): Observable<{ deviceType: string; widgets: WidgetResponse[] }> {
    const tab = deviceTab === DeviceManagementTab.LDCM ? 'ldcm' : 'gdcm';
    const path = `${this.prefixEnv}iot-portal/devices/${tab}/widget`;

    return this.#http.get<{
      deviceType: string;
      widgets: WidgetResponse[];
    }>(path);
  }

  importDevices(file: File): Observable<ResponseCommon> {
    const path = `${this.prefixEnv}iot-portal/devices/import`;
    const formData = new FormData();
    formData.append('file', file);
    return this.#http.post<ResponseCommon>(path, formData);
  }

  importSims(file: File): Observable<ResponseCommon> {
    const path = `${this.prefixEnv}iot-portal/sims/import`;
    const formData = new FormData();
    formData.append('file', file);
    return this.#http.post<ResponseCommon>(path, formData);
  }

  getLastImportResult(): Observable<LastImportResult> {
    const path = `${this.prefixEnv}iot-portal/devices/last-import-result`;

    return this.#http.get<LastImportResult>(path);
  }

  getLastImportSimResult(): Observable<LastImportResult> {
    const path = `${this.prefixEnv}iot-portal/sims/last-import-result`;
    return this.#http.get<LastImportResult>(path);
  }

  confirmPairing(
    deviceId: string,
    isLDCM: boolean
  ): Observable<ResponseCommon> {
    const tab = isLDCM ? 'ldcm' : 'gdcm';
    const path = `${this.prefixEnv}iot-portal/devices/${tab}/confirm-paring`;
    return this.#http.put<ResponseCommon>(
      path,
      {},
      {
        params: { deviceId },
      }
    );
  }

  terminateDevice(
    vin: string,
    deviceId: string,
    iccId: string
  ): Observable<ResponseCommon> {
    const path = `${this.prefixEnv}iot-portal/devices/gdcm/terminate`;
    return this.#http.delete<ResponseCommon>(path, {
      body: {
        vin,
        deviceId,
        iccId,
      },
    });
  }

  openConfirmPairing(
    device: ItemDevice,
    isLDCM: boolean,
    onSuccess: () => void
  ): void {
    const dialogRef = this.dialog.open(DialogConfirmComponent, {
      width: '530px',
      data: {
        title: this.translateService.instant('deviceAction.confirmPairing'),
        icon: 'ic-warning',
        confirmMsg: this.translateService.instant(
          'deviceAction.confirmPairingMsg'
        ),
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.confirm'),
      },
    });

    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe(() => {
        this.loadingService.showLoader();
        this.confirmPairing(device.deviceId, isLDCM).subscribe(
          (response) => {
            this.loadingService.hideLoader();
            this.notificationService.showSuccess(
              response?.message ||
                this.translateService.instant(
                  'deviceManagement.signalConfirmed'
                )
            );
            onSuccess();
          },
          (errorsRes) => {
            this.handleErrors(errorsRes);
          }
        );
      });
  }

  openTerminateDevice(
    vin: string,
    deviceId: string,
    iccId: string,
    onSuccess: () => void
  ): void {
    const dialogRef = this.dialog.open(DialogConfirmComponent, {
      width: '530px',
      data: {
        title: this.translateService.instant('deviceAction.confirmTermination'),
        icon: 'ic-warning',
        confirmMsg: this.translateService.instant(
          'deviceAction.confirmTerminationMsg',
          { deviceId }
        ),
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('deviceAction.terminate'),
      },
    });

    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe(() => {
        this.loadingService.showLoader();
        this.terminateDevice(vin, deviceId, iccId).subscribe(
          (response) => {
            this.loadingService.hideLoader();
            this.notificationService.showSuccess(
              response?.message ||
                this.translateService.instant(
                  'deviceManagement.deviceTerminated',
                  { deviceId: deviceId }
                )
            );
            onSuccess();
          },
          (err) => {
            const errorMsg =
              err?.error?.errors?.[0]?.message ||
              this.translateService.instant(
                'deviceManagement.deviceTerminatedFail'
              );
            this.notificationService.showError(errorMsg);
            this.loadingService.hideLoader();
          }
        );
      });
  }

  getModelSaleCodes(
    modelSaleCode: string
  ): Observable<{ allNewVehicles: string[]; allCodes: string[] }> {
    const path = `${this.prefixEnv}iot-portal/devices/ldcm/pair/search`;
    return this.#http
      .get<{ allNewVehicles: string[]; allCodes: string[] }>(path, {
        params: { modelSaleCode },
      })
      .pipe(
        map((response) => ({
          allNewVehicles: response.allNewVehicles.slice(0, 10),
          allCodes: response.allCodes.slice(0, 10),
        }))
      );
  }

  pairDevice(payload: {
    vin: string;
    deviceId: string;
    modelSaleCode: string;
  }): Observable<ExtendedResponseCommon> {
    const path = `${this.prefixEnv}iot-portal/devices/ldcm/pair`;

    return this.#http
      .put<ExtendedResponseCommon>(path, payload, {
        observe: 'response',
      })
      .pipe(
        map((response) => {
          if (response.status === 200 || response.status === 201) {
            return (
              response.body ||
              ({
                code: 'SUCCESS',
                message: 'successful',
              } as ExtendedResponseCommon)
            );
          } else {
            throw new Error(`Unexpected response status: ${response.status}`);
          }
        }),
        catchError((error) => {
          console.error('HTTP Error:', error);
          throw error;
        })
      );
  }

  enableSim(payload: {
    simId: string;
    vin: string;
    deviceId: string;
    deviceImei: string;
    simStatus: string;
  }): Observable<ResponseEnableSim> {
    const path = `${this.prefixEnv}iot-portal/sims/enable`;
    return this.#http.put<ResponseEnableSim>(path, payload);
  }

  disableSim(payload: {
    simId: string;
    vin: string;
    deviceId: string;
    deviceImei: string;
    simStatus: string;
  }): Observable<ResponseEnableSim> {
    const path = `${this.prefixEnv}iot-portal/sims/disable`;
    return this.#http.put<ResponseEnableSim>(path, payload);
  }

  openEnableSim(device: ItemDevice, onSuccess: () => void): void {
    const dialogRef = this.dialog.open(DialogConfirmComponent, {
      width: '530px',
      data: {
        title: this.translateService.instant('deviceAction.enableSIM'),
        icon: 'ic-warning',
        confirmMsg: this.translateService
          .instant('deviceAction.confirmEnabling')
          .replace('{{ simId }}', device.iccid),
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.enable'),
      },
    });

    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe(() => {
        this.loadingService.showLoader();
        const payload = {
          simId: device?.iccid,
          vin: device?.vin || device?.chasissNum,
          deviceId: device?.deviceId,
          deviceImei: device?.imei,
          simStatus: device?.canActiveFor,
        };
        this.enableSim(payload).subscribe(
          () => {
            this.loadingService.hideLoader();
            this.notificationService.showSuccess(
              this.translateService.instant(
                'deviceManagement.enableSimSuccess',
                {
                  simId: device?.iccid,
                }
              )
            );
            onSuccess();
          },
          (errorsRes) => {
            this.handleErrors(errorsRes);
          }
        );
      });
  }

  openDisableSim(device: ItemDevice, onSuccess: () => void): void {
    const dialogRef = this.dialog.open(DialogConfirmComponent, {
      width: '530px',
      data: {
        title: this.translateService.instant(
          'deviceAction.disableSimActionDevice'
        ),
        icon: 'ic-warning',
        confirmMsg: this.translateService
          .instant('deviceAction.disabledSimMsg')
          .replace('{{ simId }}', device.iccid),
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.disable'),
      },
    });

    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe(() => {
        this.loadingService.showLoader();
        const payload = {
          simId: device?.iccid,
          vin: device?.vin || device?.chasissNum,
          deviceId: device?.deviceId,
          deviceImei: device?.imei,
          simStatus: device?.simStatus?.code || device?.statusEnum?.code,
        };
        this.disableSim(payload).subscribe(
          () => {
            this.loadingService.hideLoader();
            this.notificationService.showSuccess(
              this.translateService.instant(
                'deviceManagement.disabledSimSuccess',
                {
                  simId: device?.iccid,
                }
              )
            );
            onSuccess();
          },
          (errorsRes) => {
            this.handleErrors(errorsRes);
          }
        );
      });
  }

  getDeviceDetails(id) {
    const path = `${this.prefixEnv}iot-portal/devices/detail`;
    return this.#http.get<ResponseCommon>(path, {
      params: {
        deviceId: id,
      },
    });
  }

  handleErrors(errorsRes): void {
    this.loadingService.hideLoader();
    const { error: { errors = [] } = {} } = errorsRes;
    this.notificationService.showError(
      errors?.map((error) => error.message)?.join(', ')
    );
  }

  downloadImportResult(
    code: string
  ): Observable<{ blob: Blob }> {

    const path = `${this.prefixEnv}iot-portal/import-result/download/${code}`;
    return this.#http
      .get(path, {
        responseType: 'blob',
        observe: 'response',
      })
      .pipe(
        map((response: HttpResponse<Blob>) => {
          return { blob: response.body as Blob };
        })
      );
  }

  uploadConsent(file: File, vin: string): Observable<ResponseCommon> {
    const path = `${this.prefixEnv}iot-portal/devices/consentFile/${vin}`;
    const formData = new FormData();
    formData.append('file', file);
    return this.#http.post<ResponseCommon>(path, formData);
  }
}
