import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class AddressService {
  constructor(
    private http: HttpClient,
  ) { }
  
  prefixEnv = environment.OCC_BASE_URL + environment.OCC_PREFIX;

  getRegionList(): Observable<any> {
    const path = `${this.prefixEnv}util/address/regions`;
    return this.http.get(path);
  }

  getProvinceByRegion(region: string): Observable<any> {
    const path = `${this.prefixEnv}util/address/provinces`;
    return this.http.get(path, {params: { region }});
  }

  getCitiesByProvince(province: string): Observable<any> {
    const path = `${this.prefixEnv}util/address/cities`;
    return this.http.get(path, {params: { province }});
  }

  getBarangaysByCity(city: string): Observable<any> {
    const path = `${this.prefixEnv}util/address/barangays`;
    return this.http.get(path, {params: { city }});
  }

  getZipCodesByProvince(province: string): Observable<any> {
    const path = `${this.prefixEnv}util/address/zipcodes`;
    return this.http.get(path, {params: { province }});
  }
}
