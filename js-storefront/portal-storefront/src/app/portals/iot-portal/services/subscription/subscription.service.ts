import { HttpClient, HttpResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import {
  catchError,
  filter,
  map,
  Observable,
  tap,
  throwError,
} from 'rxjs';

import {
  ListItemSubscription,
  SubscriptionDetail,
} from '../../interfaces/subscription.interface';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';

import { DatePipe } from '@angular/common';
import { LoadingService, NotificationService } from '../../../../core/services';
import { environment } from '../../../../../environments/environment';
import { LastImportResult, OptionDropdown, ResponseCommon, WidgetResponse } from '../../../../core/interfaces';
import { DialogConfirmComponent } from '../../../../core/shared';
import { ActionModal } from '../../../../core/enums';
import { handleErrors } from '../../../../core/helpers';

@Injectable()
export class SubscriptionService {
  http = inject(HttpClient);
  loadingService = inject(LoadingService);
  dialog = inject(MatDialog);
  datePipe = inject(DatePipe);
  notificationService = inject(NotificationService);
  translateService = inject(TranslateService);

  prefixEnv = environment.OCC_BASE_URL + environment.OCC_PREFIX;

  getWidgetList(): Observable<{ widgets: WidgetResponse[] }> {
    const path = `${this.prefixEnv}iot-portal/e-care/vehicle/subscriptions/widget`;
    return this.http.get<{ widgets: WidgetResponse[] }>(path);
  }

  getListSub(params: any): Observable<ListItemSubscription> {
    const path = `${this.prefixEnv}iot-portal/e-care/vehicle/subscriptions`;
    return this.http.get<ListItemSubscription>(path, { params });
  }

  getEndDateOpt() {
    return [
      {
        code: '',
        name: 'manageSub.endDateOpt.all',
      },
      {
        code: 'THIS_MONTH',
        name: 'manageSub.endDateOpt.thisMonth',
      },
      {
        code: 'NEXT_MONTH',
        name: 'manageSub.endDateOpt.nextMonth',
      },
      {
        code: 'NEXT_3_DAYS',
        name: 'manageSub.endDateOpt.next3Days',
      },
      {
        code: 'LAST_3_DAYS',
        name: 'manageSub.endDateOpt.last3Days',
      },
      {
        code: 'CUSTOM',
        name: 'manageSub.endDateOpt.custom',
      },
    ];
  }

  getMetadataFilter(): Observable<{
    vehicleType: OptionDropdown[];
    subStatus: OptionDropdown[];
    paymentStatus: OptionDropdown[];
  }> {
    const path = `${this.prefixEnv}iot-portal/e-care/vehicle/subscriptions/metadata`;
    return this.http.get<{
      vehicleType: OptionDropdown[];
      subStatus: OptionDropdown[];
      paymentStatus: OptionDropdown[];
    }>(path);
  }

  confirmSubscriptionPayment(vin: string) {
    const path = `${this.prefixEnv}iot-portal/e-care/vehicle/subscriptions/${vin}/confirm-payment`;
    return this.http.post(path, {});
  }

  importConfirmPayment(file: File): Observable<ResponseCommon> {
    const path = `${this.prefixEnv}iot-portal/e-care/vehicle/subscriptions/bulk-confirm-payment`;
    const formData = new FormData();
    formData.append('file', file);
    return this.http.post<ResponseCommon>(path, formData);
  }

  getSubcriptionDetail(vin: string): Observable<SubscriptionDetail> {
    const path = `${this.prefixEnv}iot-portal/e-care/vehicle/subscriptions/${vin}`;

    return this.http.get<SubscriptionDetail>(path);
  }

  activateSubscription(vin: string): Observable<any> {
    const path = `${this.prefixEnv}iot-portal/e-care/vehicle/subscriptions/${vin}/activate`;
    return this.http.post(path, {});
  }

  openActivateSubscription(
    vin: string,
    isValidForSubscriptionEnable: boolean,
    onSuccess: () => void
  ): void {
    if (!isValidForSubscriptionEnable) {
      this.dialog.open(DialogConfirmComponent, {
        width: '530px',
        data: {
          title: this.translateService.instant(
            'manageSub.subscriptionAction.activateSubscription'
          ),
          icon: 'ic-warning',
          confirmMsg: this.translateService.instant(
            'manageSub.subscriptionAction.loanEndDateEarlierToday'
          ),
          cancelBtn: this.translateService.instant('common.close'),
        },
      });
      return;
    }

    const dialogRef = this.dialog.open(DialogConfirmComponent, {
      width: '530px',
      data: {
        title: this.translateService.instant(
          'manageSub.subscriptionAction.activateSubscription'
        ),
        icon: 'ic-warning',
        confirmMsg: this.translateService.instant(
          'manageSub.subscriptionAction.activateSubscriptionMsg'
        ),
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.confirm'),
      },
    });

    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe(() => {
        this.loadingService.showLoader();
        this.activateSubscription(vin)
          .pipe(
            tap(() => {
              this.loadingService.hideLoader();
              onSuccess();
            })
          )
          .subscribe({
            next: () => {
              this.notificationService.showSuccess(
                this.translateService.instant(
                  'manageSub.subscriptionAction.activateSubscriptionSuccess'
                )
              );
            },
            error: (error) => {
              this.loadingService.hideLoader();
              handleErrors(error, this.notificationService);
            },
          });
      });
  }

  openConfirmSubscriptionPayment(vin: string, onSuccess: () => void): void {
    const dialogRef = this.dialog.open(DialogConfirmComponent, {
      width: '530px',
      data: {
        title: this.translateService.instant(
          'manageSub.subscriptionAction.confirmSubscriptionPayment'
        ),
        icon: 'ic-warning',
        confirmMsg: this.translateService.instant(
          'manageSub.subscriptionAction.confirmSubscriptionPaymentMsg',
          { vin: vin }
        ),
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.confirm'),
      },
    });

    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe(() => {
        this.loadingService.showLoader();
        this.confirmSubscriptionPayment(vin)
          .pipe(
            tap(() => {
              this.loadingService.hideLoader();
              onSuccess();
            })
          )
          .subscribe({
            next: () => {
              this.notificationService.showSuccess(
                this.translateService.instant(
                  'manageSub.subscriptionAction.confirmSubscriptionPaymentSuccess'
                )
              );
            },
            error: (error) => {
              this.loadingService.hideLoader();
              handleErrors(error, this.notificationService);
            },
          });
      });
  }

  getLastImportResult(): Observable<LastImportResult> {
    const path = `${this.prefixEnv}iot-portal/e-care/vehicle/subscriptions/last-import-result`;

    return this.http.get<LastImportResult>(path);
  }

  downloadImportResult(code: string): Observable<Blob> {
    const path = `${this.prefixEnv}iot-portal/import-result/download/${code}`;
    return this.http
      .get(path, {
        responseType: 'blob',
        observe: 'response',
      })
      .pipe(
        map((response: HttpResponse<Blob>) => {
          return response.body as Blob;
        }),
        catchError((error) => {
          this.notificationService.showError(
            this.translateService.instant('common.dowloadFail')
          );
          return throwError(() => error);
        })
      );
  }
}
