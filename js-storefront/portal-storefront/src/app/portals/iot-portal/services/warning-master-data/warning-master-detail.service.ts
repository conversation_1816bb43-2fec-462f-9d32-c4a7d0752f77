import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { WarningMasterDataDetail } from '../../interfaces';
import { environment } from '../../../../../environments/environment';
import { OptionDropdown } from '../../../../core/interfaces';

@Injectable()
export class WarningMasterDetailService {
  #http = inject(HttpClient);
  prefixEnv = environment.OCC_BASE_URL + environment.OCC_PREFIX;

  getWarningMasterDetail(
    warningCode: string
  ): Observable<WarningMasterDataDetail> {
    const path = `${this.prefixEnv}iot-portal/warning-master/detail`;
    return this.#http.get<WarningMasterDataDetail>(path, {
      params: { warningCode },
    });
  }

  createWarningMaster(
    payload: WarningMasterDataDetail
  ): Observable<WarningMasterDataDetail> {
    const path = `${this.prefixEnv}iot-portal/warning-master`;
    return this.#http.post<WarningMasterDataDetail>(path, payload);
  }

  getMetadata(): Observable<{
    priorities: OptionDropdown[],
    types: OptionDropdown[],
  }> {
    const path = `${this.prefixEnv}iot-portal/warning-master/metadata`;
    
    return this.#http.get<{
      priorities: OptionDropdown[],
      types: OptionDropdown[],
    }>(path);
  }

  updateWarningMaster(
    warningCode: string,
    payload: WarningMasterDataDetail
  ): Observable<WarningMasterDataDetail> {
    const path = `${this.prefixEnv}iot-portal/warning-master/${warningCode}`;
    return this.#http.put<WarningMasterDataDetail>(path, payload);
  }
}
