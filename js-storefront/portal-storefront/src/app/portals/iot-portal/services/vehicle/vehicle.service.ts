import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import {
  DTCWidget,
  ItemDevice,
  ItemSubscription,
  OwnershipVerificationWidget,
  OwnerWidget,
  ResponseList,
  ResponseSubscriptionList,
  TicketWidget,
} from '../../interfaces';

import { Ticket, Vehicle } from '../../interfaces/ticket-detail.interface';
import { environment } from '../../../../../environments/environment';
import { OptionDropdown, ResponseCommon, ResponseResultCommon, SectionList } from '../../../../core/interfaces';
import { removeNullParams } from '../../../../core/helpers';
import { Emergency, HistoryLog, LoanItem, PMSWidget, ServiceHistory, Trip } from '../../../../core/interfaces/vehicle.interface';

@Injectable()
export class VehicleService {
  #http = inject(HttpClient);
  prefixEnv = environment.OCC_BASE_URL + environment.OCC_PREFIX;

  $vehicleDetail = new BehaviorSubject<Vehicle>(null);

  getVehicleWidget(vin: string): Observable<Vehicle> {
    const path = `${this.prefixEnv}iot-portal/e-care/vehicle/${vin}`;
    return this.#http.get<Vehicle>(path);
  }

  getLoanWidget(vin: string): Observable<LoanItem> {
    const path = `${this.prefixEnv}iot-portal/loan/vehicles/detail`;
    return this.#http.get<LoanItem>(path, {
      params: {
        vin,
      },
    });
  }
  getTicketWidget(vin: string): Observable<TicketWidget> {
    const path = `${this.prefixEnv}iot-portal/e-care/vehicle/${vin}/tickets`;
    return this.#http.get<TicketWidget>(path);
  }

  getDeviceWidget(id: string): Observable<ItemDevice> {
    const path = `${this.prefixEnv}iot-portal/devices/${id}/get-device-widget`;
    return this.#http.get<ItemDevice>(path);
  }

  getSubscriptionWidget(id: string): Observable<ItemSubscription> {
    const path = `${this.prefixEnv}iot-portal/e-care/vehicle/subscriptions/latest`;
    return this.#http.get<ItemSubscription>(path, { params: { vin: id } });
  }

  getPMSWidget(vin: string): Observable<PMSWidget> {
    const path = `${this.prefixEnv}iot-portal/e-care/vehicle/pms/nearest`;
    return this.#http.get<PMSWidget>(path, {
      params: {
        vin,
      },
    });
  }

  getResolution(): Observable<OptionDropdown[]> {
    const path = `${this.prefixEnv}iot-portal/e-care/notifications/emergencies/resolution`;
    return this.#http.get<OptionDropdown[]>(path);
  }

  closeTheEmergency(params: {
    vin: string;
    ticketId: string;
    resolution: {
      code: string;
      name: string;
    };
    comment: string;
  }): Observable<ResponseResultCommon> {
    const path = `${this.prefixEnv}iot-portal/e-care/notifications/emergencies/close`;
    return this.#http.post<ResponseResultCommon>(path, params);
  }

  getEmergencyList(params: {
    vin: string;
    type: string;
    currentPage: number;
    pageSize: number;
  }): Observable<SectionList<Emergency>> {
    const path = `${this.prefixEnv}iot-portal/e-care/notifications/emergencies`;
    return this.#http.get<SectionList<Emergency>>(path, { params });
  }

  getServiceHistoryList(
    params: {
      repairOrderNo: string;
      jobDescription: string;
      currentPage: number;
      pageSize: number;
    },
    vin
  ): Observable<SectionList<ServiceHistory>> {
    const path = `${this.prefixEnv}iot-portal/e-care/vehicle/repairorder/${vin}`;
    return this.#http.get<SectionList<ServiceHistory>>(path, { params });
  }

  getTicketList(params: {
    vin: string;
    title: string;
    ticketId: string;
    currentPage: number;
    pageSize: number;
  }): Observable<ResponseList<Ticket>> {
    const path = `${this.prefixEnv}iot-portal/tickets`;
    return this.#http.get<ResponseList<Ticket>>(path, { params });
  }

  getDeviceList(
    params: {
      currentPage: number;
      pageSize: number;
    },
    vin: string
  ): Observable<ResponseList<ItemDevice>> {
    const path = `${this.prefixEnv}iot-portal/devices/${vin}/get-device-list`;
    return this.#http.get<ResponseList<ItemDevice>>(path, { params });
  }

  refreshCurrentLocation(vin: string): Observable<Vehicle> {
    const path = `${this.prefixEnv}iot-portal/e-care/vehicle/${vin}/refresh`;
    return this.#http.get<Vehicle>(path);
  }

  getWarningListForVehicle(vin: string): Observable<any> {
    const path = `${this.prefixEnv}iot-portal/e-care/notifications?pageSize=5&vin=${vin}`;
    return this.#http.get<any>(path);
  }

  getOwnerWidget(vin: string, customerId: string): Observable<OwnerWidget> {
    const path = `${this.prefixEnv}iot-portal/e-care/vehicle/owner-detail`;
    return this.#http.get<OwnerWidget>(path, {
      params: removeNullParams({ vin, customerId }),
    });
  }

  getOwnershipVerificationWidget(vin: string): Observable<{enabledOwnership: boolean, ownerVerificationHistory: OwnershipVerificationWidget[]}> {
    const path = `${this.prefixEnv}iot-portal/e-care/vehicle/${vin}/ownerVerificationHistory`;
    return this.#http.get<{enabledOwnership: boolean, ownerVerificationHistory: OwnershipVerificationWidget[]}>(path);
  }

  getSubscriptions(params: {
    vin: string;
    currentPage: number;
    pageSize: number;
  }): Observable<ResponseSubscriptionList> {
    const path = `${this.prefixEnv}iot-portal/e-care/vehicle/subscriptions/tab`;
    return this.#http.get<ResponseSubscriptionList>(path, { params });
  }

  getSubscriptionPackageFeatures(id: string): Observable<any> {
    // TODO
    const path = `${this.prefixEnv}iot-portal/`;
    return this.#http.get<ResponseSubscriptionList>(path);
  }

  verifyEmail(vin: string): Observable<any> {
    const path = `${this.prefixEnv}iot-portal/e-care/vehicle/verifyOwnerEmail?vin=${vin}`;
    return this.#http.post<any>(path, null);
  }
  getTripList(params: {
    vin: string;
    tripDate: string;
  }): Observable<SectionList<Trip>> {
    const path = `${this.prefixEnv}iot-portal/e-care/vehicle/trips`;
    return this.#http.get<SectionList<Trip>>(path, { params: removeNullParams(params) });
  }

  getTripDetails(vin: string, tripId: string, payload: {
    id: string;
    fuelConsumption: string;
    totalDistance: string;
    drivingTimeHour: string;
    drivingTimeMinutes: string;
  }): Observable<Trip> {
    const path = `${this.prefixEnv}iot-portal/e-care/vehicle/${vin}/trips/${tripId}`;
    return this.#http.post<Trip>(path, payload);
  }

  enableLoan(isWidget: boolean, body: any, vin?: string): Observable<ResponseCommon> {
    let path = `${this.prefixEnv}iot-portal/e-care/vehicle/enableLoan?vin=${vin}`;
    if (!isWidget) {
      path = `${this.prefixEnv}iot-portal/loan/vehicles/enableLoan`;
    }
    return this.#http.post<ResponseCommon>(path, body);
  }

  updateLoan(body: any, vin): Observable<any> {
    const path = `${this.prefixEnv}iot-portal/loan/vehicles/updateLoan?vin=${vin}`;
    return this.#http.put<ResponseCommon>(path, body);
  }

  getHistoryLogWidget(vin: string): Observable<HistoryLog[]> {
    const path = `${this.prefixEnv}iot-portal/e-care/vehicle/${vin}/history`;
    return this.#http.get<HistoryLog[]>(path, { params: { vin } });
  }

  getEmergenciesType(): Observable<OptionDropdown[]> {
    const path = `${this.prefixEnv}iot-portal/e-care/notifications/emergencies/type`;
    return this.#http.get<OptionDropdown[]>(path);
  }
}
