import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { ResponseServiceCampaignsList } from '../../interfaces';
import { environment } from '../../../../../environments/environment';
@Injectable()
export class ServiceCampaignsService {
  #http = inject(HttpClient);

  prefixEnv = environment.OCC_BASE_URL + environment.OCC_PREFIX;

  getServiceCampaigns(params: {
    vin: string;
    currentPage: number;
    pageSize: number;
  }): Observable<ResponseServiceCampaignsList> {
    const path = `${this.prefixEnv}iot-portal/e-care/vehicle/ssc`;
    return this.#http.get<ResponseServiceCampaignsList>(path, { params });
  }
}
