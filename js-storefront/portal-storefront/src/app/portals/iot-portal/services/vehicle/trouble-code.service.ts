import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { map, Observable, of } from 'rxjs';

import { TranslateService } from '@ngx-translate/core';
import { ColDef } from 'ag-grid-community';
import { environment } from '../../../../../environments/environment';
import { ActionCellRendererComponent } from '../../features/vehicle-model/cell-renderer/action-cell-renderer.component';
import { removeNullParams } from '../../../../core/helpers';
import { OptionDropdown, SectionList } from '../../../../core/interfaces';
import { DefaultValuesRequestLatest, TroubleCodeDetail } from '../../../../core/interfaces/vehicle.interface';
@Injectable()
export class TroubleCodeService {
  http = inject(HttpClient);
  translateService = inject(TranslateService);

  prefixEnv = environment.OCC_BASE_URL + environment.OCC_PREFIX;

  getColumnDefs(viewFFD: (data: any) => void): ColDef[] {
    return [
      {
        headerName: this.translateService.instant(
          'troubleCode.dtcList.warningOccurrenceTime'
        ),
        headerValueGetter: () =>
          this.translateService.instant(
            'troubleCode.dtcList.warningOccurrenceTime'
          ),
        field: 'warningOccurrenceTime',
        flex: 1,
        cellClass: 'cell-word-wrap',
        cellRenderer: (params) => this.customCellRenderer(params),
      },
      {
        headerName: this.translateService.instant(
          'troubleCode.dtcList.diagnosticTime'
        ),
        headerValueGetter: () =>
          this.translateService.instant('troubleCode.dtcList.diagnosticTime'),
        field: 'diagnosticTime',
        flex: 1,
        cellRenderer: (params) => this.customCellRenderer(params),
      },
      {
        headerName: this.translateService.instant(
          'troubleCode.dtcList.lastRecordReceivedTime'
        ),
        headerValueGetter: () =>
          this.translateService.instant(
            'troubleCode.dtcList.lastRecordReceivedTime'
          ),
        field: 'lastRecordReceivedTime',
        flex: 1,
        cellRenderer: (params) => this.customCellRenderer(params),
      },
      {
        headerName: this.translateService.instant(
          'troubleCode.dtcList.dataCollectionStatus'
        ),
        headerValueGetter: () =>
          this.translateService.instant(
            'troubleCode.dtcList.dataCollectionStatus'
          ),
        field: 'dataCollectionStatus',
        flex: 1,
        cellRenderer: (params) => this.customCellRenderer(params),
      },
      {
        headerName: this.translateService.instant('troubleCode.dtcList.nodeName'),
        headerValueGetter: () =>
          this.translateService.instant('troubleCode.dtcList.nodeName'),
        field: 'ecuName',
        flex: 1,
      },
      {
        headerName: this.translateService.instant(
          'troubleCode.dtcList.systemName'
        ),
        headerValueGetter: () =>
          this.translateService.instant('troubleCode.dtcList.systemName'),
        field: 'dtcName',
        flex: 1,
      },
      {
        headerName: this.translateService.instant('troubleCode.dtcList.dtc'),
        headerValueGetter: () =>
          this.translateService.instant('troubleCode.dtcList.dtc'),
        field: 'dtc',
        flex: 1,
      },
      {
        flex: 1,
        cellRenderer: ActionCellRendererComponent,
        cellRendererParams: {
          actionDetail: {
            icon: 'ic-view',
            text: 'troubleCode.viewFFD',
          },
          onClick: (data) => viewFFD(data),
        },
        cellClass: 'action-grid action-last-grid',
        cellClassRules: {
          'hide-action': (params) => !params.data?.ssrID,
        },
      },
    ];
  }

  spanRowsCondition(param): boolean {
    const { valueA, valueB, nodeA, nodeB } = param;
    return (
      nodeA?.data?.dtc === nodeB?.data?.dtc &&
      nodeA?.data?.warningOccurrenceTime ===
        nodeB?.data?.warningOccurrenceTime &&
      nodeA?.data?.diagnosticTime === nodeB?.data?.diagnosticTime &&
      nodeA?.data?.lastRecordReceivedTime ===
        nodeB?.data?.lastRecordReceivedTime &&
      nodeA?.data?.dataCollectionStatus === nodeB?.data?.dataCollectionStatus
    );
  }

  customCellRenderer(params) {
    const currentIndex = params.node.rowIndex;
    const previousNode = params.api.getDisplayedRowAtIndex(currentIndex - 1);
  
    if (previousNode && previousNode.data?.index === params.data?.index) {
      return '-';
    }
    return params.value;
  }


  getTroubleCodeList(
    vin,
    params: any
  ): Observable<any> {
    const path = `${this.prefixEnv}iot-portal/e-care/notifications/${vin}/dtc/list`;

    return this.http
      .get<SectionList<any>>(path, { params: removeNullParams(params) })
      .pipe(
        map((response: any) => ({
          items: response?.flatMap((item, index) => {
            const { dtcItems, ...properties } = item || {};
            if (dtcItems?.length > 0) {
              return dtcItems?.map((dtcItem) => {
                const { dtc, ...propertiesDTCItems } = dtcItem || {};
                return {
                  ...properties,
                  ...propertiesDTCItems,
                  dtcOfItem: dtc,
                  index,
                };
              });
            }
            return {
              ...properties,
              index,
            };
          }),
        }))
      );
  }

  getTroubleCodeDetail(vin: string, id: string): Observable<TroubleCodeDetail> {
    const path = `${this.prefixEnv}iot-portal/e-care/vehicle/${vin}/ssr/${id}`;
    return this.http.get<TroubleCodeDetail>(path, {
      params: { currentPage: 0, pageSize: 10 },
    });
  }

  requestDTC(
    vin: string,
    payload: {
      centerRequestType: number;
      retry: number;
      timeout: number;
      expirationTime: number;
    }
  ): Observable<any> {
    const path = `${this.prefixEnv}iot-portal/e-care/vehicle/${vin}/allDtcSsrCenterRequest`;
    return this.http.post<any>(path, removeNullParams(payload));
  }

  getCenterRequestType(vin: string): Observable<{
    centerRequestTypes: OptionDropdown[];
    defaultValues: DefaultValuesRequestLatest;
  }> {
    const path = `${this.prefixEnv}iot-portal/e-care/vehicle/dtcSSRRequest/${vin}/defaultValue`;
  
    return this.http.get<{
      centerRequestTypes: OptionDropdown[];
      defaultValues: DefaultValuesRequestLatest;
    }>(path);
  }
}
