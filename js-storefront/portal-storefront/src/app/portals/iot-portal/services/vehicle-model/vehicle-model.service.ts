import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { map, Observable } from 'rxjs';
import {
  AddVehicleModel,
  VehicleModelList,
  VehicleModelListRequest,
} from '../../interfaces';
import { environment } from '../../../../../environments/environment';
import { LastImportResult, ResponseCommon } from '../../../../core/interfaces';

@Injectable()
export class VehicleModelService {
  #http = inject(HttpClient);
  prefixEnv = environment.OCC_BASE_URL + environment.OCC_PREFIX;

  getVehicleModelList(
    params: VehicleModelListRequest | any
  ): Observable<VehicleModelList> {
    const path = `${this.prefixEnv}iot-portal/vehicle-models`;
    return this.#http.get<VehicleModelList>(path, { params });
  }

  addVehicleModel(data: AddVehicleModel | any): Observable<ResponseCommon> {
    const path = `${this.prefixEnv}iot-portal/vehicle-models/create-new-models`;
    return this.#http.post<ResponseCommon>(path, null, {observe: 'response', params: data}).pipe(map((res: any) => {
      if (res.status === 201) {
        const dataRes: ResponseCommon = {
          code: 'SUCCESS',
          message: 'vehicleModel.newModelSalesCodeAdded'
        }
        return dataRes;
      }
      return res
    }));
  }

  updateVehicleModel(
    data: AddVehicleModel | any,
    id: string
  ): Observable<ResponseCommon> {
    const path = `${this.prefixEnv}iot-portal/vehicle-models/update-models/${id}`;
    return this.#http.post<ResponseCommon>(path, null, {observe: 'response', params: data}).pipe(map((res: any) => {
      if (res.status === 200) {
        const dataRes: ResponseCommon = {
          code: 'SUCCESS',
          message: 'vehicleModel.modelSalesCodeUpdated'
        }
        return dataRes;
      }
      return res
    }));
  }

  deleteVehicleModel(id: string): Observable<ResponseCommon> {
    const path = `${this.prefixEnv}iot-portal/vehicle-models/remove-models/${id}`;
    return this.#http.delete<ResponseCommon>(path, {observe: 'response'}).pipe(map((res: any) => {
      if (res.status === 200) {
        const dataRes: ResponseCommon = {
          code: 'SUCCESS',
          message: 'vehicleModel.modelSalesCodeRemoved'
        }
        return dataRes;
      }
      return res
    }));
  }

  importVehicleModel(file: File): Observable<ResponseCommon> {
    const path = `${this.prefixEnv}iot-portal/vehicle-models/import-vehicles`;
    const formData = new FormData();
    formData.append('file', file);
    return this.#http.post<ResponseCommon>(path, formData, {observe: 'response'}).pipe(map((res: any) => {
      if (res.status === 200) {
        const dataRes: ResponseCommon = {
          code: 'SUCCESS',
          message: 'vehicleModel.fileImportedSuccessfully'
        }
        return dataRes;
      }
      return res
    }));;
  }

  getLastImpot(): Observable<LastImportResult> {
    const path = `${this.prefixEnv}iot-portal/vehicle-models/last-import-result`;
    return this.#http.get<LastImportResult>(path);
  }
}
