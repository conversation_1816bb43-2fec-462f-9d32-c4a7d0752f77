import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ListItemLoan } from '../../interfaces/loan.interface';
import { environment } from '../../../../../environments/environment';
import { removeNullParams } from '../../../../core/helpers';
import { OptionDropdown, WidgetResponse } from '../../../../core/interfaces';

@Injectable()

export class LoanService {
  constructor(
    private http: HttpClient
  ) { }

  prefixEnv = environment.OCC_BASE_URL + environment.OCC_PREFIX;

  getWidgetList(): Observable<{ widgets: WidgetResponse[] }> {
    const path = `${this.prefixEnv}iot-portal/loan/vehicles/widget`;
    return this.http.get<{ widgets: WidgetResponse[] }>(path);
  }

  getListLoan(params: {
    currentPage: string;
    pageSize: string;
    vin: string;
    loanID: string;
    status: string;
  }): Observable<ListItemLoan> {
    const path = `${this.prefixEnv}iot-portal/loan/vehicles/list`;
    return this.http.get<ListItemLoan>(path, {params: removeNullParams({ ...params })});
  }

    getFilter(): Observable<OptionDropdown[]> {
      const path = `${this.prefixEnv}iot-portal/loan/vehicles/status`;
      return this.http.get<OptionDropdown[]>(path);
    }
}
