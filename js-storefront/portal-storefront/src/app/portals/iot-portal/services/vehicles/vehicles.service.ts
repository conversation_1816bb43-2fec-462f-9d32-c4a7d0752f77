import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../../environments/environment';
import { removeNullParams } from '../../../../core/helpers';
import { VehicleRespon } from '../../../../core/interfaces/vehicle.interface';
import { WidgetResponse } from '../../../../core/interfaces';

@Injectable({
  providedIn: 'root',
})
export class VehicleService {
  #http = inject(HttpClient);
  prefixEnv = environment.OCC_BASE_URL + environment.OCC_PREFIX;

  getVehicles(params: {
    vinOrPlateNo?: string;
    customerNameOrPhone?: string;
    filterActiveEmergency?: boolean;
    filterHighWarning?: boolean;
    filterSSC?: boolean;
    pageSize: number;
    currentPage: number;
  }): Observable<VehicleRespon> {
    const path = `${this.prefixEnv}iot-portal/e-care/vehicle/list`;
    return this.#http.get<VehicleRespon>(path, {
      params: removeNullParams({ ...params }),
    });
  }

  getWidget(): Observable<{ widgets: WidgetResponse[] }> {
    const path = `${this.prefixEnv}iot-portal/e-care/vehicle/widget`;
    return this.#http.get<{
      widgets: WidgetResponse[];
    }>(path);
  }
}
