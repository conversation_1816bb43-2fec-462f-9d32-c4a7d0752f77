import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { catchError, map, Observable, of, throwError } from 'rxjs';
import {
  AvailableDates,
  MetadataServiceBooking,
  ResponseServiceBookingList,
  ServiceBookingDetail,
} from '../interfaces';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { NotificationService } from '../../../core/services';
import { environment } from '../../../../environments/environment';
import { handleErrors, removeNullParams } from '../../../core/helpers';
import { DialogConfirmComponent } from '../../../core/shared';
import { OptionDropdown } from '../../../core/interfaces';

@Injectable()
export class ServiceBookingService {
  #http = inject(HttpClient);
  #notificationService = inject(NotificationService);

  dialog = inject(MatDialog);
  translateService = inject(TranslateService);

  prefixEnv = environment.OCC_BASE_URL + environment.OCC_PREFIX;

  getAllServiceBookings(params: {
    vin: string;
    currentPage?: number;
    pageSize?: number;
    status?: string;
    id?: string;
  }): Observable<ResponseServiceBookingList> {
    const path = `${this.prefixEnv}iot-portal/service-bookings`;

    return this.#http.get<ResponseServiceBookingList>(path, {
      params: removeNullParams(params),
    });
  }

  getMetadata(vin: string): Observable<MetadataServiceBooking> {
    const path = `${this.prefixEnv}iot-portal/service-bookings/metadata`;
    return this.#http.get<MetadataServiceBooking>(path, {
      params: {
        vin,
      },
    });
  }

  getAvailableDates(
    vin: string,
    bookingReason: string
  ): Observable<{ availableBookingDays: string[] }> {
    const path = `${this.prefixEnv}iot-portal/service-bookings/available-dates`;
    return this.#http
      .get<AvailableDates>(path, {
        params: {
          vin,
          bookingReason,
        },
      })
      .pipe(
        map((response) => ({
          availableBookingDays: response?.availableBookingDays?.split(','),
        })),
        catchError((error) => {
          handleErrors(error, this.#notificationService);
          return throwError(() => error);
        })
      );
  }

  getAvailableHours(
    vin: string,
    bookingReason: string,
    dateStr: string
  ): Observable<OptionDropdown[]> {
    const path = `${this.prefixEnv}iot-portal/service-bookings/available-hours`;
    return this.#http
      .get<OptionDropdown[]>(path, {
        params: {
          vin,
          bookingReason,
          dateStr,
        },
      })
      .pipe(
        map((response) =>
          response.map((item) => ({
            name: item.name,
            code: item.name,
          }))
        ),
        catchError((error) => {
          handleErrors(error, this.#notificationService);
          return throwError(() => error);
        })
      );
  }

  getAvailablePickupTime(
    bookingDate: string,
    pickupDate: string,
    bookingTime: string,
    dealerName: string
  ): Observable<OptionDropdown[]> {
    const path = `${this.prefixEnv}iot-portal/service-bookings/available-pickup-times`;
    return this.#http
      .get<OptionDropdown[]>(path, {
        params: {
          bookingDate,
          pickupDate,
          bookingTime,
          dealerName,
        },
      })
      .pipe(
        map((response) =>
          response.map((item) => ({
            name: item.name,
            code: item.name,
          }))
        ),
        catchError((error) => {
          handleErrors(error, this.#notificationService);
          return throwError(() => error);
        })
      );
  }

  createServiceBooking(
    payload: {
      bookingDate: string;
      bookingTimeSlot: string;
      waitPickupAtDealer: boolean;
      bookingReason: string;
      mileAge: string;
      servicePickupDate: string;
      servicePickupTimeSlot: string;
      remark: string;
      vin: string;
      dealerName: string;
    },
    isEdit: boolean,
    bookingCode: string
  ): Observable<ServiceBookingDetail> {
    if (isEdit) {
      const path = `${this.prefixEnv}iot-portal/service-bookings/${bookingCode}`;
      return this.#http.put<ServiceBookingDetail>(path, payload);
    } else {
      const path = `${this.prefixEnv}iot-portal/service-bookings`;
      return this.#http.post<ServiceBookingDetail>(path, payload);
    }
  }
  cancelServiceBooking(
    id: string,
    payload: {
      cancelBookingReason: string;
      cancelBookingReasonDescription: string;
    }
  ): Observable<any> {
    const path = `${this.prefixEnv}iot-portal/service-bookings/${id}/cancel`;
    return this.#http.put(path, payload);
  }

  getServiceBookingsDetail(
    serviceBookingId: string
  ): Observable<ServiceBookingDetail> {
    const path = `${this.prefixEnv}iot-portal/service-bookings/${serviceBookingId}`;

    return this.#http.get<ServiceBookingDetail>(path);
  }

  getServiceBookingsSatus(): Observable<OptionDropdown[]> {
    const path = `${this.prefixEnv}iot-portal/service-bookings/status`;

    return this.#http.get<OptionDropdown[]>(path);
  }

  activeCustomerRelation(vin: string): void {
    this.dialog.open(DialogConfirmComponent, {
      width: '610px',
      data: {
        title: this.translateService.instant(
          'serviceBooking.serviceBookingDenied'
        ),
        icon: 'ic-warning',
        confirmMsg: this.translateService.instant(
          'serviceBooking.msgServiceBookingDenied',
          {
            vin,
          }
        ),
        cancelBtn: this.translateService.instant('common.close'),
      },
    });
  }
}
