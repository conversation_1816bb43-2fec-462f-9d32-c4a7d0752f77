import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { filter, Observable } from 'rxjs';
import {
  WarningList,
  WarningListRequest,
  WarningDetail,
  WarningMasterListRequest,
  WarningMasterList,
} from '../../interfaces';
import { TranslateService } from '@ngx-translate/core';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { LoadingService, NotificationService } from '../../../../core/services';
import { environment } from '../../../../../environments/environment';
import { DialogConfirmComponent } from '../../../../core/shared';
import { ActionModal } from '../../../../core/enums';
import { handleErrors } from '../../../../core/helpers';

@Injectable({
  providedIn: 'root',
})
export class WarningService {
  constructor(
    private http: HttpClient,
    private translateService: TranslateService,
    private dialog: MatDialog,
    protected notificationService: NotificationService,
    private loadingService: LoadingService,
    private router: Router
  ) {}

  prefixEnv = environment.OCC_BASE_URL + environment.OCC_PREFIX;

  getWarningList(params: WarningListRequest | any): Observable<WarningList> {
    const path = `${this.prefixEnv}iot-portal/e-care/notifications/warnings`;
    return this.http.get<WarningList>(path, { params });
  }

  getWarningDetail(id: string): Observable<WarningDetail> {
    const path = `${this.prefixEnv}iot-portal/e-care/notifications/warnings/${id}`;
    return this.http.get<WarningDetail>(path);
  }

  getPriorityStyle(code: string): string {
    switch (code) {
      case 'CRITICAL':
      case 'HIGH_CRITICAL':
      case 'HIGH':
        return `<span class="value-tag red-tag">${this.translateService.instant(
          `vehicle.warningTab.priority.${code}`
        )}</span>`;
      case 'MEDIUM':
        return `<span class="value-tag yellow-tag">${this.translateService.instant(
          `vehicle.warningTab.priority.${code}`
        )}</span>`;
      case 'DISPLAY':
      case 'LOW':
        return `<span class="value-tag grey-tag">${this.translateService.instant(
          `vehicle.warningTab.priority.${code}`
        )}</span>`;
      default:
        return '-';
    }
  }

  getStatusStyle(code: string): string {
    return code
      ? `<span class="value-tag grey-tag">${this.translateService.instant(
          `vehicle.warningTab.status.${code}`
        )}</span>`
      : '-';
  }

  getWarningDetailDefault(): WarningDetail {
    return {
      id: '-',
      name: '-',
      type: '-',
      priority: { code: '-', name: '-' },
      status: { code: '-', name: '-' },
      relatedTicketId: '-',
      relatedTicketTitle: '-',
      timestamp: '-',
      callCustomer: false,
      customerAction: '-',
      details: '-',
      mid: '-',
      notificationPromptCustAction: '-',
      notificationPromptDesc: '-',
      notificationPromptTitle: '-',
      notifyCustomer: false,
      requiredDealerVisit: false,
      responseLeadTime: '-',
      vehicleCondition: false,
      code: '-',
      safeCondition: '-',
    };
  }

  getWarningMasterList(
    params: WarningMasterListRequest | any
  ): Observable<WarningMasterList> {
    const path = `${this.prefixEnv}iot-portal/warning-master`;
    return this.http.get<WarningMasterList>(path, { params });
  }

  deleteWarningMaster(id: string): Observable<any> {
    const path = `${this.prefixEnv}iot-portal/warning-master/${id}`;
    return this.http.delete<any>(path);
  }

  openDeleteWarningMaster(id: string, onSuccess: () => void): void {
    const dialogRef = this.dialog.open(DialogConfirmComponent, {
      width: '530px',
      data: {
        title: this.translateService.instant(
          'warningMasterData.deleteWarning'
        ),
        icon: 'ic-warning',
        confirmMsg: this.translateService.instant(
          'warningMasterData.msgConfirmDelete'
        ),
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.delete'),
      },
    });

    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe(() => {
        this.loadingService.showLoader();
        this.deleteWarningMaster(id).subscribe(
          (response) => {
            this.loadingService.hideLoader();
            this.notificationService.showSuccess(
              this.translateService.instant('warningMasterData.deleteSuccess')
            );
            this.router.navigate(['/warning-master']);
            onSuccess();
          },
          (errorsRes) => {
            this.loadingService.hideLoader();
            handleErrors(errorsRes, this.notificationService);
          }
        );
      });
  }
}
