import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { CustomerResponse, Vehicle } from '../../interfaces';
import { environment } from '../../../../../environments/environment';
import { removeNullParams } from '../../../../core/helpers';
import { WidgetResponse } from '../../../../core/interfaces';

@Injectable()
export class CustomersService {
  http = inject(HttpClient);

  prefixEnv = environment.OCC_BASE_URL + environment.OCC_PREFIX;

  getCustomerList(params): Observable<CustomerResponse> {
    const path = `${this.prefixEnv}iot-portal/e-care/customers`;
    return this.http.get<CustomerResponse>(path, {
      params: removeNullParams({ ...params }),
    });
  }

  getWidget(): Observable<{ widgets: WidgetResponse[] }> {
    const path = `${this.prefixEnv}iot-portal/e-care/customers/widget`;

    return this.http.get<{
      widgets: WidgetResponse[];
    }>(path);
  }

  getVehicleListByCustomer(customerId: string): Observable<Vehicle[]> {
    const path = `${this.prefixEnv}iot-portal/e-care/customers/${customerId}`;
    return this.http.get<Vehicle[]>(path);
  }
}
