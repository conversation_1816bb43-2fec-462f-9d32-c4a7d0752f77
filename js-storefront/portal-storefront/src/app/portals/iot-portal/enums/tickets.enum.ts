export enum TicketSummaryTab {
  HighPriorityTickets = 'highPriorityTickets',
  UnassignedTickets = 'noAssigneeTickets',
  MyOpenTickets = 'openTickets',
}

export enum TypeTicket {
  All = '',
  SVT = 'SVT',
  WARNING = 'WARNING',
  IMMOBILISER = 'IMMOBILISER',
  SOS = 'SOS',
  ALARM = 'ALARM',
  SECURITY_ISSUES = 'SECURITY_ISSUES'
  // All ticket types from BE
}

export enum PriorityTicket {
  All = '',
  High = 'High',
  Mid = 'Medium',
  Low = 'Low',
}

export enum StatusTicket {
  All = '',
  Open = 'Open',
  InProgress = 'IN_PROGRESS',
  Reopen = 'REOPEN',
  Closed = 'Closed',
}

export enum StatusTicketDetail {
  New = 'New',
  InProgress = 'IN_PROGRESS',
  Reopen = 'REOPEN',
  Closed = 'Closed',
}

export enum AssigneeTicket {
  All = '',
  Unassigned = 'Unassigned',
}

export enum ApprovalTicket {
  All = '',
  Pending = 'PENDING',
  Approved = 'APPROVED',
  Rejected = 'REJECTED',
}

export enum StatusEngine {
  ON = 'ON',
  OFF = 'OFF',
}

export enum CallType {
  INCOMING_CALL = 'IN_CAL',
  OUTGOING_CALL = 'OUT_CAL',
}

export enum CallResult {
  UNABLE_TO_REACH = 'UNABLE_REACH',
  CUSTOMER_REQUEST_CALL_BACK = 'CUSTOMER_REQUEST_CALL_BACK',
  INFORMATION_PROVIDED = 'INFORMATION_PROVIDED',
  OTHER = 'OTHER',
}

export enum ManagerApprovalAction {
  Enable = 'ENABLE',
  Disable = 'DISABLE',
}

export enum ManagerApprovalStatus {
  Pending = 'PENDING',
  Approved = 'APPROVED',
  Rejected = 'REJECTED',
}

export enum ManagerApprovalTab {
  Authorization = 'Authorization',
  ManagerApproval = 'ManagerApproval'
}

export enum AuthorizationStatus {
  Pending = 'PENDING',
  Approved = 'APPROVED',
  Completed = 'COMPLETED',
  Rejected = 'REJECTED',
  Expired = 'EXPIRED'
}

export enum RolesForAssignee {
  CreateTicket = 'callcentergroup,callcentermanagergroup',
  AuthorizationRequest = 'callcentermanagergroup'
}