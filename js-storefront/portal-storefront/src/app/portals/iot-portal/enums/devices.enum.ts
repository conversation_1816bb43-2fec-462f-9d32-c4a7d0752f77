export enum DeviceManagementTab {
  LDCM = 'LDCM',
  GDCM = 'GDCM',
}

export enum DeviceSummaryTab {
  //LDCM
  WaitingForPairing = 'waitingForPairing',
  WaitingForEnable = 'waitingForEnable',
  NonOperational = 'nonOperationalDevices',

  // GDCM
  WaitingForTerminated = 'waitingForTermination',
  OperationalDevices = 'operationalDevices',
  NonOperationalGDCM = 'nonOperationalDevices',
}

export enum SignalTestingStatus {
  All = '',
  Verified = 'VERIFIED',
  NotVerified = 'NOT_VERIFIED',
}

export enum PairingStatus {
  All = '',
  Paired = 'PAIRED',
  NotPaired = 'NOT_PAIRED',
}

export enum SimStatus {
  All = '',
  Inactive = 'INACTIVE',
  Active = "ACTIVE",
  ActiveTesting = 'ACTIVE_FOR_TESTING',
  ActiveSubscription = 'ACTIVE_FOR_SUBSCRIPTION',
  Pending = 'PENDING',
  Terminated = 'TERMINATED',
}

export enum ActivationStatus {
  All = '',
  NotActivated = 'DEVICE_NON_ACTIVATED',
  NonOperational = 'DEVICE_NON_OPERATIONAL',
  Operational = 'DEVICE_OPERATIONAL',
}

export enum DeviceStatus {
  All = '',
  Active = 'ACTIVE',
  ForTermination = 'FOR_TERMINATION',
  Terminated = 'TERMINATED',
  TeminationInProgress = 'TERMINATION_IN_PROGRESS',
}

export enum DeviceAction {
  PairDevice = 'PairDevice',
  EnableSIM = 'EnableSIM',
  DisableSIM = 'DisableSIM',
  TerminateDevice = 'TerminateDevice',
  ConfirmPairing = 'ConfirmPairing,'
}

export enum CanActiveForSim {
  Testing = 'testing',
  Subscribe = 'subscribe',
  None = 'none'
}