export enum ChartType {
  OpenTicketByType = 'openTicketsByType',
  TicketByStatus = 'ticketByStatus',
  ApprovalTicketByType = 'approvalTicketsByType',
  ApprovalTicketByStatus = 'approvalTicketByStatus',
  LdcmDevicesBySimStatus = 'ldcmDevicesBySimStatus',
  SubscriptionToActive = 'subscriptionToActive',
  SubscriptionDueDate = 'subscriptionDueDate',
  LdcmDevicesByActivationStatus = 'ldcmDevicesByActivationStatus',
  LdcmDevicesByPairingStatus = 'ldcmDevicesByPairingStatus',
  NonOperationalDeviceVehicles = 'nonOperationalDeviceVehicles',
  DevicePairedThisMonthByDeviceType = 'devicePairedThisMonthByDeviceType',
}

export enum VehicleMode {
  SVT = 'svt',
  Immobilization = 'immobilization',
  SOS = 'sos',
}

export enum DashboardWidget {
  UnassignedTicket = 'unassignedTicket',
  HighPriorityTickets = 'highPriorityTickets',
  AssignedWarningTickets = 'assignedWarningTickets',
  VehicleWithSos = 'vehicleWithSos',
  AssignedTickets = 'assignedTickets',
  AttendedTickets = 'attendedTickets',
  VehiclesSOS = 'vehiclesSOS',
  CalPendingPaymentConfirmation = 'calPendingPaymentConfirmation',
  CalVehicleDueForRenewal = 'calVehicleDueForRenewal',
  CalLoansAboutToClose = 'calLoansAboutToClose',
  CalWithoutLoanInfo = 'calWithoutLoanInfo',
  LdcmSimPendingForActivation = 'ldcmSimPendingForActivation',
  LdcmDevicePendingForActivation = 'ldcmDevicePendingForActivation',
  LdcmDevicePendingForCancellation = 'ldcmDevicePendingForCancellation',
  WaitingForPairing = 'waitingForPairing',
  WaitingForEnable = 'waitingForEnable',
  NonOperationalDevices = 'nonOperationalDevices'
}
