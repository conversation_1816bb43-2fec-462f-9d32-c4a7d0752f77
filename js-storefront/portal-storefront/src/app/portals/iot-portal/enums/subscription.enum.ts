import { scheduled } from "rxjs";

export enum SubscriptionSummaryTab {
    requiringSubscriptionActivation = 'requiringSubscriptionActivation',
    subscriptionExpiringSoon = 'subscriptionExpiringSoon',
    pendingSubscriptionPaymentConfirmation = 'pendingSubscriptionPaymentConfirmation'
}

export enum StatusSubscription {
    All = '',
    Active = "ACTIVE",
    awaittingActivation = "AWAITING_ACTIVATION",
    cancelled = "CANCELLED",
    cancelling = "CANCELLING",
    forCancellation = "FOR_CANCELLATION",
    expiring = "EXPIRING",
    expired = "expired",
    scheduled = "SCHEDULED",
    pendingActivation = "PENDING_ACTIVATION",
    failed = "FAILED",
    renewalDue = "RENEWAL_DUE"
}

export enum StatusPayment {
    All = '',
    paid = "PAID",
    pendingConfirmation = "PENDING_CONFIRMATION",
    paymentFail = "PAYMENT_FAIL"
}

export enum SubscriptionEndDate {
    All = '',
    thisMonth = "THIS_MONTH",
    nextMonth = "NEXT_MONTH",
    next3Days = "NEXT_3_DAYS",
    last3Days = "LAST_3_DAYS",
    custom = "CUSTOM",
}

export enum VehicleType {
    All = '',
    B2C = "B2C",
    B2B = "B2B",
    B2BCAL = "B2B - CAL",
    B2BFLEET = "B2B - FLEET"
}

export enum LoanStatus {
    Active = 'ACTIVE',
    Closing = 'CLOSING',
    Closed = 'CLOSED'
}

export enum SubscriptionType {
  ACTIVATE_SUBSCRIPTION = 'Activate subscription',
  CONFIRM_PAYMENT = 'Confirm payment',
  BULK_PAYMENT = 'Bulk confirm payment',
}