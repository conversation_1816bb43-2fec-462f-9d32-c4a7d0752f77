import { Routes } from "@angular/router";
import { DashboardComponent } from "./features/dashboard/dashboard.component";
import { PermissionsGuard } from "../../core/services/guards";
import { PERMISSIONS_CODE } from "../../core/constants";
import { SubscriptionsComponent } from "./features/subscriptions/subscriptions.component";
import { SubscriptionDetailComponent } from "./features/subscriptions/subscription-detail/subscription-detail.component";
import { DeviceListComponent } from "./features/device/device-list/device-list.component";
import { DeviceDetailComponent } from "./features/device/device-detail/device-detail.component";
import { VehicleListComponent } from "./features/vehicle/vehicle-list/vehicle-list.component";
import { CustomerDetailComponent, CustomersComponent, ReportsComponent, TicketListComponent } from "./features";
import { ChangePasswordComponent } from "../../features";
import { VehicleDetailComponent } from "./features/vehicle/vehicle-detail/vehicle-detail.component";
import { TicketDetailComponent } from "./features/ticket/ticket-detail/ticket-detail.component";
import { VehicleModelComponent } from "./features/vehicle-model/vehicle-model.component";
import { B2bCalVehiclesComponent } from "./features/b2b-cal-vehicles/b2b-cal-vehicles.component";
import { WarningMasterComponent } from "./features/warning-master/warning-master.component";
import { WarningMasterDetailComponent } from "./features/warning-master/warning-master-detail/warning-master-detail.component";

export const IOT_PORTAL_ROUTES: Routes = [ 
    {
        path: 'dashboard',
        component: DashboardComponent,
        canActivate: [PermissionsGuard],
        data: { permissions: [PERMISSIONS_CODE.EMPLOYEEGROUP] },
      },
      {
        path: 'subscription',
        component: SubscriptionsComponent,
        canActivate: [PermissionsGuard],
        data: { permissions: [PERMISSIONS_CODE.IOT_SUBSCRIPTION_LIST_VIEW] },
      },
      {
        path: 'subscription/:id',
        component: SubscriptionDetailComponent,
        canActivate: [PermissionsGuard],
        data: { permissions: [PERMISSIONS_CODE.IOT_SUBSCRIPTION_LIST_VIEW] },
      },
      {
        path: 'devices',
        component: DeviceListComponent,
        canActivate: [PermissionsGuard],
        data: { permissions: [PERMISSIONS_CODE.IOT_DEVICE_SIM_LIST_VIEW] },
      },
      {
        path: 'devices/:id',
        component: DeviceDetailComponent,
        canActivate: [PermissionsGuard],
        data: { permissions: [PERMISSIONS_CODE.IOT_DEVICE_DETAIL_VIEW] },
      },
      {
        path: 'vehicles',
        component: VehicleListComponent,
        canActivate: [PermissionsGuard],
        data: { permissions: [PERMISSIONS_CODE.ECARE_VEHICLE_LIST_VIEW] },
      },
      {
        path: 'customers',
        component: CustomersComponent,
        canActivate: [PermissionsGuard],
        data: {
          permissions: [
            PERMISSIONS_CODE.CALLCENTERGROUP,
            PERMISSIONS_CODE.CALLCENTERMANAGERGROUP,
          ],
        },
      },
      {
        path: 'customers/:id',
        component: CustomerDetailComponent,
        canActivate: [PermissionsGuard],
        data: { permissions: [PERMISSIONS_CODE.EMPLOYEEGROUP] },
      },
      {
        path: 'change-password',
        component: ChangePasswordComponent,
        canActivate: [PermissionsGuard],
        data: { permissions: [PERMISSIONS_CODE.EMPLOYEEGROUP] },
      },
      {
        path: 'vehicles/:id',
        component: VehicleDetailComponent,
        canActivate: [PermissionsGuard],
        data: { permissions: [PERMISSIONS_CODE.ECARE_VEHICLE_IOT_VIEW] },
      },
      {
        path: 'tickets',
        component: TicketListComponent,
        canActivate: [PermissionsGuard],
        data: { permissions: [PERMISSIONS_CODE.ECARE_TICKET_LIST_VIEW] },
      },
      {
        path: 'tickets/:id',
        component: TicketDetailComponent,
        canActivate: [PermissionsGuard],
        data: { permissions: [PERMISSIONS_CODE.ECARE_TICKET_DETAIL_VIEW] },
      },
      {
        path: 'vehicle-model',
        component: VehicleModelComponent,
        canActivate: [PermissionsGuard],
        data: {
          permissions: [PERMISSIONS_CODE.IOT_SUPPORTED_MODEL_LIST_VIEW],
        },
      },
      {
        path: 'b2b-cal',
        component: B2bCalVehiclesComponent,
        canActivate: [PermissionsGuard],
        data: {
          permissions: [PERMISSIONS_CODE.IOT_B2B_CAL_VEHICLE_LIST_VIEW],
        },
      },
      {
        path: 'warning-master',
        component: WarningMasterComponent,
        canActivate: [PermissionsGuard],
        data: { permissions: [PERMISSIONS_CODE.IOT_WARNING_MASTER_VIEW] },
      },
      {
        path: 'warning-master/:id',
        component: WarningMasterDetailComponent,
        canActivate: [PermissionsGuard],
        data: { permissions: [PERMISSIONS_CODE.IOT_WARNING_MASTER_VIEW] },
      },
      {
        path: 'reports',
        component: ReportsComponent,
        canActivate: [PermissionsGuard],
        data: {
          permissions: [
            PERMISSIONS_CODE.ECARE_REPORT_SUBSCRIPTION,
            PERMISSIONS_CODE.ECARE_REPORT_TICKET,
            PERMISSIONS_CODE.ECARE_REPORT_WARNING,
          ],
        },
      },
    
];