@if (params.data?.pairingStatus?.code === PairingStatus.Paired &&
params.data?.simStatus?.code === SimStatus.Inactive &&
!params.data?.importFile) {
<button type="button" (click)="onUpload(false)">
  <mat-icon svgIcon="ic-import"></mat-icon>

  {{ "common.uploadConsent" | translate }}
</button>
} @else if( params.data?.pairingStatus?.code === PairingStatus.Paired &&
params.data?.simStatus?.code === SimStatus.Pending && params.data?.importFile){
<a class="file-name" target="_blank" [href]="prefixEnv + params.data?.importFile?.downloadUrl">
  <mat-icon svgIcon="ic-file"></mat-icon>
  <span>
    {{ params.data?.importFile?.realFileName }}
  </span>
</a>
<button type="button" (click)="onUpload(true)">
  <mat-icon svgIcon="ic-import"></mat-icon>

  {{ "common.replaceConsent" | translate }}
</button>
}
