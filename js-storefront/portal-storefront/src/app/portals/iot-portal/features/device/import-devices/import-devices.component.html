<mat-icon svgIcon="ic-import" class="icon-title"></mat-icon>
<h2 class="title-dialog">{{ data?.title }}</h2>

<div class="container-dialog">
  @if (confirmTemp) {
  <div class="confirm-msg">{{ data?.confirmMsg }}</div>
  } @else {
  <app-import-files
    [label]="'deviceManagement.selectDataFile' | translate"
    [placeholder]="'deviceManagement.noFileSelected' | translate"
    [control]="form.controls.selectFile"
    [accept]="accept"
    (filesSelected)="handleFiles($event)"
    (handleInvalidFile)="handleInvalidFile()"
  ></app-import-files>
  }
</div>

<div class="action-dialog">
  @if (confirmTemp) {
  <button
    class="btn-primary btn-confirm"
    [disabled]="form.invalid"
    (click)="onConfirm()"
  >
    {{ "common.confirm" | translate }}
  </button>
  } @else {
  <button class="btn-quaternary" (click)="onCancel()">
    {{ "common.cancel" | translate }}
  </button>

  <button class="btn-primary" [disabled]="form.invalid" (click)="data?.noStep ? onConfirm() : onImport()">
    {{ "deviceManagement.import" | translate }}
  </button>
  }
</div>
