import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { ICellRendererParams } from 'ag-grid-community';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { UploadConsentFileComponent } from '../upload-consent-file/upload-consent-file.component';
import { filter } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';
import { IconModule } from '../../../../../../../core/icon/icon.module';
import { PairingStatus, SimStatus } from '../../../../../enums';
import { environment } from '../../../../../../../../environments/environment';
import { ActionTable } from '../../../../../../../core/enums';


@Component({
  selector: 'app-consent-file-cell',
  standalone: true,
  imports: [
    CommonModule,
    IconModule,
    TranslateModule,
    UploadConsentFileComponent,
  ],
  templateUrl: './consent-file-cell.component.html',
  styleUrl: './consent-file-cell.component.scss',
})
export class ConsentFileCellComponent implements ICellRendererAngularComp {
  translateService = inject(TranslateService);
  dialog = inject(MatDialog);

  readonly PairingStatus = PairingStatus;
  readonly SimStatus = SimStatus;

  prefixEnv = environment.OCC_BASE_URL?.slice(0, -1);
  params: any;

  agInit(params: ICellRendererParams<any, any, any>): void {
    this.params = params;
  }

  refresh(params: ICellRendererParams<any, any, any>): boolean {
    return false;
  }

  onUpload(isReplace: boolean): void {
    this.params.onClick(
      isReplace ? ActionTable.Replace : ActionTable.Upload,
      this.params?.data
    );
  }
}
