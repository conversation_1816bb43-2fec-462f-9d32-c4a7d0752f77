import { CommonModule } from '@angular/common';
import { Component, EventEmitter, inject, Input, OnInit, Output, TemplateRef, ViewChild } from '@angular/core';
import { ColDef } from 'ag-grid-community';
import { MatIconModule } from '@angular/material/icon';
import { AgGridCustomComponent, ImportResultSummaryComponent } from '../../../../../../core/shared';
import { IconCellRendererComponent } from '../../../../../../core/shared/ag-grid-custom/icon-cell-renderer/icon-cell-renderer.component';
import { DeviceActionPipe } from '../../../../pipes';
import { ItemDevice } from '../../../../interfaces';
import { LastImportResult, PagingInfo } from '../../../../../../core/interfaces';
import { UserService } from '../../../../../../core/services';
import { PERMISSIONS_CODE } from '../../../../../../core/constants';

@Component({
  selector: 'app-device-table',
  templateUrl: './device-table.component.html',
  styleUrl: './device-table.component.scss',
  imports: [
    CommonModule,
    AgGridCustomComponent,
    IconCellRendererComponent,
    MatIconModule,
    DeviceActionPipe,
    ImportResultSummaryComponent
  ],
  standalone: true,
})
export class DeviceTableomponent implements OnInit {
  @Input() rowData: ItemDevice[];
  @Input() colDefs: ColDef[];
  @Input() importInfo: LastImportResult;
  @Input() importInfoSim: LastImportResult;
  @Input() pagingInfo: PagingInfo;
  @Input() isLDCM: boolean;
  
  @Input() isLoadingLastImport: boolean = false;
  @Input() isChangeItemPerPageTop = false;

  @Output() onPageChange = new EventEmitter<number>();
  @Output() onResultsPerPageChange = new EventEmitter<number>();
  @Output() changeItemPerPage = new EventEmitter<number>();
  @Output() exportData = new EventEmitter<void>();
  @Output() downloadFile = new EventEmitter();
  @Output() downloadFileSim = new EventEmitter();

  @ViewChild('actionTemplate', { static: true })
  actionTemplate: TemplateRef<any>;

  @ViewChild('simStatusTemplate', { static: true })
  simStatusTemplate: TemplateRef<any>;

  @ViewChild('deviceStatusTemplate', { static: true })
  deviceStatusTemplate: TemplateRef<any>;

  @ViewChild('activationStatusTemplate', { static: true })
  activationStatusTemplate: TemplateRef<any>;


  customTemplates: { [key: string]: TemplateRef<any> } = {};

  userService = inject(UserService);
  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;
  
  defaultColDef = {
    resizable: false,
    valueFormatter: params => (params.value ? params.value : '-'),
    sortable: false,
    menuTabs: [],
    wrapHeaderText: true,
    autoHeaderHeight: true,
    wrapText: true,
    autoHeight: true,
  };


  ngOnInit(): void {
    this.customTemplates = {
      action: this.actionTemplate,
      simStatus: this.simStatusTemplate,
      deviceStatus: this.deviceStatusTemplate,
      activationStatus: this.activationStatusTemplate
    };
  }
}
