import { Component, inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { IconModule } from '../../../../../../../core/icon/icon.module';
import { FormGroupComponent } from '../../../../../../../core/shared';
import { ImportFilesComponent } from '../../../../../../../core/shared/import-files/import-files.component';
import { NotificationService } from '../../../../../../../core/services';
import { ActionModal } from '../../../../../../../core/enums';

@Component({
  selector: 'app-upload-consent-file',
  standalone: true,
  imports: [UploadConsentFileComponent, IconModule, CommonModule, TranslateModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    FormGroupComponent,
    ImportFilesComponent
  ],
  providers: [NotificationService],
  templateUrl: './upload-consent-file.component.html',
  styleUrl: './upload-consent-file.component.scss'
})
export class UploadConsentFileComponent {
  data = inject(MAT_DIALOG_DATA);
  dialogRef = inject(MatDialogRef<UploadConsentFileComponent>);
  notificationService = inject(NotificationService);

  accept = '.pdf,.jpg,.jpeg,.png,.heic,.JPG';
  maxFileSize = 1 * 1024 * 1024; // 5MB Limit

  form = new FormGroup({
    selectFile: new FormControl(null, Validators.required),
  });

  handleFiles(files: FileList | null): void {
    if (files) {
      Array.from(files).forEach((file) => {
        this.form.controls['selectFile'].setValue(file);
      });
    }
  }

  handleInvalidFile(): void {
    this.notificationService.showError(
      'Please use supported file type (pdf, jpg, jpeg, png, heic)'
    );
  }

  handleMaxFile(): void {
    this.notificationService.showError(
      `File size is exceeded 5 mb`
    );
  }

  onCancel() {
    this.dialogRef.close();
  }

  onConfirm() {
    this.dialogRef.close({ action: ActionModal.Submit, data: this.form.controls.selectFile.value });
  }
}
