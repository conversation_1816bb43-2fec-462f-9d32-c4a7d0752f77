import { ChangeDetectorRef, Component, EventEmitter, inject, NgZone, OnInit, Output } from '@angular/core';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import {
  FormsModule,
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  Validators,
  FormControl,
  ValidatorFn,
  AbstractControl,
  ValidationErrors,
} from '@angular/forms';
import { of } from 'rxjs';
import { debounceTime, distinctUntilChanged, switchMap } from 'rxjs/operators';
import { AutocompleteInputComponent, FormGroupComponent } from '../../../../../../core/shared';
import { DeviceService } from '../../../../services';
import { LoadingService, NotificationService } from '../../../../../../core/services';
import { OptionDropdown } from '../../../../../../core/interfaces';
export function nonExistDeviceId(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    if (control.value) {
      return { nonExistent: true };
    }
    return null;
  };
}

@Component({
  selector: 'app-popup-pair-device',
  standalone: true,
  imports: [
    MatDialogModule,
    MatIconModule,
    TranslateModule,
    FormGroupComponent,
    AutocompleteInputComponent,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
  ],
  templateUrl: './modal-pair-device.component.html',
  providers: [DeviceService, LoadingService, NotificationService],
  styleUrls: ['./modal-pair-device.component.scss'],
})

export class ModalPairDeviceComponent implements OnInit {
  @Output() onPairingComplete = new EventEmitter<boolean>();
  data = inject(MAT_DIALOG_DATA);
  dialogRef = inject(MatDialogRef<ModalPairDeviceComponent>);
  deviceService = inject(DeviceService);
  notificationService = inject(NotificationService);
  translateService = inject(TranslateService);
  ngZone = inject(NgZone);
  private cdr = inject(ChangeDetectorRef);

  form: FormGroup;
  modelSalesCode: OptionDropdown[] = [];
  loading: boolean = false;
  loadingPair: boolean = false;
  isModelSalesCodeValid: boolean = true;
  isPaired: boolean = false;
  pairedDetails: { vin: string; deviceID: string } = { vin: '', deviceID: '' };
  errorDeviceId: { [key: string]: string };

  constructor(private fb: FormBuilder) {}

  ngOnInit(): void {
    this.form = this.fb.group({
      deviceId: [
        { value: this.data?.deviceId || '', disabled: !!this.data?.deviceId },
        Validators.required,
      ],
      vehicleVin: [
        '',
        [
          Validators.required,
          Validators.minLength(17),
          Validators.maxLength(17),
        ],
      ],
      modelSalesCode: [null, Validators.required],
    });

    this.onModelSalesCodeInput();

    this.f['deviceId'].valueChanges.subscribe(() => {
      this.f['deviceId'].setValidators(Validators.required);
      this.f['deviceId'].updateValueAndValidity();
    });
  }

  get f(): { [key: string]: FormControl } {
    return this.form.controls as { [key: string]: FormControl };
  }

  onModelSalesCodeInput(): void {
    this.f['modelSalesCode'].valueChanges
      .pipe(
        debounceTime(300),
        distinctUntilChanged(),
        switchMap((value: string) => {
          const code = value?.split(/[\s-]/)[0] || '';
          if (code.length >= 3) {
            this.loading = true;
            return this.deviceService.getModelSaleCodes(code);
          }
          return of({ allNewVehicles: [], allCodes: [] });
        })
      )
      .subscribe({
        next: (response) => {
          this.modelSalesCode = response.allNewVehicles || [];
          const listModelSalesOnlyCode = response.allCodes || [];
          const codeID = this.f['modelSalesCode'].value.split(/[\s-]/)[0];
          this.isModelSalesCodeValid = listModelSalesOnlyCode.includes(codeID);
          this.cdr.detectChanges();
          this.loading = false;
        },
        error: () => {
          this.isModelSalesCodeValid = false;
          this.modelSalesCode = [];
          this.loading = false;
        },
      });
  }

  onModelSalesCodeSelected(value: string | OptionDropdown): void {
    const selectedCode = typeof value === 'string' ? value : value?.code;
    this.isModelSalesCodeValid = this.modelSalesCode.some((option) => {
      if (typeof option === 'string') {
        return option === selectedCode;
      } else {
        return option.code === selectedCode;
      }
    });

    if (this.isModelSalesCodeValid) {
      this.f['modelSalesCode'].setValue(selectedCode);
    } else {
      this.f['modelSalesCode'].setErrors({ invalidCode: true });
    }

    this.f['modelSalesCode'].markAsTouched();
  }

  onCancel() {
    this.dialogRef.close();
  }

  onConfirm() {
    if (this.form.invalid) {
      Object.values(this.form.controls).forEach((control) =>
        control.markAsTouched()
      );
      return;
    }

    const fullModelSalesCode = this.f['modelSalesCode'].value;
    const code = fullModelSalesCode.split(' ')[0];

    const payload = {
      vin: this.f['vehicleVin'].value,
      deviceId: this.f['deviceId'].value,
      modelSaleCode: code,
    };

    this.loadingPair = true;

    this.deviceService.pairDevice(payload).subscribe({
      next: (response) => {
        this.loadingPair = false;

        if (response) {
          this.isPaired = true;
          this.pairedDetails = {
            vin: this.f['vehicleVin'].value,
            deviceID: this.f['deviceId'].value,
          };

          this.translateService
            .get('deviceManagement.pairSuccess', { deviceId: payload.deviceId })
            .subscribe(() => {
              
              this.ngZone.run(() => {
                this.onPairingComplete.emit(true);
              });
            });
        } else {
          this.dialogRef.close({ success: false });
          this.onPairingComplete.emit(false);
          this.notificationService.showError(
            'Unexpected error occurred. Response body is empty.'
          );
        }
      },
      error: (err) => {
        this.loadingPair = false;
        const [firstError] = err.error?.errors;
        if (firstError?.type === 'NotFoundError') {
          this.f['deviceId'].setValidators(nonExistDeviceId());
          this.errorDeviceId = {
            nonExistent: this.translateService.instant('deviceManagement.modalPairDevice.deviceIdNotExist')
          };
          return;
        }
        this.dialogRef.close({ success: false });
        console.error('Error Pairing Device:', err);
        this.onPairingComplete.emit(false);
        const errorMessage =
        firstError?.message ||
          'An error occurred while pairing the device.';
        this.notificationService.showError(errorMessage);
      },
    });
  }

  preventSpace(event: KeyboardEvent) {
    if (event.code === 'Space') {
      event.preventDefault();
    }
  }
}
