import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { MatTooltip, MatTooltipModule } from '@angular/material/tooltip';
import { Subscription } from 'rxjs';

import { CommonModule } from '@angular/common';
import { ModalPairDeviceComponent } from '../device-list/modal-pair-device/modal-pair-device.component';
import { MatDialog } from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';
import { BreadcrumbWithLabelComponent } from '../../../../../core/shared';
import { IconModule } from '../../../../../core/icon/icon.module';
import { LoadingComponent } from '../../../../../layout/global/loading/loading.component';
import { DeviceService } from '../../../services';
import { LoadingService, NotificationService, UserService } from '../../../../../core/services';
import { ActivationStatus, DeviceAction, DeviceManagementTab, DeviceStatus, PairingStatus, SignalTestingStatus, SimStatus } from '../../../enums';
import { PERMISSIONS_CODE } from '../../../../../core/constants';
import { ItemDevice } from '../../../interfaces';

@Component({
  selector: 'app-device-detail',
  templateUrl: './device-detail.component.html',
  standalone: true,
  imports: [
    BreadcrumbWithLabelComponent,
    IconModule,
    MatTooltipModule,
    CommonModule,
    LoadingComponent,
    TranslateModule,
  ],
  providers: [DeviceService, NotificationService, LoadingService],
})
export class DeviceDetailComponent implements OnInit {
  @ViewChild(MatTooltip) tooltip!: MatTooltip;
  @Input() deviceId: string;
  isLDCM: boolean;

  breadcrumbList: string[];
  showMore = false;
  mainSubscription = new Subscription();
  dataDevice: any = {};
  deviceInfo: any = {};
  pairingInfo: any = {};
  simInfo: any = {};
  vehicalInfo: any = {};
  deviceManagementTab = DeviceManagementTab;
  pairingStatus = PairingStatus;
  deviceAction = DeviceAction;
  activationStatus = ActivationStatus;
  deviceStatus = DeviceStatus;
  signalTestingStatus = SignalTestingStatus;
  simStatus = SimStatus;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private deviceService: DeviceService,
    public loadingService: LoadingService,
    private notificationService: NotificationService,
    private dialog: MatDialog,
    protected userService: UserService
  ) {}

  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;

  ngOnInit() {
    this.mainSubscription.add(
      this.route.paramMap.subscribe((params) => {
        this.deviceId = params.get('id');
        this.getDeviceById();
      })
    );
  }

  ngOnDestroy() {
    this.mainSubscription && this.mainSubscription.unsubscribe();
  }

  getDeviceById() {
    this.loadingService.showLoader();
    this.mainSubscription.add(
      this.deviceService.getDeviceDetails(this.deviceId).subscribe(
        (res: any) => {
          if (res) {
            this.deviceInfo = res.deviceInfo;
            this.simInfo = res.simInfo;
            this.pairingInfo = res.pairingInfo;
            this.vehicalInfo = res.vehicalInfo;
            this.dataDevice = {
              ...this.pairingInfo,
              ...this.deviceInfo,
              ...this.simInfo,
              ...this.vehicalInfo,
              canActiveFor: res.canActiveFor,
            };
            this.isLDCM =
              this.deviceInfo?.type?.code === this.deviceManagementTab['LDCM'];
            this.updateBreadcrumbs();
          }
          this.loadingService.hideLoader();
        },
        (err) => {
          this.loadingService.hideLoader();
        }
      )
    );
  }

  toggleShowMore() {
    this.showMore = !this.showMore;
  }

  onActionClick(id: string, device: ItemDevice = {}): void {
    switch (id) {
      case DeviceAction.PairDevice: {
        const dialogRef = this.dialog.open(ModalPairDeviceComponent, {
          width: '530px',
          data: {
            title: 'Pair Device',
            icon: 'ic-link-pair-red',
            cancelBtn: 'Cancel',
            submitBtn: 'Pair',
            deviceId: this.deviceId,
          },
        });
        dialogRef.componentInstance.onPairingComplete.subscribe(
          (success: boolean) => {
            if (success) {
              this.getDeviceById();
            }
          }
        );
        break;
      }

      case DeviceAction.EnableSIM: {
        this.deviceService.openEnableSim(device, () => {
          this.getDeviceById();
        });
        break;
      }
      case DeviceAction.DisableSIM: {
        this.deviceService.openDisableSim(device, () => {
          this.getDeviceById();
        });
        break;
      }
      case DeviceAction.ConfirmPairing: {
        this.deviceService.openConfirmPairing(device, this.isLDCM, () => {
          this.getDeviceById();
        });
        break;
      }

      case DeviceAction.TerminateDevice: {
        this.deviceService.openTerminateDevice(
          device.vin,
          device.deviceId,
          device.iccid,
          () => {
            this.getDeviceById();
          }
        );
        break;
      }
    }
  }

  onRefresh() {
    this.getDeviceById();
  }

  private updateBreadcrumbs() {
    this.breadcrumbList = [
      this.isLDCM
        ? 'deviceManagement.ldcmDevices'
        : 'deviceManagement.gdcmDevices',
      'deviceManagement.deviceDetails',
    ];
  }

  handleBreadcrumbClick() {
    const tabValue = this.isLDCM
      ? DeviceManagementTab.LDCM
      : DeviceManagementTab.GDCM;
    this.router.navigate(['/devices'], {
      queryParams: { tab: tabValue },
      queryParamsHandling: 'merge',
    });
  }
}
