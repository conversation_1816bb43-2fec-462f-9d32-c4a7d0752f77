<mat-icon [svgIcon]="data?.icon" class="icon-title"></mat-icon>
<h2 class="title-dialog">
    {{ isPaired ? ('deviceManagement.modalPairDevice.pairedTitle' | translate) : data?.title }}
</h2>

<div *ngIf="isPaired" class="success-message modal-pair-dialog">
    <p
        [innerHTML]="'deviceManagement.modalPairDevice.successMessage' | translate:{ vin: pairedDetails.vin, deviceID: pairedDetails.deviceID }">
    </p>
</div>

<div *ngIf="!isPaired && !loadingPair" class="form-dialog">
    <app-form-group [label]="'deviceManagement.deviceID' | translate" [control]="f['deviceId']" [controlId]="'deviceId'"
        [required]="true" [otherErrorMessages]="errorDeviceId" [placeholder]="'deviceManagement.modalPairDevice.enterDeviceId' | translate" [errorMessage]="'validation.fillInData' | translate" class="vehicle-model-section__input">
    </app-form-group>

    <app-form-group [label]="'deviceManagement.vehicleVin' | translate" [control]="f['vehicleVin']"
        [controlId]="'vehicleVin'" [placeholder]="'deviceManagement.modalPairDevice.enterVin' | translate" [required]="true"
        [errorMessage]="'validation.fillInData' | translate" [maxLength]="17" [otherErrorMessages]="{
      minlength:
        ('validation.minLength' | translate : { field: 'VIN', count: 17 }),
      maxlength:
        ('validation.maxLength' | translate : { field: 'VIN', count: 17 })
    }" class="vehicle-model-section__input"
    [preventSpace]="true"
    (onKeypress)="preventSpace($event)">
    </app-form-group>
    <app-autocomplete-input [label]="'deviceManagement.modelSalesCode' | translate" [controlId]="'modelSalesCode'"
        [placeholder]="'Enter model sales code...'" [suggestions]="modelSalesCode" [required]="true"
        [errorMessage]="'validation.fillInData' | translate" [control]="f['modelSalesCode']"
        (optionSelected)="onModelSalesCodeSelected($event)">
    </app-autocomplete-input>
    <div *ngIf="!isModelSalesCodeValid && f['modelSalesCode'].touched"
        class="form-group__error form-group__error--other">
        {{ "deviceManagement.modelSalesCodeNotSupported" | translate }}
    </div>
</div>

<div *ngIf="loadingPair" class="img-loading modal-pair-dialog">
    <img src="../../../../../assets/images/loading-pair.png" alt="loading pair" />
</div>

<div *ngIf="!loadingPair" class="action-dialog modal-pair-dialog">
    <ng-container *ngIf="!isPaired">
        <button class="btn-quaternary" (click)="onCancel()">
            {{ data?.cancelBtn }}
        </button>
        <button class="btn-primary btn-confirm" (click)="onConfirm()" 
        [disabled]="form.invalid || !isModelSalesCodeValid">
            {{ data?.submitBtn }}
        </button>
    </ng-container>
    <ng-container *ngIf="isPaired">
        <button class="btn-primary btn-confirm btn-success" (click)="onCancel()">
            {{'common.close' | translate}}
        </button>
    </ng-container>
</div>