import { Component, EventEmitter, inject, Output } from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { FormGroupComponent } from '../../../../../core/shared';
import { ImportFilesComponent } from '../../../../../core/shared/import-files/import-files.component';
import { NotificationService } from '../../../../../core/services';
import { ActionModal } from '../../../../../core/enums';
@Component({
  selector: 'app-import-devices',
  standalone: true,
  imports: [
    MatDialogModule,
    MatIconModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    FormGroupComponent,
    ImportFilesComponent,
  ],
  templateUrl: './import-devices.component.html',
  providers: [NotificationService],
})
export class ImportDevicesComponent {
  data = inject(MAT_DIALOG_DATA);
  dialogRef = inject(MatDialogRef<ImportDevicesComponent>);

  notificationService = inject(NotificationService);

  form = new FormGroup({
    selectFile: new FormControl(null, Validators.required),
  });

  accept = '.csv'
  confirmTemp = false;
  
  handleFiles(files: FileList | null): void {
    if (files) {
      Array.from(files).forEach((file) => {
        this.form.controls.selectFile.setValue(file);
      });
    }
  }

  handleInvalidFile(): void {
    this.notificationService.showError(
      'Invalid file. File should be in csv format.'
    );
  }

  onImport() {
    this.confirmTemp = true;
  }

  onCancel() {
    this.dialogRef.close();
  }

  onConfirm() {
    this.dialogRef.close({ action: ActionModal.Submit, data: this.form.controls.selectFile.value });
  }
}
