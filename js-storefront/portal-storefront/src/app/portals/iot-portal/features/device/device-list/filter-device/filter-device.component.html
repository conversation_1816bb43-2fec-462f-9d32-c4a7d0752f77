<div *ngIf="screenSizeService.isSmallDesktop() | async" class="search-device">
  <span>Search Devices</span>
  <mat-icon
    [svgIcon]="isOpenFilter ? 'ic-minus' : 'ic-add-1'"
    (click)="isOpenFilter = !isOpenFilter"
  ></mat-icon>
</div>

@if((isOpenFilter && screenSizeService.isSmallDesktop() | async) ||
!(screenSizeService.isSmallDesktop() | async)) {
<form [formGroup]="form">
  <app-form-group
    [label]="'deviceManagement.deviceIdOrVinNumber' | translate"
    [control]="form.controls.deviceId"
    placeholder="Enter Device ID or Vin to search"
    controlId="modelSalesCode"
    class="vehicle-model-section__input"
  ></app-form-group>

  <app-dropdown-form-group
    [label]="'deviceManagement.deviceStaus' | translate"
    [control]="form.controls.deviceStatus"
    [options]="deviceStatus"
    (enter)="searchDevice.emit()"
  ></app-dropdown-form-group>

  <app-dropdown-form-group
    [label]="'deviceManagement.activationStatus' | translate"
    [control]="form.controls.activationStatus"
    [options]="activationStatus"
    (enter)="searchDevice.emit()"
  ></app-dropdown-form-group>
  <app-dropdown-form-group
    *ngIf="isLDCM"
    [label]="'deviceManagement.pairingStatus' | translate"
    [control]="form.controls.pairingStatus"
    [options]="pairingStatus"
    (enter)="searchDevice.emit()"
  ></app-dropdown-form-group>

  <app-dropdown-form-group
    [label]="'deviceManagement.simStatus' | translate"
    [control]="form.controls.simStatus"
    [options]="simStatus"
    (enter)="searchDevice.emit()"
  ></app-dropdown-form-group>

  <button
    type="submit"
    class="btn-tertiary search"
    (click)="searchDevice.emit()"
  >
    {{ "common.search" | translate }}
  </button>
  <button
    *ngIf="screenSizeService.isMobile() | async"
    type="button"
    class="btn-tertiary clear-all"
    [disabled]="true"
  >
    <mat-icon svgIcon="ic-closed" class="small-icon"></mat-icon>
    <span>{{ "common.clearAll" | translate }}</span>
  </button>
</form>
}
