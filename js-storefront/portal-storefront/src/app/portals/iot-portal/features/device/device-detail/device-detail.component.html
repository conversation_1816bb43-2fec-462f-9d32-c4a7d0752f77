@if (loadingService.isLoading) {
<app-loading></app-loading>
} @if (dataDevice && dataDevice.deviceId) {
<div class="device-detail">
  <app-breadcrumb-with-label
    [breadcrumbs]="breadcrumbList"
    [breadcrumbLinks]="['/devices']"
    [value]="['devices']"
    [label]="deviceId"
    [breadcrumbMulLink]="true"
    (breadcrumbClick)="handleBreadcrumbClick()"
  >
  </app-breadcrumb-with-label>
  <div class="device-detail__wrapper">
    <div
      class="device-detail__info"
      [ngStyle]="{ 'align-items': showMore ? 'flex-start' : 'stretch' }"
    >
      <div class="device-info-section device-detail__section">
        <h3 class="device-info-section__title">
          <mat-icon svgIcon="ic-chip" class="medium-icon"></mat-icon
          >{{ "deviceManagement.deviceInfo" | translate }}
        </h3>
        <div class="device-info-section__details">
          <ng-container
            *ngTemplateOutlet="
              itemDevice;
              context: {
                label: 'deviceManagement.type',
                value: deviceInfo?.type?.name
              }
            "
          ></ng-container>
          <ng-container
            *ngTemplateOutlet="
              itemDevice;
              context: {
                label: 'deviceManagement.id',
                value: deviceInfo?.deviceId
              }
            "
          ></ng-container>
          <div class="device-info-section__item">
            <p class="device-info-section__label">
              {{ "deviceManagement.status" | translate }}
            </p>
            <p class="device-info-section__value device-info-section__status">
              <span
                ><strong>{{
                  deviceInfo?.deviceStatus?.name || "N/A"
                }}</strong>
                @if(deviceInfo?.deviceStatus?.code ===
                deviceStatus.ForTermination && !isLDCM 
                && userService.isHasPermission([PERMISSIONS_CODE.IOT_DEVICE_TERMINATE])) {
                <button
                  class="btn-link"
                  (click)="
                    onActionClick(deviceAction.TerminateDevice, dataDevice)
                  "
                >
                  <mat-icon
                    svgIcon="ic-terminate"
                    class="small-icon"
                  ></mat-icon>
                  {{ "deviceManagement.terminate" | translate }}
                </button>
                }
              </span>
            </p>
          </div>
          <ng-container
            *ngTemplateOutlet="
              itemDevice;
              context: {
                label: 'deviceManagement.activationStatus',
                value: deviceInfo?.activationStatus?.name || 'N/A'
              }
            "
          ></ng-container>
          @if(pairingInfo?.status?.code === pairingStatus.Paired) {
          <div
            class="device-info-section__item device-info-section__item-break-line width-full"
          >
            <p class="device-info-section__label">
              {{ "deviceManagement.signalTestingStatus" | translate }}
            </p>
            <p
              class="device-info-section__value device-info-section__value-icon"
            ><strong>
              {{
                deviceInfo?.signalTestingStatusEnum?.name || "N/A"
              }}</strong>
              @if(deviceInfo?.signalTestingStatusEnum?.code ===
              signalTestingStatus.NotVerified && isLDCM 
              && userService.isHasPermission([PERMISSIONS_CODE.IOT_DEVICE_PAIR])) {
              <button
                class="btn-link device-info-section__confirm"
                (click)="onActionClick(deviceAction.ConfirmPairing, dataDevice)"
              >
                <mat-icon
                  svgIcon="ic-pairing"
                  class="small-icon"
                  aria-hidden="true"
                ></mat-icon>
                {{ "deviceManagement.confirmPairing" | translate }}
              </button>
              }
            </p>
          </div>
          } @if(!isLDCM) {
          <ng-container
            *ngTemplateOutlet="
              itemDevice;
              context: {
                label: 'deviceManagement.dcmModelYear',
                value: deviceInfo?.dcmModelYear
              }
            "
          ></ng-container>
          <ng-container
            *ngTemplateOutlet="
              itemDevice;
              context: {
                label: 'deviceManagement.dcmDestination',
                value: deviceInfo?.dcmDestination
              }
            "
          ></ng-container>
          <div
            class="device-info-section__details-additional-info"
            [class.hidden]="!showMore"
          >
            <ng-container
              *ngTemplateOutlet="
                itemDevice;
                context: {
                  label: 'deviceManagement.countryCode',
                  value: deviceInfo?.countryCode
                }
              "
            ></ng-container>
            <ng-container
              *ngTemplateOutlet="
                itemDevice;
                context: {
                  label: 'deviceManagement.dcmPartNumber',
                  value: deviceInfo?.dcmPartNumber
                }
              "
            ></ng-container>
            <ng-container
              *ngTemplateOutlet="
                itemDevice;
                context: {
                  label: 'deviceManagement.dcmSupplier',
                  value: deviceInfo?.dcmSupplier
                }
              "
            ></ng-container>
            <ng-container
              *ngTemplateOutlet="
                itemDevice;
                context: {
                  label: 'deviceManagement.dcmGrade',
                  value: deviceInfo?.dcmGrade
                }
              "
            ></ng-container>
            <ng-container
              *ngTemplateOutlet="
                itemDevice;
                context: {
                  label: 'deviceManagement.cecuid',
                  value: deviceInfo?.cecuid
                }
              "
            ></ng-container>
            <ng-container
              *ngTemplateOutlet="
                itemDevice;
                context: {
                  label: 'deviceManagement.vehicleUnitTerminalSerialNumber',
                  value: deviceInfo?.vehicleUnitTerminalSerialNumber
                }
              "
            ></ng-container>
            <div class="device-info-section__item width-full">
              <p class="device-info-section__label">
                {{ "deviceManagement.bleSerialNumber" | translate }}
              </p>
              <p class="device-info-section__value">
              <strong>{{ deviceInfo?.bleSerialNumber || "N/A" }}</strong>
              </p>
            </div>
          </div>
          <div
            class="device-info-section__item device-info-section__item-action"
          >
            <button
              class="btn-link device-info-section__show-more"
              (click)="toggleShowMore()"
            >
              <mat-icon
                svgIcon="ic-down"
                class="small-icon"
                aria-hidden="true"
                [class.rotate]="showMore"
              ></mat-icon>
              {{ showMore ? "View Less" : "View More" }}
            </button>
          </div>
          }
        </div>
      </div>

      <div
        class="device-detail__section device-detail__right-section"
        [class.adjust-height]="showMore"
      >
        <div class="device-detail__section pairing-section">
          <h3>
            <mat-icon svgIcon="ic-computer" class="medium-icon"></mat-icon>
            {{ "deviceManagement.pairing" | translate }}
          </h3>

          @if(pairingInfo?.status?.code === pairingStatus.Paired) {
          <div class="pairing-section__details">
            <ng-container
              *ngTemplateOutlet="
                itemVehicle;
                context: {
                  label: 'deviceManagement.status',
                  value:
                  pairingInfo?.status?.name || 'N/A'
                }
              "
            ></ng-container>
            @if(pairingInfo?.pairedOn) {
            <ng-container
              *ngTemplateOutlet="
                itemVehicle;
                context: {
                  label: 'deviceManagement.pairedOn',
                  value: pairingInfo?.pairedOn | date : 'MM/dd/yyyy'
                }
              "
            ></ng-container>
            } @if(pairingInfo?.pairedBy && isLDCM) {
            <ng-container
              *ngTemplateOutlet="
                itemVehicle;
                context: {
                  label: 'deviceManagement.pairedBy',
                  value: pairingInfo?.pairedBy
                }
              "
            ></ng-container>
            }
          </div>
          } @else {
          <div class="device-info-section__item">
            <p class="device-info-section__label">
              {{ "deviceManagement.status" | translate }}
            </p>
            <p class="device-info-section__value device-info-section__status">
              <span
                ><strong>{{
                  pairingInfo?.status?.name || "N/A"
                }}</strong>
                <button
                  *ngIf="userService.isHasPermission([PERMISSIONS_CODE.IOT_DEVICE_PAIR])"
                  class="btn-link"
                  (click)="onActionClick(deviceAction.PairDevice, dataDevice)"
                >
                  <mat-icon
                    svgIcon="ic-link-pair-red"
                    class="small-icon"
                  ></mat-icon>
                  {{ "deviceManagement.pair" | translate }}
                </button></span
              >
            </p>
          </div>
          }
        </div>

        <div class="device-detail__section sim-section">
          <h3>
            <mat-icon svgIcon="ic-sim-1" class="medium-icon"></mat-icon>
            {{ "deviceManagement.sim" | translate }}
          </h3>
          <div class="sim-section__details">
            <ng-container
              *ngTemplateOutlet="
                itemVehicle;
                context: {
                  label: 'deviceManagement.iccid',
                  value: simInfo?.iccid
                }
              "
            ></ng-container>
            <div class="sim-section__status">
              <p>
                {{ "deviceManagement.status" | translate }}
                <span>
                  <strong>{{
                    simInfo?.statusEnum?.name || "N/A"
                  }}</strong>
                  
                  @if(pairingInfo?.status?.code === pairingStatus.Paired &&
                  isLDCM && userService.isHasPermission([PERMISSIONS_CODE.IOT_SIM_ENABLE_DISABLE])) {
                  @if((simInfo?.statusEnum?.code === simStatus.Inactive ||
                  simInfo?.statusEnum?.code  === simStatus.Pending) && dataDevice.canActiveFor !== 'none') {
                  <button
                    class="btn-link"
                    (click)="onActionClick(deviceAction.EnableSIM, dataDevice)"
                  >
                    <mat-icon
                      svgIcon="ic-red-sim"
                      class="small-icon"
                    ></mat-icon>
                    {{ "deviceManagement.enableSIM" | translate }}
                  </button>
                  } @else if(simInfo?.statusEnum?.code  === simStatus.ActiveTesting ||
                  simInfo?.statusEnum?.code  === simStatus.ActiveSubscription) {
                  <button
                    class="btn-link"
                    (click)="onActionClick(deviceAction.DisableSIM, dataDevice)"
                  >
                    <mat-icon
                      svgIcon="ic-red-sim"
                      class="small-icon"
                    ></mat-icon>
                    {{ "deviceManagement.disableSIM" | translate }}
                  </button>
                  } }
                </span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    @if(vehicalInfo && pairingInfo?.status?.code === pairingStatus.Paired) {
    <div class="device-detail__vehicle">
      <h3>
        <mat-icon svgIcon="ic-light-car" class="medium-icon"></mat-icon
        >{{ "deviceManagement.vehicle" | translate }}
      </h3>
      <div class="vehicle-info-icons">
        <div class="vehicle-info-icons__item">
          <mat-icon svgIcon="ic-automatic" class="large-icon"></mat-icon>
          <span class="vehicle-info-icons__text">
            <strong
              [class.empty]="
                vehicalInfo?.odo === undefined ||
                vehicalInfo?.odo === null ||
                vehicalInfo?.odo === ''
              "
              >{{
                vehicalInfo?.odo !== undefined &&
                vehicalInfo?.odo !== null &&
                vehicalInfo?.odo !== ""
                  ? vehicalInfo?.odo
                  : "N/A"
              }}</strong
            >
            <p class="empty">{{ "deviceManagement.traveled" | translate }}</p>
          </span>
        </div>
        <div class="vehicle-info-icons__item">
          <mat-icon svgIcon="ic-engine" class="large-icon"></mat-icon>
          <span class="vehicle-info-icons__text">
            <strong
              [class.empty]="
                vehicalInfo?.engineStatus === undefined ||
                vehicalInfo?.engineStatus === null ||
                vehicalInfo?.engineStatus === ''
              "
              [class.engine]="
              vehicalInfo?.engineStatus !== undefined &&
                vehicalInfo?.engineStatus !== null &&
                vehicalInfo?.engineStatus !== ''
              "
              >{{
                vehicalInfo?.engineStatus !== null &&
                vehicalInfo?.engineStatus !== undefined
                  ? vehicalInfo?.engineStatus
                    ? "ON"
                    : "OFF"
                  : "N/A"
              }}</strong
            >
            <p class="empty">{{ "deviceManagement.engine" | translate }}</p>
          </span>
        </div>
        <div class="vehicle-info-icons__item">
          <mat-icon svgIcon="ic-gas" class="large-icon"></mat-icon>
          <span class="vehicle-info-icons__text">
            <strong
              [class.empty]="
                vehicalInfo?.fuelRemaining === undefined ||
                vehicalInfo?.fuelRemaining === null ||
                vehicalInfo?.fuelRemaining === ''
              "
              [class.gas]="
                vehicalInfo?.fuelRemaining !== undefined &&
                vehicalInfo?.fuelRemaining !== null &&
                vehicalInfo?.fuelRemaining !== ''
              "
              >{{
                vehicalInfo?.fuelRemaining !== undefined &&
                vehicalInfo?.fuelRemaining !== null &&
                vehicalInfo?.fuelRemaining !== ""
                  ? vehicalInfo?.fuelRemaining
                  : "N/A"
              }}</strong
            >
            <p class="empty">{{ "deviceManagement.fuelLeft" | translate }}</p>
          </span>
        </div>
        <div class="vehicle-info-icons__item">
          <span class="vehicle-info-icons__text">
            <p [class.empty]="!vehicalInfo?.lastLocation">
              {{ "deviceManagement.lastLocation" | translate }}
            </p>
            <strong [class.empty]="!vehicalInfo?.lastLocation">
              <span>{{ vehicalInfo?.lastLocation || "N/A" }}</span>
              @if(pairingInfo?.status?.code === pairingStatus.Paired &&
              (simInfo?.statusEnum?.code  === simStatus.ActiveTesting ||
              simInfo?.statusEnum?.code  === simStatus.ActiveSubscription)) {
              <span
                ><button
                  class="btn-link device-info-section__refesh"
                  (click)="onRefresh()"
                >
                  <mat-icon
                    svgIcon="ic-refresh"
                    class="small-icon"
                    aria-hidden="true"
                  ></mat-icon>
                  {{ "deviceManagement.refresh" | translate }}
                </button></span
              >
              }
            </strong>
          </span>
        </div>
      </div>
      <div class="vehicle-info-details">
        <ng-container
          *ngTemplateOutlet="
            itemVehicle;
            context: {
              label: 'deviceManagement.vin',
              value: vehicalInfo?.chasissNum
            }
          "
        ></ng-container>
        <ng-container
          *ngTemplateOutlet="
            itemVehicle;
            context: {
              label: 'deviceManagement.modelSalesCode',
              value: vehicalInfo?.modelSalesCode
            }
          "
        ></ng-container>
        <ng-container
          *ngTemplateOutlet="
            itemVehicle;
            context: {
              label: 'deviceManagement.make',
              value: vehicalInfo?.vehicleMake
            }
          "
        ></ng-container>
        <ng-container
        *ngTemplateOutlet="
          itemVehicle;
          context: {
            label: 'deviceManagement.model',
            value: vehicalInfo?.vehicleModel
          }
        "
      ></ng-container>
        <ng-container
          *ngTemplateOutlet="
            itemVehicle;
            context: {
              label: 'deviceManagement.modelYear',
              value: vehicalInfo?.vehicleModel
            }
          "
        ></ng-container>
        <ng-container
          *ngTemplateOutlet="
            itemVehicle;
            context: {
              label: 'deviceManagement.plate',
              value: vehicalInfo?.plateNum
            }
          "
        ></ng-container>
      </div>
    </div>
    }
  </div>
</div>
}
<ng-template #itemDevice let-value="value" let-label="label">
  <div class="device-info-section__item">
    <p class="device-info-section__label">{{ label | translate }}</p>
    <p class="device-info-section__value"><strong>{{ value || "N/A" | translate }}</strong></p>
  </div>
</ng-template>

<ng-template #itemVehicle let-value="value" let-label="label">
  <p [class.empty]="value === undefined || value === null || value === ''">
    {{ label | translate }} <strong>{{ value || "N/A" | translate }}</strong>
  </p>
</ng-template>
