<mat-icon svgIcon="ic-import" class="icon-title"></mat-icon>
<h2 class="title-dialog">{{ data?.title }}</h2>

<div class="container-dialog">
  <app-import-files
    [label]="'deviceManagement.selectConsentFile' | translate : {
      deviceId: data?.deviceDetail?.deviceId
    }"
    [placeholder]="'deviceManagement.noFileSelected' | translate"
    [control]="form.controls.selectFile"
    [accept]="accept"
    [maxFileSize]="maxFileSize"
    (filesSelected)="handleFiles($event)"
    (handleInvalidFile)="handleInvalidFile()"
    (handleMaxFile)="handleMaxFile()"
  ></app-import-files>
</div>

<div class="action-dialog">
  <button class="btn-quaternary" (click)="onCancel()">
    {{ "common.cancel" | translate }}
  </button>

  <button class="btn-primary" [disabled]="form.invalid" (click)="onConfirm()">
    {{ "common.upload" | translate }}
  </button>
</div>
