<app-ag-grid-custom
  class="device-grid"
  [rowData]="rowData"
  [colDefs]="colDefs"
  [defaultColDef]="defaultColDef"
  [templates]="customTemplates"
  [isPaging]="true"
  [importInfo]="importInfo"
  [pagingInfo]="pagingInfo"
  [isLoadingLastImport]="isLoadingLastImport"
  (onPageChange)="onPageChange.emit($event)"
  (changeItemPerPage)="changeItemPerPage.emit($event)"
  (exportData)="exportData.emit($event)"
  (downloadFile)="downloadFile.emit()"
>
</app-ag-grid-custom>

<ng-template #actionTemplate let-value="value">
  <span class="action-grid" *ngIf="value | deviceAction : isLDCM as action">
    <mat-icon [svgIcon]="action.icon"></mat-icon>
    <span>{{ action.text }}</span>
  </span>
</ng-template>

<ng-template #simStatusTemplate let-value="value">
  <span class="value-tag">
    <span>{{ value.simStatus?.name }}</span>
  </span>
</ng-template>

<app-import-result-summary
  *ngIf="isLDCM && userService.isHasPermission([PERMISSIONS_CODE.IOT_DEVICE_IMPORT])"
  [importDate]="importInfo?.date"
  [resultMessage]="importInfo?.resultMessage"
  [fileName]="
    importInfo?.failedRecords?.realFileName ||
    importInfo?.importFile?.realFileName
  "
  [downloadUrl]="
    importInfo?.failedRecords?.downloadUrl ||
    importInfo?.importFile?.downloadUrl
  "
  [isLoading]="isLoadingLastImport"
  [title]="'lastImportResult.lastImportDeviceResult'"
  (downloadFile)="downloadFile.emit()"
></app-import-result-summary>

<app-import-result-summary
  *ngIf="isLDCM && userService.isHasPermission([PERMISSIONS_CODE.IOT_SIM_IMPORT_ACTIVE])"
  [importDate]="importInfoSim?.date"
  [resultMessage]="importInfoSim?.resultMessage"
  [fileName]="
    importInfoSim?.failedRecords?.realFileName ||
    importInfoSim?.importFile?.realFileName
  "
  [downloadUrl]="
    importInfoSim?.failedRecords?.downloadUrl ||
    importInfoSim?.importFile?.downloadUrl
  "
  [title]="'lastImportResult.lastImportSimResult'"
  (downloadFile)="downloadFileSim.emit()"
></app-import-result-summary>
