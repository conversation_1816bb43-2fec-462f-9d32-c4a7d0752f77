import { CommonModule } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';

import { FilterDeviceComponent } from './filter-device';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { DeviceTableomponent } from './device-table';

import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { MatDialog } from '@angular/material/dialog';
import { ImportDevicesComponent } from '../import-devices/import-devices.component';
import { MatIconModule } from '@angular/material/icon';

import { filter, take } from 'rxjs';
import { ModalPairDeviceComponent } from './modal-pair-device/modal-pair-device.component';
import { ConsentFileCellComponent } from './device-table/consent-file-cell/consent-file-cell.component';
import { UploadConsentFileComponent } from './device-table/upload-consent-file/upload-consent-file.component';
import { IconCellRendererComponent } from '../../../../../core/shared/ag-grid-custom/icon-cell-renderer/icon-cell-renderer.component';
import { LoadingComponent } from '../../../../../layout/global/loading/loading.component';
import { RouterLinkCellRendererComponent, WidgetSummaryComponent } from '../../../../../core/shared';
import { DeviceService } from '../../../services';
import { LoadingService, NotificationService, UserService } from '../../../../../core/services';
import { ActivationStatus, DeviceAction, DeviceManagementTab, DeviceStatus, DeviceSummaryTab, PairingStatus, SimStatus } from '../../../enums';
import { PERMISSIONS_CODE } from '../../../../../core/constants';
import { FilterDevice, ItemDevice, PayloadGetDevice } from '../../../interfaces';
import { getDeviceAction } from '../../../helpers';
import { ItemWidget, LastImportResult, PagingInfo } from '../../../../../core/interfaces';
import { ActionModal, ActionTable } from '../../../../../core/enums';
import { GDCM_WIDGETS, LDCM_WIDGETS } from '../../../constants';
import { handleErrors } from '../../../../../core/helpers';

@Component({
  selector: 'app-device-list',
  templateUrl: 'device-list.component.html',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    MatIconModule,
    FilterDeviceComponent,
    DeviceTableomponent,
    IconCellRendererComponent,
    LoadingComponent,
    WidgetSummaryComponent,
    ConsentFileCellComponent,
  ],
  providers: [DeviceService, LoadingService, NotificationService],
  standalone: true,
})
export class DeviceListComponent implements OnInit {
  router = inject(Router);
  loadingService = inject(LoadingService);
  translateService = inject(TranslateService);
  dialog = inject(MatDialog);
  deviceService = inject(DeviceService);
  notificationService = inject(NotificationService);
  userService = inject(UserService);
  private route = inject(ActivatedRoute);

  readonly DeviceManagementTab = DeviceManagementTab;
  readonly DeviceSummaryTab = DeviceSummaryTab;
  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;

  selectedTab: DeviceManagementTab;
  selectedSummaryTab: DeviceSummaryTab;

  filterForm = new FormGroup({
    deviceId: new FormControl(),
    deviceStatus: new FormControl(),
    activationStatus: new FormControl(),
    pairingStatus: new FormControl(),
    simStatus: new FormControl(),
  });

  devicesTab = [
    { id: DeviceManagementTab.LDCM, name: 'LDCM Devices' },
    { id: DeviceManagementTab.GDCM, name: 'GDCM Devices' },
  ];

  isLDCM = true;

  rowData: ItemDevice[];
  colDefs = [
    {
      headerName: this.translateService.instant('deviceManagement.deviceId'),
      headerValueGetter: () =>
        this.translateService.instant('deviceManagement.deviceId'),
      field: 'deviceId',
      wrapText: true,
      autoHeight: true,
      cellRenderer: RouterLinkCellRendererComponent,
      cellRendererParams: {
        linkBuilder: (data: any) => this.userService.isHasPermission([
          PERMISSIONS_CODE.IOT_DEVICE_DETAIL_VIEW,
        ]) ? `/devices/${data?.deviceId}` : '',
      }
    },
    {
      headerName: this.translateService.instant(
        'deviceManagement.signalTestingStatus'
      ),
      headerValueGetter: () =>
        this.translateService.instant('deviceManagement.signalTestingStatus'),
      field: 'signalTestingStatus',
      flex: 1,
      autoHeaderHeight: true,
      cellRenderer: (params) => {
        return params?.value ? `<span>${params?.value}</span>` : '';
      },
      cellClassRules: {
        'value-tag': (params) => !!params.value,
      },
    },
    {
      headerName: this.translateService.instant('deviceManagement.deviceStaus'),
      headerValueGetter: () =>
        this.translateService.instant('deviceManagement.deviceStaus'),
      field: 'deviceStatus',
      flex: 1,
      cellRenderer: (params) => {
        return params?.value ? `<span>${params?.value?.name}</span>` : '';
      },
      cellClassRules: {
        'value-tag': (params) => !!params.value,
      },
    },
    {
      headerName: this.translateService.instant(
        'deviceManagement.activationStatus'
      ),
      headerValueGetter: () =>
        this.translateService.instant('deviceManagement.activationStatus'),
      field: 'activationStatus',
      flex: 1,
      cellRenderer: (params) => {
        return params?.value ? `<span>${params?.value?.name}</span>` : '';
      },
      cellClassRules: {
        'value-tag': (params) => !!params.value,
      },
    },
    {
      headerName: this.translateService.instant(
        'deviceManagement.pairingStatus'
      ),
      headerValueGetter: () =>
        this.translateService.instant('deviceManagement.pairingStatus'),
      field: 'pairingStatus',
      flex: 1,
      cellRenderer: (params) => {
        return params?.value ? `<span>${params?.value?.name}</span>` : '';
      },
      cellClassRules: {
        'value-tag': (params) => !!params.value,
      },
    },
    {
      headerName: this.translateService.instant('deviceManagement.vin'),
      headerValueGetter: () =>
        this.translateService.instant('deviceManagement.vin'),
      field: 'vin',
      flex: 1,
    },
    {
      headerName: this.translateService.instant('deviceManagement.simStatus'),
      headerValueGetter: () =>
        this.translateService.instant('deviceManagement.simStatus'),
      field: 'simStatus',
      flex: 1,
      cellRenderer: (params) => {
        return params?.value ? `<span>${params?.value?.name}</span>` : '';
      },
      cellClassRules: {
        'value-tag': (params) => !!params.value,
      },
      headerComponentParams: {
        id: 'simStatus',
      },
    },
    {
      headerName: this.translateService.instant('deviceManagement.consentFile'),
      headerValueGetter: () =>
        this.translateService.instant('deviceManagement.consentFile'),
      field: 'consentFile',
      minWidth: 150,
      autoHeight: true,
      cellClass: 'action-upload',
      cellRenderer: ConsentFileCellComponent,
      cellRendererParams: {
        onClick: (type: string, data: any) => this.handleActionFile(type, data),
      },
      tooltipValueGetter: (params) => {
        return params?.data?.importFile
          ? params?.data?.importFile?.realFileName
          : null;
      },
    },
    {
      headerName: this.translateService.instant('deviceManagement.action'),
      headerValueGetter: () =>
        this.translateService.instant('deviceManagement.action'),
      field: 'action',
      minWidth: 100,
      wrapText: true,
      autoHeight: true,
      cellRendererSelector: (params) => {
        return {
          component: IconCellRendererComponent,
          params: getDeviceAction(params?.data, this.isLDCM, this.userService),
        };
      },
      cellClass: 'action-grid',
      headerComponentParams: {
        id: 'action',
      },
      onCellClicked: (event) => this.onActionClick(event?.data),
    },
  ];
  pagingInfo: PagingInfo = {
    totalItems: 0,
    currentPage: 0,
    numberOfPage: 10,
  };
  importInfo: LastImportResult;
  importInfoSim: LastImportResult;

  numberOfPageDefault = 10;

  filterDevice: FilterDevice;

  currentWidgets: ItemWidget[] = [
    {
      id: '' as DeviceSummaryTab,
      count: 0,
      description: '',
      icon: '',
    },
  ];

  isLoadingLastImport = false;
  isChangeItemPerPageTop = false;
  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      const tab = params['tab'];
      if (tab === DeviceManagementTab.LDCM) {
        this.isLDCM = true;
        this.selectedTab = DeviceManagementTab.LDCM;
      } else if (tab === DeviceManagementTab.GDCM) {
        this.isLDCM = false;
        this.selectedTab = DeviceManagementTab.GDCM;
      } else {
        this.isLDCM = true;
        this.selectedTab = DeviceManagementTab.LDCM;
      }
      this.router.navigate([], {
        queryParams: { tab: this.selectedTab },
        queryParamsHandling: 'merge',
      });
    });

    this.route.queryParams.pipe(take(1)).subscribe((params) => {
      const { activationStatus, pairingStatus, simStatus } = params;
      this.filterForm.patchValue({
        deviceId: '',
        deviceStatus: DeviceStatus.All,
        activationStatus: activationStatus || ActivationStatus.All,
        pairingStatus: pairingStatus || PairingStatus.All,
        simStatus: simStatus || SimStatus.All,
      });

      this.router.navigate([], {
        relativeTo: this.route,
        queryParams: { activationStatus: null, pairingStatus: null, simStatus: null },
        queryParamsHandling: 'merge',
        replaceUrl: true,
      });

      this.getDeviceList();
    });

    this.isLDCM = this.selectedTab === DeviceManagementTab.LDCM;
    this.getAllFilter();
    this.getDeviceWidgets();
    if (
      this.userService.isHasPermission([PERMISSIONS_CODE.IOT_DEVICE_IMPORT])
    ) {
      this.getLastImportResult();
    }

    if (
      this.userService.isHasPermission([PERMISSIONS_CODE.IOT_SIM_IMPORT_ACTIVE])
    ) {
      this.getLastImportSimResult();
    }
  }

  viewDeviceDetail(deviceId: number) {
    if (
      this.userService.isHasPermission([
        PERMISSIONS_CODE.IOT_DEVICE_DETAIL_VIEW,
      ])
    ) {
      this.router.navigate(['/devices', deviceId]);
    }
  }

  changeDeviceTab(id: DeviceManagementTab) {
    this.isLDCM = id === DeviceManagementTab.LDCM;
    this.selectedTab = id;
    this.pagingInfo.numberOfPage = this.numberOfPageDefault;
    this.pagingInfo.currentPage = 0;
    this.router.navigate([], {
      queryParams: { tab: id },
      queryParamsHandling: 'merge',
    });

    this.filterForm.patchValue({
      deviceId: '',
      deviceStatus: DeviceStatus.All,
      activationStatus: ActivationStatus.All,
      pairingStatus: PairingStatus.All,
      simStatus: SimStatus.All,
    });
    // GetFilter
    this.getDeviceWidgets();
    this.getDeviceList();
  }

  changeSummaryTab(id: DeviceSummaryTab) {
    this.selectedSummaryTab = id;
    this.pagingInfo.currentPage = 0;

    this.mapWidgetFilter(id);
    this.getDeviceList();
  }

  mapWidgetFilter(id: DeviceSummaryTab) {
    switch (id) {
      case DeviceSummaryTab.WaitingForPairing: {
        this.filterForm.patchValue({
          deviceId: '',
          deviceStatus: DeviceStatus.All,
          activationStatus: ActivationStatus.All,
          pairingStatus: PairingStatus.NotPaired,
          simStatus: SimStatus.All,
        });
        break;
      }
      case DeviceSummaryTab.WaitingForEnable: {
        this.filterForm.patchValue({
          deviceId: '',
          deviceStatus: DeviceStatus.All,
          activationStatus: ActivationStatus.All,
          pairingStatus: PairingStatus.All,
          simStatus: SimStatus.Pending,
        });
        break;
      }
      case DeviceSummaryTab.NonOperational: {
        this.filterForm.patchValue({
          deviceId: '',
          deviceStatus: DeviceStatus.All,
          activationStatus: ActivationStatus.NonOperational,
          pairingStatus: PairingStatus.All,
          simStatus: SimStatus.All,
        });
        break;
      }

      //GDCM
      case DeviceSummaryTab.WaitingForTerminated: {
        this.filterForm.patchValue({
          deviceId: '',
          deviceStatus: DeviceStatus.ForTermination,
          activationStatus: ActivationStatus.All,
          pairingStatus: null,
          simStatus: SimStatus.All,
        });
        break;
      }
      case DeviceSummaryTab.NonOperationalGDCM: {
        this.filterForm.patchValue({
          deviceId: '',
          deviceStatus: DeviceStatus.All,
          activationStatus: ActivationStatus.NonOperational,
          pairingStatus: null,
          simStatus: SimStatus.All,
        });
        break;
      }
      case DeviceSummaryTab.OperationalDevices: {
        this.filterForm.patchValue({
          deviceId: '',
          deviceStatus: DeviceStatus.All,
          activationStatus: ActivationStatus.Operational,
          pairingStatus: null,
          simStatus: SimStatus.All,
        });
        break;
      }
    }
  }

  getDeviceList(): void {
    const params: PayloadGetDevice = {
      currentPage: this.pagingInfo?.currentPage,
      pageSize: this.pagingInfo?.numberOfPage,
      pairingStatus: this.filterForm.value.pairingStatus,
      simStatus: this.filterForm.value.simStatus,
      activationStatus: this.filterForm.value.activationStatus,
      deviceStatus: this.filterForm.value.deviceStatus,
      idOrVin: this.filterForm.value.deviceId,
      includeHistory: false,
    };

    this.loadingService.showLoader();
    this.deviceService.getDeviceList(params, this.selectedTab).subscribe({
      next: (response: any) => {
        this.rowData = response?.items;

        this.pagingInfo.totalItems = response?.pagination?.totalResults;

        this.loadingService.hideLoader();
        this.isChangeItemPerPageTop = false;

        this.colDefs = this.colDefs.map((item) => {
          if (item?.field === 'consentFile') {
            return { ...item, hide: !this.isLDCM };
          }
          return item;
        }) as any;
      },
      error: () => {
        this.loadingService.hideLoader();
      },
    });
  }

  onPageChange(value: number): void {
    this.pagingInfo.currentPage = value;
    this.getDeviceList();
  }

  searchDevice(): void {
    this.pagingInfo.currentPage = 0;
    this.getDeviceList();
  }

  importDevices(): void {
    const dialogRef = this.dialog.open(ImportDevicesComponent, {
      width: '530px',
      data: {
        title: this.translateService.instant('deviceManagement.importDevices'),
        confirmMsg: this.translateService.instant(
          'deviceManagement.devicesImportedSuccess'
        ),
      },
    });

    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((result) => {
        this.deviceService.importDevices(result?.data).subscribe((response) => {
          this.notificationService.showSuccess(
            this.translateService.instant('deviceManagement.importInProgress')
          );
          this.getDeviceList();

          this.isLoadingLastImport = true;
          setTimeout(() => {
            this.getLastImportResult();
          }, 3000);
        });
      });
  }

  importSims(): void {
    const dialogRef = this.dialog.open(ImportDevicesComponent, {
      width: '530px',
      data: {
        title: this.translateService.instant('deviceManagement.importSims'),
        confirmMsg: this.translateService.instant(
          'deviceManagement.devicesImportedSuccess'
        ),
      },
    });

    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((result) => {
        this.deviceService.importSims(result?.data).subscribe((response) => {
          this.notificationService.showSuccess(
            this.translateService.instant('deviceManagement.importInProgress')
          );
          this.getDeviceList();
          this.isLoadingLastImport = true;
          setTimeout(() => {
            this.getLastImportSimResult();
          }, 3000);
        });
      });
  }

  changeItemPerPage(item) {
    this.pagingInfo.numberOfPage = item;
    this.pagingInfo.currentPage = 0;
    this.getDeviceList();
  }

  getAllFilter(): void {
    this.loadingService.showLoader();
    this.deviceService.getDevicesFilter().subscribe((response) => {
      this.loadingService.hideLoader();
      this.filterDevice = response;
    });
  }

  exportData(): void {
    this.loadingService.showLoader();
    this.deviceService.exportDevice(this.selectedTab).subscribe((response) => {
      this.loadingService.hideLoader();

      const link = document.createElement('a');

      link.href = URL.createObjectURL(response?.blob);
      link.download = response?.filename;
      link.click();
      URL.revokeObjectURL(link.href);
    });
  }

  getDeviceWidgets(): void {
    this.loadingService.showLoader();
    this.deviceService.deviceWidgets(this.selectedTab).subscribe((response) => {
      this.loadingService.hideLoader();
      const { widgets } = response;
      const descriptionWidget = this.isLDCM ? LDCM_WIDGETS : GDCM_WIDGETS;

      const widgetOrderMap = descriptionWidget.reduce((acc, widget, index) => {
        acc[widget.id] = index;
        return acc;
      }, {} as Record<string, number>);

      this.currentWidgets = widgets
        .map((item) => {
          const { description, icon } =
            descriptionWidget.find((widget) => widget.id === item.type) || {};
          return {
            id: item?.type,
            count: item?.count,
            description,
            icon,
          };
        })
        .sort(
          (a, b) =>
            (widgetOrderMap[a.id] ?? Infinity) -
            (widgetOrderMap[b.id] ?? Infinity)
        );
    });
  }

  getLastImportResult(): void {
    this.deviceService.getLastImportResult().subscribe((response) => {
      this.isLoadingLastImport = false;
      this.importInfo = response;
    });
  }

  getLastImportSimResult(): void {
    this.deviceService.getLastImportSimResult().subscribe((response) => {
      this.isLoadingLastImport = false;
      this.importInfoSim = response;
    });
  }

  pairDevice(): void {
    const dialogRef = this.dialog.open(ModalPairDeviceComponent, {
      width: '530px',
      data: {
        title: this.translateService.instant(
          'deviceManagement.modalPairDevice.title'
        ),
        icon: 'ic-link-pair-red',
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('deviceManagement.pair'),
      },
    });

    dialogRef.componentInstance.onPairingComplete.subscribe(
      (success: boolean) => {
        if (success) {
          this.getDeviceList();
        }
      }
    );

    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((result) => {});
  }

  onActionClick(device: ItemDevice): void {
    const typeAction = getDeviceAction(
      device,
      this.isLDCM,
      this.userService
    )?.id;
    switch (typeAction) {
      case DeviceAction.PairDevice: {
        const dialogRef = this.dialog.open(ModalPairDeviceComponent, {
          width: '530px',
          data: {
            title: this.translateService.instant(
              'deviceManagement.modalPairDevice.title'
            ),
            icon: 'ic-link-pair-red',
            cancelBtn: this.translateService.instant('common.cancel'),
            submitBtn: this.translateService.instant('deviceManagement.pair'),
            deviceId: device.deviceId,
          },
        });
        dialogRef.componentInstance.onPairingComplete.subscribe(
          (success: boolean) => {
            if (success) {
              this.getDeviceList();
            }
          }
        );
        dialogRef
          .afterClosed()
          .pipe(filter((result) => result?.action === ActionModal.Pair))
          .subscribe((result) => {});
        break;
      }

      case DeviceAction.EnableSIM: {
        this.deviceService.openEnableSim(device, () => {
          this.getDeviceList();
        });
        break;
      }
      case DeviceAction.DisableSIM: {
        this.deviceService.openDisableSim(device, () => {
          this.getDeviceList();
        });
        break;
      }
      case DeviceAction.ConfirmPairing: {
        this.deviceService.openConfirmPairing(device, this.isLDCM, () => {
          this.getDeviceList();
        });
        break;
      }

      case DeviceAction.TerminateDevice: {
        this.deviceService.openTerminateDevice(
          device.vin,
          device.deviceId,
          device.iccid,
          () => {
            this.getDeviceList();
          }
        );
        break;
      }
    }
  }

  downloadImportResult(): void {
    this.loadingService.showLoader();
    this.deviceService
      .downloadImportResult(
        this.importInfo?.failedRecords?.code ||
          this.importInfo?.importFile?.code
      )
      .subscribe(
        (response) => {
          this.loadingService.hideLoader();

          const link = document.createElement('a');

          link.href = URL.createObjectURL(response?.blob);
          link.download =
            this.importInfo?.failedRecords?.realFileNameWithoutExt ||
            this.importInfo?.importFile?.realFileNameWithoutExt;
          link.click();
          URL.revokeObjectURL(link.href);
        },
        (error) => {
          this.loadingService.hideLoader();
          handleErrors(error, this.notificationService);
        }
      );
  }

  downloadImportSimResult(): void {
    this.loadingService.showLoader();
    this.deviceService
      .downloadImportResult(
        this.importInfoSim?.failedRecords?.code ||
          this.importInfoSim?.importFile?.code
      )
      .subscribe(
        (response) => {
          this.loadingService.hideLoader();

          const link = document.createElement('a');

          link.href = URL.createObjectURL(response?.blob);
          link.download =
            this.importInfoSim?.failedRecords?.realFileNameWithoutExt ||
            this.importInfoSim?.importFile?.realFileNameWithoutExt;
          link.click();
          URL.revokeObjectURL(link.href);
        },
        (error) => {
          this.loadingService.hideLoader();
          handleErrors(error, this.notificationService);
        }
      );
  }

  handleActionFile(type: string, data: ItemDevice) {
    const isUpload = type === ActionTable.Upload;
    const dialogRef = this.dialog.open(UploadConsentFileComponent, {
      width: '550px',
      data: {
        title: this.translateService.instant(
          isUpload
            ? 'deviceManagement.uploadConsent'
            : 'deviceManagement.replaceConsent'
        ),
        deviceDetail: data,
      },
    });

    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((result) => {
        this.loadingService.showLoader();
        this.deviceService.uploadConsent(result?.data, data?.vin).subscribe(
          () => {
            this.loadingService.hideLoader();
            this.notificationService.showSuccess(
              this.translateService.instant(
                isUpload
                  ? 'deviceManagement.uploadConsentSuccess'
                  : 'deviceManagement.replaceConsentSuccess',
                { deviceId: data?.deviceId }
              )
            );
            this.getDeviceList();
          },
          (error) => {
            this.loadingService.hideLoader();
            handleErrors(error, this.notificationService);
          }
        );
      });
  }
}
