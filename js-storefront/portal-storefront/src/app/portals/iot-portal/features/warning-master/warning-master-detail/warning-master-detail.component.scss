@import "../../../../../../styles/abstracts/mixins";
@import "../../../../../../styles/abstracts/variables";

:host {
  min-height: calc(100vh - 75px);
  display: block;
  padding: 30px 30px 0;

  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;

    &__right {
      display: flex;
      gap: 20px;
      margin-bottom: 16px;
      
      > button {
        min-width: 150px;
        text-transform: uppercase;
      }
    }
  }

  .section-content {
    &__section-name {
      font-size: 17px;
      font-weight: 600;
      padding: 12px 15px;
      width: 100%;
      background-color: #eee;
      margin-bottom: 15px;
    }

    &__properties {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      padding: 0 15px 40px;
      gap: 30px;

      &__item {
        display: flex;
        flex-direction: column;
        gap: 8px;
        font-size: 16px;

        &.flex-space-between {
          justify-content: space-between;
        }
        
        &__name {
          font-weight: 600;

          .required {
            color: var($text-color-5);
          }
        }

        app-radio-button {
          ::ng-deep {
            mat-radio-group {
              flex-direction: row;
              gap: 35px;
            }
          }
        }

        &.response-leadtime {
          grid-column: 1 / span 2;
        }

        &.radio-section {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            grid-column: 1 / -1;
        }

        &.additional-info {
          grid-column: 3 / span 2;
        }

        .request-customer-action {
          ::ng-deep {
            .form-group {
              &__label {
                white-space: nowrap;
              }
            }
          }
        }
      }

      &.notification-prompt {
        grid-template-columns: repeat(3, 1fr);
      }
    }
  }
}

.custom-additional-info-chip {
  margin-top: 3px;

  ::ng-deep {
    mat-chip-row {
      margin-top: -2px;
      height: fit-content;
      padding: 2px;
    }
  }
}

.custom-response-leadtime-input {
  ::ng-deep {
    .form-group__input {
      margin-top: 10px;
    }
  }
}
