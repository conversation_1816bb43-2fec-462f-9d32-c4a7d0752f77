<div class="title">
  <div class="title__left">
    <app-breadcrumb-with-label
      [breadcrumbs]="[
        'warningMasterData.pageName',
        viewType === WarningMasterViewType.New
          ? 'warningMasterData.newWarning'
          : 'warningMasterData.editWarning'
      ]"
      [breadcrumbLinks]="[
        '/warning-master',
        '/warning-master/' + (warningMasterId || '')
      ]"
      [label]="
        viewType === WarningMasterViewType.New
          ? ('warningMasterData.newWarning' | translate)
          : warningMasterData?.name
      "
      [iconTitle]="
        viewType !== WarningMasterViewType.New && warningMasterData?.icon
          ? warningMasterData?.icon
          : null
      "
    >
    </app-breadcrumb-with-label>
  </div>
  <div class="title__right">
    <button
      *ngIf="
        viewType === WarningMasterViewType.New ||
        viewType === WarningMasterViewType.Edit
      "
      class="btn-quaternary"
      (click)="onCancel()"
    >
      {{ "common.cancel" | translate }}
    </button>

    @switch (viewType) { @case (WarningMasterViewType.New) {
    <button
      *ngIf="
        userService.isHasPermission([
          PERMISSIONS_CODE.IOT_WARNING_MASTER_CREATE
        ])
      "
      class="btn-primary btn-confirm"
      (click)="onAdd()"
      [disabled]="form.invalid"
    >
      {{ "common.add" | translate }}
    </button>
    } @case (WarningMasterViewType.Edit) {
    <button
      *ngIf="
        userService.isHasPermission([PERMISSIONS_CODE.IOT_WARNING_MASTER_EDIT])
      "
      class="btn-primary btn-confirm"
      (click)="onUpdate()"
      [disabled]="form.invalid"
    >
      {{ "common.update" | translate }}
    </button>
    } @default {
    <button
      *ngIf="
        userService.isHasPermission([
          PERMISSIONS_CODE.IOT_WARNING_MASTER_DELETE
        ])
      "
      class="btn-quaternary"
      (click)="onDelete()"
    >
      {{ "common.delete" | translate }}
    </button>
    <button
      *ngIf="
        userService.isHasPermission([PERMISSIONS_CODE.IOT_WARNING_MASTER_EDIT])
      "
      class="btn-primary btn-confirm"
      (click)="onEdit()"
    >
      {{ "common.edit" | translate }}
    </button>

    } }
  </div>
</div>

<div class="section-content">
  <!--  Overview -->
  <div class="section-content__section-name">
    {{ "warningMasterData.details.overview" | translate }}
  </div>
  <div class="section-content__properties">
    @if (viewType === WarningMasterViewType.View) {

    <div class="section-content__properties__item">
      <span class="section-content__properties__item__name">
        {{ "warningMasterData.details.name" | translate }}
        <span class="required">*</span>
      </span>
      <span> {{ warningMasterData?.name }}</span>
    </div>

    <div class="section-content__properties__item">
      <span class="section-content__properties__item__name">
        {{ "warningMasterData.details.code" | translate }}
        <span class="required">*</span>
      </span>
      <span> {{ warningMasterData?.code }}</span>
    </div>

    <div class="section-content__properties__item">
      <span class="section-content__properties__item__name">
        {{ "warningMasterData.details.type" | translate }}
      </span>
      <span> {{ warningMasterData?.type?.name }}</span>
    </div>

    <div class="section-content__properties__item">
      <span class="section-content__properties__item__name">
        {{ "warningMasterData.details.details" | translate }}
      </span>
      <span> {{ warningMasterData?.details || "-" }}</span>
    </div>

    } @else {
    <app-form-group
      [label]="'warningMasterData.details.name' | translate"
      [control]="form.controls.name"
      [required]="true"
      [errorMessage]="'validation.thisIsRequired' | translate"
    ></app-form-group>

    @if(viewType === WarningMasterViewType.Edit) {
    <div class="section-content__properties__item">
      <span class="section-content__properties__item__name">
        {{ "warningMasterData.details.code" | translate }}
        <span class="required">*</span>
      </span>
      <span> {{ warningMasterData?.code }}</span>
    </div>
    } @else {
    <app-form-group
      [label]="'warningMasterData.details.code' | translate"
      [control]="form.controls.code"
      [required]="true"
      [errorMessage]="'validation.thisIsRequired' | translate"
    ></app-form-group>
    }

    <app-dropdown-form-group
      [label]="'warningMasterData.details.type' | translate"
      [control]="form.controls.type"
      [options]="types"
    ></app-dropdown-form-group>

    <app-form-group
      [label]="'warningMasterData.details.details' | translate"
      [control]="form.controls.details"
    ></app-form-group>
    }
  </div>
  <!-- Reference information -->
  <div class="section-content__section-name">
    {{ "warningMasterData.details.referenceInformation" | translate }}
  </div>

  <div class="section-content__properties" [formGroup]="form">
    <!-- row 1 -->
    <div class="section-content__properties__item">
      @if (viewType === WarningMasterViewType.View) {
      <span class="section-content__properties__item__name">
        {{ "warningMasterData.details.requestedCustomerActions" | translate }}
      </span>
      <span> {{ warningMasterData?.actionCustomer || "-" }}</span>
      } @else {
      <app-form-group
        class="request-customer-action"
        [label]="
          'warningMasterData.details.requestedCustomerActions' | translate
        "
        [control]="form.controls.requestedCustomerAction"
      ></app-form-group>
      }
    </div>

    <div class="section-content__properties__item">
      @if (viewType === WarningMasterViewType.View) {
      <span class="section-content__properties__item__name">
        {{ "warningMasterData.details.priority" | translate }}
      </span>
      <span> {{ warningMasterData?.priority?.name || "-" }}</span>
      } @else {
      <app-dropdown-form-group
        [label]="'warningMasterData.details.priority' | translate"
        [control]="form.controls.priority"
        [options]="priorities"
      ></app-dropdown-form-group>
      }
    </div>

    <div class="section-content__properties__item">
      <app-radio-button
        [label]="'warningMasterData.details.vehicleCondition'"
        [control]="form.controls.vehicleCondition"
        [option]="vehicleConditionOptions"
      ></app-radio-button>
    </div>

    <div class="section-content__properties__item">
      @if (viewType === WarningMasterViewType.View) {
      <span class="section-content__properties__item__name">
        {{ "warningMasterData.details.safeCondition" | translate }}
      </span>
      <span> {{ warningMasterData?.safeCon || "-" }}</span>
      } @else {
      <app-form-group
        [label]="'warningMasterData.details.safeCondition' | translate"
        [control]="form.controls.safeCondition"
      ></app-form-group>
      }
    </div>

    <!-- row 2 -->
    <div class="section-content__properties__item radio-section">
        <app-radio-button
            [label]="'warningMasterData.details.requiredDealerVisit​'"
            [control]="form.controls.requiredDealerVisit"
            [option]="conditionOptions"
        ></app-radio-button>

        <app-radio-button
            [label]="'warningMasterData.details.notifyCustomer'"
            [control]="form.controls.notifyCustomer"
            [option]="conditionOptions"
        ></app-radio-button>

        <app-radio-button
            [label]="'warningMasterData.details.callTheCustomer'"
            [control]="form.controls.callTheCustomer"
            [option]="conditionOptions"
        ></app-radio-button>

        <app-radio-button
            [label]="'warningMasterData.details.flagInfoToDestributor'"
            [control]="form.controls.flagInfo"
            [option]="conditionOptions"
        ></app-radio-button>

        <app-radio-button
            [label]="'warningMasterData.details.notificationNecessity'"
            [control]="form.controls.notificationNecessity"
            [option]="conditionOptions"
        ></app-radio-button>
    </div>
    <!-- row 3 -->

    <div class="section-content__properties__item response-leadtime">
      @if (viewType === WarningMasterViewType.View) {
      <span class="section-content__properties__item__name">
        {{ "warningMasterData.details.responseLeadtime" | translate }}
      </span>
      <span> {{ warningMasterData?.responseLeadTime || "-" }}</span>
      } @else {
      <app-form-group
        class="custom-response-leadtime-input"
        [label]="'warningMasterData.details.responseLeadtime' | translate"
        [control]="form.controls.responseLeadtime"
      ></app-form-group>
      }
    </div>

    <div class="section-content__properties__item additional-info">
      <span class="section-content__properties__item__name">
        {{ "warningMasterData.details.additionalInfo" | translate }}
      </span>

      @if(additionalInfo.length <= 0 && viewType === WarningMasterViewType.View)
      { - } @else {
      <app-chip-set
        class="custom-additional-info-chip"
        [option]="additionalInfo"
        [control]="form.controls.additionalInfo"
      ></app-chip-set>
      }
    </div>
  </div>

  <!-- Notification prompt -->
  <div class="section-content__section-name">
    {{ "warningMasterData.details.notificationPrompt" | translate }}
  </div>

  <div class="section-content__properties notification-prompt">
    @if (viewType === WarningMasterViewType.View) {

    <div class="section-content__properties__item">
      <span class="section-content__properties__item__name">
        {{ "warningMasterData.details.application1" | translate }}
      </span>
      <span> {{ warningMasterData?.notificationPromptTitle || "-" }}</span>
    </div>

    <div class="section-content__properties__item">
      <span class="section-content__properties__item__name">
        {{ "warningMasterData.details.application2" | translate }}
      </span>
      <span> {{ warningMasterData?.notificationPromptDesc || "-" }}</span>
    </div>

    <div class="section-content__properties__item">
      <span class="section-content__properties__item__name">
        {{ "warningMasterData.details.application3" | translate }}
      </span>
      <span> {{ warningMasterData?.notificationPromptAction || "-" }}</span>
    </div>

    } @else {
    <app-form-group
      [label]="'warningMasterData.details.application1' | translate"
      [control]="form.controls.application1"
      [isTextArea]="true"
      [maxLength]="99999"
    ></app-form-group>

    <app-form-group
      [label]="'warningMasterData.details.application2' | translate"
      [control]="form.controls.application2"
      [isTextArea]="true"
      [maxLength]="99999"
    ></app-form-group>

    <app-form-group
      [label]="'warningMasterData.details.application3' | translate"
      [control]="form.controls.application3"
      [isTextArea]="true"
      [maxLength]="99999"
    ></app-form-group>

    }
  </div>
</div>
