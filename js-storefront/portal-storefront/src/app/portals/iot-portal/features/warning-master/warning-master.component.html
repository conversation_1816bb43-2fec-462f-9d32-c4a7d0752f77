@if (loadingService.isLoading) {
  <app-loading></app-loading>
  }

  <div class="vehicle-model-section">
    <div class="vehicle-model-section--box">
      <div class="vehicle-model-section__header">
        <h2 class="title-content-custom mb-0">
          {{ "warningMasterData.pageName" | translate }}
        </h2>
        @if (!(screenSizeService.isMobile() | async) && userService.isHasPermission([PERMISSIONS_CODE.IOT_WARNING_MASTER_CREATE])) {
        <div class="vehicle-model-section__button-group">
          <button
            type="button"
            class="btn btn-primary vehicle-model-section__button"
            (click)="onAddNew()"
          >
            {{ "warningMasterData.newWarning" | translate }}
          </button>
        </div>
        }
      </div>
      @if (!(screenSizeService.isMobile() | async)) {
      <form
        [formGroup]="warningMasterFormSearch"
        (ngSubmit)="onSearch()"
        class="vehicle-model-section__form"
      >
        <div class="vehicle-model-section__form-group">
          <app-form-group
            [label]="'warningMasterData.warningName' | translate"
            [control]="warningMasterNameControlSearch"
            [placeholder]="
              'warningMasterData.warningNamePlaceholder' | translate
            "
            controlId="warningName"
            class="vehicle-model-section__input"
          ></app-form-group>
  
          <app-form-group
            [label]="'warningMasterData.warningCode' | translate"
            [control]="warningMasterCodeControlSearch"
            [placeholder]="'warningMasterData.warningCodePlaceholder' | translate"
            controlId="warningCode"
            class="vehicle-model-section__input"
          ></app-form-group>
  
          <button
            type="submit"
            class="btn btn--primary vehicle-model-section__button"
          >
            {{ "common.search" | translate }}
          </button>
        </div>
      </form>
      }
      @if (rowData?.length > 0) {
        <app-ag-grid-custom
          [isPaging]="true"
          [pagingInfo]="pagingInfo"
          [rowData]="rowData"
          [colDefs]="colDefs"
          [isShowActionExport]="false"
          [defaultColDef]="defaultColDef"
          [isTextWrap]="true"
          (changeItemPerPage)="onResultsPerPageChange($event)"
          (onPageChange)="onPageChange($event)"
        ></app-ag-grid-custom>
      } @else {
        <div class="no-data">
          {{ "warningMasterData.table.noDataFound" | translate }}
        </div>
      }
    </div>
  </div>
