import { CommonModule } from '@angular/common';
import { Component, inject, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ActivatedRoute, Router } from '@angular/router';

import { Subscription } from 'rxjs';

import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { IconModule } from '../../../../../core/icon/icon.module';
import { BreadcrumbWithLabelComponent, ChipSetComponent, DropdownFormGroupComponent, FormGroupComponent, RadioButtonComponent } from '../../../../../core/shared';
import { LoadingService, NotificationService, UserService } from '../../../../../core/services';
import { WarningMasterDetailService, WarningService } from '../../../services';
import { NEW_WARNING_ROUTER, PERMISSIONS_CODE } from '../../../../../core/constants';
import { WarningMasterViewType } from '../../../enums';
import { WarningMasterDataDetail } from '../../../interfaces';
import { OptionDropdown } from '../../../../../core/interfaces';
import { handleErrors } from '../../../../../core/helpers';

@Component({
  selector: 'app-warning-master-detail',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    IconModule,
    ReactiveFormsModule,
    BreadcrumbWithLabelComponent,
    RadioButtonComponent,
    ChipSetComponent,
    FormGroupComponent,
    DropdownFormGroupComponent,
  ],
  templateUrl: './warning-master-detail.component.html',
  styleUrl: './warning-master-detail.component.scss',
  providers: [NotificationService, WarningMasterDetailService, WarningService],
})
export class WarningMasterDetailComponent implements OnInit, OnDestroy {
  activatedRoute = inject(ActivatedRoute);
  loadingService = inject(LoadingService);
  notificationService = inject(NotificationService);
  translateService = inject(TranslateService);
  router = inject(Router);

  warningMasterDetailService = inject(WarningMasterDetailService);
  warningService = inject(WarningService);
  userService = inject(UserService);

  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;
  readonly NEW_WARNING_ROUTER = NEW_WARNING_ROUTER;
  readonly WarningMasterViewType = WarningMasterViewType;

  subscription = new Subscription();

  viewType: WarningMasterViewType;

  warningMasterId: string;
  warningMasterData: WarningMasterDataDetail;

  additionalInfo: string[] = [];

  form = new FormGroup({
    name: new FormControl('', Validators.required),
    code: new FormControl('', Validators.required),
    type: new FormControl(''),
    details: new FormControl(''),
    requestedCustomerAction: new FormControl(''),
    priority: new FormControl('LOW'),
    vehicleCondition: new FormControl(true),
    safeCondition: new FormControl(''),
    requiredDealerVisit: new FormControl(true),
    notifyCustomer: new FormControl(true),
    callTheCustomer: new FormControl(true),
    flagInfo: new FormControl(true),
    notificationNecessity: new FormControl(true),
    detailedResolution: new FormControl(''),
    responseLeadtime: new FormControl(''),
    additionalInfo: new FormControl([]),
    application1: new FormControl(''),
    application2: new FormControl(''),
    application3: new FormControl(''),
  });

  controlsToDisable = [
    'vehicleCondition',
    'requiredDealerVisit',
    'notifyCustomer',
    'callTheCustomer',
    'flagInfo',
    'additionalInfo',
    'notificationNecessity'
  ];

  vehicleConditionOptions = [
    { code: true, name: 'warningMasterData.details.drivable' },
    { code: false, name: 'warningMasterData.details.undrivable' },
  ];

  conditionOptions = [
    { code: true, name: 'common.yes' },
    { code: false, name: 'common.no' },
  ];

  priorities: OptionDropdown[] = [];

  types: OptionDropdown[] = [];

  ngOnInit(): void {
    const id = this.activatedRoute.snapshot.paramMap.get('id');
    const isEdit = this.activatedRoute.snapshot.queryParamMap.get('edit');

    this.viewType =
      id === NEW_WARNING_ROUTER
        ? WarningMasterViewType.New
        : isEdit === 'true'
        ? WarningMasterViewType.Edit
        : WarningMasterViewType.View;

    if (id !== null && id !== NEW_WARNING_ROUTER) {
      this.warningMasterId = id;
      this.getDetail();
    }

    if (this.viewType !== WarningMasterViewType.View) {
      this.getMetadata();
    }
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  getMetadata(): void {
    this.loadingService.showLoader();
    this.subscription.add(
      this.warningMasterDetailService.getMetadata().subscribe((response) => {
        this.loadingService.hideLoader();
        this.priorities = response?.priorities || [];
        this.types = response?.types || [];
      })
    );
  }

  getDetail(): void {
    this.loadingService.showLoader();
    this.subscription.add(
      this.warningMasterDetailService
        .getWarningMasterDetail(this.warningMasterId)
        .subscribe(
          (response) => {
            this.loadingService.hideLoader();
            this.warningMasterData = response;
            this.additionalInfo = response?.addInfo?.split(',') || [];

            this.form.patchValue({
              name: response?.name,
              code: response?.code,
              type: response?.type?.code,
              details: response?.details,
              requestedCustomerAction: response?.actionCustomer,
              priority: response?.priority?.code,
              vehicleCondition: response?.vehicleCon,
              safeCondition: response?.safeCon,
              requiredDealerVisit: response?.requiredDealerVisit,
              notifyCustomer: response?.isNotifyCustomer,
              callTheCustomer: response?.isCallCustomer,
              flagInfo: response?.flagDist,
              notificationNecessity: response?.notificationNecessity,
              detailedResolution: '',
              responseLeadtime: response?.responseLeadTime,
              additionalInfo: this.additionalInfo,
              application1: response?.notificationPromptTitle,
              application2: response?.notificationPromptDesc,
              application3: response?.notificationPromptAction,
            });

            if (this.viewType === WarningMasterViewType.View) {
              this.controlsToDisable.forEach((controlName) => {
                this.form.controls[controlName].disable();
              });
            }
          },
          (error) => {
            this.loadingService.hideLoader();
          }
        )
    );
  }

  onCancel(): void {
    this.router.navigate(['/warning-master']);
  }

  onConfirm(): void {}

  preparePayload(): WarningMasterDataDetail {
    const payload = {
      actionCustomer: this.form.value.requestedCustomerAction,
      addInfo:
        this.form.value.additionalInfo?.length > 0
          ? this.form.value.additionalInfo?.join(',')
          : null,
      code: this.form.value.code,
      details: this.form.value.details,
      flagDist: this.form.value.flagInfo ?? false,
      notificationNecessity: this.form.value.notificationNecessity ?? false,
      isNotifyCustomer: this.form.value.notifyCustomer ?? false,
      name: this.form.value.name,
      notificationPromptAction: this.form.value.application3,
      notificationPromptDesc: this.form.value.application2,
      notificationPromptTitle: this.form.value.application1,
      priority: {
        code: this.form.value.priority,
      },
      requiredDealerVisit: this.form.value.requiredDealerVisit ?? false,
      responseLeadTime: this.form.value.responseLeadtime,
      vehicleCon: this.form.value.vehicleCondition ?? false,
      isCallCustomer: this.form.value.callTheCustomer ?? false,
      safeCon: this.form.value.safeCondition,
    };

    if (this.form.value.type) {
      Object.assign(payload, {
        type: {
          code: this.form.value.type,
        },
      });
    }

    return payload;
  }

  onAdd(): void {
    this.loadingService.showLoader();
    this.subscription.add(
      this.warningMasterDetailService
        .createWarningMaster(this.preparePayload())
        .subscribe(
          () => {
            this.loadingService.hideLoader();

            this.router.navigate(['/warning-master']);
            this.notificationService.showSuccess(
              this.translateService.instant('warningMasterData.addSuccess')
            );
          },
          (error) => {
            this.loadingService.hideLoader();
            handleErrors(error, this.notificationService);
          }
        )
    );
  }

  onUpdate(): void {
    this.loadingService.showLoader();
    this.subscription.add(
      this.warningMasterDetailService
        .updateWarningMaster(this.warningMasterId, this.preparePayload())
        .subscribe(
          () => {
            this.loadingService.hideLoader();

            this.router.navigate(['/warning-master']);
            this.notificationService.showSuccess(
              this.translateService.instant('warningMasterData.updateSuccess')
            );
          },
          (error) => {
            this.loadingService.hideLoader();
            handleErrors(error, this.notificationService);
          }
        )
    );
  }

  onEdit(): void {
    this.viewType = this.WarningMasterViewType.Edit;
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: { edit: 'true' },
      queryParamsHandling: 'merge',
    });
    this.getMetadata();

    this.controlsToDisable.forEach((controlName) => {
      this.form.controls[controlName].enable();
    });
  }

  onDelete(): void {
    this.warningService.openDeleteWarningMaster(this.warningMasterId, () => {});
  }
}
