import { Component, inject } from '@angular/core';
import { FormGroup, FormControl, ReactiveFormsModule } from '@angular/forms';
import { AgGridModule } from 'ag-grid-angular';
import { CommonModule, DatePipe } from '@angular/common';
import { Observable } from 'rxjs';

import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Router } from '@angular/router';
import { AgGridCustomComponent, FormGroupComponent } from '../../../../core/shared';
import { IconModule } from '../../../../core/icon/icon.module';
import { LoadingComponent } from '../../../../layout/global/loading/loading.component';
import { LoadingService, NotificationService, ScreenSizeService, UserService } from '../../../../core/services';
import { DeviceService, VehicleModelService, WarningService } from '../../services';
import { PagingInfo } from '../../../../core/interfaces';
import { NEW_WARNING_ROUTER, PERMISSIONS_CODE } from '../../../../core/constants';
import { DataStoreService } from '../../../../core/services/data-store.service';
import { RouterLinkCellRendererComponent } from '../../../../core/shared/router-link-cell-renderer/router-link-cell-renderer.component';
import { ActionCellRendererComponent } from '../vehicle-model/cell-renderer/action-cell-renderer.component';
import { WarningMasterItem, WarningMasterList, WarningMasterListRequest } from '../../interfaces';
import { ActionTable } from '../../../../core/enums';

@Component({
  selector: 'app-warning-master',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    FormGroupComponent,
    CommonModule,
    IconModule,
    AgGridModule,
    AgGridCustomComponent,
    LoadingComponent,
    TranslateModule,
  ],
  providers: [
    ScreenSizeService,
    VehicleModelService,
    LoadingService,
    NotificationService,
    DatePipe,
    DeviceService,
    WarningService,
  ],
  templateUrl: './warning-master.component.html',
  styleUrl: './warning-master.component.scss',
})
export class WarningMasterComponent {
  warningMasterFormSearch: FormGroup = new FormGroup({
    warningCode: new FormControl(''),
    warningName: new FormControl(''),
  });

  pagingInfo: PagingInfo = {
    totalItems: 0,
    currentPage: 0,
    numberOfPage: 10,
  };
  rowData: any[] = [];
  colDefs: any[] = [];
  defaultColDef: any = {};
  isMobile$: Observable<boolean>;
  sortDes: boolean = false;

  private translateService = inject(TranslateService);
  public loadingService = inject(LoadingService);
  public screenSizeService = inject(ScreenSizeService);

  userService = inject(UserService);

  router = inject(Router);

  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;

  constructor(
    private warningService: WarningService,
    private dataStoreService: DataStoreService
  ) {}

  ngOnInit(): void {
    this.dataStoreService.setSortData({ column: null, sort: null });
    this.initializeTable();
    this.getWarningMasterList();
  }

  ngOnDestroy(): void {
    this.loadingService.hideLoader();
    this.dataStoreService.setSortData({ column: null, sort: null });
  }

  public get warningMasterCodeControlSearch(): FormControl {
    return this.warningMasterFormSearch.get('warningCode') as FormControl;
  }

  public get warningMasterNameControlSearch(): FormControl {
    return this.warningMasterFormSearch.get('warningName') as FormControl;
  }

  initializeTable(): void {
    this.colDefs = [
      {
        headerValueGetter: () =>
          this.translateService.instant('warningMasterData.table.name'),
        field: 'name',
        flex: 3,
        sortable: false,
        cellRenderer: RouterLinkCellRendererComponent,
        cellRendererParams: {
          linkBuilder: (data: any) => `warning-master/${data?.code}`,
        }
      },
      {
        headerValueGetter: () =>
          this.translateService.instant('warningMasterData.table.code'),
        field: 'code',
        flex: 1,
        sortable: false,
      },
      {
        headerValueGetter: () =>
          this.translateService.instant('warningMasterData.table.details'),
        field: 'details',
        flex: 3,
        sortable: false,
      },
      {
        headerValueGetter: () =>
          this.translateService.instant('warningMasterData.table.priority'),
        field: 'priority',
        flex: 1,
        sortable: false,
        cellRenderer: (params: any) => {
          const code = params.data.priority ? params.data.priority.code : '';
          return this.warningService.getPriorityStyle(code);
        },
      },
      {
        headerValueGetter: () =>
          this.translateService.instant('warningMasterData.table.type'),
        field: 'type',
        flex: 1.2,
        sortable: false,
        cellRenderer: (params: any) =>
          params.data.type ? params.data.type.name : '-',
      },
      {
        headerValueGetter: () =>
          this.translateService.instant('warningMasterData.table.action'),
        field: 'action',
        headerClass: 'action-last-grid',
        flex: 1.5,
        cellRenderer: ActionCellRendererComponent,
        cellRendererParams: {
          hasEdit: this.userService.isHasPermission([
            PERMISSIONS_CODE.IOT_WARNING_MASTER_EDIT,
          ]),
          hasDelete: this.userService.isHasPermission([
            PERMISSIONS_CODE.IOT_WARNING_MASTER_DELETE,
          ]),
          onClick: (type: string, data: WarningMasterItem) => {
            if (type === ActionTable.Remove) {
              return this.deleteItem(data);
            } else {
              return this.goToEdit(data);
            }
          },
        },
        cellClass: 'action-last-grid',
        sortable: false,
      },
    ];
    this.defaultColDef = {
      resizable: false,
    };
  }

  getWarningMasterList() {
    const dataSend: WarningMasterListRequest = {
      currentPage: this.pagingInfo.currentPage,
      pageSize: this.pagingInfo.numberOfPage,
      warningCode: this.warningMasterFormSearch.value.warningCode,
      warningName: this.warningMasterFormSearch.value.warningName,
    };
    this.loadingService.showLoader();
    this.warningService.getWarningMasterList(dataSend).subscribe(
      (res: WarningMasterList) => {
        if (res) {
          if (res.items) {
            this.rowData = res.items;
          }
          if (res.pagination) {
            this.pagingInfo = {
              totalItems: res.pagination.totalResults,
              currentPage: res.pagination.currentPage,
              numberOfPage: res.pagination.pageSize,
            };
          }
        }
        this.loadingService.hideLoader();
      },
      (err) => {
        this.loadingService.hideLoader();
      }
    );
  }

  goToEdit(data: WarningMasterItem) {
    this.router.navigate(['warning-master/' + data?.code], {
      queryParams: { edit: true },
    });
  }

  deleteItem(data: WarningMasterItem) {
    this.warningService.openDeleteWarningMaster(data.code, () => {
      this.pagingInfo.currentPage = 0;
      this.getWarningMasterList();
    });
  }

  onAddNew() {
    this.router.navigate(['warning-master/' + NEW_WARNING_ROUTER]);
  }

  onSearch(): void {
    this.pagingInfo.currentPage = 0;
    this.getWarningMasterList();
  }

  onPageChange(newPage: number): void {
    this.pagingInfo.currentPage = newPage;
    this.getWarningMasterList();
  }

  onResultsPerPageChange(event: number): void {
    this.pagingInfo = {
      ...this.pagingInfo,
      currentPage: 0,
      numberOfPage: event,
    };
    this.getWarningMasterList();
  }

  viewDetail(id: string): void {
    this.router.navigate(['warning-master/' + id]);
  }
}
