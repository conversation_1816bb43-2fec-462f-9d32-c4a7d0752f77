import { CommonModule, DatePipe } from '@angular/common';
import {
  Component,
  EventEmitter,
  inject,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ModalCreateEditComponent } from './modal-create-edit/modal-create-edit.component';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { filter, Subscription } from 'rxjs';
import { LoadingService, NotificationService, UserService } from '../../../../../core/services';
import { ActionModal, DateFormat, TimeZone } from '../../../../../core/enums';
import { PERMISSIONS_CODE } from '../../../../../core/constants';
import { DialogConfirmComponent } from '../../../../../core/shared';
import { requestCreateTowingService } from '../../../interfaces';
import { handleErrors } from '../../../../../core/helpers';
import { TicketsDetailService } from '../../../services/tickets/ticket-detail.service';

@Component({
  selector: 'app-towing-service',
  standalone: true,
  imports: [CommonModule, MatIconModule, TranslateModule],
  providers: [TicketsDetailService, DatePipe, NotificationService],
  templateUrl: './towing-service.component.html',
  styleUrls: ['./towing-service.component.scss'],
})
export class TowingServiceComponent implements OnInit {
  @Input() towingServiceData: any;
  @Input() vin: string;
  @Input() ticketId: string;
  @Input() hasActiveCustomerRelation: boolean;

  @Output() refreshTowingDetails = new EventEmitter();
  showMore = false;
  towingServiceForm: FormGroup = new FormGroup({
    id: new FormControl(''),
    vin: new FormControl(''),
    dealer: new FormControl(''),
    dealerSelection: new FormControl('NEAREST'),
    dealerCode: new FormControl('', [Validators.required]),
    towingCompany: new FormControl('', [Validators.required]),
    personInChargeName: new FormControl('', [Validators.required]),
    personInChargePhone: new FormControl('', [Validators.required]),
    estTimeHH: new FormControl('', [Validators.required]),
    estTimeMM: new FormControl('', [Validators.required]),
    estDate: new FormControl('', [Validators.required]),
    status: new FormControl('ACTIVE', [Validators.required]),
    distance: new FormControl(''),
  });

  mainSubscription = new Subscription();
  dateFormat = DateFormat;

  private dialog = inject(MatDialog);
  private ticketsServiceDetails = inject(TicketsDetailService);
  private translateService = inject(TranslateService);
  private datePipe = inject(DatePipe);
  private notificationService = inject(NotificationService);
  private loadingService = inject(LoadingService);

  userService = inject(UserService);

  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;
  readonly TimeZone = TimeZone;

  ngOnInit() {}

  addTowingService() {
    if (!this.hasActiveCustomerRelation) {
      this.activeCustomerRelation();
      return;
    }
    this.towingServiceForm.controls['status'].setValue('ACTIVE');
    this.towingServiceForm.controls['dealerSelection'].setValue('NEAREST');
    const dialogRef = this.dialog.open(ModalCreateEditComponent, {
      width: '850px',
      data: {
        mainForm: this.towingServiceForm,
        isEdit: false,
        vin: this.vin,
        ticketId: this.ticketId,
      },
    });

    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((res) => {
        this.towingServiceData = res.data;
      });
  }

  editTowingService() {
    if (!this.hasActiveCustomerRelation) {
      this.activeCustomerRelation();
      return;
    }
    this.handleDataSend();
    const dialogRef = this.dialog.open(ModalCreateEditComponent, {
      width: '850px',

      data: {
        mainForm: this.towingServiceForm,
        isEdit: true,
        vin: this.vin,
      },
    });

    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((res) => {
        this.towingServiceData = res.data;
      });
  }

  cancelTowingService() {
    if (!this.hasActiveCustomerRelation) {
      this.activeCustomerRelation();
      return;
    }

    this.handleDataSend();
    const dialogRef = this.dialog.open(DialogConfirmComponent, {
      width: '530px',
      data: {
        title: this.translateService.instant('common.cancel'),
        icon: 'ic-warning',
        confirmMsg: this.translateService.instant(
          'tickets.confirmMsgCancelTowingService'
        ),
        cancelBtn: this.translateService.instant('common.no'),
        submitBtn: this.translateService.instant('common.yes'),
      },
    });

    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((res) => {
        this.updateTowingService('CANCELLED');
      });
  }

  confirmTowingService() {
    if (!this.hasActiveCustomerRelation) {
      this.activeCustomerRelation();
      return;
    }
    this.handleDataSend();
    const dialogRef = this.dialog.open(DialogConfirmComponent, {
      width: '530px',
      data: {
        title: this.translateService.instant('tickets.complete'),
        confirmMsg: this.translateService.instant(
          'tickets.confirmMsgCompleteTowingService'
        ),
        cancelBtn: this.translateService.instant('common.no'),
        submitBtn: this.translateService.instant('common.yes'),
      },
    });

    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((res) => {
        this.updateTowingService('COMPLETED');
      });
  }

  handleDataSend() {
    const { eta } = this.towingServiceData || {};
    const formattedDate = this.datePipe.transform(eta, DateFormat.FullDate, TimeZone.UTC8);
    const date = new Date(formattedDate);
    // const date = new Date(this.towingServiceData?.eta);
    this.towingServiceForm.patchValue({
      id: this.towingServiceData?.id || '',
      vin: this.vin || '',
      dealerSelection: this.towingServiceData?.dealerSelection || 'NEAREST',
      dealerCode: this.towingServiceData?.dealer?.name || '',
      dealer: this.towingServiceData?.dealer || '',
      towingCompany: this.towingServiceData?.towingCompany || '',
      personInChargeName: this.towingServiceData?.personInChargeName || '',
      personInChargePhone: this.towingServiceData?.personInChargePhone || '',
      estTimeHH:
        date.getHours() && date.getHours() > 9
          ? date.getHours()
          : '0' + date.getHours() || '00',
      estTimeMM:
        date.getMinutes() && date.getMinutes() > 9
          ? date.getMinutes()
          : '0' + date.getMinutes() || '00',
      estDate: date || null,
      status: this.towingServiceData?.status?.code || 'ACTIVE',
      distance: this.towingServiceData?.distanceToTargetPoint || '',
    });
  }

  updateTowingService(status: string) {
    this.loadingService.showLoader();
    const dataForm = this.towingServiceForm.value;
    const dataSend: requestCreateTowingService = {
      vin: this.vin,
      dealerCode: dataForm?.dealerCode,
      towingCompany: dataForm?.towingCompany,
      personInChargeName: dataForm?.personInChargeName,
      personInChargePhone: dataForm?.personInChargePhone,
      eta:
        this.datePipe.transform(dataForm?.estDate, 'MM/dd/yyyy') +
        ' ' +
        dataForm?.estTimeHH +
        ':' +
        dataForm?.estTimeMM,
      status: status,
      dealerSelection: dataForm?.dealerSelection,
      distance: dataForm?.dealer?.distanceToTargetPoint || dataForm?.distance,
    };
    this.mainSubscription.add(
      this.ticketsServiceDetails
        .updateTowingService(dataSend, dataForm?.id)
        .subscribe(
          (res) => {
            if (res) {
              if (status === 'CANCELLED') {
                this.notificationService.showSuccess(
                  this.translateService.instant(
                    'tickets.cancelTowingServiceSuccess'
                  )
                );
              } else {
                this.notificationService.showSuccess(
                  this.translateService.instant(
                    'tickets.completeTowingServiceSuccess'
                  )
                );
              }
              this.towingServiceData = res;
              this.loadingService.hideLoader();
            }
          },
          (error) => {
            handleErrors(error, this.notificationService);
            this.loadingService.hideLoader();
          }
        )
    );
  }

  toggleShowMore() {
    this.showMore = !this.showMore;
  }

  activeCustomerRelation() {
    this.dialog.open(DialogConfirmComponent, {
      width: '610px',
      data: {
        title: this.translateService.instant('tickets.towingServiceDenied'),
        icon: 'ic-warning',
        confirmMsg: this.translateService.instant(
          'tickets.msgTowingServiceDenied',
          {
            vin: this.vin,
          }
        ),
        cancelBtn: this.translateService.instant('common.close'),
      },
    });
  }
}
