<div class="section-insurance-coverage">
  <div class="section-insurance-coverage__header">
    <mat-icon class="medium-icon" svgIcon="ic-insurance-coverage"></mat-icon>
    <span>{{ "tickets.insuranceCoverage" | translate }}</span>
  </div>
  <ng-container *ngIf="haveData; else noData">
    <div class="section-insurance-coverage__list">
      <div class="section-insurance-coverage__list__item">
        <span>{{ "tickets.policy" | translate }}</span>
        <span>{{ insuranceData?.policyCode }} </span>
        <span class="section-insurance-coverage__list__item--bold">{{
          insuranceData?.providerCompany
        }}</span>
      </div>
      <div class="section-insurance-coverage__list__item">
        <span>{{ "tickets.status" | translate }}</span>
        <span class="section-insurance-coverage__list__item--status">{{
          insuranceData?.status
        }}</span>
        <span
          >{{ "tickets.expires" | translate }}:
          {{ insuranceData?.expiredDate | date : "MM/dd/yyyy" }}</span
        >
      </div>
      <div class="section-insurance-coverage__list__item">
        <span>{{ "tickets.roadsideAssistance" | translate }}</span>
        <span>{{
          insuranceData?.isIncludedRoadsideAssistance
            ? ("tickets.included" | translate)
            : ("tickets.notIncluded" | translate)
        }}</span>
        <span>{{
          insuranceData?.isIncludedRoadsideAssistance
            ? ("tickets.msgIncluded"
              | translate
                : { value: insuranceData?.roadsideAssistanceValue | number })
            : ("tickets.msgNotIncluded" | translate)
        }}</span>
      </div>
    </div>
  </ng-container>
  <ng-template #noData>
    <div class="section-no-data">
      {{ "tickets.insuranceCoverageExpired" | translate }}
    </div>
  </ng-template>
</div>
