import { Component, inject, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

import { TranslateModule, TranslateService } from '@ngx-translate/core';
import {
  debounceTime,
  distinctUntilChanged,
  filter,
  map,
  of,
  Subscription,
  switchMap,
} from 'rxjs';
import { CommonModule } from '@angular/common';
import {
  AbstractControl,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { IconModule } from '../../../../../core/icon/icon.module';
import { AutocompleteInputComponent, DropdownFormGroupComponent, FormGroupComponent, RadioButtonComponent } from '../../../../../core/shared';
import { LoadingService, NotificationService, UserService } from '../../../../../core/services';
import { TicketsService } from '../../../services';
import { Vehicle, VehiclePlateInfo } from '../../../interfaces';
import { PriorityTicket, RolesForAssignee, TypeTicket } from '../../../enums';
import { OptionDropdown } from '../../../../../core/interfaces';
import { handleErrors } from '../../../../../core/helpers';
import { ActionModal } from '../../../../../core/enums';
export function invalidTypeVehiclePlate(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    if (typeof control.value === 'string') {
      return { invalidTypeVehiclePlate: true };
    }
    return null;
  };
}

@Component({
  selector: 'app-create-ticket',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    IconModule,
    ReactiveFormsModule,
    DropdownFormGroupComponent,
    FormGroupComponent,
    RadioButtonComponent,
    AutocompleteInputComponent,
  ],
  templateUrl: './create-ticket.component.html',
  styleUrl: './create-ticket.component.scss',
  providers: [NotificationService, TicketsService],
})
export class CreateTicketComponent implements OnInit, OnDestroy {
  dialogRef = inject(MatDialogRef<CreateTicketComponent>);
  data: {
    createFromWidget?: boolean;
    vehicle?: Vehicle;
    vin?: string;
  } = inject(MAT_DIALOG_DATA);

  notificationService = inject(NotificationService);
  translateService = inject(TranslateService);
  loadingService = inject(LoadingService);

  ticketsService = inject(TicketsService);
  userService = inject(UserService);

  readonly TypeTicket = TypeTicket;

  subscription = new Subscription();

  form = new FormGroup({
    type: new FormControl(null, Validators.required),
    subType: new FormControl(null),
    vehiclePlate: new FormControl(null, [
      Validators.required,
      invalidTypeVehiclePlate(),
    ]),
    title: new FormControl(null, Validators.required),
    priority: new FormControl(),
    assignee: new FormControl(),
    drivable: new FormControl(),
    description: new FormControl(),
  });

  typeOption = [];
  subTypeOption = [];

  prioritiesOption: OptionDropdown[];
  suggestionsAssignee: OptionDropdown[];
  suggestionsVehiclePlate: OptionDropdown[];
  vehiclePlateOptions: VehiclePlateInfo[];

  vehiclePlateInfo: VehiclePlateInfo;

  drivableOption = [
    { code: true, name: 'Yes' },
    { code: false, name: 'No' },
  ];

  currentUser: OptionDropdown;

  ngOnInit(): void {
    this.getInitData();
    this.getUserInfo();
    this.form.controls.assignee.valueChanges
      .pipe(
        debounceTime(300),
        distinctUntilChanged(),
        filter((value) => typeof value === 'string'),
        switchMap((value: string) => {
          if (value?.length >= 3) {
            return this.ticketsService
              .getAssignee(value, RolesForAssignee.CreateTicket)
              .pipe(
                map((response) => ({
                  assignees: response?.assignees,
                  value,
                }))
              );
          }
          return of({ assignees: [], value });
        })
      )
      .subscribe(
        (response: any) => {
          const optionDefault = [this.currentUser];
          let filterOption = optionDefault;
          if (response?.value?.length >= 3) {
            filterOption = optionDefault.filter((option) =>
              option?.name
                ?.toLowerCase()
                ?.includes(response?.value.toLowerCase())
            );
          }

          this.suggestionsAssignee = [
            ...filterOption,
            ...response?.assignees
              ?.map((item) => ({
                code: item?.username,
                name: item?.name,
              }))
              ?.filter((item) => item.code !== this.currentUser?.code),
          ];
        },
        (err) => {
          this.suggestionsAssignee = [];
        }
      );

    if (this.data?.createFromWidget) {
      this.form.controls.vehiclePlate.disable();
      const {
        vehicleMake,
        vehicleModel,
        vehicleType,
        vehicleSubType,
        plateNumber,
      } = this.data?.vehicle || {};
      this.vehiclePlateInfo = {
        vin: this.data?.vin,
        vehicleMake,
        vehicleModel,
        vehicleType,
        vehicleSubType,
      };

      this.form.patchValue({
        vehiclePlate: plateNumber,
      });
    }

    this.form.controls.vehiclePlate.valueChanges
      .pipe(
        debounceTime(300),
        distinctUntilChanged(),
        switchMap((value) => {
          if (typeof value === 'string' && value.length >= 3) {
            return this.ticketsService.getVehiclePlate(value);
          } else if (typeof value === 'object') {
            this.vehiclePlateInfo = this.vehiclePlateOptions?.find(
              (item) => item.plateNumber === value?.code
            );
            return of(null);
          }
          return of([]);
        })
      )
      .subscribe(
        (response: any) => {
          if (response) {
            this.suggestionsVehiclePlate = [
              ...(response?.map((item) => ({
                code: item?.plateNumber,
                name: item?.plateNumber,
              })) || []),
            ];
            this.vehiclePlateOptions = response;
          }
        },
        (err) => {
          this.vehiclePlateOptions = [];
          this.suggestionsVehiclePlate = [];
        }
      );
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  getInitData(): void {
    this.loadingService.showLoader();
    this.subscription.add(
      this.ticketsService.getFilter().subscribe(
        (response) => {
          this.loadingService.hideLoader();
          this.typeOption = response?.typeList?.filter(
            (item) => item?.code !== ''
          );
          this.prioritiesOption = response?.priorityList?.filter(
            (item) => item?.code !== ''
          );
          this.subTypeOption = response?.subTypeList?.filter(
            (item) => item?.code !== ''
          );
          this.form.patchValue({
            priority: 'Medium',
          });
        },
        (error) => {
          this.loadingService.hideLoader();
          handleErrors(error, this.notificationService);
        }
      )
    );
  }

  getUserInfo(): void {
    this.userService.userRoles$.subscribe((value) => {
      this.currentUser = {
        code: value?.username,
        name: value?.fullName,
      };
      this.suggestionsAssignee = [this.currentUser];
      this.form.patchValue({
        assignee: this.currentUser,
      });
    });
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  createTicket(): void {
    this.loadingService.showLoader();
    const payload = {
      title: this.form.value.title,
      type: {
        code:
          this.form.value.type === TypeTicket.SECURITY_ISSUES
            ? this.form.value.subType
            : this.form.value.type,
      },
      vin: this.vehiclePlateInfo?.vin,
      description: this.form.value.description,
      priority: {
        code: this.form.value.priority,
      },
      isDrivable: this.form.value.drivable || false,
      assignee: {
        username: this.form.value.assignee?.code,
      },
    };
    this.subscription.add(
      this.ticketsService.createTicket(payload).subscribe(
        (response) => {
          this.loadingService.hideLoader();
          this.notificationService.showSuccess(
            this.translateService.instant('createTicket.createTicketSuccess', {
              ticketId: response?.ticketId,
            })
          );
          this.dialogRef.close({ action: ActionModal.Submit });
        },
        (error) => {
          this.loadingService.hideLoader();
          handleErrors(error, this.notificationService);
        }
      )
    );
  }

  changePriority(event): void {
    const { value } = event;
    if (value === PriorityTicket.High) {
      this.form.patchValue({
        drivable: false,
      });
    }
  }

  changeType(event): void {
    const { value } = event;
    if (value === TypeTicket.SECURITY_ISSUES) {
      this.form.controls.subType.setValidators(Validators.required);
    } else {
      this.form.controls.subType.setValidators(null);
    }
  }
}
