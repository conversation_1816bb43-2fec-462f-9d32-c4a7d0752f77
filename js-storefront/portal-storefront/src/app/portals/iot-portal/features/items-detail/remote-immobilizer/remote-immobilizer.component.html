<mat-icon svgIcon="ic-svt" class="medium-icon"></mat-icon>
<div class="svt-content">
  <div class="svt-content__header">
    <span class="svt-content__header--title">{{
      "remoteImmobilizer.remoteImmobilizer" | translate
    }}</span>

    <mat-slide-toggle
      class="custom-toggle"
      [(ngModel)]="riToggle"
      [disabled]="
        disabledRI ||
        ( userService.isHasPermission([PERMISSIONS_CODE.CALLCENTERMANAGERGROUP]) ?
        (managerApproval
          ? managerApproval?.status === ManagerApprovalStatus.Rejected
          : (approvalStatus?.status?.code === AuthorizationStatus.Rejected ||
            approvalStatus?.status?.code === AuthorizationStatus.Pending ||
            approvalStatus?.status?.code === AuthorizationStatus.Expired))
          : 
          (
          managerApproval ? managerApproval?.status !== ManagerApprovalStatus.Approved : (
            approvalStatus?.status?.code !== AuthorizationStatus.Approved
          )
        )) ||
        isLoading
      "
      (change)="changeToggle($event)"
    ></mat-slide-toggle>

    @if(isLoading) {
    <div class="svt-content__header--loading">
      <mat-spinner class="custom-spinner custom-spinner--red"></mat-spinner>
      {{ "common.loading" | translate }}
    </div>
    } @if(riStatus === RIStatus.Activating || riStatus === RIStatus.WaitOn ||
    riStatus === RIStatus.Deactivating) {
    <div class="svt-content__header--action" (click)="refresh()">
      <mat-icon svgIcon="ic-refresh" class="small-icon"></mat-icon>
      {{ "common.refresh" | translate }}
    </div>
    }
  </div>
  <div class="svt-content__description">
    {{ getRIDescription() }}
  </div>
</div>
