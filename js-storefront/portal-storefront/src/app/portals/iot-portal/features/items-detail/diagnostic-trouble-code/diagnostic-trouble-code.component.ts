import { CommonModule, DatePipe } from '@angular/common';
import { Component, inject, Input, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { DiagnosticTroubleCodeDetailComponent } from './diagnostic-trouble-code-detail/diagnostic-trouble-code-detail.component';
import { DiagnosticsDetails, DTCWidget } from '../../../interfaces';
import { DateFormat } from '../../../../../core/enums';
@Component({
  selector: 'app-diagnostic-trouble-code',
  standalone: true,
  imports: [CommonModule, MatIconModule, TranslateModule],
  providers: [DatePipe],
  templateUrl: './diagnostic-trouble-code.component.html',
  styleUrls: ['./diagnostic-trouble-code.component.scss'],
})
export class DiagnosticTroubleCodeComponent {
  @Input() data: DTCWidget[];

  dialog = inject(MatDialog);
  datePipe = inject(DatePipe);
  ngOnInit() {}

  viewDetail(data: DTCWidget): void {
    let tempDTCInfo = data?.diagnosticsTrigger || {};
    let tempVehicleInfo = data?.vehicleInformation || {};
    const tempDiagnosticsDetail: DiagnosticsDetails =
      data?.diagnosticsDetails && data?.diagnosticsDetails.length
        ? data?.diagnosticsDetails[0]
        : {};
    const tempDTCdetails =
      tempDiagnosticsDetail?.sid &&
      tempDiagnosticsDetail?.sid.length &&
      tempDiagnosticsDetail?.sid[0]?.dtc &&
      tempDiagnosticsDetail?.sid[0]?.dtc.length
        ? tempDiagnosticsDetail?.sid[0]?.dtc[0]
        : {};

    const dtcDetail = [
      {
        icon: 'ic-info',
        name: 'vehicle.dtc.diagnosticsTriggerInformation',
        items: [
          {
            name: 'vehicle.dtc.triggerType',
            value:
              tempDTCInfo?.triggerType !== null &&
              tempDTCInfo?.triggerType !== undefined
                ? tempDTCInfo.triggerType
                : '',
          },
          {
            name: 'vehicle.dtc.counterValue',
            value:
              tempDTCInfo?.counterValue !== null &&
              tempDTCInfo?.counterValue !== undefined
                ? tempDTCInfo.counterValue
                : '',
          },
          {
            name: 'vehicle.dtc.diagnosticsAcquisitionTime',
            value: tempDTCInfo?.diagnosticsAcquisitionTime
              ? this.datePipe.transform(
                  tempDTCInfo?.diagnosticsAcquisitionTime,
                  DateFormat.Full
                )
              : '',
          },
          {
            name: 'vehicle.dtc.warningTriggerOccurrenceTime',
            value: tempDTCInfo?.warningTriggerOccurrenceTime
              ? this.datePipe.transform(
                  tempDTCInfo?.warningTriggerOccurrenceTime,
                  DateFormat.Full
                )
              : '',
          },
        ],
      },
      {
        icon: 'ic-car',
        name: 'vehicle.dtc.vehicleInformation',
        items: [
          {
            name: 'vehicle.dtc.location',
            value: tempVehicleInfo?.lastLocation || '',
          },
          {
            name: 'vehicle.dtc.obdInstalled',
            value:
              tempVehicleInfo?.obd2InstalledFlag !== null &&
              tempVehicleInfo?.obd2InstalledFlag !== undefined
                ? tempVehicleInfo?.obd2InstalledFlag
                  ? 'Yes'
                  : 'No'
                : '',
          },
          {
            name: 'vehicle.dtc.odometerReadings',
            value: `${tempVehicleInfo?.odoInformationKm || ''} km`,
          },
        ],
      },
      {
        icon: 'ic-dtc',
        name: 'vehicle.dtc.diagnosticsDetails',
        items: [
          {
            name: 'vehicle.dtc.ecuAddress',
            value: tempDiagnosticsDetail?.ecuAddress || '',
          },
          {
            name: 'vehicle.dtc.nta',
            value: tempDiagnosticsDetail?.nta || '',
          },
          {
            name: 'vehicle.dtc.diagnosticsPhase',
            value:
              tempDiagnosticsDetail?.diagnosticsPhase !== null &&
              tempDiagnosticsDetail?.diagnosticsPhase !== undefined
                ? tempDiagnosticsDetail.diagnosticsPhase
                : '',
          },
          {
            name: 'vehicle.dtc.ecuPhaseId',
            value: tempDiagnosticsDetail?.ecuPhaseId || '',
          },
          {
            name: 'vehicle.dtc.communicationProtocol',
            value:
              tempDiagnosticsDetail?.communicationProtocol !== null &&
              tempDiagnosticsDetail?.communicationProtocol !== undefined
                ? tempDiagnosticsDetail.communicationProtocol
                : '',
          },
          {
            name: 'vehicle.dtc.communicationType',
            value:
              tempDiagnosticsDetail?.communicationType !== null &&
              tempDiagnosticsDetail?.communicationType !== undefined
                ? tempDiagnosticsDetail.communicationType
                : '',
          },
          {
            name: 'vehicle.dtc.odxVersion',
            value: tempDiagnosticsDetail?.odxVersion || '',
          },
          {
            name: 'vehicle.dtc.dtcCode',
            value: tempDTCdetails?.dtcCode || '',
          },
          {
            name: 'vehicle.dtc.dtcValue',
            value: tempDTCdetails?.dtcValue || '',
          },
          {
            name: 'vehicle.dtc.friendlyName',
            value: tempDTCdetails?.friendlyName || '',
          },
          {
            name: 'vehicle.dtc.status',
            value: tempDTCdetails?.status || '',
          },
        ],
      },
    ];
    const dialogRef = this.dialog.open(DiagnosticTroubleCodeDetailComponent, {
      width: '650px',
      maxHeight: '90vh',
      data: { dtcDetail },
    });

    dialogRef.afterClosed().subscribe();
  }
}
