import { CommonModule } from '@angular/common';
import { Component, inject, Input, OnInit } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { FormsModule } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';

import { filter, interval, Subscription, switchMap, takeWhile } from 'rxjs';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { IconModule } from '../../../../../core/icon/icon.module';
import { RemoteImmobilizerService, TicketsDataService } from '../../../services';
import { LoadingService, NotificationService, UserService } from '../../../../../core/services';
import { ApprovalStatus, ResponseRequestInfo, Ticket } from '../../../interfaces';
import { AuthorizationStatus, ManagerApprovalAction, ManagerApprovalStatus, RIStatus } from '../../../enums';
import { PERMISSIONS_CODE } from '../../../../../core/constants';
import { handleErrors } from '../../../../../core/helpers';
import { DialogConfirmComponent } from '../../../../../core/shared';
import { ActionModal } from '../../../../../core/enums';
@Component({
  selector: 'app-remote-immobilizer',
  standalone: true,
  imports: [
    CommonModule,
    IconModule,
    TranslateModule,
    FormsModule,
    MatSlideToggleModule,
    MatProgressSpinnerModule,
  ],
  templateUrl: './remote-immobilizer.component.html',
  styleUrl: './remote-immobilizer.component.scss',
  providers: [RemoteImmobilizerService, NotificationService],
})
export class RemoteImmobilizerComponent implements OnInit {
  @Input() set ticket(value: Ticket) {
    if (value) {
      this.vin = value.vin;
      this.disabledRI = !value?.authorization;
    }
  }

  @Input() approvalStatus: ApprovalStatus | null = null;

  dialog = inject(MatDialog);
  translateService = inject(TranslateService);
  loadingService = inject(LoadingService);
  notificationService = inject(NotificationService);

  remoteImmobilizerService = inject(RemoteImmobilizerService);
  ticketsDataService = inject(TicketsDataService);
  userService = inject(UserService);

  readonly authorizationApproval = 'APPROVED';
  readonly RIStatus = RIStatus;
  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;
  readonly ManagerApprovalStatus = ManagerApprovalStatus;
  readonly AuthorizationStatus = AuthorizationStatus;

  subscription = new Subscription();

  riToggle = false;
  riStatus: RIStatus;
  pollingSubscription: Subscription;

  isLoading = false;
  disabledRI = false;
  managerApproval: ResponseRequestInfo;
  

  vin: string;
  ngOnInit(): void {
    this.getRIStatus(true);

    this.subscription.add(
      this.ticketsDataService.actionApproveRequest$.subscribe((action) => {
        if (
          action === ManagerApprovalAction.Enable &&
          this.riToggle === false
        ) {
          this.isLoading = true;
          this.enableImmobilizer();
        } else if (action === ManagerApprovalAction.Disable && this.riToggle) {
          this.isLoading = true;
          this.disableImmobilizer();
        }
      })
    );

    this.subscription.add(
      this.ticketsDataService.managerApprovalStatus$
      .subscribe(
        (managerApproval) => {
          this.managerApproval = managerApproval;
        }
      )
    );
  }

  ngOnDestroy(): void {
    this.pollingSubscription?.unsubscribe();
    this.subscription?.unsubscribe();
  }

  getRIStatus(isFirstLoad = false, isEnabled = false): void {
    this.remoteImmobilizerService.getImmobilizerStatus(this.vin).subscribe(
      (response) => {
        this.loadingService.hideLoader();

        this.riStatus = response?.status;
        this.isLoading =
          this.riStatus === RIStatus.Activating ||
          this.riStatus === RIStatus.Deactivating;

        this.riToggle =
          this.riStatus === RIStatus.Activating ||
          this.riStatus === RIStatus.Active;
        if (
          this.riStatus === RIStatus.Activating ||
          this.riStatus === RIStatus.Deactivating ||
          this.riStatus === RIStatus.WaitOn
        ) {
          this.startPolling(isFirstLoad, isEnabled);
        } else {
          this.stopPolling(isFirstLoad, isEnabled);
        }
      },
      (error) => {
        this.loadingService.hideLoader();
        handleErrors(error, this.notificationService);
      }
    );
  }

  changeToggle(event): void {
    const { checked } = event;

    const dialogRef = this.dialog.open(DialogConfirmComponent, {
      width: '530px',
      autoFocus: false,
      data: {
        title: this.translateService.instant(
          'remoteImmobilizer.remoteImmobilizer'
        ),
        icon: 'ic-svt-red',
        confirmMsg: this.translateService.instant(
          checked
            ? 'remoteImmobilizer.confirmActivation'
            : 'remoteImmobilizer.confirmDeactivation'
        ),
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.confirm'),
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result?.action === ActionModal.Submit) {
        this.isLoading = true;
        if (checked) {
          this.enableImmobilizer();
        } else {
          this.disableImmobilizer();
        }
      } else {
        this.riToggle = this.riStatus === RIStatus.Active;
      }
    });
  }

  enableImmobilizer(): void {
    this.remoteImmobilizerService.enableImmobilizer(this.vin).subscribe(
      (response) => {
        if (response?.result === 'SUCCESS') {
          // this.notificationService.showInfo(
          //   this.translateService.instant('remoteImmobilizer.activateTurnedOff')
          // );
          this.notificationService.showInfo(
            this.translateService.instant(
              'remoteImmobilizer.activationProcessed'
            )
          );
          this.riStatus = response?.status;
          this.getRIStatus(false, true);
        } else {
          this.notificationService.showError(
            this.translateService.instant('remoteImmobilizer.activatedFail')
          );
        }
      },
      (error) => {
        this.riToggle = false;
        this.isLoading = false;
        this.notificationService.showError(
          this.translateService.instant('remoteImmobilizer.activatedFail')
        );
      }
    );
  }

  disableImmobilizer(): void {
    this.remoteImmobilizerService.disableImmobilizer(this.vin).subscribe(
      (response) => {
        if (response?.result === 'SUCCESS') {
          this.notificationService.showInfo(
            this.translateService.instant(
              'remoteImmobilizer.deactivationProcessed'
            )
          );
          this.riStatus = response?.status;
          this.getRIStatus();
        } else {
          this.notificationService.showError(
            this.translateService.instant('remoteImmobilizer.deactivatedFail')
          );
        }
      },
      (error) => {
        this.riToggle = true;
        this.isLoading = false;
        this.notificationService.showError(
          this.translateService.instant('remoteImmobilizer.deactivatedFail')
        );
      }
    );
  }

  refresh(): void {
    this.loadingService.showLoader();
    this.remoteImmobilizerService.synchRegionalStatus(this.vin).subscribe(
      (response) => {
        this.loadingService.hideLoader();
        this.riStatus = response?.status;
        this.isLoading =
          this.riStatus === RIStatus.Activating ||
          this.riStatus === RIStatus.Deactivating;

        this.riToggle =
          this.riStatus === RIStatus.Activating ||
          this.riStatus === RIStatus.Active;
      },
      (error) => {
        this.loadingService.hideLoader();
        handleErrors(error, this.notificationService);
      }
    );
  }

  startPolling(isFirstLoad = false, isEnabled = false): void {
    this.isLoading = true;
    this.disabledRI = true;
    if (!this.pollingSubscription || this.pollingSubscription.closed) {
      this.pollingSubscription = interval(5000)
        .pipe(
          switchMap(() =>
            this.remoteImmobilizerService.getImmobilizerStatus(this.vin)
          ),
          takeWhile(
            (response) =>
              response?.status === RIStatus.Activating ||
              response?.status === RIStatus.Deactivating ||
              response?.status === RIStatus.WaitOn,
            true
          )
        )
        .subscribe(
          (response) => {
            this.riStatus = response?.status;
            if (
              this.riStatus !== RIStatus.Activating &&
              this.riStatus !== RIStatus.Deactivating &&
              this.riStatus !== RIStatus.WaitOn
            ) {
              this.stopPolling(isFirstLoad, isEnabled);
            }
          },
          (error) => {
            handleErrors(error, this.notificationService);
            this.stopPolling(isFirstLoad, isEnabled);
          }
        );
    }
  }

  stopPolling(isFirstLoad = false, isEnabled = false): void {
    this.isLoading = false;
    this.disabledRI = false;
    if (this.pollingSubscription) {
      this.pollingSubscription.unsubscribe();
    }

    this.riToggle =
      this.riStatus === RIStatus.Activating ||
      this.riStatus === RIStatus.Active;

    if (this.riStatus === RIStatus.Active && !isFirstLoad) {
      this.notificationService.showNotification(
        this.translateService.instant(
          isEnabled
            ? 'remoteImmobilizer.activatedSuccess'
            : 'remoteImmobilizer.deactivatedFail'
        ),
        isEnabled ? 'success' : 'error'
      );
    } else if (this.riStatus === RIStatus.Inactive && !isFirstLoad) {
      this.notificationService.showNotification(
        this.translateService.instant(
          isEnabled
            ? 'remoteImmobilizer.activatedFail'
            : 'remoteImmobilizer.deactivatedSuccess'
        ),
        isEnabled ? 'error' : 'success'
      );
    }
  }

  getRIDescription(): string {
    switch (this.riStatus) {
      case RIStatus.Active: {
        return this.translateService.instant('remoteImmobilizer.riActivated');
      }
      case RIStatus.Activating: {
        return this.translateService.instant(
          'remoteImmobilizer.activationProcessed'
        );
      }
      case RIStatus.Deactivating: {
        return this.translateService.instant(
          'remoteImmobilizer.deactivationProcessed'
        );
      }
      case RIStatus.WaitOn: {
        return this.translateService.instant(
          'remoteImmobilizer.activateTurnedOff'
        );
      }
      default: {
        return this.translateService.instant(
          'remoteImmobilizer.remoteImmobilizerDescription'
        );
      }
    }
  }
}
