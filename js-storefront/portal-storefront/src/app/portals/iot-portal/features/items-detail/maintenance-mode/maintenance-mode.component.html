<div class="maintenance-mode">
  <div class="maintenance-mode__item">
    <mat-icon svgIcon="ic-repair"></mat-icon>
    <div class="maintenance-mode__item__content">
      <span>{{ "maintenanceMode.vehicleMaintenanceMode" | translate }}</span>
      <div
        *ngIf="
          vehicle?.iotVehicleProperty?.maintenanceModeSetting?.maintenanceMode
        "
        class="maintenance-mode__item__content__description"
      >
        @if(vehicle?.iotVehicleProperty?.maintenanceModeSetting?.maintenanceMode
        === MaintenanceModeVehicle.On) {
        <span class="maintenance-mode__item__content__description--green">
          <mat-icon svgIcon="ic-checked-red"></mat-icon>
          {{
            vehicle?.iotVehicleProperty?.maintenanceModeSetting?.maintenanceMode
          }}
        </span>
        } @else {
        <span class="maintenance-mode__item__content__description--red">
          <mat-icon svgIcon="ic-cancel"></mat-icon>
          {{
            vehicle?.iotVehicleProperty?.maintenanceModeSetting?.maintenanceMode
          }}
        </span>
        }

        <span class="maintenance-mode__item__content__description--gray">{{
          vehicle?.iotVehicleProperty?.maintenanceModeSetting
            ?.maintenanceModeOnDate
        }}</span>
      </div>
    </div>
  </div>
  <div class="maintenance-mode__item">
    <mat-icon svgIcon="ic-svt"></mat-icon>
    <div class="maintenance-mode__item__content">
      <span>{{ "maintenanceMode.stolenVehicleTracking" | translate }}</span>
        <div class="maintenance-mode__item__content__description">
            @switch(vehicle?.iotVehicleProperty?.svtStatusDetail?.svtStatus) {
            @case(SvtStatus.Activated) {
            <span class="maintenance-mode__item__content__description--green">
                <mat-icon svgIcon="ic-checked-red"></mat-icon>
                {{ "maintenanceMode.riActivatedStatus" | translate }}
            </span>
            }
            @case(SvtStatus.Deactivated) {
            <span class="maintenance-mode__item__content__description--red">
                <mat-icon svgIcon="ic-cancel"></mat-icon>
                {{ "maintenanceMode.riDeactivatedStatus" | translate }}
            </span>
            }
            @case(SvtStatus.Deactivating) {
              <span class="maintenance-mode__item__content__description--yellow">
                  <mat-icon svgIcon="ic-info"></mat-icon>
                  {{ vehicle?.iotVehicleProperty?.svtStatusDetail?.svtStatus }}
              </span>
              }
              @case(SvtStatus.Activating) {
                <span class="maintenance-mode__item__content__description--blue">
                    <mat-icon svgIcon="ic-info"></mat-icon>
                    {{ vehicle?.iotVehicleProperty?.svtStatusDetail?.svtStatus }}
                </span>
                }
            }
            <span class="maintenance-mode__item__content__description--gray">{{
                vehicle?.iotVehicleProperty?.svtStatusDetail?.svtActivationDateTime
            }}</span>
        
        
        </div>
    </div>
  </div>
  <div class="maintenance-mode__item">
    <mat-icon svgIcon="ic-lock"></mat-icon>
    <div class="maintenance-mode__item__content remote-immobilizer">
      <span>{{ "maintenanceMode.remoteImmobilizer" | translate }}</span>
      <div class="maintenance-mode__item__content__description">
        @switch(vehicle?.iotVehicleProperty?.remoteImmobilizerDetail?.remoteImmobilizerStatus) {
          @case(RemoteImmobilizerStatus.Enable) {
            <span class="maintenance-mode__item__content__description--green">
              <mat-icon svgIcon="ic-checked-red"></mat-icon>
              {{ "maintenanceMode.riEnabledStatus" | translate }}
            </span>
          }
          @case(RemoteImmobilizerStatus.Disabled) {
            <span class="maintenance-mode__item__content__description--red">
              <mat-icon svgIcon="ic-cancel"></mat-icon>
              {{ "maintenanceMode.riDisabledStatus" | translate }}
            </span>
          }
          @case(RemoteImmobilizerStatus.Enabling) {
            <span class="maintenance-mode__item__content__description--yellow">
              <mat-icon svgIcon="ic-enabling"></mat-icon>
              {{ "maintenanceMode.riEnablingInProgressStatus" | translate }}
            </span>
          }
          @case(RemoteImmobilizerStatus.Disabling) {
            <span class="maintenance-mode__item__content__description--yellow">
              <mat-icon svgIcon="ic-enabling"></mat-icon>
              {{ "maintenanceMode.riDisablingInProgressStatus" | translate }}
            </span>
          }
        }

        <span class="maintenance-mode__item__content__description--gray">{{
          vehicle?.iotVehicleProperty?.remoteImmobilizerDetail?.remoteImmobilizerDateTime
        }}</span>

       
      </div>
      <div class="remote-immobilizer-tooltip">
        @switch(vehicle?.iotVehicleProperty?.remoteImmobilizerDetail?.remoteImmobilizerStatus) {
          @case(RemoteImmobilizerStatus.Enable) {
            {{ "maintenanceMode.riDisabled" | translate }}
          }
          @case(RemoteImmobilizerStatus.Disabled) {
            {{ "maintenanceMode.riEnabled" | translate }}
          }
          @case(RemoteImmobilizerStatus.Enabling) {
            {{ "maintenanceMode.riEnablingInProgress" | translate }}
          }
          @case(RemoteImmobilizerStatus.Disabling) {
            {{ "maintenanceMode.riDisablingInProgress" | translate }}
          }
        }
      </div>
    </div>
  </div>
</div>
