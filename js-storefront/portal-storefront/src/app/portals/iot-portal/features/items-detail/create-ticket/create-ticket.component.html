<h2 class="title-dialog">
  {{ "createTicket.newTicket" | translate }}
</h2>

<div class="container-dialog" [formGroup]="form">
  <app-dropdown-form-group
    [label]="'createTicket.type' | translate"
    [control]="form.controls.type"
    [options]="typeOption"
    [required]="true"
    (changeOption)="changeType($event)"
  ></app-dropdown-form-group>

  
  <app-dropdown-form-group
    *ngIf="form.value.type === TypeTicket.SECURITY_ISSUES"
    [label]="'createTicket.subType' | translate"
    [control]="form.controls.subType"
    [options]="subTypeOption"
    [required]="true"
  ></app-dropdown-form-group>

  <div class="container-dialog__vehicle-plate">
    <app-autocomplete-input
      [label]="'createTicket.vehiclePlate' | translate"
      [suggestions]="suggestionsVehiclePlate"
      [control]="form.controls.vehiclePlate"
      [required]="true"
      [errorMessage]="'validation.fillInData' | translate"
      [otherErrorMessages]="{
        invalidTypeVehiclePlate: 'createTicket.invalidPlateNumber' | translate
      }"
    >
    </app-autocomplete-input>

    <div
      *ngIf="
        vehiclePlateInfo &&
        (form.controls.vehiclePlate.valid ||
          form.controls.vehiclePlate.disabled)
      "
      class="container-dialog__vehicle-plate__more-info"
    >
      <div>
        <mat-icon svgIcon="ic-vin"></mat-icon>
        {{ vehiclePlateInfo.vin }}
      </div>
      <div  *ngIf="vehiclePlateInfo.vehicleMake || vehiclePlateInfo.vehicleModel">
        <mat-icon svgIcon="ic-car"></mat-icon>
        {{ vehiclePlateInfo.vehicleMake }} {{ vehiclePlateInfo.vehicleModel }}
      </div>
      <div *ngIf="vehiclePlateInfo.vehicleType || vehiclePlateInfo.vehicleSubType">
        <mat-icon svgIcon="ic-book"></mat-icon>
        {{ vehiclePlateInfo.vehicleType }}
        <span *ngIf="vehiclePlateInfo.vehicleSubType">
          - {{ vehiclePlateInfo.vehicleSubType }}</span
        >
      </div>
    </div>
  </div>

  <app-form-group
    [label]="'createTicket.title' | translate"
    [control]="form.controls.title"
    [required]="true"
    [maxLength]="255"
  ></app-form-group>

  <div class="container-dialog__priority">
    <app-dropdown-form-group
      [label]="'createTicket.priority' | translate"
      [control]="form.controls.priority"
      [options]="prioritiesOption"
      [required]="true"
      (changeOption)="changePriority($event)"
    ></app-dropdown-form-group>

    <app-autocomplete-input
      [label]="'tickets.filter.assignee' | translate"
      [placeholder]="'enterHereToSearch' | translate"
      [suggestions]="suggestionsAssignee"
      [control]="form.controls.assignee"
    >
    </app-autocomplete-input>

    <app-radio-button
      [label]="'createTicket.drivable'"
      [control]="form.controls.drivable"
      [option]="drivableOption"
    ></app-radio-button>
  </div>

  <div class="form-group">
    <label class="form-group__label">
      {{ "createTicket.description" | translate }}
    </label>
    <textarea class="form-textarea" formControlName="description"></textarea>
  </div>
</div>

<div class="action-dialog">
  <button class="btn-quaternary" (click)="onCancel()">
    {{ "common.cancel" | translate }}
  </button>

  <button
    class="btn-primary"
    [disabled]="form.invalid"
    (click)="createTicket()"
  >
    {{ "common.create" | translate }}
  </button>
</div>
