@import "../../../../../../../styles/abstracts/variables";

:host {
  .title-dialog {
    margin: 45px auto 35px;
  }

  .container-dialog {
    padding: 0 55px 25px;
    .sub-title {
      font-size: 15px;
      padding-bottom: 13px;
      margin: 0;
      text-align: center;
    }

    app-radio-button {
      ::ng-deep {
        mat-radio-group {
          flex-direction: row;
          gap: 35px;
        }
      }
    }

    app-form-group {
      ::ng-deep {
        .form-textarea {
          min-height: 88px;
          font-size: 16px;
        }
      }
    }
  }

  .action-dialog {
    justify-content: center;
    gap: 20px;
    padding: 0 55px 45px;
  }

  app-loading {
    top: 0;
  }
}
