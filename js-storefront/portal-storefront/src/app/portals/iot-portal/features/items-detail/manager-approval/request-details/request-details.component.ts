import { CommonModule } from '@angular/common';
import { Component, inject, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import {
  debounceTime,
  distinctUntilChanged,
  filter,
  map,
  of,
  Subscription,
  switchMap,
} from 'rxjs';
import { AutocompleteInputComponent, FormGroupComponent, RadioButtonComponent } from '../../../../../../core/shared';
import { ManagerApprovalService, TicketsService } from '../../../../services';
import { LoadingService, NotificationService } from '../../../../../../core/services';
import { OptionDropdown } from '../../../../../../core/interfaces';
import { ManagerApprovalAction, RolesForAssignee, TypeTicket } from '../../../../enums';
import { ActionModal } from '../../../../../../core/enums';
import { handleErrors } from '../../../../../../core/helpers';


@Component({
  selector: 'app-request-details',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    ReactiveFormsModule,
    RadioButtonComponent,
    AutocompleteInputComponent,
    FormGroupComponent,
  ],
  templateUrl: './request-details.component.html',
  styleUrl: './request-details.component.scss',
  providers: [TicketsService, ManagerApprovalService, NotificationService],
})
export class RequestDetailsComponent implements OnInit, OnDestroy {
  dialogRef = inject(MatDialogRef<RequestDetailsComponent>);

  loadingService = inject(LoadingService);
  notificationService = inject(NotificationService);
  translateService = inject(TranslateService);

  managerApprovalService = inject(ManagerApprovalService);
  ticketsService = inject(TicketsService);

  data: {
    ticketType: OptionDropdown;
    ticketId: string;
  } = inject(MAT_DIALOG_DATA);

  readonly TypeTicket = TypeTicket;

  subscription = new Subscription();

  form = new FormGroup({
    action: new FormControl(null, Validators.required),
    comment: new FormControl('', Validators.required),
    assignee: new FormControl(null, Validators.required),
  });

  actionRequest = [
    {
      code: ManagerApprovalAction.Enable,
      name: 'managerApproval.requestDetails.enable',
    },
    {
      code: ManagerApprovalAction.Disable,
      name: 'managerApproval.requestDetails.disable',
    },
  ];

  suggestionsAssignee: OptionDropdown[];

  ngOnInit(): void {
    this.form.controls.assignee.valueChanges
      .pipe(
        debounceTime(300),
        distinctUntilChanged(),
        filter((value) => typeof value === 'string'),
        switchMap((value: string) => {
          if (value?.length >= 3) {
            return this.ticketsService.getAssignee(value, RolesForAssignee.AuthorizationRequest).pipe(
              map((response) => ({
                assignees: response?.assignees,
                value,
              }))
            );
          }
          return of({ assignees: [], value });
        })
      )
      .subscribe(
        (response: any) => {
          this.suggestionsAssignee = [
            ...response?.assignees?.map((item) => ({
              code: item?.username,
              name: item?.name,
            })),
          ];
        },
        (err) => {
          this.suggestionsAssignee = [];
        }
      );
  }
  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  sendRequest(): void {
    this.loadingService.showLoader();
    const payload = {
      action: this.form.value.action,
      comment: this.form.value.comment,
      assignee: this.form.value.assignee?.code || '',
    };
    this.subscription.add(
      this.managerApprovalService
        .createRequest(this.data?.ticketId, payload)
        .subscribe(
          (response) => {
            this.notificationService.showSuccess(
              this.translateService.instant(
                'managerApproval.requestDetails.createdRequestSuccess'
              )
            );
            this.loadingService.hideLoader();
            this.dialogRef.close({ action: ActionModal.Submit });
          },
          (error) => {
            this.loadingService.hideLoader();
            handleErrors(error, this.notificationService);
          }
        )
    );
  }
}
