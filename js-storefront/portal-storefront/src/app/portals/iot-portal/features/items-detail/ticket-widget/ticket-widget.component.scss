.section-ticket-widget {
  display: flex;
  flex-direction: column;
  gap: 30px;

  &__body {
    overflow: auto;
    width: 100%;
  }

  &__box {
    width: 100%;
    &__content {
      display: inline-flex;
      flex-direction: column;
      span {
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 20px;
        color: #101010;
        cursor: pointer;
        &:last-child {
          font-size: 14px;
          font-weight: 400;
        }
      }
      &:hover {
        text-decoration: underline;
      }
    }

    tbody {
      tr {
        td {
          border-bottom: 1px solid var(--Primary-Light-Grey, #EEE);
        }
        &:first-child {
          td {
            &:first-child {
              width: 100%;
              padding: 0 5px 10px 0;
            }
            &:not(:first-child) {
              padding: 0 5px;
              white-space: nowrap;
            }
          }
        }
        &:not(:first-child) {
          td {
            &:first-child {
              width: 100%;
              padding: 10px 5px 10px 0;
            }
            &:not(:first-child) {
              padding: 0 5px;
              white-space: nowrap;
            }
          }
        }
      }
    }

    .value-tag {
      span {
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 14px;
        letter-spacing: -0.42px;
        padding: 8px;
        color: #3a3a3a;
        background-color: #eeeeee;
        display: inline-block;
      }
    }
  }
}
