:host {
  .history-log {
    &__title {
      display: flex;
      align-items: center;
      font-size: 22px;
      font-weight: 600;
      gap: 12px;
      margin-bottom: 30px;
    }

    &__list {
      list-style: none;
      padding: 0;
      margin: 0;

      .history-log__item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        .time-line {
          position: relative;
        }

        .dot {
          width: 7px;
          height: 7px;
          background-color: #333;
          border-radius: 50%;
          margin: 5px 10px 0px 0;
          position: relative;
          z-index: 1;
        }

        .line {
          width: 2px;
          height: 32px;
          background-color: #eeeeee;
          margin: 5px auto 0;
          position: absolute;
          z-index: 0;
          top: 0;
          left: 2.7px;
        }

        .time-info {
          display: flex;
          width: 100%;
          justify-content: space-between;

          .text {
            font-size: 14px;
            color: #232323;
          }

          .time {
            color: #808080;
          }
        }
      }

      .history-log__item:last-child .line {
        display: none;
      }
    }

    &__date {
      color: #808080;
      padding: 0 0 10px 15px;
    }
  }
}
