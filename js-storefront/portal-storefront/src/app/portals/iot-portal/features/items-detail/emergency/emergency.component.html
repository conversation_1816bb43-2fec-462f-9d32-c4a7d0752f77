<div class="section-emergency">
  <div class="section-header">
    <mat-icon
      svgIcon="ic-emergency"
      class="medium-icon section-header--icon"
    ></mat-icon>
    <div class="section-header--content">
      <span>{{ "vehicle.emergency.title" | translate }}</span>
    </div>
  </div>

  <div class="section-emergency__body">
    <ng-container *ngIf="data && data.length; else noData">
      <table class="section-emergency__box">
        <tbody>
          @for (item of data; track $index) {
          <tr>
            <td>
              <div class="section-emergency__box__content">
                <div>
                  <span>{{ item?.name }}</span>
                </div>
                @if(isTicket) {
                  <span class="section-emergency__box__content--address">{{ item?.placeName }}</span>
                }

                <span>{{ item?.timestamp }}</span
                >
              </div>
            </td>
            <td>
              <div
                class="value-tag"
                [ngClass]="
                  item?.status?.code === 'ACTIVE' ? 'red-tag' : 'grey-tag'
                "
              >
                <span class="tag-box">{{ item?.status?.name }}</span>
              </div>
            </td>
            @if(item?.status?.code === 'ACTIVE' && isTicket) {
            <td>
              <div>
                <button class="btn-link" (click)="openModalClose(item)">
                  <mat-icon
                    svgIcon="ic-close-no-radius"
                    class="small-icon"
                  ></mat-icon>
                  {{ "common.close" | translate }}
                </button>
              </div>
            </td>
            }
          </tr>
          }
        </tbody>
      </table>
    </ng-container>
    <ng-template #noData>
      <div class="section-no-data">
        {{ "vehicle.emergency.noData" | translate }}
      </div>
    </ng-template>
  </div>
</div>
