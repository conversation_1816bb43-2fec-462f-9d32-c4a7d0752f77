@import "../../../../../../../styles/abstracts/mixins";
@import "../../../../../../../styles/abstracts/variables";

:host ::ng-deep {
  .edit-towing-form {
    position: relative;
    display: flex;
    flex-direction: column;

    .form-group {
      margin-bottom: 0;
    }

    .container-dialog {
      padding: 30px 40px;
    }

    h2 {
      padding: 25px 40px 0;
      margin: 0;
      text-align: center;
    }

    form {
      display: flex;
      flex-direction: column;
      gap: 30px;
    }

    &__dealer-radio {
      display: flex;
      justify-content: center;
      gap: 60px;
      .spinner {
        position: unset;
      }

      mat-radio-group {
        display: flex;
        align-items: center;
        gap: 24px;
        margin-left: -8px;

        .mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle {
          background-color: #eb0a1e !important;
        }

        .mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle {
          border-color: #eb0a1e !important;
        }

        .mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle {
          border-color: #eb0a1e !important;
        }

        .custom-radio-button {
          font-size: 16px;
          font-weight: 400;
          color: $text-color;

          .mat-mdc-radio .mdc-radio__background .mdc-radio__outer-circle {
            border-color: $text-color !important;
          }

          &.mat-mdc-radio-checked .mdc-radio__background .mdc-radio__outer-circle {
            border-color: $text-color !important;
          }

          .mat-radio-inner-circle {
            background-color: transparent;
          }
        }
      }
    }

    &__icon {
      display: flex;
      flex-direction: column;
      gap: 10px;
      margin-top: 10px;
      p {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 14px;
        font-weight: 400;
        margin: 0;
        font-family: $text-font-stack;
      }
    }

    &__actions {
      display: flex;
      justify-content: center;
      gap: 20px;

      .btn {
        border-radius: 0;
        min-width: 215px;
        height: 50px;
      }
    }
  }

  .date-time {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-end;

    .form-group {
      margin-bottom: 0px;
    }
  }

  .item-hours {
    display: flex;
    position: relative;
    align-items: flex-end;

    mat-icon {
      position: absolute;
      right: 0px;
      bottom: 7px;
    }

    &__separator {
      padding-bottom: 4px;
      border-bottom: 1px solid #cccccc;
      transition: border-color 0.3s;
    }

    &__time-hh {
      max-width: 25px;
    }

    &__time-mm {
      padding-left: 5px
    }

    &:has(.form-control:focus) {
      .form-control {
        border-bottom: 1px solid #000 !important;
      }

      .item-hours__separator {
        border-bottom: 1px solid #000 !important;
      }
    }
  }

  .item-date {
    .form-group__input {
      position: relative;

      mat-icon {
        position: absolute;
        right: 0;
        bottom: 7px;
        cursor: pointer;
      }

      .form-control {
        cursor: pointer;
      }
    }
  }

  .status-error {
    margin-top: -20px;
  }
}