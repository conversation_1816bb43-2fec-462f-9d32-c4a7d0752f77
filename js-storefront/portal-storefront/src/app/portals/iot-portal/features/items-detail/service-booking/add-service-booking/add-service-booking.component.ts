import { Component, inject, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import {
  MAT_DIALOG_DATA,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatRadioModule } from '@angular/material/radio';
import { MatStepperModule } from '@angular/material/stepper';
import { CommonModule } from '@angular/common';
import { MatCheckboxModule } from '@angular/material/checkbox';
import {
  combineLatest,
  filter,
  map,
  Observable,
  of,
  startWith,
  Subscription,
} from 'rxjs';
import { MatDatepickerModule } from '@angular/material/datepicker';

import { PreferredDateComponent } from '../preferred-date/preferred-date.component';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { StepperSelectionEvent } from '@angular/cdk/stepper';
import { IconModule } from '../../../../../../core/icon/icon.module';
import { DateFormGroupComponent, DropdownFormGroupComponent, FormGroupComponent, RadioButtonComponent } from '../../../../../../core/shared';
import { LoadingComponent } from '../../../../../../layout/global/loading/loading.component';
import { ServiceBookingService, ServiceBookingShareDataService, TicketsService } from '../../../../services';
import { LoadingService, NotificationService } from '../../../../../../core/services';
import { UtcToPhtPipe } from '../../../../../../core/pipes';
import { DealerItem, ServiceBookingDetail } from '../../../../interfaces';
import { OptionDropdown } from '../../../../../../core/interfaces';
import { ActionModal } from '../../../../../../core/enums';
import { handleErrors } from '../../../../../../core/helpers';

@Component({
  selector: 'app-add-service-booking',
  standalone: true,
  imports: [
    CommonModule,
    IconModule,
    MatIconModule,
    TranslateModule,
    FormsModule,
    ReactiveFormsModule,
    MatRadioModule,
    MatStepperModule,
    MatCheckboxModule,
    MatDatepickerModule,
    MatAutocompleteModule,
    DropdownFormGroupComponent,
    FormGroupComponent,
    PreferredDateComponent,
    DateFormGroupComponent,
    LoadingComponent,
    RadioButtonComponent,
  ],
  templateUrl: './add-service-booking.component.html',
  providers: [
    provideNativeDateAdapter(),
    ServiceBookingService,
    TicketsService,
    NotificationService,
    UtcToPhtPipe,
  ],
  styleUrl: './add-service-booking.component.scss',
})
export class AddServiceBookingComponent implements OnInit, OnDestroy {
  data: {
    isEdit: boolean;
    vin: string;
    vehicleMileage?: number;
    bookingDetail?: ServiceBookingDetail;
  } = inject(MAT_DIALOG_DATA);

  dialogRef = inject(MatDialogRef<AddServiceBookingComponent>);

  serviceBookingService = inject(ServiceBookingService);
  ticketsDetailService = inject(TicketsService);
  notificationService = inject(NotificationService);
  translateService = inject(TranslateService);
  loadingService = inject(LoadingService);

  utcToPhtPipe = inject(UtcToPhtPipe);
  serviceBookingShareDataService = inject(ServiceBookingShareDataService);

  subscription = new Subscription();
  isLoading = false;

  dealerOption: DealerItem[];

  filteredDealerOption: Observable<DealerItem[]>;

  stepOneForm = new FormGroup({
    dealer: new FormControl('', Validators.required),
    reason: new FormControl('', Validators.required),
    vehicleMileage: new FormControl(),
  });

  stepTwoForm = new FormGroup({
    preferredDate: new FormControl(null, Validators.required),
    preferredTime: new FormControl(null, Validators.required),
    waitFor: new FormControl(true),
    remark: new FormControl(),
    pickupDate: new FormControl(),
    pickTime: new FormControl(),
  });

  dealerInfo: DealerItem;

  reasons: OptionDropdown[] = [];

  preferredTimeOptions = [];

  selectedPreferredDate = null;
  availableDates: Date[] = [];
  apiDates: string[] = [];

  startDateCalendar1: Date = new Date();
  startDateCalendar2: Date = new Date();

  // Extracted months from the API dates
  uniqueMonths: { year: number; month: number }[] = [];

  availableHours: OptionDropdown[] = [];
  availablePickupTime: OptionDropdown[] = [];

  minDatePickupDate: Date = null;

  currentStepIndex = 0;
  countAPI: number = 0;
  ngOnInit(): void {
    this.getInitValues();
    this.initValueForm();

    this.filteredDealerOption =
      this.stepOneForm.controls.dealer.valueChanges.pipe(
        startWith(''),
        filter((value: string) => value.length >= 3 || value === ''),
        map((value) =>
          this.dealerOption?.filter((option) =>
            option?.displayName.toLowerCase().includes(value || '')
          )
        )
      );
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  initValueForm(): void {
    if (this.data?.isEdit) {
      this.stepOneForm.patchValue({
        vehicleMileage: this.data?.bookingDetail?.odo
          ? Number(this.data?.bookingDetail?.odo)
          : 0,
      });
    } else {
      this.stepOneForm.patchValue({
        vehicleMileage: this.data?.vehicleMileage,
      });
    }
  }

  onCancel(): void {
    this.dialogRef.close({ action: ActionModal.Cancel });
  }

  onContinue(): void {}

  createServiceBooking(): void {
    const payload = {
      bookingDate: this.stepTwoForm.get('preferredDate')?.value
        ? this.formattedDate(this.stepTwoForm.get('preferredDate')?.value)
        : '',
      bookingTimeSlot: this.stepTwoForm.get('preferredTime')?.value,
      waitPickupAtDealer: this.stepTwoForm.get('waitFor').value,
      bookingReason: this.stepOneForm.get('reason').value,
      mileAge: this.stepOneForm.get('vehicleMileage').value,
      servicePickupDate: this.stepTwoForm.get('pickupDate').value
        ? this.formattedDate(this.stepTwoForm.get('pickupDate').value)
        : '',
      servicePickupTimeSlot: this.stepTwoForm.get('pickTime').value,
      remark: this.stepTwoForm.get('remark').value,
      vin: this.data?.vin,
      dealerName: this.stepOneForm.get('dealer').value,
    };
    this.isLoading = true;
    this.loadingService.showLoader();
    this.subscription.add(
      this.serviceBookingService
        .createServiceBooking(
          payload,
          this.data?.isEdit,
          this.data?.bookingDetail?.bookingNo
        )
        .subscribe(
          (response) => {
            this.isLoading = false;
            this.loadingService.hideLoader();
            this.dialogRef.close({ action: ActionModal.Submit });
            this.notificationService.showSuccess(
              this.translateService.instant(
                this?.data?.isEdit
                  ? 'serviceBooking.addServiceBooking.updateBookingSuccess'
                  : 'serviceBooking.addServiceBooking.bookSuccess'
              )
            );
            this.serviceBookingShareDataService.isReloadList$.next(true);
          },
          (error) => {
            this.isLoading = false;
            this.loadingService.hideLoader();
            if (this.data?.isEdit) {
              this.notificationService.showError(
                this.translateService.instant(
                  'serviceBooking.addServiceBooking.updateBookingFail'
                )
              );
            } else {
              handleErrors(error, this.notificationService);
            }
          }
        )
    );
  }

  onStepChange(event: StepperSelectionEvent): void {
    this.currentStepIndex = event.selectedIndex;

    if (event.selectedIndex === 1) {
      this.getAvailableDates();
      this.checkEnablePreferredTime();

      if (this.data?.isEdit) {
        this.stepTwoForm.patchValue({
          preferredDate: new Date(
            this.stepTwoForm.get('preferredDate')?.value ||
              this.utcToPhtPipe.transform(this.data?.bookingDetail?.bookingDate)
          ),
          preferredTime:
            this.stepTwoForm.get('preferredTime')?.value ||
            this.data?.bookingDetail?.bookingTime,
          waitFor:
            this.stepTwoForm.get('pickupDate')?.value ||
            this.stepTwoForm.get('pickTime')?.value
              ? this.stepTwoForm.get('waitFor')?.value
              : this.data?.bookingDetail?.waitPickupAtDealer,
          remark:
            this.stepTwoForm.get('remark')?.value ||
            this.data?.bookingDetail?.remark,
          pickupDate:
            this.stepTwoForm.get('pickupDate')?.value ||
            (this.data?.bookingDetail?.pickupDate
              ? new Date(
                  this.utcToPhtPipe.transform(
                    this.data?.bookingDetail?.pickupDate
                  )
                )
              : null),
          pickTime:
            this.stepTwoForm.get('pickTime')?.value ||
            this.data?.bookingDetail?.pickupTime,
        });
        this.selectedPreferredDate =
          this.stepTwoForm.get('preferredDate')?.value;
        this.minDatePickupDate = this.selectedPreferredDate;

        this.countAPI = 1;
        this.getAvailableHours();

        if (this.stepTwoForm.get('pickupDate')?.value) {
          this.countAPI = 2;
          this.getAvailablePickupTimes();
          this.checkValidatePickupDateTime(false);
        }
      }
      this.checkEnablePreferredTime();
    }
  }

  checkEnablePreferredTime(): void {
    if (this.stepTwoForm.controls.preferredDate.invalid) {
      this.stepTwoForm.controls.preferredTime.disable();
    } else {
      this.stepTwoForm.controls.preferredTime.enable();
    }
  }

  convertApiDatesToDateObjects(dates: string[]): void {
    this.availableDates = dates.map((dateString) => {
      const [month, day, year] = dateString.split('/');
      return new Date(+year, +month - 1, +day);
    });

    this.availableDates.sort((a, b) => a.getTime() - b.getTime());

    this.uniqueMonths = Array.from(
      new Set(
        this.availableDates.map(
          (date) => `${date.getFullYear()}-${date.getMonth()}`
        )
      )
    ).map((monthYear) => {
      const [year, month] = monthYear.split('-');
      return { year: +year, month: +month };
    });
  }

  setStartDates(): void {
    if (this.uniqueMonths.length >= 2) {
      this.startDateCalendar1 = new Date(
        this.uniqueMonths[0].year,
        this.uniqueMonths[0].month,
        1
      );
      this.startDateCalendar2 = new Date(
        this.uniqueMonths[1].year,
        this.uniqueMonths[1].month,
        1
      );
    } else if (this.uniqueMonths.length === 1) {
      this.startDateCalendar1 = new Date();
      this.startDateCalendar2 = new Date(
        this.uniqueMonths[0].year,
        this.uniqueMonths[0].month,
        1
      );
    }
  }

  isDateAvailable(date: Date): boolean {
    return this.availableDates.some(
      (d) =>
        d.getDate() === date.getDate() &&
        d.getMonth() === date.getMonth() &&
        d.getFullYear() === date.getFullYear()
    );
  }

  addDisableClass = (date: Date): string => {
    return this.isDateAvailable(date) ? '' : 'mat-calendar-disabled-cell';
  };

  changeWaitForUnit(): void {
    this.checkValidatePickupDateTime();
  }

  checkValidatePickupDateTime(isReset = true): void {
    const waitFor = this.stepTwoForm.get('waitFor').value;

    const stepTwoFormControl = this.stepTwoForm.controls;

    if (waitFor) {
      stepTwoFormControl.pickupDate.setValidators(null);
      stepTwoFormControl.pickTime.setValidators(null);
    } else {
      stepTwoFormControl.pickupDate.setValidators(Validators.required);
      stepTwoFormControl.pickTime.setValidators(Validators.required);
    }

    if (isReset) {
      stepTwoFormControl.pickupDate.setValue(null);
      stepTwoFormControl.pickTime.setValue(null);
    }

    stepTwoFormControl.pickupDate.markAsUntouched();
    stepTwoFormControl.pickTime.markAsUntouched();

    stepTwoFormControl.pickupDate.updateValueAndValidity();
    stepTwoFormControl.pickTime.updateValueAndValidity();
  }

  getInitValues(): void {
    this.loadingService.showLoader();
    this.isLoading = true;
    this.subscription.add(
      this.serviceBookingService.getMetadata(this?.data?.vin).subscribe(
        (response) => {
          this.loadingService.hideLoader();
          this.isLoading = false;
          this.dealerOption = response?.dealers;
          this.stepOneForm.controls.dealer.patchValue(
            response?.preferDealer?.name
          );
          this.dealerInfo = response?.preferDealer;

          this.reasons = response?.bookingReasons;

          if (this.data?.isEdit) {
            this.stepOneForm.patchValue({
              dealer: this.data?.bookingDetail?.dealer?.name,
              reason: this.data?.bookingDetail?.bookingReason?.code,
            });
          }
        },
        (errorsRes) => {
          this.loadingService.hideLoader();
          this.isLoading = false;
          handleErrors(errorsRes, this.notificationService);
        }
      )
    );
  }

  displayFn(option: string): string {
    return this.dealerOption?.find((item) => item?.name === option)
      ?.displayName;
  }

  onInputDealer(event): void {
    const { value } = event?.target;
    if (value.length >= 3 || value === '') {
      this.filteredDealerOption = of(
        this.dealerOption?.filter((option) =>
          option?.displayName?.toLowerCase()?.includes(value.toLowerCase())
        )
      );
    }
  }

  changeDealer(event): void {
    const { value } = event?.option;
    const dealer = this.dealerOption?.find((item) => item?.name === value);
    this.dealerInfo = dealer;
  }

  getAvailableDates(): void {
    this.isLoading = true;
    this.subscription.add(
      this.serviceBookingService
        .getAvailableDates(
          this.data?.vin,
          this.stepOneForm.get('reason')?.value
        )
        .subscribe(
          (response) => {
            this.isLoading = false;
            this.convertApiDatesToDateObjects(response?.availableBookingDays);
            this.setStartDates();
          },
          (error) => {
            this.isLoading = false;
          }
        )
    );
  }

  changePreferredDate(date: Date): void {
    this.stepTwoForm.controls.preferredDate.setValue(date);
    this.selectedPreferredDate = date;
    this.minDatePickupDate = date;
    this.stepTwoForm.patchValue({
      pickupDate: date,
      pickTime: null,
      preferredTime: null,
    });
    this.loadingService.showLoader();

    this.checkEnablePreferredTime();
    this.countAPI = 1;
    this.getHoursAndPickupTimes();
  }

  getHoursAndPickupTimes(): void {
    this.loadingService.showLoader();
    this.isLoading = true;

    const formattedPreferredDate = this.formattedDate(
      this.stepTwoForm.get('preferredDate')?.value
    );
    const formattedPickupDate = this.formattedDate(
      this.stepTwoForm.get('pickupDate')?.value
    );

    this.subscription.add(
      combineLatest([
        this.serviceBookingService.getAvailableHours(
          this.data?.vin,
          this.stepOneForm.get('reason')?.value,
          formattedPreferredDate
        ),
        this.serviceBookingService.getAvailablePickupTime(
          formattedPreferredDate,
          formattedPickupDate,
          this.stepTwoForm.get('preferredTime').value || '',
          this.stepOneForm.get('dealer')?.value
        ),
      ]).subscribe(
        ([availableHours, availablePickupTimes]) => {
          this.handleCountAPI();

          if (availableHours.length > 0) {
            this.availableHours = availableHours;
          } else {
            this.notificationService.showError(
              this.translateService.instant(
                'serviceBooking.addServiceBooking.noTime'
              )
            );
          }

          if (availablePickupTimes.length > 0) {
            this.availablePickupTime = availablePickupTimes;
          } else {
            this.notificationService.showError(
              this.translateService.instant(
                'serviceBooking.addServiceBooking.noTime'
              )
            );
          }
        },
        (error) => {
          this.handleCountAPI();
        }
      )
    );
  }

  getAvailableHours(): void {
    this.loadingService.showLoader();
    const preferredDate = this.stepTwoForm.controls.preferredDate.value;
    const formattedDate = this.formattedDate(preferredDate);

    this.subscription.add(
      this.serviceBookingService
        .getAvailableHours(
          this.data?.vin,
          this.stepOneForm?.value?.reason,
          formattedDate
        )
        .subscribe(
          (response) => {
            if (response.length > 0) {
              this.availableHours = response;

              const { bookingTime } = this.data?.bookingDetail || {};
              const isExist = this.availableHours.some(
                (item) => item?.code === bookingTime
              );
              if (this.data?.isEdit && !isExist) {
                this.availableHours?.push({
                  code: bookingTime,
                  name: bookingTime,
                });
                this.availableHours = this.availableHours
                  ?.filter(
                    (item, index, self) =>
                      index ===
                      self.findIndex(
                        (t) => t.name === item.name && t.code === item.code
                      )
                  )
                  ?.sort(
                    (item1, item2) =>
                      this.parseTime(item1.code) - this.parseTime(item2.code)
                  );
              }
            } else {
              this.notificationService.showError(
                this.translateService.instant(
                  'serviceBooking.addServiceBooking.noTime'
                )
              );
            }
            this.handleCountAPI();
          },
          (error) => {
            // this.isLoading = false;
            this.handleCountAPI();
          }
        )
    );
  }

  formattedDate(date: Date): string {
    if (date) {
      return new Intl.DateTimeFormat('en-US', {
        month: '2-digit',
        day: '2-digit',
        year: 'numeric',
      }).format(date);
    }
    return '';
  }

  getAvailablePickupTimes(): void {
    this.loadingService.showLoader();
    this.isLoading = true;
    const formattedPreferredDate = this.formattedDate(
      this.stepTwoForm.get('preferredDate')?.value
    );
    const formattedPickupDate = this.formattedDate(
      this.stepTwoForm.get('pickupDate')?.value
    );

    this.subscription.add(
      this.serviceBookingService
        .getAvailablePickupTime(
          formattedPreferredDate,
          formattedPickupDate,
          this.stepTwoForm.get('preferredTime').value || '',
          this.stepOneForm.get('dealer')?.value
        )
        .subscribe(
          (response) => {
            if (response.length > 0) {
              this.availablePickupTime = response;

              const { pickupTime } = this.data?.bookingDetail || {};
              const isExist = this.availablePickupTime.some(
                (item) => item?.code === pickupTime
              );
              if (this.data?.isEdit && !isExist) {
                this.availablePickupTime?.push({
                  code: pickupTime,
                  name: pickupTime,
                });
                this.availablePickupTime = this.availablePickupTime
                  ?.filter(
                    (item, index, self) =>
                      index ===
                      self.findIndex(
                        (t) => t.name === item.name && t.code === item.code
                      )
                  )
                  ?.sort(
                    (item1, item2) =>
                      this.parseTime(item1.code) - this.parseTime(item2.code)
                  );
              }
            } else {
              this.notificationService.showError(
                this.translateService.instant(
                  'serviceBooking.addServiceBooking.noTime'
                )
              );
            }
            this.handleCountAPI();
          },
          (error) => {
            this.handleCountAPI();
          }
        )
    );
  }

  changePickupDate(event): void {
    this.countAPI = 1;
    this.getAvailablePickupTimes();
    this.stepTwoForm.patchValue({
      pickTime: null,
    });
  }

  changePreferredTime(): void {
    this.stepTwoForm.patchValue({
      pickTime: null,
    });
  }

  validateInput(event: KeyboardEvent) {
    const allowedChars = /^[0-9]$/;
    if (!allowedChars.test(event.key)) {
      event.preventDefault();
    }
  }

  parseTime(time: string) {
    const [hourMin, period] = time.split(' ');
    let [hour, min] = hourMin.split(':').map(Number);
    if (period === 'PM' && hour !== 12) hour += 12;
    if (period === 'AM' && hour === 12) hour = 0;
    return hour * 60 + min;
  }

  handleCountAPI() {
    this.countAPI -= 1;
    if (this.countAPI === 0) {
      this.loadingService.hideLoader();
      this.isLoading = false;
    }
  }
}
