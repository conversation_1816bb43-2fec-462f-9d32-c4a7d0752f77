<div class="section-authorization section-authorization--{{
    approvalStatus?.status?.code?.toLowerCase()
  }}">
    <mat-icon svgIcon="ic-lock-grey" class="medium-icon section-authorization__icon"></mat-icon>
    <div class="section-authorization__content">
        <span>{{ "ticket.authorization" | translate }}</span>
        <div class="small-text">
            <ng-container [ngSwitch]="approvalStatus?.status?.code">
                <!-- Default message -->
                <span *ngSwitchDefault>
                    {{ (typeOfAuthorization === TypeTicket.IMMOBILISER  ? "ticket.authorizationRIDefaultMessage" : "ticket.authorizationDefaultMessage") | translate }}
                </span>

                <!-- Pending -->
                <span *ngSwitchCase="'PENDING'" class="text-{{ approvalStatus?.status?.code?.toLowerCase() }}">
                    {{ "ticket.authorizationPending" | translate }}
                </span>

                <!-- Approved -->
                <span *ngSwitchCase="'APPROVED'" class="text-{{ approvalStatus?.status?.code?.toLowerCase() }}">
                    {{ "ticket.authorizationApproved" | translate }}
                    <span class="lower-case">{{ approvalStatus?.status?.name }}</span>
                    {{ "common.onDate" | translate }}
                    {{ verifiedDate | date : dateFormat.FullDate }}
                </span>

                <!-- Completed -->
                <span *ngSwitchCase="'COMPLETED'" class="text-{{ approvalStatus?.status?.code?.toLowerCase() }}">
                    {{ "ticket.authorizationCompleted" | translate }}
                    {{ verifiedDate | date : dateFormat.FullDate }}
                </span>

                <!-- Rejected -->
                <span *ngSwitchCase="'REJECTED'" class="text-{{ approvalStatus?.status?.code?.toLowerCase() }}">
                    {{ "ticket.authorizationRejected" | translate }}
                    {{ verifiedDate | date : dateFormat.FullDate }}
                </span>

                <!-- Expired -->
                <span *ngSwitchCase="'EXPIRED'" class="text-{{ approvalStatus?.status?.code?.toLowerCase() }}">
                    {{ "ticket.authorizationExpired" | translate }}
                    {{ verifiedDate | date : dateFormat.FullDate }}
                </span>
            </ng-container>

            <!-- Countdown -->
            <span *ngIf="remainingTime !== '' && approvalStatus?.status?.code === 'APPROVED' || remainingTime !== '' && approvalStatus?.status?.code === 'PENDING'"
                class="section-authorization__timer-inline">
                {{ "ticket.expiresAfter" | translate }} {{ remainingTime }}
            </span>
        </div>
    </div>

    <!-- Verify Now button -->
    <div *ngIf="
      approvalStatus?.status?.code !== 'APPROVED' &&
      approvalStatus?.status?.code !== 'PENDING'
    " class="section-authorization__action">
        <button class="btn-link section-authorization__confirm" (click)="onVerifyNow()">
            <mat-icon svgIcon="ic-verify" class="small-icon" aria-hidden="true"></mat-icon>
            {{ "common.verifyNow" | translate }}
        </button>
    </div>
</div>