<div class="section-ownership-verification">
  <div class="section-header">
    <mat-icon
      svgIcon="ic-ownership-verification"
      class="medium-icon section-header--icon"
    ></mat-icon>
    <div class="section-header--content">
      <span>{{ "vehicle.ownershipVerificationWidget.title" | translate }}</span>
    </div>
  </div>
  <ng-container *ngIf="data.length > 0; else noData">
    <table class="section-ownership-verification-widget__box">
      <tbody>
        @for (item of data; track $index) {
          <tr>
            <td>
              <div class="section-ownership-verification-widget__box__content">
                <span>{{"vehicle.ownershipVerificationWidget.requestCreatedOn" | translate }} {{item.requestedDate}}</span>
                @if(!item.verified) {
                  <span>{{"vehicle.ownershipVerificationWidget.waitingPeriodRemaining" | translate }}: 
                    {{item.verifyRemainingDays !== null && item.verifyRemainingDays !== undefined ? item.verifyRemainingDays : 0}} {{"vehicle.ownershipVerificationWidget.days" | translate }}
                  </span>
                }
              </div>
            </td>
            <td>
              <div [class]="item.verified ? 'value-tag green-tag' : 'value-tag red-tag'">
                <span>{{(item.verified ? "vehicle.ownershipVerificationWidget.verified" : "vehicle.ownershipVerificationWidget.notVerified") | translate }}</span>
              </div>
            </td>
          </tr>
        }
      </tbody>
    </table>
  </ng-container>
  <ng-template #noData>
    <div class="section-no-data">
      {{ "vehicle.ownershipVerificationWidget.noData" | translate }}
    </div>
  </ng-template>
</div>
