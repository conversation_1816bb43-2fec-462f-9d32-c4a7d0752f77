@import "../../../../../../styles/abstracts/variables";

:host {
  .maintenance-mode {
    display: flex;
    justify-content: space-between;
    color: $text-color-white;

    &__item {
      display: flex;
      gap: 10px;

      > mat-icon {
        width: 20px;
        height: 20px;

        ::ng-deep svg {
          path {
            fill: $text-color-white;
          }
        }
      }

      &__content {
        display: flex;
        flex-direction: column;
        gap: 5px;

        &:first-child {
          font-weight: 600;
        }

        &__description {
          display: flex;
          gap: 10px;
          font-size: 12px;
          align-items: center;
          cursor: default;

          &--green {
            color: #2aba6c;
            display: flex;
            align-items: center;
            gap: 4px;
            font-weight: 600;

            > mat-icon {
              width: 12px;

              ::ng-deep svg {
                path {
                  fill: #2aba6c;
                }
              }
            }
          }

          &--red {
            color: #eb0a1e;
            display: flex;
            align-items: center;
            gap: 4px;
            font-weight: 600;

            > mat-icon {
              width: 12px;

              ::ng-deep svg {
                path {
                  fill: #eb0a1e;
                }
              }
            }
          }

          &--yellow {
            color: #fcbf45;
            display: flex;
            align-items: center;
            gap: 4px;
            font-weight: 600;

            > mat-icon {
              width: 12px;

              ::ng-deep svg {
                path {
                  fill: #fcbf45;
                }
              }
            }
          }

          &--gray {
            color: #ccc;
          }

          &--blue {
            color: #266ef2;
            display: flex;
            align-items: center;
            gap: 4px;
            font-weight: 600;

            > mat-icon {
              width: 12px;

              ::ng-deep svg {
                path {
                  fill: #266ef2;
                }
              }
            }
          }

          .svt-description {
            padding-top: 2px;
          }
        }
      }

      .remote-immobilizer {
        position: relative;

        .remote-immobilizer-tooltip {
          display: none;
          position: absolute;
          background-color: #101010;
          padding: 10px;
          z-index: 1;
          color: $text-color-white;
          top: 67px;
          min-width: 300px;
          left: -135px;

          &::before {
            content: "";
            width: 15px;
            height: 15px;
            display: block;
            position: absolute;
            transform: rotate(-137deg);
            left: 50%;
            top: -4px;
            background-color: #101010;
          }
        }

        .maintenance-mode__item__content__description:hover
          ~ .remote-immobilizer-tooltip {
          display: block;
        }
      }
    }
  }
}
