import { CommonModule } from '@angular/common';
import { Component, EventEmitter, inject, Input, OnInit, Output } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { filter } from 'rxjs';
import { ModalCloseComponent } from '../../vehicle/vehicle-detail/emergency-tab/modal-close/modal-close.component';
import { MatDialog } from '@angular/material/dialog';
import { ResolutionEmergency } from '../../../enums';
import { ActionModal, DateFormat } from '../../../../../core/enums';
import { Emergency } from '../../../../../core/interfaces/vehicle.interface';

@Component({
  selector: 'app-emergency',
  standalone: true,
  imports: [CommonModule, MatIconModule, TranslateModule],
  templateUrl: './emergency.component.html',
  styleUrls: ['./emergency.component.scss'],
})
export class EmergencyComponent implements OnInit {
  @Input() data: Emergency[];
  @Input() vin: string = '';
  @Input() isTicket: boolean = false;
  dateFormat = DateFormat;
  constructor() { }

  @Output() refresh = new EventEmitter();
  emergencyFormClose: FormGroup = new FormGroup({
    resolution: new FormControl('', [Validators.required]),
    comment: new FormControl(''),
  });
  translateService = inject(TranslateService);
  dialog = inject(MatDialog);

  ngOnInit() {}

  openModalClose(data: Emergency) {
    this.emergencyFormClose.reset();
    this.emergencyFormClose.patchValue({ resolution: ResolutionEmergency.all });
    const dialogRef = this.dialog.open(ModalCloseComponent, {
      width: '600px',
      data: {
        title: this.translateService.instant('vehicle.emergency.modal.title'),
        confirmMsg: this.translateService.instant(
          'vehicle.emergency.modal.subTitle'
        ),
        vin: this.vin,
        ticketId: data.relatedTicketId,
        emergencyFormClose: this.emergencyFormClose,
        resolution: this.emergencyFormClose.get('resolution') as FormControl,
        comment: this.emergencyFormClose.get('comment') as FormControl,
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.confirm'),
      },
    });
    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((result) => {
        this.refresh.emit();
      });
  }
}
