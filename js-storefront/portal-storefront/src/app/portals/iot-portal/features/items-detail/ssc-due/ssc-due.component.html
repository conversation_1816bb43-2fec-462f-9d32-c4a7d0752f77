<div class="section-ssc">
  <div class="section-header">
    <mat-icon svgIcon="ic-pms" class="medium-icon section-header--icon"></mat-icon>
    <div class="section-header--content">
      <span>{{ "serviceCampaigns.serviceCampaigns" | translate }}</span>
    </div>
  </div>

  <div class="section-ssc__body">
    <ng-container *ngIf="data && data.length; else noData">
      @for (item of data; track $index) {
      <div class="section-ssc__body--item">
        <div class="section-ssc__body--item__content">
          <span>{{item?.subjectSSCCategory}}</span>
          <span>Valid from {{item?.validFrom}}</span>
        </div>
        <div class="section-ssc__body--item__tag" [ngClass]="item?.status?.code === 'ACTIVE' ? 'red-tag' : 'grey-tag'">
          <span class="tag-box">{{ item?.status?.name }}</span>
        </div>
      </div>
      }
    </ng-container>
    <ng-template #noData>
      <div class="section-no-data">{{ 'serviceCampaigns.noServiceCampaigns' | translate }}</div>
    </ng-template>
  </div>
</div>