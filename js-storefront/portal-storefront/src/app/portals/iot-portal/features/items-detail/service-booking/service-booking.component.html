<div class="service-booking">
  <div class="service-booking-title">
    <div class="service-booking-title__title">
      <mat-icon svgIcon="ic-car-service"></mat-icon>
      {{ "serviceBooking.serviceBooking" | translate }}
    </div>
    <div *ngIf="userService.isHasPermission([PERMISSIONS_CODE.ECARE_SERVICE_BOOKING_MANAGE])" class="service-booking-title__book" (click)="addServiceBooking()">
      <mat-icon svgIcon="ic-add-red" class="small-icon"></mat-icon>
      {{ "serviceBooking.add" | translate }}
    </div>
  </div>

  <div class="service-booking-content">
    @if (serviceBookingData.length > 0) { @for (item of serviceBookingData;
    track $index) {
    <div class="booking-item" (click)="openDetailBooking(item?.bookingNo, item?.dbmBookingNo)">
      <div class="booking-item__id">
        <span>{{ item?.dbmBookingNo }}</span>
        &nbsp;·&nbsp;
        <span>{{ item?.status?.name }}</span>
        &nbsp;·&nbsp;
        <span
          >{{ item?.bookingDate | date : DateFormat.ShortDate : TimeZone.UTC8 }}
          {{ item?.bookingTime }}</span
        >
      </div>
      <div class="booking-item__description">
        {{ item?.dealerText }}
      </div>
    </div>
    } } @else {
    <div class="no-service-booking">
      {{ "serviceBooking.noServiceBooking" | translate }}
    </div>
    }
  </div>
</div>
<div class="has-data" *ngIf="serviceBookingData.length > 0"></div>