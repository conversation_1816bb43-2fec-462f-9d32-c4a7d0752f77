<div
  class="border-section {{
    isRequest
      ? requestInfo?.status?.toLowerCase()
      : approvalStatus?.status?.code?.toLowerCase()
  }}"
></div>
<mat-icon svgIcon="ic-lock-grey" class="medium-icon"></mat-icon>

@if(isRequest && approvalStatus?.status?.code !== AuthorizationStatus.Approved)
{
<div class="manager-approval-section has-request">
  <div class="manager-approval-section__left-content">
    <div class="manager-approval-section__left-content__title">
      <span>{{ "managerApproval.managerApproval" | translate }}</span>

      @if ( requestInfo?.status === ManagerApprovalStatus.Pending &&
      userService.isHasPermission([PERMISSIONS_CODE.ECARE_MANAGER_TICKET_APPROVAL]))
      {
      <div class="action-has-request">
        <button (click)="actionRequest(1)">
          <mat-icon
            svgIcon="ic-checked-red"
            class="small-icon"
            aria-hidden="true"
          ></mat-icon>
          {{ "managerApproval.approve" | translate }}
        </button>
        <button (click)="actionRequest(0)">
          <mat-icon
            svgIcon="ic-cancel"
            class="small-icon"
            aria-hidden="true"
          ></mat-icon>
          {{ "managerApproval.reject" | translate }}
        </button>
      </div>
      }
    </div>
    <div class="manager-approval-section__left-content__description">
      <div
        class="manager-approval-section__left-content__description__status--{{
          requestInfo?.status?.toLowerCase()
        }}"
      >
        @switch (requestInfo?.status) { @case (ManagerApprovalStatus.Pending) {
        {{ "managerApproval.requestWaitingApproval" | translate }}
        } @case (ManagerApprovalStatus.Approved) {
        {{ "managerApproval.requestApproved" | translate }}
        } @case (ManagerApprovalStatus.Rejected) {
        {{ "managerApproval.requestRejected" | translate }}
        } }
      </div>
      <div class="manager-approval-section__left-content__description__info">
        <span>{{ "managerApproval.requestedAction" | translate }}: </span>
        {{
          "managerApproval." + requestInfo?.action?.toLowerCase() | translate
        }}
      </div>
      <div class="manager-approval-section__left-content__description__info">
        <span>{{ "managerApproval.comment" | translate }}: </span>
        {{ requestInfo?.comment }}
      </div>

      @if (requestInfo?.status === ManagerApprovalStatus.Approved) {
      <div class="manager-approval-section__left-content__description__info">
        <span>{{ "managerApproval.approvedBy" | translate }} </span>
        {{ requestInfo?.approvedBy }} · {{ requestInfo?.approvedDate }}
      </div>
      } @if (requestInfo?.status === ManagerApprovalStatus.Rejected) {
      <div class="manager-approval-section__left-content__description__info">
        <span>{{ "managerApproval.rejectedBy" | translate }} </span>
        {{ requestInfo?.approvedBy }} · {{ requestInfo?.approvedDate }}
      </div>
      }

      <div class="manager-approval-section__left-content__description__info">
        <span>{{ "managerApproval.requestedBy" | translate }} </span>
        {{ requestInfo?.requestBy }} · {{ requestInfo?.requestDate }}
      </div>
    </div>
  </div>
</div>
} @else {
<div class="manager-approval-section">
  <div class="manager-approval-section__left-content">
    <div class="manager-approval-section__left-content__title">
      <span
        [ngClass]="{
          'manager-approval-section__left-content__title--gray-title':
            selectedTab === ManagerApprovalTab.ManagerApproval
        }"
        (click)="changeSelectedTab(ManagerApprovalTab.Authorization)"
        >{{ "managerApproval.authorization" | translate }}</span
      >

      @if(approvalStatus?.status?.code !== AuthorizationStatus.Approved &&
      approvalStatus?.status?.code !== AuthorizationStatus.Pending) {
      <mat-icon svgIcon="ic-exchange"></mat-icon>

      <span
        [ngClass]="{
          'manager-approval-section__left-content__title--gray-title':
            selectedTab === ManagerApprovalTab.Authorization
        }"
        (click)="changeSelectedTab(ManagerApprovalTab.ManagerApproval)"
        >{{ "managerApproval.managerApproval" | translate }}
      </span>
      }
    </div>
    @if(selectedTab === ManagerApprovalTab.ManagerApproval){
    <div class="manager-approval-section__left-content__description">
      {{ "managerApproval.managerAuthorizationRequired" | translate }}
    </div>
    } @else {
    <div class="manager-approval-section__left-content__description">
      <div class="small-text">
        <ng-container [ngSwitch]="approvalStatus?.status?.code">
          <!-- Default message -->
          <span *ngSwitchDefault>
            {{
              (ticketInfo?.type?.code === TypeTicket.IMMOBILISER
                ? "ticket.authorizationRIDefaultMessage"
                : "ticket.authorizationDefaultMessage"
              ) | translate
            }}
          </span>

          <!-- Pending -->
          <span
            *ngSwitchCase="AuthorizationStatus.Pending"
            class="manager-approval-section__left-content__description__status--{{
              approvalStatus?.status?.code?.toLowerCase()
            }}"
          >
            {{ "ticket.authorizationPending" | translate }}
          </span>

          <!-- Approved -->
          <span
            *ngSwitchCase="AuthorizationStatus.Approved"
            class="manager-approval-section__left-content__description__status--{{
              approvalStatus?.status?.code?.toLowerCase()
            }}"
          >
            {{ "ticket.authorizationApproved" | translate }}
            <span class="lower-case">{{ approvalStatus?.status?.name }}</span>
            {{ "common.onDate" | translate }}
            {{ verifiedDate | date : DateFormat.FullDate }}
          </span>

          <!-- Completed -->
          <span
            *ngSwitchCase="AuthorizationStatus.Completed"
            class="text-{{ approvalStatus?.status?.code?.toLowerCase() }}"
          >
            {{ "ticket.authorizationCompleted" | translate }}
            {{ verifiedDate | date : DateFormat.FullDate }}
          </span>

          <!-- Rejected -->
          <span
            *ngSwitchCase="AuthorizationStatus.Rejected"
            class="manager-approval-section__left-content__description__status--{{
              approvalStatus?.status?.code?.toLowerCase()
            }}"
          >
            {{ "ticket.authorizationRejected" | translate }}
            {{ approvalStatus?.rejectedDate | date : DateFormat.FullDate }}
          </span>

          <!-- Expired -->
          <span
            *ngSwitchCase="AuthorizationStatus.Expired"
            class="text-{{ approvalStatus?.status?.code?.toLowerCase() }}"
          >
            {{ "ticket.authorizationExpired" | translate }}
            {{ verifiedDate | date : DateFormat.FullDate }}
          </span>
        </ng-container>

        <!-- Countdown -->
        <span
          *ngIf="
            (remainingTime !== '' &&
              approvalStatus?.status?.code === 'APPROVED') ||
            (remainingTime !== '' && approvalStatus?.status?.code === 'PENDING')
          "
          class="section-authorization__timer-inline"
        >
          {{ "ticket.expiresAfter" | translate }} {{ remainingTime }}
        </span>
      </div>
    </div>

    }
  </div>
  <div class="manager-approval-section__right-content">
    @if(selectedTab === ManagerApprovalTab.ManagerApproval &&
    userService.isHasPermission([PERMISSIONS_CODE.ECARE_MANAGER_APPROVAL_CREATE])){
    <button (click)="sendRequest()">
      <mat-icon
        svgIcon="ic-verify"
        class="small-icon"
        aria-hidden="true"
      ></mat-icon>
      {{ "managerApproval.sendRequest" | translate }}
    </button>
    } @else {
    <div
      *ngIf="
        approvalStatus?.status?.code !== 'APPROVED' &&
        approvalStatus?.status?.code !== 'PENDING' &&
        userService.isHasPermission([
          PERMISSIONS_CODE.ECARE_AUTHORIZATION_CREATE
        ])
      "
      class="section-authorization__action"
    >
      <button
        class="btn-link section-authorization__confirm"
        (click)="onVerifyNow()"
      >
        <mat-icon
          svgIcon="ic-verify"
          class="small-icon"
          aria-hidden="true"
        ></mat-icon>
        {{ "common.verifyNow" | translate }}
      </button>
    </div>
    }
  </div>
</div>
}
