import { Component, EventEmitter, inject, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { ColDef } from 'ag-grid-community';
import { Subscription } from 'rxjs';
import { Router } from '@angular/router';
import { IconModule } from '../../../../../core/icon/icon.module';
import { AgGridCustomComponent } from '../../../../../core/shared';
import { TroubleCodeService } from '../../../services';
import { LoadingService, NotificationService } from '../../../../../core/services';
import { DEFAULT_COL_DEF } from '../../../../../core/constants';
import { handleErrors } from '../../../../../core/helpers';
import { VehicleDetailsTab } from '../../../enums';
@Component({
  selector: 'app-dtc-widget',
  standalone: true,
  imports: [CommonModule, TranslateModule, IconModule, AgGridCustomComponent],
  templateUrl: './dtc-widget.component.html',
  styleUrl: './dtc-widget.component.scss',
  providers: [TroubleCodeService, NotificationService],
})
export class DtcWidgetComponent implements OnInit, OnDestroy {
  @Input() vin: string;
  @Input() fromTicket = false;

  @Output() onViewFFD = new EventEmitter<string>();

  router = inject(Router);
  loadingService = inject(LoadingService);
  troubleCodeService = inject(TroubleCodeService);
  notificationService = inject(NotificationService);

  subscription = new Subscription();

  defaultColDef = {
    ...DEFAULT_COL_DEF,
    valueFormatter: (params) => (params.value ? params.value : '-'),
    wrapText: true,
    autoHeight: true,
  };
  rowData = [];
  colDefs: ColDef[];
  ngOnInit(): void {
    this.colDefs = this.troubleCodeService.getColumnDefs(
      this.viewFFD.bind(this)
    );
    this.getList();
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  getList(): void {
    this.loadingService.showLoader();
    this.subscription.add(
      this.troubleCodeService
        .getTroubleCodeList(this.vin, {} as any)
        .subscribe(
          (response) => {
            this.loadingService.hideLoader();
            this.rowData = response?.items;
          },
          (error) => {
            this.loadingService.hideLoader();
            handleErrors(error, this.notificationService);
          }
        )
    );
  }

  viewFFD(data): void {
    if (data?.ssrID) {
      if(this.fromTicket) {
        this.router.navigate([`/vehicles/${this.vin}`], {
          queryParams: {
            selectedTab: VehicleDetailsTab.TroubleCode,
            ssrID: data?.ssrID
          }
        })
      } else {
        this.onViewFFD.emit(data?.ssrID);
      }
    }
  }
}
