import { Component, inject, OnInit } from '@angular/core';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { Subscription } from 'rxjs';
import { CommonModule } from '@angular/common';
import { CancelBookingComponent } from '../cancel-booking/cancel-booking.component';
import { AddServiceBookingComponent } from '../add-service-booking/add-service-booking.component';
import { IconModule } from '../../../../../../core/icon/icon.module';
import { LoadingComponent } from '../../../../../../layout/global/loading/loading.component';
import { ServiceBookingService } from '../../../../services';
import { NotificationService, UserService } from '../../../../../../core/services';
import { PERMISSIONS_CODE } from '../../../../../../core/constants';
import { ActionModal, DateFormat, TimeZone } from '../../../../../../core/enums';
import { ServiceBookingStatus } from '../../../../enums';
import { ServiceBookingDetail } from '../../../../interfaces';
import { handleErrors } from '../../../../../../core/helpers';

@Component({
  selector: 'app-service-booking-details',
  standalone: true,
  imports: [
    CommonModule,
    IconModule,
    MatIconModule,
    TranslateModule,
    LoadingComponent,
  ],
  templateUrl: './service-booking-details.component.html',
  styleUrl: './service-booking-details.component.scss',
  providers: [ServiceBookingService, NotificationService],
})
export class ServiceBookingDetailsComponent implements OnInit {
  data: {
    serviceBookingId: string;
    vin: string;
    dbmBookingNo: string;
    hasActiveCustomerRelation?: boolean;
  } = inject(MAT_DIALOG_DATA);

  dialogRef = inject(MatDialogRef<ServiceBookingDetailsComponent>);
  serviceBookingService = inject(ServiceBookingService);
  notificationService = inject(NotificationService);

  dialog = inject(MatDialog);
  userService = inject(UserService);
    
  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;
  readonly DateFormat = DateFormat;
  readonly TimeZone = TimeZone;
  readonly ServiceBookingStatus = ServiceBookingStatus;

  subscription = new Subscription();
  isLoading = false;

  bookingDetail: ServiceBookingDetail = {} as ServiceBookingDetail;

  ngOnInit(): void {
    this.getServiceBookingDetail();
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  onCancelBooking(): void {
    if (!this.data?.hasActiveCustomerRelation) {
      this.serviceBookingService.activeCustomerRelation(this.data?.vin);
      return;
    }
    
    this.dialogRef.close();
    const dialogRef = this.dialog.open(CancelBookingComponent, {
      width: '660px',
      autoFocus: false,

      data: this.data,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result?.action === ActionModal.Cancel) {
        this.openCurrentDialog();
      }
    });
  }

  onEditBooking(): void {
    if (!this.data?.hasActiveCustomerRelation) {
      this.serviceBookingService.activeCustomerRelation(this.data?.vin);
      return;
    }

    this.dialogRef.close();
    const dialogRef = this.dialog.open(AddServiceBookingComponent, {
      width: '850px',
      maxHeight: '90vh',
      autoFocus: false,
      data: {
        isEdit: true,
        ...this.data,
        bookingDetail: this.bookingDetail,
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result?.action === ActionModal.Cancel) {
        this.openCurrentDialog();
      }
    });
  }

  getServiceBookingDetail(): void {
    this.isLoading = true;
    this.subscription.add(
      this.serviceBookingService
        .getServiceBookingsDetail(this.data?.serviceBookingId)
        .subscribe(
          (response) => {
            this.isLoading = false;
            this.bookingDetail = response;
          },
          (error) => {
            this.isLoading = false;
            handleErrors(error, this.notificationService);
          }
        )
    );
  }

  openCurrentDialog(): void {
    this.dialog.open(ServiceBookingDetailsComponent, {
      width: '600px',
      maxHeight: '90vh',
      data: this.data,
    });
  }
}
