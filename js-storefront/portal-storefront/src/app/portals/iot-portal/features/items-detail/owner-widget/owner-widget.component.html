<div class="section-owner">
  <div class="section-header">
    <mat-icon
      svgIcon="ic-owner-details"
      class="medium-icon section-header--icon"
    ></mat-icon>
    <div class="section-header--content">
      <span>{{ "vehicle.ownerWidget.title" | translate }}</span>
    </div>
  </div>
  <ng-container *ngIf="data; else noData">
    <div class="section-owner__body">
      <div class="section-owner__body--item-box">
        <div class="section-owner__body__content">
          <span class="section-owner__body__content--title">{{
            "vehicle.ownerWidget.name" | translate
          }}</span>
          <span class="section-owner__body__content--value">{{
            data?.name || "-"
          }}</span>
        </div>
        <div class="section-owner__body__content">
          <span class="section-owner__body__content--title">{{
            "vehicle.ownerWidget.mobileNumber" | translate
          }}</span>
          <span class="section-owner__body__content--value">{{
            data?.mobileNumber || "-"
          }}</span>
        </div>
      </div>
      <div class="section-owner__body__content email-box">
        <div class="email-content">
          <div class="email-content-header">
            <span class="section-owner__body__content--title">{{
              "vehicle.ownerWidget.email" | translate
            }}</span>
            <button class="btn-link" [class.green]="data?.isEmailVerified">
              <mat-icon
                [svgIcon]="
                  data?.isEmailVerified ? 'ic-check-circle' : 'ic-close-red'
                "
                class="super-small-icon"
                aria-hidden="true"
              ></mat-icon>
              {{
                data?.isEmailVerified
                  ? ("vehicle.ownerWidget.verified" | translate)
                  : ("vehicle.ownerWidget.unverified" | translate)
              }}
            </button>
          </div>
          <span class="section-owner__body__content--value">{{
            data?.emailAddress || "-"
          }}</span>
        </div>
        @if(!data?.isEmailVerified) {
        <div>
          <button class="btn-link" (click)="onVerifyEmail()">
            <mat-icon
              svgIcon="ic-lock"
              class="small-icon"
              aria-hidden="true"
            ></mat-icon>
            {{ "vehicle.ownerWidget.verify" | translate }}
          </button>
        </div>
        }
      </div>
      <div class="section-owner__body__content">
        <span class="section-owner__body__content--title">{{
          "vehicle.ownerWidget.address" | translate
        }}</span>
        <span class="section-owner__body__content--value">{{
          data?.address || "-"
        }}</span>
      </div>
    </div>
  </ng-container>
  <ng-template #noData>
    <div class="section-no-data">
      {{ "vehicle.ownerWidget.noData" | translate }}
    </div>
  </ng-template>
</div>
