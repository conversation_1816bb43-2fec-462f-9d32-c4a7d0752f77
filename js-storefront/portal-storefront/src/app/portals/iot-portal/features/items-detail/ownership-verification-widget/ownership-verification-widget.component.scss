.section-ownership-verification {
  display: flex;
  flex-direction: column;
  gap: 20px;
  &__body {
    display: flex;
    flex-direction: column;
    gap: 20px;
    &--item-box {
      display: flex;
      flex-wrap: wrap;
      gap: 25px;
      div {
        &:first-child {
          width: calc(50% - 12.5px);
          min-width: 150px;
        }
        &:last-child {
          width: calc(50% - 12.5px);
        }
      }
    }
    &__content {
      display: flex;
      flex-direction: column;
      flex-wrap: wrap;
      gap: 5px;
      &--title {
        color: var(--Primary-Black, #3A3A3A);
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
      }
      &--value {
        color: var(--Primary-Black, #101010);
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px;
      }
      &.email-box {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        align-items: center;
        gap: 15px;
        .email-content {
            display: flex;
            flex-direction: column;
            gap: 5px;
            .email-content-header {
                display: flex;
                align-items: center;
                flex-wrap: wrap;
                gap: 10px;
                .btn-link {
                    font-size: 12px;
                }
            }
        }
      }
    }
  }

  .section-ownership-verification-widget__box {

    tbody {
      width: 100%;
      display: block;

      tr {
        border-bottom: 1px solid #EEE;
        display: flex;
        width: 100%;
        justify-content: space-between;
        align-items: center;

        &:not(:first-child) {
          .section-ownership-verification-widget__box__content, .value-tag{
            margin-top: 10px;
          }
        }
        :not(:first-child) {
          div {
            display: flex;
            justify-content: end;
          }
        }
      }
    }

    .section-ownership-verification-widget__box__content {
      display: flex;
      flex-direction: column;
      gap: 5px;
      margin-bottom: 10px;

      span {
        font-size: 14px;
        font-style: normal;
        color: #101010;
        font-weight: 400;

        &:first-child {
          font-weight: 600;
        }
      }
    }

    .value-tag {
      margin-bottom: 10px;
      span {
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 14px;
        letter-spacing: -.42px;
        padding: 8px;
        display: inline-block;
      }
    }
  }
  
}


:host ::ng-deep {
    .section-owner {
        mat-icon {
            svg g path {
                fill: #EB0A1E !important;
            }
        }
    }
}
