<div class="section-dtc">
    <div class="section-header">
      <mat-icon
        svgIcon="ic-dtc"
        class="medium-icon section-header--icon"
      ></mat-icon>
      <div class="section-header--content">
        <span>{{ "vehicle.dtc.title" | translate }}</span>
      </div>
    </div>
    <div class="section-dtc__body">
     @if(rowData?.length > 0) {
        <app-ag-grid-custom
        [rowData]="rowData"
        [colDefs]="colDefs"
        [defaultColDef]="defaultColDef"
        [isShowActionExport]="false"
        [onlyTable]="true"
      >
      </app-ag-grid-custom>
     } @else {
        <div class="section-no-data">{{ "vehicle.dtc.noData" | translate }}</div>
     }
    </div>
  </div>
  