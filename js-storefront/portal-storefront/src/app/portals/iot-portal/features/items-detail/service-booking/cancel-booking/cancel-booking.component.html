<h2 class="title-dialog">
  {{ "serviceBooking.cancelBooking.cancelBooking" | translate }}
</h2>

<div class="container-dialog" [formGroup]="form">
  <p class="sub-title">
    {{
      "serviceBooking.cancelBooking.subTitleCancelBooking"
        | translate : { bookingId: data?.dbmBookingNo || data?.serviceBookingId }
    }}
  </p>
  <app-radio-button
    [label]="'serviceBooking.cancelBooking.reasonCancel'"
    [required]="true"
    [control]="form.controls.reason"
    [option]="cancelReasons"
    (changeOption)="changeReason()"
  ></app-radio-button>

  <div class="form-group" *ngIf="form.value.reason === otherReason">
    <label class="form-group__label">
      {{ "serviceBooking.addServiceBooking.remark" | translate }}
    </label>
    <textarea class="form-textarea" formControlName="remark"></textarea>
  </div>
</div>

<div class="action-dialog">
  <button class="btn-quaternary" (click)="onCancel()">
    {{ "serviceBooking.cancelBooking.keepBooking" | translate }}
  </button>

  <button
    class="btn-primary btn-confirm"
    (click)="onCancelBooking()"
    [disabled]="form.invalid"
  >
    {{ "serviceBooking.cancelBooking.cancelBookingAction" | translate }}
  </button>
</div>
