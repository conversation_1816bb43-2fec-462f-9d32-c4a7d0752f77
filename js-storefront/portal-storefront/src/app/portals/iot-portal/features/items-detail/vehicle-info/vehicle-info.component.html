<div class="vehicle-info">
  <ng-container *ngIf="vehicle; else noData">

    <div class="vehicle-info__left">
      <div class="vehicle-info__image">
        <img [src]="
            vehicle?.vehicleImage
              ? getFullImageUrl(vehicle.vehicleImage)
              : 'assets/images/missing_product_EN.png'
          " [alt]="vehicle?.vehicleModel || 'Missing Product'" />
      </div>
      <div class="vehicle-info__status">
        <span class="vehicle-info__status-text" [class.vehicle-info__status-disconnected]="
            vehicle?.sim?.code !== simStatus.ActiveSubscription &&
            vehicle?.sim?.code !== simStatus.ActiveTesting &&
            vehicle?.sim?.code !== simStatus.Active
          ">
          {{
          vehicle?.sim?.code === simStatus.ActiveSubscription ||
          vehicle?.sim?.code === simStatus.ActiveTesting ||
          vehicle?.sim?.code === simStatus.Active
          ? ("vehicle.vehicleWidget.deviceConnected" | translate)
          : ("vehicle.vehicleWidget.deviceDisconnected" | translate)
          }}
          @if ( vehicle?.sim?.code === simStatus.ActiveSubscription ||
          vehicle?.sim?.code === simStatus.ActiveTesting || vehicle?.sim?.code ===
          simStatus.Active) {
          <mat-icon class="normal-icon" svgIcon="ic-green-check"></mat-icon>
          } @else {
          <mat-icon class="normal-icon" svgIcon="ic-close-grey"></mat-icon>
          }
        </span>
      </div>
    </div>
    

    <div class="vehicle-info__right">
      <div class="vehicle-info__details-name">
        <div class="vehicle-info__details">
          <h3 class="vehicle-info__title">
            {{ vehicle?.vehicleModel ? vehicle?.vehicleModel : "-" }}
          </h3>
          <p class="vehicle-info__sub-details">
            {{ vin ? vin : "-" }}
            <span *ngIf="vehicle?.vehicleColor"> · {{ vehicle.vehicleColor }}</span>
            <span *ngIf="vehicle?.plateNumber"> · {{ vehicle.plateNumber }}</span>
          </p>
        </div>
    
        <div class="vehicle-info__stats">
          <div class="vehicle-info__stat">
            <div class="vehicle-info__stat-icon">
              <mat-icon class="large-icon" svgIcon="ic-automatic"></mat-icon>
            </div>
            <div class="vehicle-info__stat-text">
              <p>
                {{
                vehicle?.odo !== undefined && vehicle?.odo !== null
                ? (vehicle?.odo | number)
                : 0
                }}
                {{'common.km' | translate}}
              </p>
              <small>{{ "tickets.traveled" | translate }}</small>
            </div>
          </div>
          <div class="vehicle-info__stat">
            <div class="vehicle-info__stat-icon">
              <mat-icon class="large-icon" svgIcon="ic-engine"></mat-icon>
            </div>
            <div class="vehicle-info__stat-text">
              <p class="{{ vehicle?.engineStatus ? 'green' : 'red' }}">
                {{ vehicle?.engineStatus ? "On" : "Off" }}
              </p>
              <small>{{ "tickets.engine" | translate }}</small>
            </div>
          </div>
          <div class="vehicle-info__stat">
            <div class="vehicle-info__stat-icon">
              <mat-icon class="large-icon" svgIcon="ic-gas"></mat-icon>
            </div>
            <div class="vehicle-info__stat-text">
              <p [class.red]="true">
                {{ vehicle?.fuelRemaining && vehicle?.fuelRemaining !== undefined && vehicle?.fuelRemaining !== null 
                && vehicle?.fuelRemaining !== "null" ? vehicle?.fuelRemaining : "0.0" }}
              </p>
              <small> {{ "tickets.fuelLeft" | translate }}</small>
            </div>
          </div>
        </div>
      </div>
      

      @if(isAction) {
        <div class="vehicle-info__action">
          <button
            *ngIf="
              userService.isHasPermission([
                PERMISSIONS_CODE.ECARE_VEHICLE_IOT_VIEW
              ])
            "
            class="btn-quaternary"
            (click)="viewDetailVehicle.emit(vin)"
          >
            {{ "customers.customerDetail.viewDetail" | translate }}
          </button>
          <button
            *ngIf="
              userService.isHasPermission([
                PERMISSIONS_CODE.ECARE_TICKET_CREATE
              ])
            "
            class="btn-quaternary btn-create-ticket"
            (click)="createTicket.emit(vin)"
          >
            {{ "customers.customerDetail.createTicket" | translate }}
          </button>
        </div>
        }
    </div>
    
  </ng-container>
  <ng-template #noData>
    <div class="section-no-data">
      {{ "vehicle.noDataVehicle" | translate }}
    </div>
  </ng-template>
</div>