<h2 class="title-dialog">
  {{ "managerApproval.requestDetails.requestDetails" | translate }}
</h2>

<div class="container-dialog" [formGroup]="form">
  <p class="sub-title">
    {{
      (data.ticketType?.code === TypeTicket.IMMOBILISER
        ? "managerApproval.requestDetails.requestForRI"
        : "managerApproval.requestDetails.requestForSVT"
      ) | translate
    }}
  </p>

  <app-radio-button
    [label]="'managerApproval.requestDetails.action'"
    [required]="true"
    [control]="form.controls.action"
    [option]="actionRequest"
  ></app-radio-button>

  <app-form-group
    [label]="'managerApproval.requestDetails.comment' | translate"
    [control]="form.controls.comment"
    [isTextArea]="true"
    [required]="true"
  >
  </app-form-group>

  <app-autocomplete-input
    [label]="'managerApproval.requestDetails.assignee' | translate"
    [placeholder]="'enterHereToSearch' | translate"
    [suggestions]="suggestionsAssignee"
    [control]="form.controls.assignee"
    [required]="true"
  >
  </app-autocomplete-input>
</div>

<div class="action-dialog">
  <button class="btn-quaternary" (click)="onCancel()">
    {{ "common.cancel" | translate }}
  </button>

  <button class="btn-primary btn-confirm" [disabled]="form.invalid" (click)="sendRequest()">
    {{ "managerApproval.requestDetails.send" | translate }}
  </button>
</div>
