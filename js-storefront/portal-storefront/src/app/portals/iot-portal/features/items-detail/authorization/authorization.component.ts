import {
  Component,
  Input,
  OnInit,
  inject,
  OnDestroy,
  Output,
  EventEmitter,
  ChangeDetectorRef,
} from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { AuthorizationDialogComponent } from './modal-authorization/authorization-dialog.component';
import { CommonModule } from '@angular/common';
import { interval, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { IconModule } from '../../../../../core/icon/icon.module';
import { ApprovalStatus } from '../../../interfaces';
import { DateFormat } from '../../../../../core/enums';
import { TypeTicket } from '../../../enums';
import { NotificationService } from '../../../../../core/services';

@Component({
  selector: 'app-authorization',
  templateUrl: './authorization.component.html',
  styleUrls: ['./authorization.component.scss'],
  standalone: true,
  imports: [TranslateModule, IconModule, CommonModule],
})
export class AuthorizationComponent implements OnInit, OnDestroy {
  @Input() approvalStatus: ApprovalStatus | null;
  @Input() verifiedDate: string | null;
  @Input() typeOfAuthorization: string | null;
  @Output() refreshApprovalStatus = new EventEmitter<void>();

  ticketId: string;
  remainingTime: string = '';
  private destroy$ = new Subject<void>();
  dateFormat = DateFormat;
  readonly TypeTicket = TypeTicket;
    
  dialog = inject(MatDialog);
  translateService = inject(TranslateService);
  route = inject(ActivatedRoute);
  cdr = inject(ChangeDetectorRef);
  notificationService = inject(NotificationService);

  ngOnInit() {
    this.ticketId = this.route.snapshot.paramMap.get('id') || '';
    if (
      this.approvalStatus?.status?.code === 'APPROVED' ||
      this.approvalStatus?.status?.code === 'PENDING'
    ) {
      this.startCountdown();
    }
  }

  startCountdown() {
    if (this.approvalStatus?.status?.code === 'APPROVED') {
      this.notificationService.showSuccess(
        this.translateService.instant('ticket.authorizationApprovedMess')
      );
    }
    const validTill = new Date(this.approvalStatus?.validTill).getTime();

    if (isNaN(validTill)) {
      console.error('Invalid triggerDate:', this.approvalStatus?.validTill);
      return;
    }

    interval(900)
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        const now = Date.now();
        const timeLeft = validTill - now;

        if (timeLeft > 0) {
          const totalSeconds = Math.floor(timeLeft / 1000);
          const minutes = Math.floor(totalSeconds / 60);
          const seconds = totalSeconds % 60;

          const formattedMinutes = minutes.toString().padStart(2, '0');
          const formattedSeconds = seconds.toString().padStart(2, '0');

          this.remainingTime = `${formattedMinutes}:${formattedSeconds}`;
        } else if (timeLeft === 0) {
          this.remainingTime = '';
          this.destroy$.next();
        } else {
          this.remainingTime = '';
          this.destroy$.next();
        }
      });
  }

  onVerifyNow() {
    const dialogRef = this.dialog.open(AuthorizationDialogComponent, {
      width: '530px',
      data: {
        title: this.translateService.instant('tickets.verificationTitlePopup'),
        icon: 'ic-verify',
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.confirm'),
        ticketId: this.ticketId,
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.refreshApprovalStatus.emit();

        setTimeout(() => {
          this.approvalStatus = {
            ...this.approvalStatus,
            status: { code: 'PENDING', name: 'Pending' },
            validTill: new Date(Date.now() + 2 * 60 * 1000).toISOString(),
          };

          this.startCountdown();
          this.cdr.detectChanges();
        }, 900);
      }
    });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
