import { CommonModule } from '@angular/common';
import { Component, inject, Input, OnDestroy, OnInit } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { MatDialog } from '@angular/material/dialog';
import { ServiceBookingDetailsComponent } from './service-booking-details/service-booking-details.component';
import { AddServiceBookingComponent } from './add-service-booking/add-service-booking.component';

import { Subscription } from 'rxjs';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { IconModule } from '../../../../../core/icon/icon.module';
import { ServiceBookingService, ServiceBookingShareDataService } from '../../../services';
import { LoadingService, NotificationService, UserService } from '../../../../../core/services';
import { PERMISSIONS_CODE } from '../../../../../core/constants';
import { ActionModal, DateFormat, TimeZone } from '../../../../../core/enums';
import { ItemServiceBooking } from '../../../interfaces';

@Component({
  selector: 'app-service-booking',
  standalone: true,
  imports: [
    CommonModule,
    IconModule,
    TranslateModule,
    MatProgressSpinnerModule,
  ],
  templateUrl: './service-booking.component.html',
  styleUrl: './service-booking.component.scss',
  providers: [ServiceBookingService, NotificationService],
})
export class ServiceBookingComponent implements OnInit, OnDestroy {
  @Input() set vin(value: string) {
    if (value) {
      this.vinValue = value;
      this.getServiceBookings();
    }
  }
  @Input() vehicleMileage: number;
  @Input() hasActiveCustomerRelation: boolean;

  dialog = inject(MatDialog);
  serviceBookingService = inject(ServiceBookingService);
  loadingService = inject(LoadingService);
  serviceBookingShareDataService = inject(ServiceBookingShareDataService);
  userService = inject(UserService);

  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;
  readonly DateFormat = DateFormat;
  readonly TimeZone = TimeZone;

  subscription = new Subscription();
  isLoading = false;
  serviceBookingData: ItemServiceBooking[] = [];

  vinValue: string;
  isGetServiceBooking = false;

  ngOnInit(): void {
    this.serviceBookingShareDataService.isReloadList$.subscribe((value) => {
      if (value) {
        this.getServiceBookings();
      }
    });
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  openDetailBooking(serviceBookingId: string, dbmBookingNo: string): void {
    const dialogRef = this.dialog.open(ServiceBookingDetailsComponent, {
      width: '600px',
      maxHeight: '90vh',
      data: {
        serviceBookingId,
        vin: this.vinValue,
        dbmBookingNo,
        hasActiveCustomerRelation: this.hasActiveCustomerRelation
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
    });
  }

  addServiceBooking(): void {
    if (!this.hasActiveCustomerRelation) {
      this.serviceBookingService.activeCustomerRelation(this.vinValue);
      return;
    }

    const dialogRef = this.dialog.open(AddServiceBookingComponent, {
      width: '850px',
      maxHeight: '90vh',
      autoFocus: false,
      data: {
        isEdit: false,
        vin: this.vinValue,
        vehicleMileage: this.vehicleMileage,
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result?.action === ActionModal.Submit) {
        // TODO: remove
        // this.getServiceBookings();
      }
    });
  }

  getServiceBookings(): void {
    this.isLoading = true;
    this.subscription.add(
      this.serviceBookingService
        .getAllServiceBookings({ vin: this.vinValue, pageSize: 5 })
        .subscribe(
          (response) => {
            this.isLoading = false;
            this.serviceBookingData = response?.items;
          },
          (error) => {
            this.isLoading = false;
          }
        )
    );
  }
}
