@import "../../../../../../styles/abstracts/variables";

@mixin button-icon {
  display: flex;
  align-items: center;
  gap: 5px;

  color: $text-color-5;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
}

:host {
  display: flex;
  align-items: center;
  gap: 12px;

  .border-section {
    position: absolute;
    height: 100%;
    left: 0;
    border-left: 4px solid #6b7cfe;

    &.approved {
      border-left: 4px solid #2aba6c;
    }

    &.rejected {
      border-left: 4px solid #eb0a1e;
    }
  }

  .manager-approval-section {
    display: flex;
    gap: 12px;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    &__left-content {
      &__title {
        font-weight: 600;
        font-size: 16px;
        cursor: pointer;

        display: flex;
        gap: 10px;
        align-items: center;

        &--gray-title {
          color: #808080;
          font-size: 14px;
        }

        mat-icon {
          width: 20px;
          height: 20px;
        }
      }

      &__description {
        font-size: 12px;

        &__info {
          font-size: 14px;
          span {
            font-weight: 600;
          }
        }

        &__status {
          &--pending {
            color: #266ef2;
          }

          &--approved {
            color: #2aba6c;
          }

          &--rejected {
            color: #eb0a1e;
          }
        }
      }
    }

    &__right-content {
      button {
        @include button-icon;
      }
    }

    &.has-request {
      .manager-approval-section {
        &__left-content {
          width: 100%;

          &__title {
            justify-content: space-between;

            .action-has-request {
              display: flex;
              gap: 15px;

              button {
                @include button-icon;
                mat-icon {
                  ::ng-deep svg {
                    path {
                      fill: $text-color-5;
                    }
                  }
                }
              }
            }
          }

          &__description {
            &__status {
              &--pending, &--approved, &--rejected {
                font-weight: 600;
              }
            }
          }
        }
      }
    }
  }
}
