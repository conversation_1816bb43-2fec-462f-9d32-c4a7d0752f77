@import "../../../../../../styles/abstracts/variables";

:host {
  .title-dialog {
    margin: 45px auto 35px;
  }

  .container-dialog {
    padding: 0 55px 25px;
    display: flex;
    flex-direction: column;
    gap: 10px;

    &__priority {
      display: grid;
      gap: 30px;
      grid-template-columns: repeat(3, 1fr);

      app-radio-button {
        ::ng-deep {
          mat-radio-group {
            flex-direction: row;
            gap: 35px;
          }
        }
      }
    }

    &__vehicle-plate {
      &__more-info {
        display: flex;
        gap: 10px;
        flex-direction: column;
        margin-bottom: 20px;

        div {
          display: flex;
          gap: 10px;
          align-items: center;

          mat-icon {
            width: 16px;
            height: 16px;

            ::ng-deep svg {
              path {
                fill: $text-color;
              }
            }
          }
        }
      }
    }

    .form-textarea {
      resize: none;
      min-height: 70px;
    }
  }

  .action-dialog {
    justify-content: center;
    gap: 20px;
  }
}
