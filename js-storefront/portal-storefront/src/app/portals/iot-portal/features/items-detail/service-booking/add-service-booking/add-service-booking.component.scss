@import "../../../../../../../styles/abstracts/variables";

:host {
  .title-dialog {
    margin: 45px auto 0;
  }

  .container-dialog {
    padding: 15px 55px 35px;

    .info-booking {
      display: flex;
      gap: 30px;
      flex-direction: column;

      &__dealer {
        &__detail {
          display: flex;
          gap: 10px;
          flex-direction: column;
          margin-top: 10px;

          &__item {
            display: flex;
            align-items: center;
            gap: 10px;
            font-family: $text-font-stack;

            mat-icon {
              width: 16px;
              height: 16px;

              ::ng-deep svg {
                path {
                  fill: $text-color;
                }
              }
            }
          }
        }
      }

      &__reason {
        ::ng-deep {
          mat-radio-group {
            display: grid;
            grid-template-columns: auto auto;
            gap: 15px;
          }
        }
      }

      .form-textarea {
        resize: none;
      }

      &__pickup-date-time {
        display: flex;
        gap: 30px;

        > * {
          width: 100%;
        }

        &__date {
          .form-group {
            margin-bottom: 0;
            &__input .form-control {
              padding: 9px 0;
            }
          }
        }

        &__time {
          ::ng-deep .dropdown-form-group {
            padding-bottom: 0;
          }
        }
      }

      &__preferred-time {
        ::ng-deep .dropdown-form-group {
          padding-bottom: 0;
        }
      }
    }

    .action-step-one {
      gap: 20px;
      justify-content: center;
      padding-bottom: 10px;
      padding-top: 35px;
    }

    .action-step-two {
      padding: 35px 0 10px;
      justify-content: center;
      position: relative;

      &__action {
        display: flex;
        gap: 20px;
      }

      &__back {
        width: fit-content;
        display: flex;
        align-items: center;
        gap: 5px;
        text-transform: capitalize;
        font-weight: $fw600;
        cursor: pointer;

        position: absolute;
        left: 0;
        top: 56%;
      }
    }

    .step-booking {
      ::ng-deep .custom-stepper {
        &.step-1-completed {
          .mat-step-icon {
            background-color: #2aba6c;
            color: $text-color-white;
          }

          .mat-horizontal-stepper-header::before,
          .mat-horizontal-stepper-header::after,
          .mat-stepper-horizontal-line {
            border-top-color: #2aba6c;
            top: 40px;
          }
        }
      }
    }
  }

  ::ng-deep {
    .mat-mdc-form-field-subscript-wrapper {
      display: none;
    }
  }

  app-loading {
    top: 0;
  }
}

.custom-autocomplete-option {
  ::ng-deep .mdc-list-item__primary-text {
    .dealer-option {
      padding: 8px 0;
      p {
        margin: 0 !important;

        &:first-child {
          font-weight: 600;
        }

        &:last-child {
          font-size: $fs12;
          color: #808080;
        }
      }
    }
  }
}
