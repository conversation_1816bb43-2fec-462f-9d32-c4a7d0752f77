import { Component, EventEmitter, Input, Output } from '@angular/core';
import {
  DateAdapter,
  MAT_DATE_FORMATS,
  MatNativeDateModule,
  provideNativeDateAdapter,
} from '@angular/material/core';
import { MatCalendarCellClassFunction, MatDatepickerModule } from '@angular/material/datepicker';
import { CommonModule } from '@angular/common';
import { CustomDateAdapter } from '../../../../../../core/services';

export const CUSTOM_DATE_FORMATS = {
  parse: {
    dateInput: 'month-year',
  },
  display: {
    dateInput: 'month-year',
    monthYearLabel: 'month-year',
    dateA11yLabel: 'month-year',
    monthYearA11yLabel: 'month-year',
  },
};

@Component({
  selector: 'app-preferred-date',
  standalone: true,
  imports: [CommonModule,MatDatepickerModule, MatNativeDateModule],
  templateUrl: './preferred-date.component.html',
  styleUrl: './preferred-date.component.scss',
  providers: [
    provideNativeDateAdapter(),
    { provide: DateAdapter, useClass: CustomDateAdapter },
    { provide: MAT_DATE_FORMATS, useValue: CUSTOM_DATE_FORMATS },
  ],
})
export class PreferredDateComponent {
  @Input() selectedPreferredDate: Date;
  @Input() startDateCalendar1: Date;
  @Input() startDateCalendar2: Date;
  @Input() addDisableClass: MatCalendarCellClassFunction<any>;

  @Output() changePreferredDate = new EventEmitter();
}
