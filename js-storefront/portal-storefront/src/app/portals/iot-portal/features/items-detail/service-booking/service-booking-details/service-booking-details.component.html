<app-loading *ngIf="isLoading"></app-loading>
<div class="icon-header">
  <mat-icon svgIcon="ic-car-service" class="icon-title"></mat-icon>
</div>

<h2 class="title-dialog">
  {{ "serviceBooking.serviceBookingDetails.serviceBookingDetails" | translate }}
</h2>

<div class="container-dialog">
  <p class="title-session">
    {{ "serviceBooking.serviceBookingDetails.bookingInfo" | translate }}
  </p>

  <div class="service-info">
    <div class="service-info__item service-info__item__booking-info">
      <div>
        <mat-icon svgIcon="ic-info"></mat-icon>
        {{ "serviceBooking.serviceBookingDetails.id" | translate }}
        {{ bookingDetail?.dbmBookingNo }}
      </div>
      <div>
        {{ "serviceBooking.serviceBookingDetails.status" | translate }}
        {{ bookingDetail?.status?.name }}
      </div>
    </div>

    <div class="service-info__item" *ngIf="bookingDetail?.cancelReason">
      <mat-icon svgIcon="ic-package-1"></mat-icon>
      {{
        "serviceBooking.serviceBookingDetails.cancellationReason" | translate
      }}
      {{ bookingDetail?.cancelReason?.name }}
    </div>

    <div
      class="service-info__item"
      *ngIf="bookingDetail?.cancelReasonDescription"
    >
      <mat-icon svgIcon="ic-remark"></mat-icon>
      {{
        "serviceBooking.serviceBookingDetails.cancellationRemark" | translate
      }}
      {{ bookingDetail?.cancelReasonDescription }}
    </div>
  </div>

  <p class="title-session">
    {{ "serviceBooking.serviceBookingDetails.dealerInfo" | translate }}
  </p>

  <div class="service-info">
    <div class="service-info__item">
      <mat-icon svgIcon="ic-car-service"></mat-icon>
      {{ bookingDetail?.dealer?.displayName }}
    </div>

    <div class="service-info__item">
      <mat-icon svgIcon="ic-call"></mat-icon>
      {{ bookingDetail?.dealer?.phoneNumber }}
    </div>

    <div class="service-info__item">
      <mat-icon svgIcon="ic-location"></mat-icon>
      {{ bookingDetail?.dealer?.addressDisplay }}
    </div>
  </div>

  <p class="title-session">
    {{ "serviceBooking.serviceBookingDetails.serviceInfo" | translate }}
  </p>

  <div class="service-info">
    <div class="service-info__item">
      <mat-icon svgIcon="ic-calendar"></mat-icon>
      {{ "serviceBooking.serviceBookingDetails.serviceOn" | translate }}
      {{ bookingDetail?.bookingDate | date : DateFormat.ShortDate : TimeZone.UTC8 }}
      {{ bookingDetail?.bookingTime }}
    </div>

    <div class="service-info__item">
      <mat-icon svgIcon="ic-car-service"></mat-icon>
      {{ "serviceBooking.serviceBookingDetails.bookingReason" | translate }}
      {{ bookingDetail?.bookingReason?.name }}
    </div>

    <div class="service-info__item" *ngIf="bookingDetail?.odo">
      <mat-icon svgIcon="ic-milage"></mat-icon>
      {{ bookingDetail?.odo + ("common.km" | translate) }}
    </div>

    <div class="service-info__item">
      <mat-icon svgIcon="ic-car-black"></mat-icon>
      {{ "serviceBooking.addServiceBooking.waitForTheUnit" | translate }}:
      {{
        (bookingDetail?.waitPickupAtDealer ? "common.yes" : "common.no")
          | translate
      }}
    </div>

    <div class="service-info__item" *ngIf="bookingDetail?.pickupDate">
      <mat-icon svgIcon="ic-calendar"></mat-icon>
      {{
        "serviceBooking.serviceBookingDetails.bookingServicePickupAt"
          | translate
      }}
      {{ bookingDetail?.pickupDate | date : DateFormat.ShortDate : TimeZone.UTC8 }}
      {{ bookingDetail?.pickupTime }}
    </div>

    <div class="service-info__item" *ngIf="bookingDetail?.remark">
      <mat-icon svgIcon="ic-remark"></mat-icon>
      {{ bookingDetail?.remark }}
    </div>
  </div>
</div>

<div class="action-dialog">
  @if (bookingDetail?.status?.code === ServiceBookingStatus.Confirmed &&
  userService.isHasPermission([PERMISSIONS_CODE.ECARE_SERVICE_BOOKING_MANAGE]))
  {
  <div class="action-dialog__booking">
    <button class="btn-secondary" (click)="onCancelBooking()">
      {{ "serviceBooking.serviceBookingDetails.cancelBooking" | translate }}
    </button>
    <button class="btn-primary" (click)="onEditBooking()">
      {{ "serviceBooking.serviceBookingDetails.editBooking" | translate }}
    </button>
  </div>
  }

  <button class="btn-close" (click)="onCancel()">
    {{ "common.close" | translate }}
  </button>
</div>
