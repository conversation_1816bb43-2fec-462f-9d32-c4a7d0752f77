@import "../../../../../../styles/abstracts/variables";
.section-emergency {
  display: flex;
  flex-direction: column;
  gap: 20px;
  &__header {
    display: flex;
    align-items: center;
    gap: 12px;
    span {
      font-size: 22px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
      color: #101010;
    }
  }
  &__list {
    overflow: auto;
    width: 100%;
  }
  &__box {
    width: 100%;
    &__content {
      display: inline-flex;
      flex-direction: column;
      &--address {
        margin-bottom: 5px;
      }
      div {
        display: flex;
        align-items: flex-start;
        gap: 5px;
        margin-bottom: 5px;
        .content-icon {
          width: 20px;
          height: 20px;
        }
        span {
          cursor: default;
          font-weight: 600;
          align-self: flex-end;
        }
      }
      span {
        font-size: 14px;
        font-style: normal;
        color: #101010;
        font-weight: 400;
      }
    }
    tbody {
      tr {
        td {
          border-bottom: 1px solid var(--Primary-Light-Grey, #E<PERSON>);
        }
        &:first-child {
          td {
            &:first-child {
              width: 100%;
              padding: 0 5px 10px 0;
            }
            &:not(:first-child) {
              padding: 0 5px;
              white-space: nowrap;
            }
          }
        }
        &:not(:first-child) {
          td {
            &:first-child {
              width: 100%;
              padding: 10px 5px 10px 0;
            }
            &:not(:first-child) {
              padding: 0 5px;
              white-space: nowrap;
            }
          }
        }
      }
    }

    .value-tag {
      span {
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 14px;
        letter-spacing: -0.42px;
        padding: 8px;
        color: #3a3a3a;
        background-color: #eeeeee;
        display: inline-block;
      }
    }
  }
}
