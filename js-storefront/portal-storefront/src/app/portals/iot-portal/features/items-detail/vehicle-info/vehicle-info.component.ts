import { CommonModule } from '@angular/common';
import { Component, EventEmitter, inject, Input, OnInit, Output } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { Vehicle } from '../../../interfaces';
import { UserService } from '../../../../../core/services';
import { PERMISSIONS_CODE } from '../../../../../core/constants';
import { SimStatus } from '../../../enums';
import { environment } from '../../../../../../environments/environment';

@Component({
  selector: 'app-vehicle-info',
  standalone: true,
  imports: [CommonModule, TranslateModule, MatIconModule],
  templateUrl: './vehicle-info.component.html',
  styleUrls: ['./vehicle-info.component.scss'],
})
export class VehicleInfoComponent implements OnInit {
  @Input() vehicle: Vehicle;
  @Input() vin: string = '';
  @Input() isAction = false;

  @Output() viewDetailVehicle = new EventEmitter();
  @Output() createTicket = new EventEmitter();

  userService = inject(UserService);

   readonly PERMISSIONS_CODE = PERMISSIONS_CODE;
   
  simStatus = SimStatus;
  constructor() {}

  ngOnInit() {}

  getFullImageUrl(imagePath: string): string {
    if (!imagePath) return 'assets/images/missing_product_EN.png';
    return `${environment.OCC_BASE_URL}${imagePath}`;
  }
}
