import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  inject,
  Input,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatDialog } from '@angular/material/dialog';
import { filter } from 'rxjs';
import { LoadingService, NotificationService, UserService } from '../../../../../core/services';
import { Ticket } from '../../../interfaces';
import { TicketsDetailService } from '../../../services/tickets/ticket-detail.service';
import { PERMISSIONS_CODE } from '../../../../../core/constants';
import { ActionModal, DateFormat } from '../../../../../core/enums';
import { DialogConfirmComponent } from '../../../../../core/shared';
import { handleErrors } from '../../../../../core/helpers';
@Component({
  selector: 'app-verification',
  standalone: true,
  imports: [CommonModule, MatIconModule, TranslateModule],
  providers: [NotificationService],
  templateUrl: './verification.component.html',
  styleUrls: ['./verification.component.scss'],
})
export class VerificationComponent implements OnInit {
  @Input() ticket: Ticket | null = null;
  @Output() verificationSuccess = new EventEmitter<boolean>();

  verifiedInfo: any;
  dialog = inject(MatDialog);
  translateService = inject(TranslateService);
  ticketDetailsService = inject(TicketsDetailService);
  notificationService = inject(NotificationService);
  loadingService = inject(LoadingService);
  userService = inject(UserService);

  responseConfirmVerify: boolean;

  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;
  dateFormat = DateFormat;

  ngOnChanges(changes: SimpleChanges) {
    if (changes['ticket'] && changes['ticket'].currentValue) {
      this.updateVerifiedInfo();
    }
  }
  ngOnInit() {
    if (this.ticket) {
      this.updateVerifiedInfo();
    }
  }

  private updateVerifiedInfo() {
    this.verifiedInfo = this.ticket?.isVerified ? {
      isVerified: this.ticket?.isVerified,
      verifiedBy: this.ticket?.verifiedBy,
      verifiedDate: this.ticket?.verifiedDate,
    } : null;
  }

  onVerification() {
    const dialogRef = this.dialog.open(DialogConfirmComponent, {
      width: '530px',
      data: {
        title: this.translateService.instant('tickets.verificationTitlePopup'),
        icon: 'ic-warning',
        confirmMsg: this.translateService.instant(
          'tickets.verificationSubPopup'
        ),
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.confirm'),
      },
    });

    dialogRef
      .afterClosed()
      .pipe(filter((result: any) => result?.action === ActionModal.Submit))
      .subscribe((result) => {
        this.loadingService.showLoader();
        this.ticketDetailsService.verifyTicket(this.ticket?.ticketID).subscribe(
          (res) => {
            if (res) {
              this.notificationService.showSuccess(
                this.translateService.instant('tickets.verificationSuccess')
              );
              this.verificationSuccess.emit(true);
              this.verifiedInfo = res;
              this.responseConfirmVerify = res?.isVerified;
              this.loadingService.hideLoader();
            }
          },
          (error) => {
            this.verificationSuccess.emit(false);
            this.loadingService.hideLoader();
            handleErrors(error, this.notificationService);
          }
        );
      });
  }
}
