<div class="section-ticket-widget">
  <div class="section-header">
    <mat-icon
      svgIcon="ic-ticket"
      class="medium-icon section-header--icon"
    ></mat-icon>
    <div class="section-header--content">
      <span>{{ "vehicle.ticket.title" | translate }}</span>
    </div>
    <div *ngIf="userService.isHasPermission([PERMISSIONS_CODE.ECARE_TICKET_CREATE])">
      <button
        class="btn-link section-verification__confirm"
        (click)="createTicket()"
      >
        <mat-icon
          svgIcon="ic-add-red"
          class="small-icon"
          aria-hidden="true"
        ></mat-icon>
        {{ "vehicle.ticket.createTicket" | translate }}
      </button>
    </div>
  </div>

  <div class="section-ticket-widget__body">
    <ng-container *ngIf="data && data.length; else noData">
      <table class="section-ticket-widget__box">
        <tbody>
          @for (item of data; track $index) {
          <tr>
            <td>
              <div
                class="section-ticket-widget__box__content"
                (click)="viewTicketDetail(item?.id)"
              >
                <span>{{ item?.id }} · {{ item?.type | getTypeTicket }}</span>
                <span>{{ item?.title }}</span>
              </div>
            </td>
            <td>
              <div
                class="value-tag"
                [class.red-tag]="item?.priority?.code === priorityTicket.High"
                [class.yellow-tag]="item?.priority?.code === priorityTicket.Mid"
              >
                <span>{{ item?.priority?.name }}</span>
              </div>
            </td>
            <td>
              <div
                class="value-tag"
                [ngClass]="
                  item?.status?.code === statusTicket.InProgress
                    ? 'yellow-tag'
                    : ''
                "
              >
                <span>{{ item?.status?.name }}</span>
              </div>
            </td>
          </tr>
          }
        </tbody>
      </table>
    </ng-container>
    <ng-template #noData>
      <div class="section-no-data">
        {{ "vehicle.ticket.noData" | translate }}
      </div>
    </ng-template>
  </div>
</div>
