.vehicle-info {
  &__stat-icon {
    column-gap: 15px;
    mat-icon {
      width: 40px !important;
      height: 40px !important;
    }
  }
  &__title {
    color: #101010;
    font-size: 22px;
    margin-bottom: 8px;
    font-weight: 600;
  }

  &__sub-details {
    font-size: 14px;
    font-weight: 400;

    .dot {
      font-weight: 600;
      margin: 0 10px;
    }
  }

  &__image {
    margin: 0 auto 10px;
    display: flex;
    justify-content: center;

    img {
      width: 100%;
      max-width: 300px;
      height: auto;
    }
  }

  &__status {
    display: flex;
    justify-content: center;
    margin: 0 auto 15px;
    &-disconnected {
      &.vehicle-info__status-text {
          color: #808080;
          background: #f5efef;
          border: 2px solid #808080;
      }
    }
    &-text {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      background: #e8f5e9;
      color: #4caf50;
      border: 2px solid #2aba6c;
      border-radius: 29px;
      padding: 8px 25px 10px;
      font-size: 16px;
      font-weight: 600;

      mat-icon {
        margin: 5px 0 0 5px;
      }
    }
  }

  &__details {
    margin-bottom: 20px;

    &__title {
      font-size: 16px;
      font-weight: bold;
      margin: 5px 0;
    }

    &__sub-details {
      font-size: 14px;
      color: #666;
    }
  }

  &__stats {
    display: flex;
    flex-wrap: wrap;
    gap: 33px;
    justify-content: space-between;
    margin-top: 20px;

    @media screen and (min-width: 1280px) {
      gap: 15px;
    }
  }

  &__stat {
    display: flex;
    column-gap: 10px;
    align-items: center;
    &-icon {
      display: flex;
      justify-content: center;
      align-items: center;

      &--green {
        mat-icon {
          color: #4caf50;
        }
      }

      &--red {
        mat-icon {
          color: #f44336;
        }
      }
    }

    &-text {
      display: flex;
      flex-direction: column;

      p {
        font-size: 16px;
        font-weight: 600;
        margin: 0;
        &.disconnect {
          color: #808080;
        }

        &.green {
          color: #4caf50;
        }

        &.red {
          color: #f44336;
        }
      }

      small {
        font-size: 12px;
        font-weight: 400;
        color: #101010;
        &.disconnect {
          color: #808080;
        }
      }
    }
  }

  &__action {
    display: flex;
    gap: 15px;
    justify-content: end;
    padding-bottom: 10px;

    button {
      height: 40px;
      text-transform: uppercase;
      padding: 11px 20px;

      &.btn-create-ticket {
        background-color: #3a3a3a;
        color: #fff;

        &:hover {
          background-color: #101010;
        }
      }
    }
  }
}
