<div class="icon-header">
  <mat-icon svgIcon="ic-sub-widget" class="icon-title"></mat-icon>
</div>

<h2 class="title-dialog">{{ "subscriptionTab.subscription" | translate }}</h2>

<div class="container-dialog">
  <div class="container-dialog__feature">
    <mat-icon svgIcon="ic-info"></mat-icon>
    {{ "subscriptionTab.packageFeatures" | translate }}
  </div>

  <ul class="container-dialog__list">
    @for (item of data?.features; track $index) {
    <li>{{ item?.name }}</li>
    }
  </ul>
</div>

<div class="action-dialog">
  <button class="btn-quaternary" (click)="onCancel()">
    {{ "common.close" | translate }}
  </button>
</div>
