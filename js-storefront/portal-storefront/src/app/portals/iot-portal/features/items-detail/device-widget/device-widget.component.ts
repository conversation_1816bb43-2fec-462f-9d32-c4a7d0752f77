import { CommonModule } from '@angular/common';
import { Component, inject, Input, OnInit } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { AgGridAngular } from 'ag-grid-angular';
import { ColDef } from 'ag-grid-community';
import { ItemDevice } from '../../../interfaces';

@Component({
  selector: 'app-device-widget',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    TranslateModule,
    AgGridAngular
  ],
  templateUrl: './device-widget.component.html',
  styleUrls: ['./device-widget.component.scss']
})
export class DeviceWidgetComponent implements OnInit {
  @Input() rowData: ItemDevice[];
  defaultColDef: ColDef<any> = {
    resizable: false,
    valueFormatter: (params) => (params.value ? params.value : '-'),
    sortable: false,
    menuTabs: [],
    suppressMovable: true,
  };
  colDefs: ColDef<any>[];
  rowHeight = 50;
  
  private translateService = inject(TranslateService);

  ngOnInit() {
    this.colDefs = [
      { 
        headerName: this.translateService.instant('deviceManagement.deviceID'),
        headerValueGetter: () => this.translateService.instant('deviceManagement.deviceID'),
        field: 'deviceId',
        flex: 1,
      },
      {
        headerName: this.translateService.instant('deviceManagement.deviceStatus'),
        headerValueGetter: () => this.translateService.instant('deviceManagement.deviceStatus'),
        field: 'deviceStatus',
        cellRenderer: (params) => {
          return params?.value && params?.value?.name ? params?.value?.name : 'N/A';
        },
        flex: 1,
      },
      {
        headerName: this.translateService.instant('deviceManagement.activationStatus'),
        headerValueGetter: () => this.translateService.instant('deviceManagement.activationStatus'),
        field: 'activationStatus',
        cellRenderer: (params) => {
          return params?.value && params?.value?.name ? params?.value?.name : 'N/A';
        },
        flex: 1,
      },
      {
        headerName: this.translateService.instant('deviceManagement.simStatus'),
        headerValueGetter: () => this.translateService.instant('deviceManagement.simStatus'),
        field: 'simStatus',
        cellRenderer: (params) => {
          return params?.value && params?.value?.name ? params?.value?.name : 'N/A';
        },
        flex: 1,
      }
    ];
  }
}
