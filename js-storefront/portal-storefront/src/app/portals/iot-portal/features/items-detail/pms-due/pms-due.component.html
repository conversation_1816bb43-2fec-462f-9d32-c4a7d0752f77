<div class="section-pms">
  <div class="section-header">
    <mat-icon svgIcon="ic-pms" class="medium-icon section-header--icon"></mat-icon>
    <div class="section-header--content">
      <span>{{ "vehicle.pmsDue.title" | translate }}</span>
    </div>
  </div>
  <div class="section-pms__content">
    <ng-container *ngIf="pmsData; else noData">
      <div class="section-pms__content--way">{{ 'vehicle.pmsDue.theVehicleIsDueFor' | translate }} <span>{{pmsData?.nextPmMileage | number}}{{'common.km' | translate}} {{'common.checkUp' | translate}}</span></div>
      <!-- <div class="section-pms__content--item">
        <mat-icon svgIcon="ic-calendar" class="small-icon"></mat-icon>
        <span>{{ 'vehicle.pmsDue.dueDate' | translate }}: {{pmsData?.freePMS?.dueDate}}</span>
      </div>
      <div class="section-pms__content--item">
        <mat-icon svgIcon="ic-pms" class="small-icon"></mat-icon>
        <span>{{ 'vehicle.pmsDue.pmsType' | translate }}: {{pmsData?.freePMS?.pmsType}}</span>
      </div> -->
    </ng-container>
    <ng-template #noData>
      <div class="section-no-data">{{ 'common.noService' | translate }}</div>
    </ng-template>
  </div>
</div>