import { Component, inject } from '@angular/core';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';

import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';
import { VehicleService } from '../../../../services';
import { LoadingService, NotificationService } from '../../../../../../core/services';
import { FeatureSubscription } from '../../../../interfaces';

@Component({
  selector: 'app-package-features',
  standalone: true,
  imports: [CommonModule, MatDialogModule, MatIconModule, TranslateModule],
  templateUrl: './package-features.component.html',
  styleUrl: './package-features.component.scss',
  providers: [VehicleService, NotificationService],
})
export class PackageFeaturesComponent {
  data: {
    features: FeatureSubscription[]
  } = inject(MAT_DIALOG_DATA);
  dialogRef = inject(MatDialogRef<PackageFeaturesComponent>);
  loadingService = inject(LoadingService);
  vehicleService = inject(VehicleService);
  notificationService = inject(NotificationService);

  subscription = new Subscription();


  onCancel() {
    this.dialogRef.close();
  }
}
