@import "../../../../../../styles/abstracts/variables";
.section-warning {
  display: flex;
  flex-direction: column;
  gap: 20px;
  &__header {
    display: flex;
    align-items: center;
    gap: 12px;
    span {
      font-size: 22px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
      color: #101010;
    }
  }
  &__list {
    width: 100%;
  }
  &__box {
    width: 100%;
    &__content {
      display: flex;
      align-items: flex-start;
      gap: 5px;
      width: 100%;
      div {
        span {
          &:first-child {
            cursor: pointer;
            font-weight: 600;
            
            &:hover {
              text-decoration: underline;
            }
          }
        }
      }
      span {
        font-size: 14px;
        font-style: normal;
        color: #101010;
        font-weight: 400;
      }
      &--icon {
        flex-shrink: 0;
        height: 20px;
        width: 20px;
        img {
          height: 20px;
          width: 20px;
        }
      }
      &--value {
        display: inline-flex;
        flex-direction: column;
        gap: 5px;
        margin-bottom: 5px;
      }
    }
    tbody {
      tr {
        td {
          border-bottom: 1px solid var(--Primary-Light-Grey, #EEE);
        }
        &:first-child {
          td {
            &:first-child {
              width: 100%;
              padding: 0 5px 10px 0;
            }
            &:not(:first-child) {
              padding: 0 5px;
              white-space: nowrap;
            }
          }
        }
        &:not(:first-child) {
          td {
            &:first-child {
              width: 100%;
              padding: 10px 5px 10px 0;
            }
            &:not(:first-child) {
              padding: 0 5px;
              white-space: nowrap;
            }
          }
        }
      }
    }

    .value-tag {
      span {
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 14px;
        letter-spacing: -0.42px;
        padding: 8px;
        color: #3a3a3a;
        background-color: #eeeeee;
        display: inline-block;
      }
    }
  }
}

.section-content {
  &__section-name {
    font-size: 17px;
    font-weight: 600;
    padding: 12px 15px;
    width: 100%;
    background-color: #eee;
    margin-bottom: 15px;
  }

  &__properties {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    padding: 0 15px 40px;
    gap: 30px;

    &__item {
      display: flex;
      flex-direction: column;
      gap: 8px;
      font-size: 16px;

      &.flex-space-between {
        justify-content: space-between;
      }
      
      &__name {
        font-weight: 600;
      }

        ::ng-deep {
          mat-radio-group {
            display: flex;
            flex-direction: row;
            gap: 35px;
          }
        }
    }
  }
}