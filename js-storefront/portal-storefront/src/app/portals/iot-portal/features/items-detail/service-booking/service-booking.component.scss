@import "../../../../../../styles/abstracts/variables";

:host {
  .has-data {
    border-bottom: 4px solid #2aba6c;
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
  }

  .service-booking {
    display: block;

    .service-booking-title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      > * {
        display: flex;
        align-items: center;
        font-weight: $fw600;
      }

      &__title {
        gap: 12px;
        font-size: $fs22;

        mat-icon {
          width: 32px;
          height: 32px;
        }
      }

      &__book {
        gap: 5px;
        color: $text-color-5;
        cursor: pointer;
      }
    }

    .service-booking-content {
      margin-top: 20px;
      display: flex;
      flex-direction: column;
      gap: 10px;

      .booking-item {
        display: flex;
        gap: 4px;
        flex-direction: column;
        border-bottom: 1px solid $border-color-4;
        padding-bottom: 10px;
        cursor: pointer;

        &:hover {
          text-decoration: underline;
        }

        &__id {
          :nth-child(1) {
            font-size: $fs16;
            font-weight: $fw600;
          }
        }

        &__description {
          font-weight: $fw600;
        }
      }

      .no-service-booking {
        font-size: $fs20;
        font-weight: $fw600;
        color: $text-color-4;
        text-align: center;
        margin-top: 10px;
      }
    }
  }
}
