<div class="section-loan-widget">
  <div class="section-header" [class.section-header-margin-bottom]="rowData && rowData.length > 0">
    <mat-icon
      svgIcon="ic-loan-widget"
      class="medium-icon section-header--icon"
    ></mat-icon>
    <div class="section-header--content">
      <span>{{ "vehicle.loanWidget.title" | translate }}</span>
    </div>
    <button
      class="btn-link"
      (click)="enableLoan(false)"
      *ngIf="
        rowData &&
        rowData.length <= 0 &&
        userService.isHasPermission([PERMISSIONS_CODE.IOT_LOAN_MANAGE])
      "
    >
      <mat-icon
        svgIcon="ic-enable-loan"
        class="small-icon"
        aria-hidden="true"
      ></mat-icon>
      {{ "common.enable" | translate }}
    </button>
  </div>
  <ng-container *ngIf="rowData && rowData.length > 0">
    <div class="section-loan-widget__content widget-table">
      <ag-grid-angular
        class="ag-theme-quartz custom-grid"
        [rowData]="rowData"
        [columnDefs]="colDefs"
        [domLayout]="'autoHeight'"
        [defaultColDef]="defaultColDef"
        [rowHeight]="rowHeight"
      />
      <!-- <ng-template #noData>
        <div class="no-data">{{ "callLog.noData" | translate }}</div>
      </ng-template> -->
    </div>
  </ng-container>
</div>
