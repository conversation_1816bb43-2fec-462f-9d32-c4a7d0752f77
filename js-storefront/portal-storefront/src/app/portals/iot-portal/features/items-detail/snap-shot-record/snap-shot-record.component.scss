.section-snap-shot-record {
    display: flex;
    flex-direction: column;
    gap: 20px;
    &__body {
        display: flex;
        flex-direction: column;
        gap: 10px;

        &--item {
            display: flex;
            gap: 10px;
            align-items: center;
            border-bottom: 1px solid var(--Primary-Light-Grey, #EEE);
            padding-bottom: 10px;
            &__content {
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
                display: flex;
                flex-direction: column;
                gap: 5px;
                margin-right: auto;
                span {
                    color: var(--Primary-Black, #101010);
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: 20px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    &:last-child {
                        overflow: hidden;
                        text-overflow: ellipsis;
                        font-weight: 400;
                    }
                }
            }
        }
    }
}