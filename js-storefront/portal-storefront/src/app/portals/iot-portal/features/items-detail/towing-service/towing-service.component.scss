.towing-service {
  display: flex;
  flex-direction: column;
  gap: 30px;

  &__header {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 12px;
    flex: 0 1 0;

    span {
      font-size: 22px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
      color: #101010;
      margin-right: auto;
    }

    &__action {
      display: flex;
      align-items: center;
      gap: 15px;

      button {
        padding: 0;
      }
    }
  }

  &__body {
    &--empty {
      text-align: center;
      font-size: 20px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px;
      color: #808080;
    }

    &__confirm {
      display: flex;
      flex-direction: column;
      gap: 7px;

      &--box {
        display: flex;
        flex-wrap: wrap;
        padding: 20px;
        align-items: center;
        gap: 15px;
        align-self: stretch;
        flex: 1 0;
        background: var(--illustration-blue-5, rgba(38, 110, 242, 0.05));
        width: 100%;

        &__content {
          display: flex;
          flex-direction: column;
          gap: 5px;
          margin-right: auto;

          span {
            &:first-child {
              color: var(--Primary-Dark-Grey, #3A3A3A);
              font-size: 14px;
              font-style: normal;
              font-weight: 600;
              line-height: 20px;
            }

            &:last-child {
              color: var(--Illustration-Blue, #266EF2);
              font-size: 20px;
              font-style: normal;
              font-weight: 600;
              line-height: 26px;
            }
          }
        }

        &__date {
          color: var(--Primary-Mid-Grey, #808080);
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 20px;
        }
      }
    }
  }

  .edit-towing-show-more {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-top: 30px;

    button {
      color: var(--Primary-Black, #101010);
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 20px;
    }

    mat-icon {
      transition: transform 0.3s ease;
    }

    .rotate {
      transform: rotate(180deg);
    }
  }

  .show-more-content {
    display: flex;
    flex-direction: column;
    gap: 30px;
    margin-top: 30px;
    &.hidden {
      display: none;
    }

    .show-more-item {
      display: flex;
      flex-direction: column;
      gap: 10px;

      h2 {
        padding-bottom: 5px;
        color: var(--Primary-Black, #101010);
        font-size: 18px;
        font-style: normal;
        font-weight: 600;
        line-height: 24px;
        margin: 0;
      }

      &__icon {
        display: flex;
        flex-direction: column;
        gap: 10px;

        div {
          display: flex;
          align-items: center;
          gap: 10px;
          font-size: 14px;
          font-weight: 400;
          margin: 0;
          mat-icon {
            flex-shrink: 0;
          }
        }
      }
    }
  }

  .towing-cancel-button {
    color: #101010;
  }
}