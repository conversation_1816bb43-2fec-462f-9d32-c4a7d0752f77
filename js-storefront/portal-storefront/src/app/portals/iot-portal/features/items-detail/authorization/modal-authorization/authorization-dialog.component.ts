import { Component, OnInit, inject } from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { RadioButtonComponent } from '../../../../../../core/shared';
import { TicketsDetailService } from '../../../../services/tickets/ticket-detail.service';
import { LoadingService, NotificationService } from '../../../../../../core/services';
@Component({
  selector: 'app-authorization-dialog',
  templateUrl: './authorization-dialog.component.html',
  styleUrls: ['./authorization-dialog.component.scss'],
  standalone: true,
  imports: [ReactiveFormsModule, RadioButtonComponent, TranslateModule],
  providers: [TicketsDetailService, NotificationService],
})
export class AuthorizationDialogComponent implements OnInit {
  dialogRef = inject(MatDialogRef<AuthorizationDialogComponent>);
  fb = inject(FormBuilder);
  translateService = inject(TranslateService);
  ticketsService = inject(TicketsDetailService);
  notificationService = inject(NotificationService);
  loadingService = inject(LoadingService);

  authorizationTicketForm = new FormGroup({
    method: new FormControl('token', Validators.required),
  });
  radioOptions = [
    { code: 'token', name: 'Via digital token' },
    { code: 'email', name: 'Via email' },
  ];

  ticketId: string;

  constructor() {
    const data = inject(MAT_DIALOG_DATA);
    this.ticketId = data.ticketId;
  }

  ngOnInit(): void {}

  onCancel(): void {
    this.dialogRef.close();
  }

  onSubmit(): void {
    if (this.authorizationTicketForm.valid) {
      const selectedMethod = this.authorizationTicketForm.value.method;
      const apiMethod = selectedMethod === 'token' ? 'DIGITAL_TOKEN' : 'EMAIL';

      this.loadingService.showLoader();
      this.ticketsService.generateToken(this.ticketId, apiMethod).subscribe({
        next: () => {
          this.notificationService.showInfo(
            this.translateService.instant('tickets.tokenGeneratedSuccess', {
              method:
                selectedMethod === 'token'
                  ? this.translateService.instant('tickets.viaDigitalToken')
                  : this.translateService.instant('tickets.viaEmail'),
            })
          );
          this.loadingService.hideLoader();
          this.dialogRef.close({ action: 'GENERATED', method: apiMethod });
        },
        error: (error) => {
          this.loadingService.hideLoader();
          this.notificationService.showError(
            this.translateService.instant('tickets.tokenGenerationError')
          );
          console.error('Error generating token:', error);
        },
      });
    }
  }
}
