import { CommonModule } from '@angular/common';
import { Component, inject, Input, OnInit } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { AgGridAngular } from 'ag-grid-angular';
import { ColDef } from 'ag-grid-community';
import { MatDialog } from '@angular/material/dialog';
import { PackageFeaturesComponent } from './package-features/package-features.component';
import { ItemSubscription } from '../../../interfaces';

@Component({
  selector: 'app-subscription-widget',
  standalone: true,
  imports: [CommonModule, MatIconModule, TranslateModule, AgGridAngular],
  templateUrl: './subscription-widget.component.html',
  styleUrls: ['./subscription-widget.component.scss'],
})
export class SubscriptionWidgetComponent implements OnInit {
  @Input() rowData: ItemSubscription[];
  private translateService = inject(TranslateService);
  private dialog = inject(MatDialog);

  defaultColDef: ColDef<any> = {
    resizable: false,
    valueFormatter: (params) => (params.value ? params.value : '-'),
    sortable: false,
    menuTabs: [],
    suppressMovable: true,
  };

  colDefs: ColDef<any>[] = [
    {
      headerName: this.translateService.instant('vehicle.subWidget.package'),
      headerValueGetter: () => this.translateService.instant('vehicle.subWidget.package'),
      field: 'package',
      flex: 1,
      wrapText: true,
      autoHeight: true,
      cellClass: 'cell-word-wrap can-click',
      cellRenderer: (params) => {
        return params?.value
          ? `<p>${params?.value?.packageType}</p><p>${
              params?.value?.isFreeSubscription
                ? this.translateService.instant(
                    'vehicle.subWidget.freePackage'
                  )
                : ''
            }</p>`
          : '-';
      },
      onCellClicked: (event) => this.subscriptionDetail(event?.data),
    },
    {
      headerName: this.translateService.instant('vehicle.subWidget.period'),
      headerValueGetter: () => this.translateService.instant('vehicle.subWidget.period'),
      field: 'period',
      wrapText: true,
      autoHeight: true,
      cellClass: 'cell-word-wrap',
      cellRenderer: (params) => {
        return params?.value
          ? `<p>${params?.value?.startDate || '-'}</p><p>${
              params?.value?.endDate || '-'
            }</p>`
          : '-';
      },
      flex: 1,
    },
    {
      headerName: this.translateService.instant('vehicle.subWidget.type'),
      headerValueGetter: () => this.translateService.instant('vehicle.subWidget.type'),
      field: 'type',
      wrapText: true,
      autoHeight: true,
      cellRenderer: (params) => {
        return params?.value && params?.value?.name
          ? params?.value?.name
          : '-';
      },
      flex: 1,
    },
    {
      headerName: this.translateService.instant(
        'vehicle.subWidget.paymentStatus'
      ),
      headerValueGetter: () => this.translateService.instant('vehicle.subWidget.paymentStatus'),
      field: 'subscriptionPaymentStatus',
      cellRenderer: (params) => {
        return params?.value ? `<span>${params?.value?.name}</span>` : '';
      },
      cellClassRules: {
        'value-tag': (params) => !!params.value,
      },
      flex: 1,
      wrapText: true,
      autoHeight: true,
    },
    {
      headerName: this.translateService.instant('vehicle.subWidget.paidBy'),
      headerValueGetter: () => this.translateService.instant('vehicle.subWidget.paidBy'),
      field: 'paidBy',
      cellRenderer: (params) => {
        return params?.value || '-';
      },
      flex: 1,
      wrapText: true,
      autoHeight: true,
    },
    {
      headerName: this.translateService.instant('vehicle.subWidget.status'),
      headerValueGetter: () => this.translateService.instant('vehicle.subWidget.status'),
      field: 'subscriptionStatus',
      cellRenderer: (params) => {
        return params?.value ? `<span>${params?.value?.name}</span>` : '';
      },
      cellClassRules: {
        'value-tag': (params) => !!params.value,
      },
      flex: 1,
      wrapText: true,
      autoHeight: true,
    },
  ];;
  rowHeight = 50;

  ngOnInit() {

  }

  subscriptionDetail(data: ItemSubscription): void {
    const dialogRef = this.dialog.open(PackageFeaturesComponent, {
      width: '650px',
      maxHeight: '90vh',
      data: {
        features: data?.features?.filter(item => item?.enable)
      },
    });

    dialogRef.afterClosed().subscribe();
  }
}
