<div class="current-location">
  @if(!onlyMap) {
    <div class="current-location__header">
      <mat-icon class="medium-icon" svgIcon="ic-location"></mat-icon>
      <span>{{ "tickets.currentLocation" | translate }}</span>
    </div>
  }
  
  <div [id]="mapId" class="map-content"></div>
  @if(!onlyMap) {
  <div class="current-location__address-box">
    <div class="current-location__address">
      <div class="current-location__address--content">
        <span>{{ vehicle?.currentAddress }}</span>
        <span>{{ vehicle?.dateAndTime | date : dateFormat.FullDate }}</span>
      </div>
      @if (vehicle?.sim?.code === simStatus.ActiveSubscription ||
      vehicle?.sim?.code === simStatus.ActiveTesting || !setHideRefresh) {
      <div class="current-location__address--action">
        <button
          class="btn-link device-info-section__refesh"
          (click)="onRefresh()"
        >
          <mat-icon
            svgIcon="ic-refresh"
            class="small-icon"
            aria-hidden="true"
          ></mat-icon>
          {{ "deviceManagement.refresh" | translate }}
        </button>
      </div>
      }
    </div>
  </div>
  }
</div>
