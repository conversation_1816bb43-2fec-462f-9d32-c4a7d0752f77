import { CommonModule } from '@angular/common';
import { Component, inject, Input, OnInit } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { FormsModule } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';

import { filter, interval, Subscription, switchMap, takeWhile } from 'rxjs';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { IconModule } from '../../../../../core/icon/icon.module';
import { StolenVehicleTrackingService, TicketsDataService } from '../../../services';
import { LoadingService, NotificationService, UserService } from '../../../../../core/services';
import { ApprovalStatus, ResponseRequestInfo, Ticket } from '../../../interfaces';
import { PERMISSIONS_CODE } from '../../../../../core/constants';
import { AuthorizationStatus, ManagerApprovalAction, ManagerApprovalStatus, SVTStatus } from '../../../enums';
import { handleErrors } from '../../../../../core/helpers';
import { DialogConfirmComponent } from '../../../../../core/shared';
import { ActionModal } from '../../../../../core/enums';


@Component({
  selector: 'app-stolen-vehicle-tracking',
  standalone: true,
  imports: [
    CommonModule,
    IconModule,
    TranslateModule,
    FormsModule,
    MatSlideToggleModule,
    MatProgressSpinnerModule,
  ],
  templateUrl: './stolen-vehicle-tracking.component.html',
  styleUrl: './stolen-vehicle-tracking.component.scss',
  providers: [StolenVehicleTrackingService, NotificationService],
})
export class StolenVehicleTrackingComponent implements OnInit {
  @Input() set ticket(value: Ticket) {
    if (value) {
      this.vin = value.vin;
      this.disabledSVT = !value?.authorization;
    }
  }
  @Input() checkApprovalStatus: ApprovalStatus | null = null;
  dialog = inject(MatDialog);
  translateService = inject(TranslateService);
  loadingService = inject(LoadingService);
  notificationService = inject(NotificationService);
  stolenVehicleTrackingService = inject(StolenVehicleTrackingService);
  ticketsDataService = inject(TicketsDataService);
  userService = inject(UserService);

  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;
  readonly ManagerApprovalStatus = ManagerApprovalStatus;
  readonly AuthorizationStatus = AuthorizationStatus;

  subscription = new Subscription();

  svtToggle = false;
  svtStatus: SVTStatus;
  pollingSubscription: Subscription;
  isLoading = false;
  disabledSVT = false;
  managerApproval: ResponseRequestInfo;

  vin: string;
  ngOnInit(): void {
    this.getSVTStatus(true);
    this.subscription.add(
      this.ticketsDataService.actionApproveRequest$.subscribe((action) => {
        if (
          action === ManagerApprovalAction.Enable &&
          this.svtToggle === false
        ) {
          this.svtToggle = true;
          this.updateSVT(true);
        } else if (action === ManagerApprovalAction.Disable && this.svtToggle) {
          this.svtToggle = false;
          this.updateSVT(false);
        }
      })
    );
    this.subscription.add(
      this.ticketsDataService.managerApprovalStatus$.subscribe(
        (managerApproval) => {
          this.managerApproval = managerApproval;
        }
      )
    );
  }
  ngOnDestroy(): void {
    this.pollingSubscription?.unsubscribe();
    this.subscription?.unsubscribe();
  }
  getSVTStatus(isFirstLoad = false, isEnabled = false): void {
    this.loadingService.showLoader();
    this.stolenVehicleTrackingService.getSVTStatus(this.vin).subscribe(
      (response) => {
        this.loadingService.hideLoader();
        this.svtStatus = response?.svtStatus;
        this.svtToggle =
          this.svtStatus === SVTStatus.Activating ||
          this.svtStatus === SVTStatus.Active;
        if (
          this.svtStatus === SVTStatus.Activating ||
          this.svtStatus === SVTStatus.Deactivating
        ) {
          this.startPolling(isFirstLoad, isEnabled);
        } else {
          this.stopPolling(isFirstLoad, isEnabled);
        }
      },
      (error) => {
        this.loadingService.hideLoader();
        handleErrors(error, this.notificationService);
      }
    );
  }
  startPolling(isFirstLoad = false, isEnabled = false): void {
    this.isLoading = true;
    this.disabledSVT = true;
    if (!this.pollingSubscription || this.pollingSubscription.closed) {
      this.pollingSubscription = interval(5000)
        .pipe(
          switchMap(() =>
            this.stolenVehicleTrackingService.getSVTStatus(this.vin)
          ),
          takeWhile(
            (response) =>
              response?.svtStatus === SVTStatus.Activating ||
              response?.svtStatus === SVTStatus.Deactivating,
            true
          )
        )
        .subscribe(
          (response) => {
            this.svtStatus = response?.svtStatus;
            if (
              this.svtStatus !== SVTStatus.Activating &&
              this.svtStatus !== SVTStatus.Deactivating
            ) {
              this.stopPolling(isFirstLoad, isEnabled);
            }
          },
          (error) => {
            handleErrors(error, this.notificationService);
            this.stopPolling(isFirstLoad, isEnabled);
          }
        );
    }
  }
  stopPolling(isFirstLoad = false, isEnabled = false): void {
    this.isLoading = false;
    this.disabledSVT = false;
    if (this.pollingSubscription) {
      this.pollingSubscription.unsubscribe();
    }
    this.svtToggle = this.svtStatus === SVTStatus.Active;
    if (this.svtStatus === SVTStatus.Active && !isFirstLoad) {
      this.notificationService.showNotification(
        this.translateService.instant(
          isEnabled ? 'svt.enableSVTSuccess' : 'svt.disableSVTFail'
        ),
        isEnabled ? 'success' : 'error'
      );
    } else if (this.svtStatus === SVTStatus.Inactive && !isFirstLoad) {
      this.notificationService.showSuccess(
        this.translateService.instant('svt.disableSVTSuccess')
      );
      this.notificationService.showNotification(
        this.translateService.instant(
          isEnabled ? 'svt.enableSVTFail' : 'svt.disableSVTSuccess'
        ),
        isEnabled ? 'error' : 'success'
      );
    }
  }
  changeSvtToggle(event): void {
    const { checked } = event;
    const dialogRef = this.dialog.open(DialogConfirmComponent, {
      width: '530px',
      autoFocus: false,
      data: {
        title: this.translateService.instant('svt.stolenVehicleTracking'),
        icon: 'ic-svt-red',
        confirmMsg: this.translateService.instant(
          checked ? 'svt.confirmEnableSVT' : 'svt.confirmDisableSVT'
        ),
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.confirm'),
      },
    });
    dialogRef.afterClosed().subscribe((result) => {
      if (result?.action === ActionModal.Submit) {
        this.updateSVT(checked);
      } else {
        this.svtToggle = this.svtStatus === SVTStatus.Active;
      }
    });
  }
 
  updateSVT(checked: boolean): void {
    // 1: enable, 0: disable
    const isSVT = this.svtToggle ? 1 : 0;
    this.isLoading = true;
    this.stolenVehicleTrackingService.updateSVT(this.vin, isSVT).subscribe(
      (response) => {
        if (
          response?.result?.toLowerCase() === 'success' ||
          response?.result?.toLowerCase() === 'ok'
        ) {
          this.notificationService.showInfo(
            this.translateService.instant(
              checked ? 'svt.enableSVTInProgress' : 'svt.disableSVTInProgress'
            )
          );
          this.getSVTStatus(false, this.svtToggle);
        } else {
          this.svtToggle =
            this.svtStatus === SVTStatus.Active ||
            this.svtStatus === SVTStatus.Activating;
          this.isLoading = false;
          this.notificationService.showError(
            this.translateService.instant(
              checked ? 'svt.enableSVTFail' : 'svt.disableSVTFail'
            )
          );
        }
      },
      (error) => {
        this.svtToggle =
          this.svtStatus === SVTStatus.Active ||
          this.svtStatus === SVTStatus.Activating;
        this.isLoading = false;
        this.notificationService.showError(
          this.translateService.instant(
            checked ? 'svt.enableSVTFail' : 'svt.disableSVTFail'
          )
        );
      }
    );
  }
}
