<div class="section-warning">
  <div class="section-warning__header">
    <mat-icon class="medium-icon" svgIcon="ic-warning"></mat-icon>
    <span>{{
      onlyOneItem
        ? ("tickets.warning" | translate)
        : ("tickets.warnings" | translate)
    }}</span>
  </div>
  <div class="section-warning__list">
    <ng-container *ngIf="data && data.length; else noData">
      <table class="section-warning__box">
        <tbody>
          @for (item of data; track $index) {
          <tr>
            <td>
              <div class="section-warning__box__content">
                <div class="section-warning__box__content--icon">
                  @if (item?.icon) {
                    <img [src]="getFullImageUrl(item?.icon)"
                    [alt]="item?.name || ''" />
                  }
                </div>
                <div class="section-warning__box__content--value">
                  <span (click)="onWarningDetails(onlyOneItem? item?.code : item.id)">{{ item?.name }}</span>
                  <span>{{ item?.timestamp }}</span>
                </div>
              </div>
            </td>
            <td>
              <div
                class="value-tag"
                [class.red-tag]="
                  item?.priority?.code === 'HIGH' ||
                  item?.priority?.code === 'CRITICAL'
                "
                [class.yellow-tag]="item?.priority?.code === 'MEDIUM'"
              >
                <span>{{
                  "vehicle.warningTab.priority." + item?.priority?.code
                    | translate
                }}</span>
              </div>
            </td>
            <td>
              <div class="value-tag">
                <span>{{
                  "vehicle.warningTab.status." + item?.status?.code | translate
                }}</span>
              </div>
            </td>
          </tr>
          }
        </tbody>
      </table>
    </ng-container>
    <ng-template #noData>
      <div class="section-no-data">
        {{ "tickets.noDataWarning" | translate }}
      </div>
    </ng-template>
  </div>
</div>

@if(onlyOneItem && data.length > 0) {
  <div class="section-content">
    <!-- Reference information -->
    <div class="section-content__section-name">
      {{ "warningMasterData.details.referenceInformation" | translate }}
    </div>

    <div class="section-content__properties">
      <div class="section-content__properties__item">
        <span class="section-content__properties__item__name">
          {{ "warningMasterData.details.requestedCustomerActions" | translate }}
        </span>
        <span> {{ data?.[0]?.customerAction ||  "-" }}</span>
      </div>

      <div class="section-content__properties__item">
        <span class="section-content__properties__item__name">
          {{ "warningMasterData.details.priority" | translate }}
        </span>
        <span> {{ data?.[0]?.priority?.name || "-" }}</span>
      </div>

      <div class="section-content__properties__item">
        <div class="form-group">
          <label class="form-group__label"
            >{{ 'warningMasterData.details.vehicleCondition' | translate }}
          </label>
          <mat-radio-group
            class="custom-radio-group"
            [disabled]="true"
            [value]="data?.[0]?.vehicleCondition"
          >
            @for (item of vehicleConditionOptions; track $index) {
            <mat-radio-button [value]="item.code">
              {{ item.name | translate }}
            </mat-radio-button>
            }
          </mat-radio-group>
        </div>
      </div>
    </div>

    <!-- Notification prompt -->
    <div class="section-content__section-name">
      {{ "warningMasterData.details.notificationPrompt" | translate }}
    </div>

    <div class="section-content__properties">
      <div class="section-content__properties__item">
        <span class="section-content__properties__item__name">
          {{ "warningMasterData.details.application1" | translate }}
        </span>
        <span> {{ data?.[0]?.notificationPromptTitle || "-" }}</span>
      </div>

      <div class="section-content__properties__item">
        <span class="section-content__properties__item__name">
          {{ "warningMasterData.details.application2" | translate }}
        </span>
        <span> {{ data?.[0]?.notificationPromptDesc || "-" }}</span>
      </div>

      <div class="section-content__properties__item">
        <span class="section-content__properties__item__name">
          {{ "warningMasterData.details.application3" | translate }}
        </span>
        <span> {{ data?.[0]?.notificationPromptCustAction || "-" }}</span>
      </div>
    </div>

  </div>
}
