import { Component, inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { Subscription } from 'rxjs';
import { CommonModule } from '@angular/common';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { IconModule } from '../../../../../../core/icon/icon.module';
import { RadioButtonComponent } from '../../../../../../core/shared';
import { ServiceBookingService, ServiceBookingShareDataService } from '../../../../services';
import { LoadingService, NotificationService } from '../../../../../../core/services';
import { ActionModal, DateFormat } from '../../../../../../core/enums';
import { OptionDropdown } from '../../../../../../core/interfaces';

@Component({
  selector: 'app-cancel-booking',
  standalone: true,
  imports: [
    CommonModule,
    IconModule,
    MatIconModule,
    TranslateModule,
    ReactiveFormsModule,
    RadioButtonComponent,
  ],
  templateUrl: './cancel-booking.component.html',
  styleUrl: './cancel-booking.component.scss',
  providers: [
    ServiceBookingService,
    NotificationService,
  ],
})
export class CancelBookingComponent implements OnInit {
  data: {
    serviceBookingId: string;
    vin: string;
    dbmBookingNo?: string;
  } = inject(MAT_DIALOG_DATA);

  dialogRef = inject(MatDialogRef<CancelBookingComponent>);
  serviceBookingService = inject(ServiceBookingService);
  notificationService = inject(NotificationService);
  translateService = inject(TranslateService);
  loadingService = inject(LoadingService);
  serviceBookingShareDataService = inject(ServiceBookingShareDataService);

  readonly DateFormat = DateFormat;
  readonly otherReason = 'OTHERS';

  subscription = new Subscription();
  cancelReasons: OptionDropdown[] = [];

  form = new FormGroup({
    reason: new FormControl(null, Validators.required),
    remark: new FormControl(),
  });

  ngOnInit(): void {
    this.getCancelReasons();
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  onCancel(): void {
    this.dialogRef.close({ action: ActionModal.Cancel });
  }

  onCancelBooking(): void {
    const payload = {
      cancelBookingReason: this.form.value.reason,
      cancelBookingReasonDescription: this.form.value.remark,
    };
    this.loadingService.showLoader();
    this.serviceBookingService
      .cancelServiceBooking(this.data?.serviceBookingId, payload)
      .subscribe(
        (response) => {
          this.loadingService.hideLoader();
          this.dialogRef.close({ action: ActionModal.Submit });
          this.notificationService.showSuccess(
            this.translateService.instant(
              'serviceBooking.cancelBooking.cancelSuccess'
            )
          );
          
          this.serviceBookingShareDataService.isReloadList$.next(true);
        },
        (error) => {
          this.loadingService.hideLoader();
          this.notificationService.showError(
            this.translateService.instant(
              'serviceBooking.cancelBooking.cancelFail'
            )
          );
        }
      );
  }

  getCancelReasons(): void {
    this.loadingService.showLoader();
    this.subscription.add(
      this.serviceBookingService.getMetadata(this?.data?.vin).subscribe(
        (response) => {
          this.loadingService.hideLoader();
          this.cancelReasons = response?.cancelReasons;
        },
        (error) => {
          this.loadingService.hideLoader();
        }
      )
    );
  }

  changeReason(): void {
    this.form.patchValue({
      remark: '',
    });
  }
}
