<app-loading *ngIf="isLoading"></app-loading>

<h2 class="title-dialog">
  {{ "serviceBooking.serviceBooking" | translate }}
</h2>

<div class="container-dialog">
  <div class="step-booking">
    <mat-stepper
      linear
      labelPosition="bottom"
      #stepper
      class="custom-stepper"
      [class.step-1-completed]="stepOneForm.valid && currentStepIndex === 1"
      (selectionChange)="onStepChange($event)"
    >
      <mat-step [stepControl]="stepOneForm" completed="false">
        <form [formGroup]="stepOneForm">
          <ng-template matStepLabel>{{
            "serviceBooking.addServiceBooking.generalInfo" | translate
          }}</ng-template>

          @if (currentStepIndex === 0) {
          <div class="info-booking" [formGroup]="stepOneForm">
            <div class="info-booking__dealer">
              <div class="info-booking__dealer__search">
                <label class="form-group__label">
                  {{ "serviceBooking.addServiceBooking.dealer" | translate }}
                  <span class="required">*</span>
                </label>

                <div class="form-group__input">
                  <input
                    type="text"
                    matInput
                    formControlName="dealer"
                    class="form-control"
                    [placeholder]="'enterHereToSearch' | translate"
                    [matAutocomplete]="auto"
                    (input)="onInputDealer($event)"
                  />
                  <mat-autocomplete
                    #auto="matAutocomplete"
                    class="custom-scroll-panel"
                    [displayWith]="displayFn.bind(this)"
                    (optionSelected)="changeDealer($event)"
                    (blur)="stepOneForm.controls.dealer.markAsTouched()"
                  >
                    <mat-option
                      class="custom-autocomplete-option"
                      *ngFor="let suggestion of filteredDealerOption | async"
                      [value]="suggestion.name"
                    >
                      <div class="dealer-option">
                        <p>{{ suggestion?.displayName }}</p>
                        <p>{{ suggestion?.addressDisplay }}</p>
                      </div>
                    </mat-option>
                  </mat-autocomplete>
                  <div
                    *ngIf="
                      stepOneForm.controls.dealer.invalid &&
                      stepOneForm.controls.dealer.touched
                    "
                    class="form-group__error"
                  >
                    {{ "validation.fillInData" | translate }}
                  </div>
                </div>
              </div>

              @if(stepOneForm.value.dealer) {
              <div class="info-booking__dealer__detail">
                <div class="info-booking__dealer__detail__item">
                  <mat-icon svgIcon="ic-call"></mat-icon>
                  {{ dealerInfo?.phoneNumber || "-" }}
                </div>
                <div class="info-booking__dealer__detail__item">
                  <mat-icon svgIcon="ic-location"></mat-icon>
                  {{ dealerInfo?.addressDisplay || "-" }}
                </div>
              </div>
              }
            </div>

            <div class="info-booking__reason">
              <app-radio-button
                [label]="'serviceBooking.addServiceBooking.reason'"
                [required]="true"
                [control]="stepOneForm.controls.reason"
                [option]="reasons"
              ></app-radio-button>
            </div>

            <app-form-group
              [label]="
                'serviceBooking.addServiceBooking.vehicleMileage' | translate
              "
              [control]="stepOneForm.controls.vehicleMileage"
              [type]="'number'"
              (onKeypress)="validateInput($event)"
            >
              <span suffix>{{'common.km' | translate}}</span>
            </app-form-group>
          </div>

          <div class="action-dialog action-step-one">
            <button class="btn-quaternary" (click)="onCancel()">
              {{ "common.cancel" | translate }}
            </button>
            <button
              class="btn-primary"
              matStepperNext
              [disabled]="stepOneForm.invalid"
              (click)="onContinue()"
            >
              {{ "serviceBooking.addServiceBooking.continue" | translate }}
            </button>
          </div>
          }
        </form>
      </mat-step>
      <mat-step [stepControl]="stepTwoForm" completed="false">
        <form [formGroup]="stepTwoForm">
          <ng-template matStepLabel>{{
            "serviceBooking.addServiceBooking.serviceDate" | translate
          }}</ng-template>

          @if (currentStepIndex === 1) {
          <div class="info-booking">
            <div class="info-booking__preferred-date">
              @if (availableDates?.length > 0) {
              <app-preferred-date
                [selectedPreferredDate]="selectedPreferredDate"
                [startDateCalendar1]="startDateCalendar1"
                [startDateCalendar2]="startDateCalendar2"
                [addDisableClass]="addDisableClass"
                (changePreferredDate)="changePreferredDate($event)"
              ></app-preferred-date>
              } @else {
              <app-preferred-date
                [selectedPreferredDate]="selectedPreferredDate"
                [startDateCalendar1]="startDateCalendar1"
                [startDateCalendar2]="startDateCalendar2"
                [addDisableClass]="addDisableClass"
              ></app-preferred-date>
              }
            </div>

            <div class="info-booking__preferred-time">
              <app-dropdown-form-group
                [label]="
                  'serviceBooking.addServiceBooking.preferredTime' | translate
                "
                [control]="stepTwoForm.controls.preferredTime"
                [options]="availableHours"
                [required]="true"
                [errorMessage]="'validation.fillInData' | translate"
                (changeOption)="changePreferredTime()"
              ></app-dropdown-form-group>
            </div>

            <mat-checkbox
              class="custom-mat-checkbox"
              formControlName="waitFor"
              (change)="changeWaitForUnit()"
              >{{
                "serviceBooking.addServiceBooking.waitForTheUnit" | translate
              }}</mat-checkbox
            >

            @if (!stepTwoForm.value.waitFor) {
            <div class="info-booking__pickup-date-time">
              <div class="info-booking__pickup-date-time__date">
                <div class="form-group item-date">
                  <div class="form-group__input">
                    <label for="description" class="form-group__label">
                      {{
                        "serviceBooking.addServiceBooking.pickupDate"
                          | translate
                      }}
                      <span class="required">*</span>
                    </label>
                    <input
                      class="form-control"
                      [matDatepicker]="picker1"
                      [min]="minDatePickupDate"
                      formControlName="pickupDate"
                      (click)="picker1.open()"
                      (keydown)="$event.preventDefault()"
                      (keypress)="$event.preventDefault()"
                      (dateChange)="changePickupDate($event)"
                    />
                    <mat-icon
                      svgIcon="ic-calendar"
                      (click)="picker1.open()"
                    ></mat-icon>
                    <mat-datepicker #picker1></mat-datepicker>
                  </div>
                </div>
                <div
                  *ngIf="
                    stepTwoForm.controls.pickupDate.invalid &&
                    stepTwoForm.controls.pickupDate.touched
                  "
                  class="form-group__error"
                >
                  {{ "validation.fillInData" | translate }}
                </div>
              </div>

              <div class="info-booking__pickup-date-time__time">
                <app-dropdown-form-group
                  [label]="
                    'serviceBooking.addServiceBooking.pickupTime' | translate
                  "
                  [control]="stepTwoForm.controls.pickTime"
                  [options]="availablePickupTime"
                  [required]="true"
                  [errorMessage]="'validation.fillInData' | translate"
                ></app-dropdown-form-group>
              </div>
            </div>
            }

            <div class="form-group">
              <label class="form-group__label">
                {{ "serviceBooking.addServiceBooking.remark" | translate }}
              </label>
              <textarea
                class="form-textarea"
                formControlName="remark"
              ></textarea>
            </div>
          </div>

          <div class="action-dialog action-step-two">
            <button matStepperPrevious class="action-step-two__back">
              <mat-icon svgIcon="ic-arrow-back" class="small-icon"></mat-icon>
              {{ "common.back" | translate }}
            </button>
            <div class="action-step-two__action">
              <button class="btn-quaternary" (click)="onCancel()">
                {{ "common.cancel" | translate }}
              </button>
              <button
                class="btn-primary"
                [disabled]="stepTwoForm.invalid"
                (click)="createServiceBooking()"
              >
                {{ "serviceBooking.add" | translate }}
              </button>
            </div>
          </div>
          }
        </form>
      </mat-step>
    </mat-stepper>
  </div>
</div>
