
<div class="icon-header">
  <mat-icon svgIcon="ic-dtc" class="icon-title"></mat-icon>
</div>

<h2 class="title-dialog">{{ "vehicle.dtc.title" | translate }}</h2>

<mat-dialog-content class="container-dialog">
  @for (dtc of dtcDetail; track $index) {
  <div class="container-dialog__section">
    <div class="container-dialog__section__title">
      <mat-icon [svgIcon]="dtc?.icon"></mat-icon>
      {{ dtc?.name | translate }}
    </div>

    <ul class="container-dialog__section__list">
      @for (item of dtc?.items; track $index) {
      <li>{{ item?.name | translate }}: {{item?.value}}</li>
      }
    </ul>
  </div>
  }
</mat-dialog-content>

<mat-dialog-actions class="action-dialog">
  <button class="btn-quaternary" (click)="onCancel()">
    {{ "common.close" | translate }}
  </button>
</mat-dialog-actions>
