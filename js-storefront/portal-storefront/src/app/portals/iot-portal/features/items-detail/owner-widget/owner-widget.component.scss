.section-owner {
  display: flex;
  flex-direction: column;
  gap: 20px;
  &__body {
    display: flex;
    flex-direction: column;
    gap: 20px;
    &--item-box {
      display: flex;
      flex-wrap: wrap;
      gap: 25px;
      div {
        &:first-child {
          width: calc(50% - 12.5px);
          min-width: 150px;
        }
        &:last-child {
          width: calc(50% - 12.5px);
        }
      }
    }
    &__content {
      display: flex;
      flex-direction: column;
      flex-wrap: wrap;
      gap: 5px;
      &--title {
        color: var(--Primary-Black, #3A3A3A);
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
      }
      &--value {
        color: var(--Primary-Black, #101010);
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px;
      }
      &.email-box {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        align-items: center;
        gap: 15px;
        .email-content {
            display: flex;
            flex-direction: column;
            gap: 5px;
            .email-content-header {
                display: flex;
                align-items: center;
                flex-wrap: wrap;
                gap: 10px;
                .btn-link {
                    font-size: 12px;
                }
            }
        }
      }
    }
  }
}


:host ::ng-deep {
    .section-owner {
        mat-icon {
            svg g path {
                fill: #EB0A1E !important;
            }
        }
    }
}
