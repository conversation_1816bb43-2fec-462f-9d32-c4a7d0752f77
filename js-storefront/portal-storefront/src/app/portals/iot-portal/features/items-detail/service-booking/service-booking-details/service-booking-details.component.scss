@import "../../../../../../../styles/abstracts/variables";

:host {
  mat-icon.icon-title {
    ::ng-deep svg {
      path {
        fill: $text-color-5;
      }
    }
  }
  .container-dialog {
    .title-session {
      font-size: $fs18;
      font-weight: $fw600;
      padding: 20px 0 15px;
      margin: 0;
    }

    .service-info {
      display: flex;
      flex-direction: column;
      gap: 10px;

      &__item {
        display: flex;
        gap: 10px;
        align-items: center;

        mat-icon {
          width: 16px;
          height: 16px;

          ::ng-deep svg {
            path {
              fill: $text-color;
            }
          }
        }

        &__booking-info {
          display: grid;
          grid-template-columns: auto auto;

          div {
            display: flex;
            gap: 10px;
            align-items: center;
          }
        }
      }
    }
  }

  .action-dialog {
    flex-direction: column;
    gap: 25px;
    align-items: center;

    .btn-close {
      text-transform: capitalize;
      cursor: pointer;
      font-size: $fs16;
      font-weight: $fw600;

      &:hover {
        text-decoration: underline;
      }
    }

    &__booking {
      display: flex;
      gap: 20px;
    }
  }

  app-loading {
    top: 0;
  }
}
