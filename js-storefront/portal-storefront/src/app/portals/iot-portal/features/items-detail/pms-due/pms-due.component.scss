.section-pms {
    display: flex;
    flex-direction: column;
    gap: 20px;

    &__content {
        &--way {
            color: var(--Primary-Black, #101010);
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            margin-bottom: 20px;
            span {
                color: var(--Illustration-Purple, #6B7CFE);
                font-size: 20px;
                font-style: normal;
                font-weight: 600;
                line-height: 26px;
            }
        }

        &--item {
            display: flex;
            flex: 0 1;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
            color: var(--Primary-Black, #101010);
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;

            &:not(:last-child) {
                margin-bottom: 10px;
            }
        }
    }
}