<h2 class="title-dialog">Authorization</h2>
<div class="container-dialog">
    <form class="authorization-ticket-form" [formGroup]="authorizationTicketForm" (ngSubmit)="onSubmit()">
        <div class="authorization-ticket-form__row">
            <app-radio-button [label]="'Select authorization method:'" [required]="true"
                [control]="authorizationTicketForm.controls.method" [option]="radioOptions">
            </app-radio-button>
        </div>

        <div class="action-dialog authorization-ticket-form__actions">
            <button type="button" class="btn btn--outline cancel-btn" (click)="onCancel()">
                {{ 'common.cancel' | translate }}
            </button>
            <button class="btn btn--primary" type="submit" [disabled]="authorizationTicketForm.invalid">
                {{ 'common.sendRequest' | translate }}
            </button>
        </div>
    </form>
</div>