import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IconModule } from '../../../../../core/icon/icon.module';
import { DateFormat } from '../../../../../core/enums';
import { HistoryLog } from '../../../../../core/interfaces/vehicle.interface';

@Component({
  selector: 'app-history-log-widget',
  standalone: true,
  imports: [CommonModule, IconModule, TranslateModule],
  templateUrl: './history-log-widget.component.html',
  styleUrl: './history-log-widget.component.scss',
})
export class HistoryLogWidgetComponent {
  @Input() data: HistoryLog[];

  readonly DateFormat = DateFormat;

  today = new Date();
}
