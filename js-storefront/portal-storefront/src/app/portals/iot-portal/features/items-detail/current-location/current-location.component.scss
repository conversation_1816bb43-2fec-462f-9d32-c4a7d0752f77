.current-location {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 20px;
  align-self: stretch;

  &__header {
    display: flex;
    align-items: center;
    gap: 12px;

    span {
      font-family: "Toyota Type";
      font-size: 18px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px;
      color: #101010;
    }
  }

  &__address-box {
    padding-left: 10px;
    padding-right: 10px;
    position: absolute;
    bottom: 10px;
    width: 100%;
  }

  &__address {
    display: flex;
    width: 100%;
    align-items: center;
    gap: 5px;
    background: #ffffff;
    padding: 15px;

    &--content {
      display: flex;
      flex-direction: column;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 20px;
      color: #101010;
      margin-right: auto;

      span {
        &:last-child {
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 18px;
        }
      }
    }
    &--action {
      flex-shrink: 0;
    }
  }

  .map-content {
    width: 100%;
    height: 280px;
  }
}