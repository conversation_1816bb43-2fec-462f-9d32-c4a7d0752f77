<div class="section-verification">
  <mat-icon
    svgIcon="ic-lock-grey"
    class="medium-icon section-verification__icon"
  ></mat-icon>
  <div class="section-verification__content">
    <span>{{ "tickets.verification" | translate }}</span>
    
    <span [class.verified]="verifiedInfo || ticket?.isVerified"
      >{{
        verifiedInfo || ticket?.isVerified
          ? ("tickets.verificationBy" | translate) +
            " " +
            (verifiedInfo?.verifiedBy || ticket.verifiedBy) +
            " " +
            ("tickets.verificationOn" | translate) +
            " " +
            (verifiedInfo?.verifiedDate || ticket.verifiedDate
              | date : dateFormat.FullDate)
          : ("tickets.verificationSub" | translate)
      }}</span
    >
  </div>


  @if((responseConfirmVerify? !responseConfirmVerify : !ticket?.isVerified) && userService.isHasPermission([PERMISSIONS_CODE.ECARE_CUSTOMER_VERIFICATION_VIEW])) {
    <div class="section-verification__action">
      <button
        class="btn-link section-verification__confirm"
        (click)="onVerification()"
      >
        <mat-icon
          svgIcon="ic-checked-red"
          class="small-icon"
          aria-hidden="true"
        ></mat-icon>
        {{ "common.confirm" | translate }}
      </button>
    </div>
  }

</div>
