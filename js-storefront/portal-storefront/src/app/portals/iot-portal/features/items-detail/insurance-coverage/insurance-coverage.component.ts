import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  OnInit,
} from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { TicketsService } from '../../../services';
import { InsuranceDetails } from '../../../interfaces';

@Component({
  selector: 'app-insurance-coverage',
  standalone: true,
  imports: [CommonModule, TranslateModule, MatIconModule],
  providers: [TicketsService],
  templateUrl: './insurance-coverage.component.html',
  styleUrls: ['./insurance-coverage.component.scss'],
})
export class InsuranceCoverageComponent implements OnInit {
  @Input() insuranceData: InsuranceDetails;
  @Input() haveData: boolean = false;
  ngOnInit() {
  }
}
