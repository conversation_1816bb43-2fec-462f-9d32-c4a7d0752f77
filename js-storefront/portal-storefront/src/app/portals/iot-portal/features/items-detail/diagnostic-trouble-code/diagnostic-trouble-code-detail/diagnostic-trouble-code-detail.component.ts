import { Component, inject, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';

import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';
import { VehicleService } from '../../../../services';
import { LoadingService, NotificationService } from '../../../../../../core/services';

@Component({
  selector: 'app-diagnostic-trouble-code-detail',
  standalone: true,
  imports: [CommonModule, MatDialogModule, MatIconModule, TranslateModule],
  templateUrl: './diagnostic-trouble-code-detail.component.html',
  styleUrl: './diagnostic-trouble-code-detail.component.scss',
  providers: [VehicleService, NotificationService],
})
export class DiagnosticTroubleCodeDetailComponent implements OnInit, OnDestroy {
  data = inject(MAT_DIALOG_DATA);
  dialogRef = inject(MatDialogRef<DiagnosticTroubleCodeDetailComponent>);
  loadingService = inject(LoadingService);
  vehicleService = inject(VehicleService);
  notificationService = inject(NotificationService);

  subscription = new Subscription();

  dtcDetail: {
    icon: string;
    name: string;
    items: { name: string; value: string }[];
  }[] = [];

  ngOnInit(): void {
    this.dtcDetail = this.data?.dtcDetail || [];
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  onCancel() {
    this.dialogRef.close();
  }
}
