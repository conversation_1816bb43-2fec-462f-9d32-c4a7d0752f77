<div class="section-device-widget">
  <div class="section-header">
    <mat-icon svgIcon="ic-device-sim" class="medium-icon section-header--icon"></mat-icon>
    <div class="section-header--content">
      <span>{{ "vehicle.deviceWidget.title" | translate }}</span>
    </div>
  </div>
  <div class="section-device-widget__content widget-table">
    <ng-container *ngIf="rowData && rowData.length > 0; else noData">
      <ag-grid-angular class="custom-grid" [rowData]="rowData" [columnDefs]="colDefs"
        [domLayout]="'autoHeight'" [defaultColDef]="defaultColDef" [rowHeight]="rowHeight" />
    </ng-container>

    <ng-template #noData>
      <div class="section-no-data">{{ 'callLog.noData' | translate }}</div>
    </ng-template>
  </div>
</div>