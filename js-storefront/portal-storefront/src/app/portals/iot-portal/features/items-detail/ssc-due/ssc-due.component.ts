import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { ItemServiceCampaign } from '../../../interfaces';

@Component({
  selector: 'app-ssc-due',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    TranslateModule,
  ],
  templateUrl: './ssc-due.component.html',
  styleUrls: ['./ssc-due.component.scss']
})
export class SscDueComponent implements OnInit {
  @Input() data: ItemServiceCampaign[];
  constructor() { }

  ngOnInit() {
  }

}
