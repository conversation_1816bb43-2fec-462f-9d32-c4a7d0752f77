import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { PMSWidget } from '../../../../../core/interfaces/vehicle.interface';

@Component({
  selector: 'app-pms-due',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    TranslateModule,
  ],
  templateUrl: './pms-due.component.html',
  styleUrls: ['./pms-due.component.scss']
})
export class PmsDueComponent implements OnInit {
  @Input() pmsData: PMSWidget;

  ngOnInit() {
  }

}
