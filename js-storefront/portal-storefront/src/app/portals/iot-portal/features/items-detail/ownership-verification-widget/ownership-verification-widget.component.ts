import { CommonModule } from '@angular/common';
import { Component, inject, Input, OnInit } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';

import { Subscription } from 'rxjs';
import { OwnershipVerificationWidget } from '../../../../../core/interfaces';

@Component({
  selector: 'app-ownership-verification-widget',
  standalone: true,
  imports: [CommonModule, MatIconModule, TranslateModule],
  templateUrl: './ownership-verification-widget.component.html',
  styleUrls: ['./ownership-verification-widget.component.scss'],
})
export class OwnershipVerificationWidgetComponent implements OnInit {
  @Input() data: OwnershipVerificationWidget[];
  @Input() vin: string = '';
  mainSubscription = new Subscription();

  ngOnInit() {}

  ngOnDestroy() {
    this.mainSubscription && this.mainSubscription.unsubscribe();
  }

}
