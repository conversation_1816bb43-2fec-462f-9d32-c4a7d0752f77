import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { Loader } from '@googlemaps/js-api-loader';
import { TranslateModule } from '@ngx-translate/core';
import { Vehicle } from '../../../interfaces';
import { SimStatus } from '../../../enums';
import { DateFormat } from '../../../../../core/enums';
@Component({
  selector: 'app-current-location',
  standalone: true,
  imports: [CommonModule, MatIconModule, TranslateModule],
  templateUrl: './current-location.component.html',
  styleUrls: ['./current-location.component.scss'],
})
export class CurrentLocationComponent implements OnInit {
  @Input() vehicle: Vehicle;
  @Input() setHideRefresh: boolean = false;
  @Input() flightPlanCoordinates: { lat: number; lng: number }[] = [];
  @Input() onlyMap: boolean = false;
  @Input() mapId: string = 'map';
  @Output() refresh = new EventEmitter();
  simStatus = SimStatus;
  loader = new Loader({
    apiKey: 'AIzaSyDZluXNIwKgkXOx86r1UiKBRt-VEVBTsqQ',
    version: 'weekly',
  });
  map: any;
  dateFormat = DateFormat;

  ngOnChanges(changes: SimpleChanges) {
    if (changes['vehicle'] && changes['vehicle'].currentValue) {
      this.initMap();
    }
  }

  ngOnInit() {}

  initMap() {
    this.loader
      .importLibrary('maps')
      .then(({ Map }) => {
        this.map = new Map(document.getElementById(this.mapId), {
          center: {
            lat: this.vehicle?.latitude? +this.vehicle?.latitude : 0,
            lng: this.vehicle?.longitude? +this.vehicle?.longitude : 0,
          },
          zoom: 15,
          clickableIcons: false,
          fullscreenControl: false,
          mapTypeControl: false,
          streetViewControl: false,
          scrollwheel: false,
          keyboardShortcuts: false,
          zoomControl: false,
          zoomControlOptions: {
            position: google.maps.ControlPosition.TOP_RIGHT,
          },
        });

        const svgMarkerLink = `<svg xmlns="http://www.w3.org/2000/svg" width="31" height="40" viewBox="0 0 31 40" fill="none">
                        <circle cx="15.5" cy="14" r="10" fill="white"/>
                        <path d="M21.4257 15.1399C21.0286 23.1253 9.8004 22.5099 9.89049 15.1399C9.81108 7.71963 21.0413 7.20129 21.4257 15.1399ZM27.3736 25.0868C24.7173 28.4651 18.2061 36.7232 15.6218 40C13.0809 36.7661 6.49771 28.4219 3.86997 25.0868C-4.48909 14.6345 3.75447 -1.39015 17.065 0.0965026C29.1199 1.0638 34.8656 15.5871 27.3727 25.0868H27.3736ZM24.313 15.1399C24.3076 12.83 23.3837 10.6158 21.7451 8.98638C20.1083 7.35684 17.8886 6.44369 15.5786 6.44909C4.16628 6.88761 4.16998 23.4176 15.6219 23.8311C17.9264 23.8293 20.1352 22.9125 21.7648 21.283C23.3945 19.6534 24.3112 17.4445 24.313 15.1399Z" fill="#EB0A1E"/>
                        </svg>`
        const svgMarker = {
          url:
            'data:image/svg+xml;charset=UTF-8,' +
            encodeURIComponent(svgMarkerLink),
          scaledSize: new google.maps.Size(30, 40),
        }

        const svgMarkerEnd = `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="23" viewBox="0 0 24 23" fill="none">
                      <circle cx="12" cy="11.5" r="9" fill="white" stroke="#EB0A1E" stroke-width="5"/>
                      </svg>`;

        if (
          this.flightPlanCoordinates &&
          this.flightPlanCoordinates.length === 0
        ) {
          new google.maps.Marker({
            position: {
              lat: +this.vehicle?.latitude,
              lng: +this.vehicle?.longitude,
            },
            map: this.map,
            optimized: true,
            icon: svgMarker,
          });
        } else {
          const directionsService = new google.maps.DirectionsService();
          const directionsDisplay = new google.maps.DirectionsRenderer({
            suppressMarkers: true,
            map: this.map,
            polylineOptions: {
              strokeColor: '#FF0000',
              strokeOpacity: 1.0,
              strokeWeight: 5,
            },
          });

          var start = `${this.flightPlanCoordinates[0].lat}, ${this.flightPlanCoordinates[0].lng}`;
          var end = `${this.flightPlanCoordinates[1].lat}, ${this.flightPlanCoordinates[1].lng}`;
          var request = {
            origin: start,
            destination: end,
            travelMode: google.maps.TravelMode.DRIVING,
          };
          directionsService.route(request, (response, status) => {
            if (status == google.maps.DirectionsStatus.OK) {
              directionsDisplay.setDirections(response);
              const myRoute = response.routes[0].legs[0];
              // Create a marker for the start location
              new google.maps.Marker({
                position: myRoute.start_location,
                map: this.map,
                icon: svgMarker,
              });

              // Create a marker for the end location
              new window.google.maps.Marker({
                position: myRoute.end_location,
                map: this.map,
                icon: {
                  url:
                    'data:image/svg+xml;charset=UTF-8,' +
                    encodeURIComponent(svgMarkerEnd),
                  scaledSize: new google.maps.Size(20, 20),
                },
                optimized: false,
              });
            }
          });
        }

        const zoomControlDiv = document.createElement('div');
        // Create the control.
        this.zoomControl(zoomControlDiv, this.map);

        this.map.controls[google.maps.ControlPosition.TOP_RIGHT].push(
          zoomControlDiv
        );
      })
      .catch((e) => {
        // do something
      });
  }

  zoomControl(controlDiv, map) {
    controlDiv.style.padding = '5px';

    // Set CSS for the control wrapper
    const controlWrapper = document.createElement('div');
    controlWrapper.style.backgroundColor = 'white';
    controlWrapper.style.borderRadius = '10px';
    controlWrapper.style.cursor = 'pointer';
    controlWrapper.style.textAlign = 'center';
    controlWrapper.style.width = '32px';
    controlWrapper.style.height = '64px';
    controlDiv.appendChild(controlWrapper);

    const zoomInButton = document.createElement('div');
    zoomInButton.style.width = '32px';
    zoomInButton.style.height = '32px';
    zoomInButton.style.backgroundImage =
      "url('assets/images/icons/ic-plus-map.svg')";
    zoomInButton.style.backgroundPosition = 'center';
    zoomInButton.style.backgroundSize = '13px 13px';
    zoomInButton.style.backgroundRepeat = 'no-repeat';
    /* Change this to be the .png image you want to use */
    controlWrapper.appendChild(zoomInButton);
    const borderBox = document.createElement('div');
    borderBox.style.paddingLeft = '7px';
    borderBox.style.paddingRight = '7px';
    borderBox.style.width = '100%';
    const border = document.createElement('div');
    border.style.width = '100%';
    border.style.height = '1px';
    border.style.background = '#dadce0';
    borderBox.appendChild(border);
    controlWrapper.appendChild(borderBox);
    // Set CSS for the zoomOut
    const zoomOutButton = document.createElement('div');
    zoomOutButton.style.width = '32px';
    zoomOutButton.style.height = '32px';
    zoomOutButton.style.backgroundImage =
      "url('assets/images/icons/ic-minus-map.svg')";
    zoomOutButton.style.backgroundPosition = 'center -2px';
    zoomOutButton.style.backgroundSize = '23px 32px';
    zoomOutButton.style.backgroundRepeat = 'no-repeat';
    /* Change this to be the .png image you want to use */
    controlWrapper.appendChild(zoomOutButton);

    // Setup the click event listener - zoomIn
    google.maps.event.addDomListener(zoomInButton, 'click', function () {
      map.setZoom(map.getZoom() + 1);
    });

    // Setup the click event listener - zoomOut
    google.maps.event.addDomListener(zoomOutButton, 'click', function () {
      map.setZoom(map.getZoom() - 1);
    });
  }

  onRefresh() {
    this.refresh.emit();
  }
}
