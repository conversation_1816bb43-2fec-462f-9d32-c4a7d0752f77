import { CommonModule, DatePipe } from '@angular/common';
import { Component, inject, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

import { Subscription } from 'rxjs';

import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatInputModule } from '@angular/material/input';
import { IconModule } from '../../../../../../../core/icon/icon.module';
import { DateFormGroupComponent, DateTimePickerFormGroupComponent, DropdownFormGroupComponent, FormGroupComponent, InputTimeComponent } from '../../../../../../../core/shared';
import { InputNumberDirective } from '../../../../../../../core/directives/input-interger.directive';
import { LoadingService, NotificationService } from '../../../../../../../core/services';
import { TroubleCodeService } from '../../../../../services';
import { DateFormat } from '../../../../../../../core/enums';
import { OptionDropdown } from '../../../../../../../core/interfaces';
import { DefaultValuesRequestLatest } from '../../../../../../../core/interfaces/vehicle.interface';

@Component({
  selector: 'app-request-latest-dtc',
  standalone: true,
  imports: [
    CommonModule,
    IconModule,
    TranslateModule,
    ReactiveFormsModule,
    DropdownFormGroupComponent,
    MatDatepickerModule,
    MatInputModule,

    FormGroupComponent,
    InputTimeComponent,
    DateFormGroupComponent,

    InputNumberDirective,
    DateTimePickerFormGroupComponent,
  ],
  templateUrl: './request-latest-dtc.component.html',
  styleUrl: './request-latest-dtc.component.scss',
  providers: [NotificationService, TroubleCodeService, DatePipe],
})
export class RequestLatestDtcComponent implements OnInit, OnDestroy {
  data: {
    vin: string;
  } = inject(MAT_DIALOG_DATA);

  dialogRef = inject(MatDialogRef<RequestLatestDtcComponent>);

  notificationService = inject(NotificationService);
  translateService = inject(TranslateService);
  loadingService = inject(LoadingService);
  datePipe = inject(DatePipe);

  troubleCodeService = inject(TroubleCodeService);

  subscription = new Subscription();
  today = new Date();

  centerRequestTypeOptions: OptionDropdown[] = [];
  group1 = ['numberRetries', 'timeOut'];
  group2 = ['expirationTime'];
  defaultValues: DefaultValuesRequestLatest;

  form = new FormGroup({
    centerRequestType: new FormControl(null, Validators.required),
    numberRetries: new FormControl(),
    timeOut: new FormControl(),
    expirationTime: new FormControl(null),
  });
  minDate: Date = new Date();

  ngOnInit(): void {
    this.getCenterRequestType();
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  getCenterRequestType(): void {
    this.loadingService.showLoader();
    this.subscription.add(
      this.troubleCodeService.getCenterRequestType(this.data?.vin).subscribe(
        (response) => {
          this.loadingService.hideLoader();
          this.centerRequestTypeOptions = response?.centerRequestTypes;
          this.defaultValues = response?.defaultValues;

          this.form.patchValue({
            centerRequestType: this.defaultValues?.centerRequestType,
            numberRetries: this.defaultValues?.retry,
            timeOut: this.defaultValues?.timeout,
            expirationTime: new Date(this.defaultValues?.expirationTime),
          });
        },
        (error) => {
          this.loadingService.hideLoader();
        }
      )
    );
  }

  onConfirm(): void {
    this.loadingService.showLoader();
    const { centerRequestType, numberRetries, timeOut, expirationTime } =
      this.form.value;

    let payload;
    if (
      this.form.value.centerRequestType === 1 ||
      this.form.value.centerRequestType === 2
    ) {
      payload = {
        centerRequestType,
        retry:
          numberRetries || numberRetries === 0 ? Number(numberRetries) : null,
        timeout: timeOut ? Number(timeOut) : null,
      };
    } else {
      payload = {
        centerRequestType,
        expirationTime: this.datePipe.transform(
          expirationTime,
          DateFormat.FullDate
        ),
      };
    }

    this.subscription.add(
      this.troubleCodeService.requestDTC(this.data?.vin, payload).subscribe(
        (response) => {
          this.loadingService.hideLoader();
          this.dialogRef.close();
          this.notificationService.showSuccess(
            this.translateService.instant('troubleCode.requestSuccess')
          );
        },
        (error) => {
          this.loadingService.hideLoader();
          this.dialogRef.close();
          this.notificationService.showError(
            this.translateService.instant('troubleCode.requestFail')
          );
        }
      )
    );
  }

  changeCenterRequestType(event): void {
    const { value } = event;
    if (value === 1 || value === 2) {
      this.form.patchValue({
        numberRetries: this.defaultValues?.retry,
        timeOut: this.defaultValues?.timeout,
      });
      this.updateValidate(this.group1, this.group2);
    } else {
      this.form.patchValue({
        expirationTime: this.defaultValues?.expirationTime
          ? new Date(this.defaultValues?.expirationTime)
          : new Date(),
      });
      this.updateValidate(this.group2, this.group1);
    }
  }

  updateValidate(groupRequired: string[], groupNonRequired: string[]): void {
    groupRequired.forEach((item) => {
      this.form.controls[item]?.setValidators(Validators.required);
      this.form.controls[item].updateValueAndValidity();
      this.form.controls[item].markAsUntouched();
    });

    groupNonRequired.forEach((item) => {
      this.form.controls[item]?.setValidators(null);
      this.form.controls[item].updateValueAndValidity();
      this.form.controls[item].markAsUntouched();
    });
  }
}
