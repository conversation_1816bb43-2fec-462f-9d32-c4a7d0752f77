import { Component, Input, SimpleChanges } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { ActivatedRoute, Router } from '@angular/router';
import { WarningListComponent } from "./warning-list/warning-list.component";
import { LoadingComponent } from '../../../../../../layout/global/loading/loading.component';
import { LoadingService } from '../../../../../../core/services';
import { WarningDetailComponent } from './warning-detail/warning-detail.component';

@Component({
  selector: 'app-warning-tab',
  standalone: true,
  imports: [
    TranslateModule,
    MatIconModule,
    LoadingComponent,
    WarningListComponent,
    WarningDetailComponent
  ],
  providers: [
    LoadingService,
  ],
  templateUrl: './warning-tab.component.html',
  styleUrl: './warning-tab.component.scss'
})
export class WarningTabComponent {
  @Input() warningOverviewID: string;
  vin: string = '';
  warningId: string = ''; // Id of the selected warning to show detail

  constructor(
    public loadingService: LoadingService,
    private route: ActivatedRoute
  ) { }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['warningOverviewID'] && changes['warningOverviewID'].currentValue) {
      this.warningId = changes['warningOverviewID'].currentValue;
    }
  }

  ngOnInit(): void {
    this.getVinID();
  }

  getVinID(): void {
    this.vin = this.route.snapshot.paramMap.get('id');
  }

  setWarningId(warningId: string): void {
    // Set warningId to show detail
    this.warningId = warningId;
  }
}
