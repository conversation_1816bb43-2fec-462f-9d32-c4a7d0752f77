<div class="service-booking-tab">
  <div class="service-booking-tab__title">
    <div>
      <mat-icon svgIcon="ic-car-service"></mat-icon>
      {{ "serviceBookingTab.serviceBooking" | translate }}
    </div>
    <button
      *ngIf="
        userService.isHasPermission([
          PERMISSIONS_CODE.ECARE_SERVICE_BOOKING_MANAGE
        ])
      "
      class="btn-primary"
      (click)="addServiceBooking()"
    >
      {{ "serviceBookingTab.addBooking" | translate }}
    </button>
  </div>
  <form class="service-booking-tab__filter" [formGroup]="form">
    <app-form-group
      [label]="'serviceBookingTab.id' | translate"
      [control]="form.controls.id"
      [placeholder]="'enterHereToSearch' | translate"
      (onEnter)="search()"
    ></app-form-group>

    <app-dropdown-form-group
      [label]="'serviceBookingTab.status' | translate"
      [control]="form.controls.status"
      [options]="statusServiceBooking"
      (enter)="search()"
    ></app-dropdown-form-group>

    <button type="submit" class="btn-tertiary search" (click)="search()">
      {{ "common.search" | translate }}
    </button>
  </form>

  @if (rowData?.length > 0) {

  <app-ag-grid-custom
    [rowData]="rowData"
    [colDefs]="colDefs"
    [defaultColDef]="defaultColDef"
    [isPaging]="true"
    [pagingInfo]="pagingInfo"
    [isShowActionExport]="false"
    (onPageChange)="onPageChange($event)"
    (changeItemPerPage)="changeItemPerPage($event)"
  >
  </app-ag-grid-custom>
  } @else {
  <div class="no-data">
    {{ "serviceBookingTab.noData" | translate }}
  </div>
  }
</div>
