import { CommonModule, DatePipe } from '@angular/common';
import { Component, inject, Input, OnInit } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatDialog } from '@angular/material/dialog';
import { Subscription } from 'rxjs';
import { IconModule } from '../../../../../../core/icon/icon.module';
import { AgGridCustomComponent, FormGroupComponent } from '../../../../../../core/shared';
import { VehicleService } from '../../../../services';
import { LoadingService, NotificationService } from '../../../../../../core/services';
import { PagingInfo } from '../../../../../../core/interfaces';
import { DEFAULT_COL_DEF } from '../../../../../../core/constants';
import { DateFormat } from '../../../../../../core/enums';
import { handleErrors } from '../../../../../../core/helpers';
import { ServiceHistory } from '../../../../../../core/interfaces/vehicle.interface';

@Component({
  selector: 'app-service-history-tab',
  standalone: true,
  imports: [
    CommonModule,
    IconModule,
    TranslateModule,
    AgGridCustomComponent,
    FormGroupComponent,
    ReactiveFormsModule,
  ],
  providers: [VehicleService, NotificationService, DatePipe],
  templateUrl: './service-history-tab.component.html',
  styleUrls: ['./service-history-tab.component.scss']
})
export class ServiceHistoryTabComponent implements OnInit {
  @Input() vin: string;
  vehicleService = inject(VehicleService);
  notificationService = inject(NotificationService);
  translateService = inject(TranslateService);
  loadingService = inject(LoadingService);
  dialog = inject(MatDialog);
  datePipe = inject(DatePipe);

  isSearch: boolean = false;
  subscription = new Subscription();

  rowData: ServiceHistory[] = [];

  pagingInfo: PagingInfo = {
    totalItems: 0,
    currentPage: 0,
    numberOfPage: 10,
  };

  serviceHistoryFormSearch: FormGroup = new FormGroup({
    txtSearchOrder: new FormControl(''),
    txtSearchDes: new FormControl(''),
  });

  defaultColDef = {
    ...DEFAULT_COL_DEF,
    valueFormatter: (params) => (params.value ? params.value : '-'),
  };
  colDefs = [
    {
      headerName: this.translateService.instant(
        'vehicle.serviceHistory.table.repairOrder'
      ),
      headerValueGetter: () => this.translateService.instant('vehicle.serviceHistory.table.repairOrder'),
      flex: 1,
      autoHeight: true,
      wrapText: true,
      cellClass: 'cell-word-wrap',
      field: 'repairOrderNo',
    },
    {
      headerName: this.translateService.instant(
        'vehicle.serviceHistory.table.saName'
      ),
      headerValueGetter: () => this.translateService.instant('vehicle.serviceHistory.table.saName'),
      autoHeight: true,
      wrapText: true,
      flex: 1,
      field: 'serviceAssistantName',
    },
    {
      headerName: this.translateService.instant('vehicle.serviceHistory.table.dealer'),
      headerValueGetter: () => this.translateService.instant('vehicle.serviceHistory.table.dealer'),
      flex: 1,
      autoHeight: true,
      wrapText: true,
      field: 'dealerName',
    },
    {
      headerName: this.translateService.instant('vehicle.serviceHistory.table.jobDes'),
      headerValueGetter: () => this.translateService.instant('vehicle.serviceHistory.table.jobDes'),
      field: 'jobDescription',
      flex: 1,
      autoHeight: true,
      wrapText: true,
    },
    {
      headerName: this.translateService.instant(
        'vehicle.serviceHistory.table.totalAmount'
      ),
      headerValueGetter: () => this.translateService.instant('vehicle.serviceHistory.table.totalAmount'),
      maxWidth: 190,
      minWidth: 190,
      autoHeight: true,
      wrapText: true,
      field: 'totalAmountFormatted',
    },
    {
      headerName: this.translateService.instant('vehicle.serviceHistory.table.status'),
      headerValueGetter: () => this.translateService.instant('vehicle.serviceHistory.table.status'),
      maxWidth: 120,
      minWidth: 120,
      field: 'statusEnum',
      autoHeight: true,
      wrapText: true,
      cellRenderer: (params) => {
        return params?.value ? `<span>${params?.value?.name}</span>` : '-';
      },
    },
    {
      headerName: this.translateService.instant('vehicle.serviceHistory.table.submitted'),
      headerValueGetter: () => this.translateService.instant('vehicle.serviceHistory.table.submitted'),
      maxWidth: 190,
      minWidth: 190,
      field: 'submitted',
      cellRenderer: (params) => {
        return params?.value ? `<span>${this.datePipe.transform(params?.value, DateFormat.ShortDate)}</span>` : '-';
      },
      autoHeight: true,
      wrapText: true,
    },
  ];

  ngOnInit(): void {
    this.getServiceHistoryList();
  }

  ngOnDestroy() {
    this.subscription && this.subscription.unsubscribe();
  }

  public get txtSearchOrder(): FormControl {
    return this.serviceHistoryFormSearch.get('txtSearchOrder') as FormControl;
  }

  public get txtSearchDes(): FormControl {
    return this.serviceHistoryFormSearch.get('txtSearchDes') as FormControl;
  }


  onSearch(): void {
    this.pagingInfo.currentPage = 0;
    this.isSearch = true;
    this.getServiceHistoryList();
  }

  getServiceHistoryList(): void {
    const params = {
      repairOrderNo: this.serviceHistoryFormSearch.value.txtSearchOrder,
      jobDescription: this.serviceHistoryFormSearch.value.txtSearchDes,
      currentPage: this.pagingInfo.currentPage,
      pageSize: this.pagingInfo.numberOfPage,
    };
    this.loadingService.showLoader();
    this.subscription.add(
      this.vehicleService.getServiceHistoryList(params, this.vin).subscribe(
        (response) => {
          this.loadingService.hideLoader();
          this.rowData = response?.items;
          this.pagingInfo.totalItems = response?.pagination?.totalResults;
        },
        (error) => {
          this.loadingService.hideLoader();
          handleErrors(error, this.notificationService);
        }
      )
    );
  }

  onPageChange(value: number): void {
    this.pagingInfo.currentPage = value;
    this.getServiceHistoryList();
  }

  changeItemPerPage(item) {
    this.pagingInfo.numberOfPage = item;
    this.pagingInfo.currentPage = 0;
    this.getServiceHistoryList();
  }
}