import { CommonModule } from '@angular/common';
import { Component, inject, Input, OnDestroy, OnInit } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs';

import { MatDialog } from '@angular/material/dialog';
import { PackageFeaturesComponent } from '../../../items-detail/subscription-widget/package-features/package-features.component';
import { IconModule } from '../../../../../../core/icon/icon.module';
import { AgGridCustomComponent } from '../../../../../../core/shared';
import { ServiceCampaignsService, VehicleService } from '../../../../services';
import { LoadingService, NotificationService } from '../../../../../../core/services';
import { ItemSubscription } from '../../../../interfaces';
import { PagingInfo } from '../../../../../../core/interfaces';
import { DEFAULT_COL_DEF } from '../../../../../../core/constants';
import { handleErrors } from '../../../../../../core/helpers';

@Component({
  selector: 'app-subscription-tab',
  standalone: true,
  imports: [CommonModule, IconModule, TranslateModule, AgGridCustomComponent],
  templateUrl: './subscription-tab.component.html',
  styleUrl: './subscription-tab.component.scss',
  providers: [ServiceCampaignsService, NotificationService, VehicleService],
})
export class SubscriptionTabComponent implements OnInit, OnDestroy {
  @Input() vin: string;
  notificationService = inject(NotificationService);
  translateService = inject(TranslateService);
  loadingService = inject(LoadingService);

  vehicleService = inject(VehicleService);
  dialog = inject(MatDialog);

  subscription = new Subscription();

  rowData: ItemSubscription[];

  pagingInfo: PagingInfo = {
    totalItems: 0,
    currentPage: 0,
    numberOfPage: 10,
  };

  defaultColDef = {
    ...DEFAULT_COL_DEF,
    valueFormatter: (params) => (params.value ? params.value : '-'),
  };

  colDefs = [
    {
      headerName: this.translateService.instant('subscriptionTab.package'),
      headerValueGetter: () => this.translateService.instant('subscriptionTab.package'),
      flex: 1,
      wrapText: true,
      autoHeight: true,
      cellClass: 'cell-word-wrap can-click',
      cellRenderer: (params) => {
        return `<p>${
          params?.data?.packageType ? params?.data?.packageType : '-'
        }</p><p>${
          params?.data?.isFreeSubscription
            ? this.translateService.instant('vehicle.subWidget.freePackage')
            : ''
        }</p>`;
      },
      onCellClicked: (event) => this.subscriptionDetail(event?.data),
    },
    {
      headerName: this.translateService.instant('subscriptionTab.startDate'),
      headerValueGetter: () => this.translateService.instant('subscriptionTab.startDate'),
      field: 'startDate',
      wrapText: true,
      autoHeight: true,
      cellClass: 'cell-word-wrap',
      flex: 1,
    },
    {
      headerName: this.translateService.instant('subscriptionTab.endDate'),
      headerValueGetter: () => this.translateService.instant('subscriptionTab.endDate'),
      field: 'endDate',
      wrapText: true,
      autoHeight: true,
      cellClass: 'cell-word-wrap',
      flex: 1,
    },
    {
      headerName: this.translateService.instant('vehicle.subWidget.type'),
      headerValueGetter: () => this.translateService.instant('vehicle.subWidget.type'),
      field: 'type',
      cellRenderer: (params) => {
        return params?.value?.name || '-';
      },
      flex: 1,
      wrapText: true,
      autoHeight: true,
    },
    {
      headerName: this.translateService.instant(
        'subscriptionTab.paymentStatus'
      ),
      headerValueGetter: () => this.translateService.instant('subscriptionTab.paymentStatus'),
      field: 'subscriptionPaymentStatus',
      cellRenderer: (params) => {
        return params?.value ? `<span>${params?.value?.name}</span>` : '';
      },
      cellClassRules: {
        'value-tag': (params) => !!params.value,
      },
      flex: 1,
      wrapText: true,
      autoHeight: true,
    },
    {
      headerName: this.translateService.instant('subscriptionTab.paidBy'),
      headerValueGetter: () => this.translateService.instant('subscriptionTab.paidBy'),
      field: 'paidBy',
      cellRenderer: (params) => {
        return params?.value || '-';
      },
      flex: 1,
      wrapText: true,
      autoHeight: true,
    },
    {
      headerName: this.translateService.instant('vehicle.subWidget.status'),
      headerValueGetter: () => this.translateService.instant('vehicle.subWidget.status'),
      field: 'subscriptionStatus',
      cellRenderer: (params) => {
        return params?.value ? `<span>${params?.value?.name}</span>` : '';
      },
      cellClassRules: {
        'value-tag': (params) => !!params.value,
      },
      flex: 1,
      wrapText: true,
      autoHeight: true,
    },
  ];

  ngOnInit(): void {
    this.getList();
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  getList(): void {
    const params = {
      vin: this.vin,
      currentPage: this.pagingInfo.currentPage,
      pageSize: this.pagingInfo.numberOfPage,
    };
    this.loadingService.showLoader();
    this.subscription.add(
      this.vehicleService.getSubscriptions(params).subscribe(
        (response) => {
          this.loadingService.hideLoader();
          this.rowData = response?.items;
          this.pagingInfo.totalItems = response?.pagination?.totalResults;
        },
        (error) => {
          this.loadingService.hideLoader();
          handleErrors(error, this.notificationService);
        }
      )
    );
  }

  onPageChange(value: number): void {
    this.pagingInfo.currentPage = value;
    this.getList();
  }

  changeItemPerPage(item) {
    this.pagingInfo.numberOfPage = item;
    this.pagingInfo.currentPage = 0;
    this.getList();
  }

  subscriptionDetail(data: ItemSubscription): void {
    const dialogRef = this.dialog.open(PackageFeaturesComponent, {
      width: '650px',
      maxHeight: '90vh',
      data: {
        features: data?.features?.filter(item => item?.enable)
      },
    });

    dialogRef.afterClosed().subscribe();
  }
}
