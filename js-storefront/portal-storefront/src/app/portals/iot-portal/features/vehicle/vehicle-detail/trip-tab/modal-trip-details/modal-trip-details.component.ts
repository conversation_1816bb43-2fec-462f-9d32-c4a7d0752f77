import { CommonModule, DatePipe } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs';
import { DiagnosticTroubleCodeDetailComponent } from '../../../../items-detail/diagnostic-trouble-code/diagnostic-trouble-code-detail/diagnostic-trouble-code-detail.component';
import { CurrentLocationComponent } from '../../../../items-detail/current-location/current-location.component';
import { AgGridCustomComponent } from '../../../../../../../core/shared';
import { VehicleService } from '../../../../../services';
import { LoadingService, NotificationService } from '../../../../../../../core/services';
import { environment } from '../../../../../../../../environments/environment';
import { Trip } from '../../../../../../../core/interfaces/vehicle.interface';
import { DEFAULT_COL_DEF } from '../../../../../../../core/constants';
import { DateFormat, TimeZone } from '../../../../../../../core/enums';
import { handleErrors } from '../../../../../../../core/helpers';

@Component({
  selector: 'app-modal-trip-details',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatIconModule,
    TranslateModule,
    AgGridCustomComponent,
    CurrentLocationComponent,
  ],
  providers: [VehicleService, NotificationService, DatePipe],
  templateUrl: './modal-trip-details.component.html',
  styleUrls: ['./modal-trip-details.component.scss'],
})
export class ModalTripDetailsComponent implements OnInit {
  data = inject(MAT_DIALOG_DATA);
  dialogRef = inject(MatDialogRef<DiagnosticTroubleCodeDetailComponent>);
  loadingService = inject(LoadingService);
  vehicleService = inject(VehicleService);
  notificationService = inject(NotificationService);
  translateService = inject(TranslateService);
  datePipe = inject(DatePipe);

  subscription = new Subscription();

  prefixImg = environment.OCC_BASE_URL?.slice(0, -1);

  rowData: Trip[] = [];
  vehicle: any;
  flightPlanCoordinates: any;
  defaultColDef = {
    ...DEFAULT_COL_DEF,
    suppressRowHoverHighlight: true,
    valueFormatter: (params) => (params.value ? params.value : '-'),
  };
  tripDetail: Trip;

  colDefs = [
    {
      headerName: this.translateService.instant('vehicle.trip.period'),
      headerValueGetter: () =>
        this.translateService.instant('vehicle.trip.period'),
      field: 'period',
      flex: 1.5,
      wrapText: true,
      autoHeight: true,
      cellClass: 'cell-word-wrap',
      cellRenderer: (params) => {
        return `<span>${
          this.data?.startTime
            ? this.datePipe.transform(
                this.data?.startTime,
                DateFormat.FullDate,
                TimeZone.UTC8
              )
            : '-'
        }</span> - <span>${
          this.data?.endTime
            ? this.datePipe.transform(
                this.data?.endTime,
                DateFormat.FullDate,
                TimeZone.UTC8
              )
            : '-'
        }</span>`;
      },
    },
    {
      headerName: this.translateService.instant('vehicle.trip.duration'),
      headerValueGetter: () =>
        this.translateService.instant('vehicle.trip.duration'),
      field: 'totalTravelTime',
      flex: 1,
      cellRenderer: () => this.data?.totalTravelTime,
    },
    {
      headerName: this.translateService.instant('vehicle.trip.distance'),
      headerValueGetter: () =>
        this.translateService.instant('vehicle.trip.distance'),
      field: 'totalDistance',
      flex: 1,
      cellRenderer: () => {
        return this.data?.totalDistance
          ? `${this.data?.totalDistance} km`
          : '-';
      },
    },
    {
      headerName: this.translateService.instant('vehicle.trip.fuelConsumption'),
      headerValueGetter: () =>
        this.translateService.instant('vehicle.trip.fuelConsumption'),
      field: 'fuelConsumption',
      flex: 1,
      cellRenderer: () => {
        return this.data?.fuelConsumption || '-';
      },
    },
  ];

  ngOnInit(): void {
    this.getTripDetails();
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  onCancel() {
    this.dialogRef.close();
  }

  getTripDetails() {
    const time = this.data?.totalTravelTime?.match(/\d+/g);
    const isHour = this.data?.totalTravelTime?.includes('hrs');
    this.loadingService.showLoader();
    const payload = {
      id: this.data?.id,
      fuelConsumption: this.data?.fuelConsumption?.replace(' liters', ''),
      totalDistance: this.data?.totalDistance,
      drivingTimeHour: isHour
        ? time
          ? parseInt(time[0], 10)?.toString()
          : null
        : null,
      drivingTimeMinutes: time
        ? parseInt(time[isHour ? 1 : 0], 10)?.toString()
        : null,
    };
    this.subscription.add(
      this.vehicleService
        .getTripDetails(this.data.vin, this.data.id, payload)
        .subscribe(
          (res: Trip) => {
            if (res) {
              this.rowData = [{ ...res }];
              this.tripDetail = res;

              const startLocation = res?.igOffGps
                ? this.parseGpsData(res?.igOffGps)
                : {};
              const endLocation = res?.igOnGps
                ? this.parseGpsData(res?.igOnGps)
                : null;

              this.vehicle = {
                latitude: startLocation?.lat,
                longitude: startLocation.lng,
              };
              this.flightPlanCoordinates = [startLocation, endLocation];
            }
            this.loadingService.hideLoader();
          },
          (err) => {
            handleErrors(err, this.notificationService);
            this.loadingService.hideLoader();
          }
        )
    );
  }

  parseGpsData(text: any) {
    return JSON.parse(text?.replace(/(\w+):/g, '"$1":')) || {};
  }
}
