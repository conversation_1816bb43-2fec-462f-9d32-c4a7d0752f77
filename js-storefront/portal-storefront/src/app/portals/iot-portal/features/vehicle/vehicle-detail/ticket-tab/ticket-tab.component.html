<app-loading *ngIf="isLoading"></app-loading>

<div class="ticket-tab">
  <div class="header-section">
    <div class="ticket-tab__title">
      <mat-icon svgIcon="ic-ticket"></mat-icon>
      {{ "vehicle.ticket.title" | translate }}
    </div>
    <button
      *ngIf="
        userService.isHasPermission([PERMISSIONS_CODE.ECARE_TICKET_CREATE])
      "
      class="btn-primary"
      (click)="createTicket()"
    >
      {{ "tickets.createTicket" | translate }}
    </button>
  </div>
  <form
    [formGroup]="ticketFormSearch"
    (ngSubmit)="onSearch()"
    class="vehicle-model-section__form"
  >
    <div class="vehicle-model-section__form-group">
      <app-form-group
        [label]="'vehicle.ticket.ticketId' | translate"
        [control]="txtSearchId"
        [placeholder]="
          'vehicle.ticket.enterHereToSearch' | translate
        "
        controlId="modelSalesCode"
        class="vehicle-model-section__input"
      ></app-form-group>
      <app-form-group
      [label]="'ticket.ticketTitle' | translate"
      [control]="txtSearchTitle"
      [placeholder]="
        'vehicle.ticket.enterHereToSearch' | translate
      "
      controlId="modelSalesCode"
      class="vehicle-model-section__input"
    ></app-form-group>

      <button
        type="submit"
        class="btn btn--primary vehicle-model-section__button"
      >
        {{ "common.search" | translate }}
      </button>
    </div>
  </form>
  @if (rowData?.length > 0) {
  <app-ag-grid-custom
    [rowData]="rowData"
    [colDefs]="colDefs"
    [defaultColDef]="defaultColDef"
    [isPaging]="true"
    [pagingInfo]="pagingInfo"
    [isShowActionExport]="false"
    (onPageChange)="onPageChange($event)"
    (changeItemPerPage)="changeItemPerPage($event)"
  >
  </app-ag-grid-custom>
  } @else {
  <div class="no-data">
    {{ isSearch ?("vehicle.ticket.noDataFound" | translate ) : ("vehicle.ticket.noData" | translate ) }}
  </div>
  }
</div>
