<div class="service-history-tab">

  <div class="service-history-tab__title">
    <mat-icon svgIcon="ic-loan-widget"></mat-icon>
    {{ "vehicle.serviceHistory.title" | translate }}
  </div>
  <form
    [formGroup]="serviceHistoryFormSearch"
    (ngSubmit)="onSearch()"
    class="vehicle-model-section__form"
  >
    <div class="vehicle-model-section__form-group">
      <app-form-group
        [label]="'vehicle.serviceHistory.repairOrder' | translate"
        [control]="txtSearchOrder"
        [placeholder]="
          'enterHereToSearch' | translate
        "
        controlId="modelSalesCode"
        class="vehicle-model-section__input"
      ></app-form-group>
      <app-form-group
      [label]="'vehicle.serviceHistory.jobDes' | translate"
      [control]="txtSearchDes"
      [placeholder]="
        'enterHereToSearch' | translate
      "
      controlId="modelSalesCode"
      class="vehicle-model-section__input"
    ></app-form-group>

      <button
        type="submit"
        class="btn btn--primary vehicle-model-section__button"
      >
        {{ "common.search" | translate }}
      </button>
    </div>
  </form>
  @if (rowData && rowData?.length > 0) {
  <app-ag-grid-custom
    [rowData]="rowData"
    [colDefs]="colDefs"
    [defaultColDef]="defaultColDef"
    [isPaging]="true"
    [pagingInfo]="pagingInfo"
    [isShowActionExport]="false"
    (onPageChange)="onPageChange($event)"
    (changeItemPerPage)="changeItemPerPage($event)"
  >
  </app-ag-grid-custom>
  } @else {
  <div class="no-data">
    {{ isSearch ?("vehicle.serviceHistory.noDataFound" | translate ) : ("vehicle.serviceHistory.noData" | translate ) }}
  </div>
  }
</div>
