import { CommonModule } from '@angular/common';
import { Component, inject, Input, OnD<PERSON>roy, OnInit } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { Subscription } from 'rxjs';

import { TroubleCodeListComponent } from './trouble-code-list/trouble-code-list.component';
import { TroubleCodeDetailComponent } from './trouble-code-detail/trouble-code-detail.component';
import { TroubleCodeService } from '../../../../services';
import { LoadingService, NotificationService } from '../../../../../../core/services';

@Component({
  selector: 'app-trouble-code',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    TroubleCodeListComponent,
    TroubleCodeDetailComponent,
  ],
  templateUrl: './trouble-code.component.html',
  styleUrl: './trouble-code.component.scss',
  providers: [TroubleCodeService],
})
export class TroubleCodeComponent implements OnInit, OnDestroy {
  @Input() set id(value) {
    if (value) {
      this.setTroubleCodeId(value);
    }
  }

  notificationService = inject(NotificationService);
  translateService = inject(TranslateService);
  loadingService = inject(LoadingService);
  troubleCodeService = inject(TroubleCodeService);

  subscription = new Subscription();
  troubleCodeId: string;

  ngOnInit(): void {}

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  setTroubleCodeId(id: string): void {
    this.troubleCodeId = id;
  }
}
