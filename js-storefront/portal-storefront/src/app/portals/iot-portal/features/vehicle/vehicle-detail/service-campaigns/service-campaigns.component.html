<div class="service-campaigns">
  @if (rowData?.length > 0) {
  <div class="service-campaigns__title">
    <mat-icon svgIcon="ic-service-campaigns"></mat-icon>
    {{ "serviceCampaigns.serviceCampaigns" | translate }}
  </div>
  <app-ag-grid-custom
    [rowData]="rowData"
    [colDefs]="colDefs"
    [defaultColDef]="defaultColDef"
    [isPaging]="true"
    [pagingInfo]="pagingInfo"
    [isShowActionExport]="false"
    (onPageChange)="onPageChange($event)"
    (changeItemPerPage)="changeItemPerPage($event)"
  >
  </app-ag-grid-custom>
  } @else {
  <div class="no-data">
    {{ "serviceCampaigns.noServiceCampaigns" | translate }}
  </div>
  }
</div>
