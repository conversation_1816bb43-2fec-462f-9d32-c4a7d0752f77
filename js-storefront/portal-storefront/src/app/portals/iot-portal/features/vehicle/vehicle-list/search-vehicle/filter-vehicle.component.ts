import { Component, EventEmitter, inject, Input, Output } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { FormGroupComponent } from '../../../../../../core/shared';
import { ScreenSizeService, UserService } from '../../../../../../core/services';
import { PERMISSIONS_CODE } from '../../../../../../core/constants';

@Component({
  selector: 'app-filter-vehicle',
  templateUrl: './filter-vehicle.component.html',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    FormGroupComponent,
    TranslateModule,
  ],
  providers: [ScreenSizeService],
  standalone: true,
})
export class FilterVehicleComponent {
  private _form: FormGroup;

  @Input() set form(value: FormGroup) {
    if (value) {
      this._form = value;
    } else {
      console.error(
        'FilterVehicleComponent requires a FormGroup to function correctly.'
      );
    }
  }
  get form(): FormGroup {
    return this._form;
  }

  @Output() searchVehicle = new EventEmitter<void>();
  screenSizeService = inject(ScreenSizeService);
  userService = inject(UserService);
  
  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;
  isOpenFilter = false;

  getControl(controlName: string): FormControl {
    const control = this.form.get(controlName);
    return control as FormControl;
  }
  search(): void {
    this.searchVehicle.emit();
  }
}
