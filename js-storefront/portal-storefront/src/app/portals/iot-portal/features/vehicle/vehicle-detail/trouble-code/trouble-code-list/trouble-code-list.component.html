<div class="trouble-code-tab__title">
  <div>
    <mat-icon svgIcon="ic-dtc"></mat-icon>
    {{ "troubleCode.diagnosticTroubleCode" | translate }}
  </div>
  <button class="btn-primary" (click)="requestLastestDtc()">
    {{ "troubleCode.requestLastestDtc" | translate }}
  </button>
</div>
<form class="trouble-code-tab__filter" [formGroup]="form">
  <app-form-group
    [label]="'troubleCode.nodeName' | translate"
    [control]="form.controls.nodeName"
    [placeholder]="'troubleCode.enterNodeName' | translate"
  ></app-form-group>

  <app-form-group
    [label]="'troubleCode.systemName' | translate"
    [control]="form.controls.systemName"
    [placeholder]="'troubleCode.enterSystemName' | translate"
  ></app-form-group>

  <app-form-group
    [label]="'troubleCode.dtc' | translate"
    [control]="form.controls.dtc"
    [placeholder]="'troubleCode.enterDTC' | translate"
  ></app-form-group>

  <button type="submit" class="btn-tertiary search" (click)="search()">
    {{ "common.search" | translate }}
  </button>
</form>
@if (rowData && rowData?.length > 0) {
<app-ag-grid-custom
  [rowData]="rowData"
  [colDefs]="colDefs"
  [defaultColDef]="defaultColDef"
  [onlyTable]="true"
>
</app-ag-grid-custom>
} @else {
<div class="no-data">
  {{ "troubleCode.noData" | translate }}
</div>
}
