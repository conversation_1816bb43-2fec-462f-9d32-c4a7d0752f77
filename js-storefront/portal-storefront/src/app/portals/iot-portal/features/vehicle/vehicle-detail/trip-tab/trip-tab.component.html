<div class="trip-tab">
  <div class="header-section">
    <div class="trip-tab__title">
      <mat-icon svgIcon="ic-trip"></mat-icon>
      {{ "vehicle.trip.title" | translate }}
    </div>
  </div>
  <form
    [formGroup]="tripFormSearch"
    (ngSubmit)="onSearch()"
    class="vehicle-model-section__form"
  >
    <div class="vehicle-model-section__form-group">
      <app-date-form-group
        [label]="'vehicle.trip.searchTripEndDate' | translate"
        [control]="txtSearch"
        [preventKeyboard]="true"
        controlId="searchTripEndDate"
        class="vehicle-model-section__input"
      ></app-date-form-group>

      <button
        type="submit"
        class="btn btn--primary vehicle-model-section__button"
      >
        {{ "common.search" | translate }}
      </button>
    </div>
  </form>
  @if (rowData?.length > 0) {
  <app-ag-grid-custom
    [rowData]="rowData"
    [colDefs]="colDefs"
    [defaultColDef]="defaultColDef"
    [onlyTable]="true"
  >
  </app-ag-grid-custom>
  } @else {
  <div class="no-data">
    {{ isSearch ? ("vehicle.trip.noDataFound" | translate) : ("vehicle.trip.noData" | translate) }}
  </div>
  }
</div>
