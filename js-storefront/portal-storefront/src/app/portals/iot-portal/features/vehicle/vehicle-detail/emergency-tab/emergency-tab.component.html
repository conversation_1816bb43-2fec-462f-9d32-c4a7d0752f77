<div class="emergency-tab">
  <div class="emergency-tab__title">
    <mat-icon svgIcon="ic-emergency"></mat-icon>
    {{ "vehicle.emergency.emergencies" | translate }}
  </div>
  <form
    [formGroup]="emergencyFormSearch"
    (ngSubmit)="onSearch()"
    class="vehicle-model-section__form"
  >
    <div class="vehicle-model-section__form-group">
      <app-dropdown-form-group
        [label]="'vehicle.emergency.emergencyType' | translate"
        [control]="txtSearch"
        [options]="emergencyTypeOption"
        class="vehicle-model-section__input"
        (enter)="onSearch()"
      ></app-dropdown-form-group>

      <button
        type="submit"
        class="btn btn--primary vehicle-model-section__button"
      >
        {{ "common.search" | translate }}
      </button>
    </div>
  </form>
  @if (rowData && rowData?.length > 0) {
  <app-ag-grid-custom
    [rowData]="rowData"
    [colDefs]="colDefs"
    [defaultColDef]="defaultColDef"
    [isPaging]="true"
    [pagingInfo]="pagingInfo"
    [isShowActionExport]="false"
    (onPageChange)="onPageChange($event)"
    (changeItemPerPage)="changeItemPerPage($event)"
  >
  </app-ag-grid-custom>
  } @else {
  <div class="no-data">
    {{
      isSearch
        ? ("vehicle.emergency.noDataFound" | translate)
        : ("vehicle.emergency.noData" | translate)
    }}
  </div>
  }
</div>
