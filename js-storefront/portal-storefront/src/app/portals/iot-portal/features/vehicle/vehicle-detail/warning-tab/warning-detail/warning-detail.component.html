<div class="section-warning__header">
  <mat-icon class="medium-icon" svgIcon="ic-warning"></mat-icon>
  <span>{{ "vehicle.tab.warnings" | translate }}</span>
</div>

<button class="back-to-list" (click)="backToList()">
  <mat-icon svgIcon="ic-back-to-list" class="small-icon"></mat-icon>
  <span>{{ "common.backToList" | translate }}</span>
</button>
<div class="detail-wrapper mt30">
  <div class="detail-block">
    <div class="detail-header">{{ "vehicle.warningTab.overview" | translate }}</div>
    <div class="detail-content">
      <div class="detail-row">
        <div class="detail-element">
          <div class="detail-label">{{ "vehicle.warningTab.header.warningName" | translate }}</div>
          <div class="detail-value">{{ warningDetail.name }}</div>
        </div>
        <div class="detail-element">
          <div class="detail-label">{{ "vehicle.warningTab.header.relatedTicket" | translate }}</div>
          <div class="detail-value">
            @if (warningDetail.relatedTicketId === '-' && warningDetail.relatedTicketTitle === '-') {
              -
            } @else {
              <a [routerLink]="['/tickets', warningDetail.relatedTicketId]">{{ warningDetail.relatedTicketId }}</a> <br> {{ warningDetail.relatedTicketTitle }}
            }
          </div>
        </div>
        <div class="detail-element">
          <div class="detail-label">{{ "vehicle.warningTab.header.priority" | translate }}</div>
          <div class="detail-value" [innerHTML]="warningDetail.priority.name"></div>
        </div>
        <div class="detail-element">
          <div class="detail-label">{{ "vehicle.warningTab.header.status" | translate }}</div>
          <div class="detail-value" [innerHTML]="warningDetail.status.name"></div>
        </div>
        <div class="detail-element">
          <div class="detail-label">{{ "vehicle.warningTab.header.occurredTime" | translate }}</div>
          <div class="detail-value">{{ warningDetail?.timestamp || '-' }}</div>
        </div>
      </div>
    </div>
  </div>
  <div class="detail-block">
    <div class="detail-header">{{ "vehicle.warningTab.details" | translate }}</div>
    <div class="detail-content">
      <div class="detail-row">
        <div class="detail-element">
          <div class="detail-label">{{ "vehicle.warningTab.code" | translate }}</div>
          <div class="detail-value">{{ warningDetail.code }}</div>
        </div>
        <div class="detail-element">
          <div class="detail-label">{{ "vehicle.warningTab.type" | translate }}</div>
          <div class="detail-value">{{ warningDetail.warningType }}</div>
        </div>
        <div class="detail-element">
          <div class="detail-label">{{ "vehicle.warningTab.details" | translate }}</div>
          <div class="detail-value">{{ warningDetail.details }}</div>
        </div>
      </div>
      <div class="detail-row">
        <div class="detail-element">
          <div class="detail-label">{{ "vehicle.warningTab.mid" | translate }}</div>
          <div class="detail-value">{{ warningDetail.mid }}</div>
        </div>
        <div class="detail-element">
          <div class="detail-label">{{ "vehicle.warningTab.safeCondition" | translate }}</div>
          <div class="detail-value">{{ warningDetail.safeCondition }}</div>
        </div>
        <div class="detail-element">
          <div class="detail-label">{{ "vehicle.warningTab.actionToBeRequestedFromTheCustomer" | translate }}</div>
          <div class="detail-value">{{ warningDetail.customerAction }}</div>
        </div>
      </div>
      <div class="detail-row">
        <div class="detail-element">
          <div class="detail-label">{{ "vehicle.warningTab.responseLeadTime" | translate }}</div>
          <div class="detail-value">{{ warningDetail.responseLeadTime }}</div>
        </div>
        <div class="detail-element">
          <div class="detail-label">{{ "vehicle.warningTab.vehicleCondition" | translate }}</div>
          <div class="detail-value">
            @if (warningDetail.vehicleCondition === true) {
              {{ "common.yes" | translate }}
            } @else {
              {{ "common.no" | translate }}
            }
          </div>
        </div>
        <div class="detail-element">
          <div class="detail-label">{{ "vehicle.warningTab.requiredDealerVisit" | translate }}</div>
          <div class="detail-value">
            @if (warningDetail.requiredDealerVisit === true) {
              {{ "common.yes" | translate }}
            } @else {
              {{ "common.no" | translate }}
            }
          </div>
        </div>
      </div>
      <div class="detail-row">
        <div class="detail-element">
          <div class="detail-label">{{ "vehicle.warningTab.notifyCustomer" | translate }}</div>
          <div class="detail-value">
            @if (warningDetail.notifyCustomer === true) {
              {{ "common.yes" | translate }}
            } @else {
              {{ "common.no" | translate }}
            }
          </div>
        </div>
        <div class="detail-element">
          <div class="detail-label">{{ "vehicle.warningTab.callTheCustomer" | translate }}​</div>
          <div class="detail-value">
            @if (warningDetail.callCustomer === true) {
              {{ "common.yes" | translate }}
            } @else {
              {{ "common.no" | translate }}
            }
          </div>
        </div>
        <div class="detail-element">
          <div class="detail-label"></div>
          <div class="detail-value"></div>
        </div>
      </div>
      <div class="detail-row">
        <div class="detail-element">
          <div class="detail-label">{{ "vehicle.warningTab.notificationPrompt1" | translate }}</div>
          <div class="detail-value">{{ warningDetail.notificationPromptTitle }}</div>
        </div>
        <div class="detail-element">
          <div class="detail-label">{{ "vehicle.warningTab.notificationPrompt2" | translate }}</div>
          <div class="detail-value">{{ warningDetail.notificationPromptDesc }}</div>
        </div>
        <div class="detail-element">
          <div class="detail-label">{{ "vehicle.warningTab.notificationPrompt3" | translate }}</div>
          <div class="detail-value">{{ warningDetail.notificationPromptCustAction }}</div>
        </div>
      </div>
    </div>
  </div>
</div>