@import "../../../../../../../../styles/abstracts/variables";

:host ::ng-deep {
  .view-map {
    margin-bottom: 40px;

    #map {
      min-height: 400px !important;
      height: auto !important;
    }
  }

  .icon-title {
    ::ng-deep svg {
      path {
        fill: $text-color-5;
      }
    }
  }

  .title-dialog {
    margin-bottom: 35px;
    margin-top: 40px;
  }

  .container-dialog {
    padding: 0 45px;
    color: unset !important;
    display: flex;
    flex-direction: column;
    gap: 35px;

    &__section {
      &__title {
        display: flex;
        gap: 15px;

        font-weight: 600;
        font-size: 16px;

        mat-icon {
          width: 26px;
          height: 26px;

          ::ng-deep svg {
            path {
              fill: #cccccc;
            }
          }
        }
      }

      &__list {
        display: flex;
        flex-direction: column;
        gap: 5px;
        font-size: 16px;
        margin-left: 25px;
        margin-top: 5px;

        li {
          color: var(--Primary-Black, #101010);
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
        }
      }
    }

    .no-data {
      padding-bottom: 45px;
      font-family: $text-font-stack !important;
    }

    .img-map {
      text-align: center;
    }
  }

  .action-dialog {
    padding: 35px 40px 40px;
  }

  app-ag-grid-custom {

    .custom-grid .ag-row:nth-child(odd),
    app-ag-grid .custom-grid .ag-row:nth-child(odd) {
      background-color: #FFFFFF !important;
    }

    .ag-row-hover {
      &:before {
        background-color: #FFFFFF !important;
      }
    }

    .custom-grid {
      .ag-cell {
        line-height: 20px !important;
      }
      .ag-cell-wrap-text {
        padding: 0;
        display: block;
      }
    }
  }
}