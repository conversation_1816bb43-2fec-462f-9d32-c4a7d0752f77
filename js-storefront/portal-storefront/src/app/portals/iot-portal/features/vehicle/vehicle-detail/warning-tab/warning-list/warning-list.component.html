@if (!isSearching && rowData?.length === 0 && !loadingService.isLoading) {
  <div class="section-no-data">
    {{ "vehicle.warningTab.noWarnings" | translate }}
  </div>
} @else {
  <div class="section-warning__header">
    <mat-icon class="medium-icon" svgIcon="ic-warning"></mat-icon>
    <span>{{ "vehicle.tab.warnings" | translate }}</span>
  </div>
  <form
    [formGroup]="warningFormSearch"
    (ngSubmit)="onSearch()"
    class="vehicle-model-section__form"
  >
    <div class="vehicle-model-section__form-group">
      <app-form-group
        [label]="'vehicle.warningTab.name' | translate"
        [control]="warningNameSearch"
        [placeholder]="'vehicle.warningTab.placeholder' | translate"
        controlId="warningName"
        class="vehicle-model-section__input"
      ></app-form-group>
      <button
        type="submit"
        class="btn btn--primary vehicle-model-section__button"
      >
        {{ "common.search" | translate }}
      </button>
    </div>
  </form>
  @if (rowData?.length > 0) {
    <app-ag-grid-custom
      [isPaging]="true"
      [pagingInfo]="pagingInfo"
      [rowData]="rowData"
      [colDefs]="colDefs"
      [isShowActionExport]="false"
      [defaultColDef]="defaultColDef"
      [isTextWrap]="true"
      (changeItemPerPage)="onResultsPerPageChange($event)"
      (onPageChange)="onPageChange($event)"
    ></app-ag-grid-custom>
  } @else {
    <div class="no-data">
      {{ "vehicle.warningTab.noDataFound" | translate }}
    </div>
  }
}
