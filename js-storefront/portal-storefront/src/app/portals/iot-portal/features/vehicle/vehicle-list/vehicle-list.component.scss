:host {

    min-height: calc(100vh - 75px);
    display: block;

    ::ng-deep app-ag-grid-custom .custom-grid .ag-header-cell.multi-line-header,
    ::ng-deep app-ag-grid .custom-grid .ag-header-cell.multi-line-header {
        .ag-header-cell-text {
            white-space: pre-line;
            word-wrap: break-word;
        }
    }

    ::ng-deep app-ag-grid-custom .custom-grid .ag-header-row,
    ::ng-deep app-ag-grid .custom-grid .ag-header-row {
        height: 68px;
    }

    ::ng-deep .custom-header {
        margin-bottom: 10px;
    }

    ::ng-deep .truncate {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 150px;
    }

    .vehicle-list-table {
        ::ng-deep {
            .custom-grid {
                .ag-cell {
                    line-height: 24px;
                    padding: 15px;

                    span {
                        p {
                            margin: 0;
                        }
                    }
                }
            }
        }
    }
}