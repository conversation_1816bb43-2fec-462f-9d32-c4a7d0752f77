<button class="back-to-list" (click)="backToList.emit('')">
  <mat-icon svgIcon="ic-back-to-list" class="small-icon"></mat-icon>
  <span>{{ "troubleCode.backToDTC" | translate }}</span>
</button>

<div class="trouble-code-detail">
  <div class="trouble-code-detail__type">
    <p>{{ "troubleCode.detail.freezeFrameData" | translate }}</p>
    <p>
      {{ "troubleCode.detail.triggerType" | translate }}
      {{ troubleCodeDetail?.triggerType }}
    </p>
  </div>

  <div class="trouble-code-detail__detail">
    @if(rowData?.length > 0) {
    <app-ag-grid-custom
      [rowData]="rowData"
      [colDefs]="colDefs"
      [defaultColDef]="defaultColDef"
      [isPaging]="false"
      [isShowActionExport]="false"
      [onlyTable]="true"
    >
    </app-ag-grid-custom>
    }
  </div>

  <div class="trouble-code-detail__status">
    <div class="trouble-code-detail__status__item">
      <p class="trouble-code-detail__status__item--label">
        {{ "troubleCode.detail.dataCollectionStatus" | translate }}
      </p>
      <p class="trouble-code-detail__status__item--value">
        {{ troubleCodeDetail?.collectionStatus || "-" }}
      </p>
    </div>

    <div class="trouble-code-detail__status__item">
      <p class="trouble-code-detail__status__item--label">
        {{ "troubleCode.detail.lastRecordReceivedTime" | translate }}
      </p>
      <p class="trouble-code-detail__status__item--value">
        {{ troubleCodeDetail?.receiveLastNotificationAt || "-" }}
      </p>
    </div>
  </div>
</div>
