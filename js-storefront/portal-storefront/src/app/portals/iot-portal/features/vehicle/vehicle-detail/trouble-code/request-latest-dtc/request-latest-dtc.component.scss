:host {
  .container-dialog {
    form {
      display: flex;
      flex-direction: column;
    }

    .date-time {
      &__fields {
        display: flex;
        align-items: flex-end;
        gap: 20px;
        align-items: flex-end;

        > * {
          width: 100%;
        }

        ::ng-deep {
          app-date-form-group {
            margin-bottom: -6px;
            .form-group {
              margin-bottom: 0;
            }
          }

          mat-icon {
            width: 22px !important;
            height: 22px !important;
          }
        }
      }

      &__error {
        color: #ff0000;
        font-size: 12px;
        margin-top: 4px;
      }
    }
  }
}
