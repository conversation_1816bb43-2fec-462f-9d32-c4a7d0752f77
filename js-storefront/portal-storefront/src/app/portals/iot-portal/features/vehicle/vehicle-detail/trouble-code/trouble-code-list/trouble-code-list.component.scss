@import "../../../../../../../../styles/abstracts/variables";
@import "../../../../../../../../styles/abstracts/mixins";

:host {
    .trouble-code-tab {
        &__title {
          display: flex;
          justify-content: space-between;
          margin-bottom: 30px;
    
          > div {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 22px;
            font-weight: 600;
    
            mat-icon {
              width: 32px;
              height: 32px;
            }
          }
    
          .btn-primary {
            text-transform: uppercase;
          }
        }
    
        &__filter {
          display: flex;
          justify-content: space-around;
          gap: 20px;
          margin-bottom: 15px;
    
          > * {
            width: 100%;
          }
          .search {
            height: 40px;
            margin-top: 20px;
          }
        }
    
        .no-data {
          color: $text-color-4;
          font-size: 20px;
          font-weight: 600;
          text-align: center;
          padding: 30px;
        }
      }

      app-ag-grid-custom {
        ::ng-deep {
          .custom-grid {
            .hide-action {
              .ag-cell-wrapper {
                display: none;
              }
            }
          }

          .border-right {
            border-right: 1px solid #cccccc;
            pointer-events: none;
          }

          .border-bottom {
            border-bottom: 1px solid #cccccc;
          }

          .ag-spanned-cell-wrapper {
            border-bottom: 1px solid white;
          }

          .row-odd {
            background-color: #ffffff!important;
          }
  
          .row-even {
            background-color: #f5f5f5!important;
          }
        }
      }
}