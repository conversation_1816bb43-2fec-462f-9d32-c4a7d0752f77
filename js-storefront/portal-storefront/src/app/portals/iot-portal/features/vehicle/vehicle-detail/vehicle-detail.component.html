<div class="vehicle-details container-details">
  <div class="container-details__header p-b-0">
    <app-breadcrumb-with-label
      [breadcrumbs]="['Vehicles', vehicleId || '']"
      [breadcrumbLinks]="['/vehicles', '/vehicles/' + (vehicleId || '')]"
      [label]="'Vehicle Details'"
    >
    </app-breadcrumb-with-label>
  </div>

  <mat-tab-group
    class="tabs-group p-t-0"
    mat-align-tabs="start"
    animationDuration="0ms"
    [selectedIndex]="currentTabIndex"
    (selectedTabChange)="onTabChange($event)"
  >
    <mat-tab [label]="vehicleDetailsTab.Overview">
      @if (curTabName === vehicleDetailsTab.Overview) {
      <app-overview-tab [vin]="vehicleId" (waringId)="onWarningDetails($event)" (viewFFD)="viewFFD($event)" (vehilceInfo)="getVehilceInfo($event)"></app-overview-tab>
      }
    </mat-tab>

    @if (userService.isHasPermission([PERMISSIONS_CODE.ECARE_TICKET_LIST_VIEW])) {
      <mat-tab [label]="vehicleDetailsTab.Tickets">
        @if (curTabName === vehicleDetailsTab.Tickets) { 
          <app-ticket-tab [vin]="vehicleId"></app-ticket-tab>
         }
      </mat-tab>
    }
    
    @if (userService.isHasPermission([PERMISSIONS_CODE.ECARE_WARNING_VIEW])) {
      <mat-tab [label]="vehicleDetailsTab.Warnings">
        @if (curTabName === vehicleDetailsTab.Warnings) {
          <app-warning-tab [warningOverviewID]="warningId"></app-warning-tab>
        }
      </mat-tab>
    }

    @if (userService.isHasPermission([PERMISSIONS_CODE.ECARE_EMERGENCY_VIEW])) {
      <mat-tab [label]="vehicleDetailsTab.Emergencies">
        @if (curTabName === vehicleDetailsTab.Emergencies) { 
          <app-emergency-tab [vin]="vehicleId"></app-emergency-tab>
        }
      </mat-tab>
    }

    @if (userService.isHasPermission([PERMISSIONS_CODE.ECARE_DTC_VIEW]) && vehicleInfo?.deviceType === DeviceManagementTab.GDCM) {
      <mat-tab [label]="vehicleDetailsTab.TroubleCode">
        @if (curTabName === vehicleDetailsTab.TroubleCode) { 
          <app-trouble-code [id]="ssrId"></app-trouble-code>
        }
      </mat-tab>
    }


    @if (userService.isHasPermission([PERMISSIONS_CODE.IOT_SUBSCRIPTION_LIST_VIEW])) {
      <mat-tab [label]="vehicleDetailsTab.Subscriptions">
        @if (curTabName === vehicleDetailsTab.Subscriptions) { 
          <app-subscription-tab [vin]="vehicleId"></app-subscription-tab>
        }
      </mat-tab>
    }

    @if (userService.isHasPermission([PERMISSIONS_CODE.ECARE_SERVICE_BOOKING_VIEW])) {
      <mat-tab [label]="vehicleDetailsTab.ServiceBooking">
        @if (curTabName === vehicleDetailsTab.ServiceBooking) { 
          <app-service-booking-tab [vin]="vehicleId"></app-service-booking-tab>
        }
      </mat-tab>
    }

    @if (userService.isHasPermission([PERMISSIONS_CODE.ECARE_TRIP_LIST_VIEW])) {
      <mat-tab [label]="vehicleDetailsTab.Trips">
        @if (curTabName === vehicleDetailsTab.Trips) {
          <app-trip-tab [vin]="vehicleId"></app-trip-tab>
        }
      </mat-tab>
    }

    @if (userService.isHasPermission([PERMISSIONS_CODE.ECARE_VEHICLE_IOT_VIEW])) {
      <mat-tab [label]="vehicleDetailsTab.ServiceHistory">
        @if (curTabName === vehicleDetailsTab.ServiceHistory) { 
          <app-service-history-tab [vin]="vehicleId"></app-service-history-tab>
        }
      </mat-tab>
    }

    @if (userService.isHasPermission([PERMISSIONS_CODE.ECARE_SSC_VIEW])) {
      <mat-tab [label]="vehicleDetailsTab.ServiceCampaigns">
        @if (curTabName === vehicleDetailsTab.ServiceCampaigns) {
        <app-service-campaigns [vin]="vehicleId"></app-service-campaigns>
        }
      </mat-tab>
    }

    @if (userService.isHasPermission([PERMISSIONS_CODE.IOT_DEVICE_SIM_LIST_VIEW])) {
      <mat-tab [label]="vehicleDetailsTab.Devices">
        @if (curTabName === vehicleDetailsTab.Devices) { 
          <app-devices-tab [vin]="vehicleId"></app-devices-tab>
        }
      </mat-tab>
    }
  </mat-tab-group>
</div>
