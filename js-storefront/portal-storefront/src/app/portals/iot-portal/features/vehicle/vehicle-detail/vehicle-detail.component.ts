import { Component, inject, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { MatTabsModule } from '@angular/material/tabs';
import { ServiceCampaignsComponent } from './service-campaigns/service-campaigns.component';

import { TranslateModule } from '@ngx-translate/core';
import { OverviewTabComponent } from './overview-tab/overview-tab.component';
import { WarningTabComponent } from './warning-tab/warning-tab.component';
import { EmergencyTabComponent } from './emergency-tab/emergency-tab.component';
import { ServiceBookingTabComponent } from './service-booking-tab/service-booking-tab.component';
import { SubscriptionTabComponent } from './subscription-tab/subscription-tab.component';
import { TicketTabComponent } from './ticket-tab/ticket-tab.component';
import { DevicesTabComponent } from "./devices-tab/devices-tab.component";
import { TripTabComponent } from './trip-tab/trip-tab.component';
import { ServiceHistoryTabComponent } from './service-history-tab/service-history-tab.component';
import { TroubleCodeComponent } from './trouble-code/trouble-code.component';
import { take } from 'rxjs';
import { BreadcrumbWithLabelComponent } from '../../../../../core/shared';
import { VehicleModelService, WarningService } from '../../../services';
import { NotificationService, UserService } from '../../../../../core/services';
import { DeviceManagementTab, VehicleDetailsTab } from '../../../enums';
import { DataStoreService } from '../../../../../core/services/data-store.service';
import { PERMISSIONS_CODE } from '../../../../../core/constants';
import { Vehicle } from '../../../interfaces';
@Component({
  selector: 'app-vehicle-detail',
  templateUrl: './vehicle-detail.component.html',
  styleUrl: './vehicle-detail.component.scss',
  standalone: true,
  imports: [
    MatTabsModule,
    TranslateModule,
    BreadcrumbWithLabelComponent,
    OverviewTabComponent,
    ServiceCampaignsComponent,
    WarningTabComponent,
    EmergencyTabComponent,
    ServiceBookingTabComponent,
    SubscriptionTabComponent,
    TicketTabComponent,
    DevicesTabComponent,
    TripTabComponent,
    ServiceHistoryTabComponent,
    TroubleCodeComponent
],
  providers: [VehicleModelService, WarningService, NotificationService]
})
export class VehicleDetailComponent implements OnInit {
  vehicleId!: string;
  vehicleDetailsTab = VehicleDetailsTab;
  curTabName: string = "Overview"; 
  currentTabIndex: number = 0;
  warningId: string = '';
  ssrId: string;
  vehicleInfo: Vehicle;
  private route = inject(ActivatedRoute);
  private dataStoreService = inject(DataStoreService);

  userService = inject(UserService);
  router = inject(Router);

  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;
  readonly DeviceManagementTab = DeviceManagementTab;

  ngOnInit() {
    this.route.paramMap.subscribe((params) => {
      this.vehicleId = params.get('id');
    });

    this.route.queryParams.pipe(take(1)).subscribe((params) => {
      const { selectedTab, ssrID } = params;

      if(selectedTab) {
        this.curTabName = selectedTab
        this.currentTabIndex = 4;
        this.ssrId = ssrID;
        this.router.navigate([], {
          relativeTo: this.route,
          queryParams: {
            selectedTab: null,
            ssrID: null
          },
          queryParamsHandling: 'merge',
          replaceUrl: true,
        });
      }
    });
  }

  ngOnDestroy() {
    this.dataStoreService.setDeviceType('');
  }
  onTabChange(event: any) {
    this.curTabName = event.tab.textLabel
    this.currentTabIndex = event.index;
  }

  onWarningDetails(warningId) {
    this.warningId = warningId;
    this.curTabName = this.vehicleDetailsTab.Warnings;
    this.currentTabIndex = this.mapIndexTab(this.vehicleDetailsTab.Warnings);
  }

  viewFFD(ssrId: string) {
    this.ssrId = ssrId;
    this.curTabName = this.vehicleDetailsTab.TroubleCode;
    this.currentTabIndex = this.mapIndexTab(this.vehicleDetailsTab.TroubleCode);
  }

  mapIndexTab(curTabName: VehicleDetailsTab): number {
    const tabMappings = [
      { permission: PERMISSIONS_CODE.EMPLOYEEGROUP, tab: this.vehicleDetailsTab.Overview },
      { permission: PERMISSIONS_CODE.ECARE_TICKET_LIST_VIEW, tab: this.vehicleDetailsTab.Tickets },
      { permission: PERMISSIONS_CODE.ECARE_WARNING_VIEW, tab: this.vehicleDetailsTab.Warnings },
      { permission: PERMISSIONS_CODE.ECARE_EMERGENCY_VIEW, tab: this.vehicleDetailsTab.Emergencies },
      { permission: PERMISSIONS_CODE.ECARE_DTC_VIEW, tab: this.vehicleDetailsTab.TroubleCode },
      { permission: PERMISSIONS_CODE.IOT_SUBSCRIPTION_LIST_VIEW, tab: this.vehicleDetailsTab.Subscriptions },
      { permission: PERMISSIONS_CODE.ECARE_SERVICE_BOOKING_VIEW, tab: this.vehicleDetailsTab.ServiceBooking },
      { permission: PERMISSIONS_CODE.ECARE_TRIP_LIST_VIEW, tab: this.vehicleDetailsTab.Trips },
      { permission: PERMISSIONS_CODE.ECARE_VEHICLE_IOT_VIEW, tab: this.vehicleDetailsTab.ServiceHistory },
      { permission: PERMISSIONS_CODE.ECARE_SSC_VIEW, tab: this.vehicleDetailsTab.ServiceCampaigns },
      { permission: PERMISSIONS_CODE.IOT_DEVICE_SIM_LIST_VIEW, tab: this.vehicleDetailsTab.Devices },
    ];
  
    const availableTabs = tabMappings
      .filter(mapping => !mapping.permission || this.userService.isHasPermission([mapping.permission]))
      .map(mapping => mapping.tab);
  
    return availableTabs.indexOf(curTabName);
  }

  getVehilceInfo(data: Vehicle): void {
    this.vehicleInfo = data;
  }
}
