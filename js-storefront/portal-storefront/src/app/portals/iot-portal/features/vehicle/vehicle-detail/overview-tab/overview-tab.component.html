<div class="overview-container">
  <div class="overview-container__left">
    @if (userService.isHasPermission([PERMISSIONS_CODE.ECARE_VEHICLE_IOT_VIEW])) {
      <div class="overview-container__box">
        <app-vehicle-info [vehicle]="vehicle" [vin]="vin"></app-vehicle-info>
      </div>
      <div class="overview-container__box">
        <app-current-location [setHideRefresh]="true" [vehicle]="vehicle" (refresh)="refreshCurrentLocation()"></app-current-location>
      </div>
    }
    
    <div *ngIf="userService.isHasPermission([PERMISSIONS_CODE.ECARE_PMS_VIEW])" class="overview-container__box">
      <app-pms-due [pmsData]="pmsData"></app-pms-due>
    </div>

    <div *ngIf="userService.isHasPermission([PERMISSIONS_CODE.ECARE_SSC_VIEW])" class="overview-container__box" [class.overview-container__box__ssc]="sscData?.length > 0">
      <app-ssc-due [data]="sscData"></app-ssc-due>
    </div>
    <div *ngIf="userService.isHasPermission([PERMISSIONS_CODE.ECARE_OWNER_VIEW])" class="overview-container__box">
      <app-owner-widget [data]="ownerData" [vin]="vin"></app-owner-widget>
    </div>
    <div *ngIf="userService.isHasPermission([PERMISSIONS_CODE.ECARE_OWNER_VIEW]) && ownershipVerificationData?.enabledOwnership" class="overview-container__box">
      <app-ownership-verification-widget [data]="ownershipVerificationData?.ownerVerificationHistory" [vin]="vin"></app-ownership-verification-widget>
    </div>
  </div>
  <div class="overview-container__right">
    <div *ngIf="userService.isHasPermission([PERMISSIONS_CODE.ECARE_VEHICLE_IOT_VIEW])" class="overview-container__box overview-container--maintenance-mode">
      <app-maintenance-mode [vehicle]="vehicle"></app-maintenance-mode>
    </div>

    <div *ngIf="userService.isHasPermission([PERMISSIONS_CODE.ECARE_TICKET_LIST_VIEW])" class="overview-container__box">
      <app-ticket-widget [data]="ticketData" [vehicle]="vehicle" [vin]="vin" (refreshTicket)="getTicketWidget(true)"></app-ticket-widget>
    </div>

    <div class="overview-container__box-haft">
      <div *ngIf="userService.isHasPermission([PERMISSIONS_CODE.ECARE_EMERGENCY_VIEW])" class="overview-container__box">
        <app-emergency [data]="emergencyData"></app-emergency>
      </div>
      <div *ngIf="userService.isHasPermission([PERMISSIONS_CODE.ECARE_WARNING_VIEW])" class="overview-container__box">
        <app-warning [data]="warningData" (warningId)="onWarningDetails($event)"></app-warning>
      </div>
    </div>

    <div class="overview-container__box-haft">
      <div *ngIf="userService.isHasPermission([PERMISSIONS_CODE.ECARE_TOWING_VIEW])" class="overview-container__box">
        <app-towing-service [vin]="vin" [towingServiceData]="towingServiceData" [hasActiveCustomerRelation]="vehicle?.hasActiveCustomerRelation" (refreshTowingDetails)="getTowingServiceDetails(true)"></app-towing-service>
      </div>
      <div *ngIf="userService.isHasPermission([PERMISSIONS_CODE.ECARE_SERVICE_BOOKING_VIEW])" class="overview-container__box overview-container--service-booking">
        <app-service-booking [vin]="vin" [vehicleMileage]="vehicle?.odo" [hasActiveCustomerRelation]="vehicle?.hasActiveCustomerRelation"></app-service-booking>
      </div>
    </div>
    <div *ngIf="userService.isHasPermission([PERMISSIONS_CODE.IOT_SUBSCRIPTION_LIST_VIEW])" class="overview-container__box">
      <app-subscription-widget [rowData]="rowDataSub"></app-subscription-widget>
    </div>
    <!-- TODO: Permissions -->
    @if (vehicle?.vehicleType === "B2B" && vehicle?.vehicleSubType === "CAL") {
      <div class="overview-container__box">
        <app-loan-widget [rowData]="rowLoanData" [vin]="vin" (refresh)="getLoanWidget(true)"></app-loan-widget>
      </div>
    }
    
    <div *ngIf="userService.isHasPermission([PERMISSIONS_CODE.IOT_DEVICE_SIM_LIST_VIEW])" class="overview-container__box">
      <app-device-widget [rowData]="rowData"></app-device-widget>
    </div>

    
    <!-- <div class="overview-container__box">
      <app-snap-shot-record [data]="sscData"></app-snap-shot-record>
    </div> -->

    @if(vehicle?.vin && vehicle?.deviceType === DeviceManagementTab.GDCM && userService.isHasPermission([PERMISSIONS_CODE.ECARE_DTC_VIEW]) ) {
      <div class="overview-container__box">
        <app-dtc-widget [vin]="vehicle?.vin" (onViewFFD)="viewFFD.emit($event)"></app-dtc-widget>
      </div>
    }

    <div class="overview-container__box">
      <app-history-log-widget [data]="historyLogData"></app-history-log-widget>
    </div>
  </div>
</div>
