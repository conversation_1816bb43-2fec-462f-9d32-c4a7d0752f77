import { Component, EventEmitter, Input, Output } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { WarningDetail } from '../../../../../interfaces';
import { DateFormat } from '../../../../../../../core/enums';
import { WarningService } from '../../../../../services';

@Component({
  selector: 'app-warning-detail',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    TranslateModule,
    RouterModule
  ],
  templateUrl: './warning-detail.component.html',
  styleUrl: './warning-detail.component.scss'
})
export class WarningDetailComponent {
  @Input() id: string;
  @Output() backToListEvent = new EventEmitter<string>();
  warningDetail: WarningDetail = this.warningService.getWarningDetailDefault();
  dateFormat = DateFormat;

  constructor(
    private warningService: WarningService,
  ) {}

  ngOnInit(): void {
    this.getWarningDetail();
  }

  getWarningDetail(): void {
    // Get warning detail by id
    this.warningService.getWarningDetail(this.id).subscribe((res) => {
      this.warningDetail = {
        ...this.warningService.getWarningDetailDefault(),
        ...res,
        priority: {
          code: res?.priority?.code || '-',
          name: this.warningService.getPriorityStyle(res.priority ? res.priority.code : '')
        },
        status: {
          code: res?.status?.code || '-',
          name: this.warningService.getStatusStyle(res.status ? res.status.code : '')
        },
      };
    });
  }

  backToList() {
    // Back to warning list by remove warningId
    this.backToListEvent.emit('');
  }
}
