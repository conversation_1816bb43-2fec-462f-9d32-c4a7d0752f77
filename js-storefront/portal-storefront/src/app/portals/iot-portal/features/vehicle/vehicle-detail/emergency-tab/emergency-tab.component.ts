import { CommonModule } from '@angular/common';
import { Component, inject, Input, OnInit } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { filter, Subscription } from 'rxjs';

import { FormGroup, FormControl, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActionCellCloseComponent } from './action-cell-close/action-cell-close.component';
import { MatDialog } from '@angular/material/dialog';
import { ModalCloseComponent } from './modal-close/modal-close.component';
import { IconModule } from '../../../../../../core/icon/icon.module';
import { LoadingComponent } from '../../../../../../layout/global/loading/loading.component';
import { AgGridCustomComponent, DropdownFormGroupComponent, FormGroupComponent } from '../../../../../../core/shared';
import { VehicleService } from '../../../../services';
import { LoadingService, NotificationService } from '../../../../../../core/services';
import { OptionDropdown, PagingInfo } from '../../../../../../core/interfaces';
import { DEFAULT_COL_DEF } from '../../../../../../core/constants';
import { handleErrors } from '../../../../../../core/helpers';
import { ActionModal } from '../../../../../../core/enums';
import { ResolutionEmergency } from '../../../../enums';
import { Emergency } from '../../../../../../core/interfaces/vehicle.interface';

@Component({
  selector: 'app-emergency-tab',
  standalone: true,
  imports: [
    CommonModule,
    IconModule,
    TranslateModule,
    LoadingComponent,
    AgGridCustomComponent,
    FormGroupComponent,
    ReactiveFormsModule,
    DropdownFormGroupComponent
  ],
  providers: [VehicleService, NotificationService],
  templateUrl: './emergency-tab.component.html',
  styleUrls: ['./emergency-tab.component.scss'],
})
export class EmergencyTabComponent implements OnInit {
  @Input() vin: string;
  vehicleService = inject(VehicleService);
  notificationService = inject(NotificationService);
  translateService = inject(TranslateService);
  loadingService = inject(LoadingService);
  dialog = inject(MatDialog);

  isSearch: boolean = false;
  subscription = new Subscription();

  rowData: Emergency[] = [];

  pagingInfo: PagingInfo = {
    totalItems: 0,
    currentPage: 0,
    numberOfPage: 10,
  };

  emergencyFormClose: FormGroup = new FormGroup({
    resolution: new FormControl('', [Validators.required]),
    comment: new FormControl(''),
  });
  emergencyFormSearch: FormGroup = new FormGroup({
    txtSearch: new FormControl(''),
  });

  emergencyTypeOption: OptionDropdown[];

  defaultColDef = {
    ...DEFAULT_COL_DEF,
    valueFormatter: (params) => (params.value ? params.value : '-'),
  };
  colDefs = [
    {
      headerName: this.translateService.instant(
        'vehicle.emergency.emergencyName'
      ),
      headerValueGetter: () => this.translateService.instant('vehicle.emergency.emergencyName'),
      flex: 1,
      autoHeight: true,
      wrapText: true,
      cellClass: 'cell-word-wrap',
      field: 'name',
    },
    {
      headerName: this.translateService.instant(
        'vehicle.emergency.relatedTicket'
      ),
      headerValueGetter: () => this.translateService.instant('vehicle.emergency.relatedTicket'),
      autoHeight: true,
      wrapText: true,
      flex: 1,
      field: 'relatedTicketId',
    },
    {
      headerName: this.translateService.instant('vehicle.emergency.location'),
      headerValueGetter: () => this.translateService.instant('vehicle.emergency.location'),
      flex: 1,
      autoHeight: true,
      wrapText: true,
      field: 'placeName',
    },
    {
      headerName: this.translateService.instant('vehicle.emergency.status'),
      headerValueGetter: () => this.translateService.instant('vehicle.emergency.status'),
      maxWidth: 100,
      minWidth: 100,
      field: 'status',
      cellRenderer: (params) => {
        return params?.value ? `<span>${params?.value?.name}</span>` : '-';
      },
      cellClassRules: {
        'value-tag': (params) => !!params?.value,
        'red-tag': (params) => params?.value?.code === 'ACTIVE',
      },
    },
    {
      headerName: this.translateService.instant(
        'vehicle.emergency.occurredTime'
      ),
      headerValueGetter: () => this.translateService.instant('vehicle.emergency.occurredTime'),
      maxWidth: 190,
      minWidth: 190,
      field: 'timestamp',
    },
    {
      headerName: this.translateService.instant('vehicle.emergency.remarks'),
      headerValueGetter: () => this.translateService.instant('vehicle.emergency.remarks'),
      flex: 1,
      field: 'remarks',
      autoHeight: true,
      wrapText: true,
    },
    {
      maxWidth: 100,
      minWidth: 100,
      cellRenderer: ActionCellCloseComponent,
      cellRendererParams: {
        onClick: (data: Emergency) => this.openModalClose(data),
      },
      cellClass: 'action-grid',
      sortable: false,
    },
  ];

  ngOnInit(): void {
    this.getEmergencyType();
    this.getEmergencyList();
  }

  ngOnDestroy() {
    this.subscription && this.subscription.unsubscribe();
  }

  public get txtSearch(): FormControl {
    return this.emergencyFormSearch.get('txtSearch') as FormControl;
  }

  onSearch(): void {
    this.pagingInfo.currentPage = 0;
    this.isSearch = true;
    this.getEmergencyList();
  }

  getEmergencyType(): void {
    this.loadingService.showLoader();
    this.subscription.add(
      this.vehicleService.getEmergenciesType().subscribe((response) => {
        this.emergencyTypeOption = response;
      })
    );
  }

  getEmergencyList(): void {
    const params = {
      vin: this.vin,
      type: this.emergencyFormSearch.value.txtSearch,
      currentPage: this.pagingInfo.currentPage,
      pageSize: this.pagingInfo.numberOfPage,
    };
    this.loadingService.showLoader();
    this.subscription.add(
      this.vehicleService.getEmergencyList(params).subscribe(
        (response) => {
          this.loadingService.hideLoader();
          this.rowData = response?.items;
          this.pagingInfo.totalItems = response?.pagination?.totalResults;
        },
        (error) => {
          this.loadingService.hideLoader();
          handleErrors(error, this.notificationService);
        }
      )
    );
  }

  onPageChange(value: number): void {
    this.pagingInfo.currentPage = value;
    this.getEmergencyList();
  }

  changeItemPerPage(item) {
    this.pagingInfo.numberOfPage = item;
    this.pagingInfo.currentPage = 0;
    this.getEmergencyList();
  }

  openModalClose(data: Emergency) {
    this.emergencyFormClose.reset();
    this.emergencyFormClose.patchValue({ resolution: ResolutionEmergency.all });
    const dialogRef = this.dialog.open(ModalCloseComponent, {
      width: '600px',
      data: {
        title: this.translateService.instant('vehicle.emergency.modal.title'),
        confirmMsg: this.translateService.instant(
          'vehicle.emergency.modal.subTitle'
        ),
        vin: this.vin,
        ticketId: data.relatedTicketId,
        emergencyFormClose: this.emergencyFormClose,
        resolution: this.emergencyFormClose.get('resolution') as FormControl,
        comment: this.emergencyFormClose.get('comment') as FormControl,
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.confirm'),
      },
    });
    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((result) => {
        this.getEmergencyList();
      });
  }
}
