import { Component, EventEmitter, inject, Input, Output } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { AgGridModule } from 'ag-grid-angular';
import { DatePipe } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { AgGridCustomComponent, FormGroupComponent } from '../../../../../../../core/shared';
import { LoadingService } from '../../../../../../../core/services';
import { PagingInfo } from '../../../../../../../core/interfaces';
import { WarningList } from '../../../../../interfaces';
import { WarningService } from '../../../../../services';
import { DateFormat } from '../../../../../../../core/enums';

@Component({
  selector: 'app-warning-list',
  standalone: true,
  imports: [
    TranslateModule,
    MatIconModule,
    ReactiveFormsModule,
    FormGroupComponent,
    AgGridModule,
    AgGridCustomComponent,
  ],
  providers: [
    LoadingService,
    DatePipe,
  ],
  templateUrl: './warning-list.component.html',
  styleUrl: './warning-list.component.scss'
})
export class WarningListComponent {
  @Output() setWarningIdEvent = new EventEmitter<string>();
  vin: string = '';
  pagingInfo: PagingInfo = {
    totalItems: 0,
    currentPage: 0,
    numberOfPage: 10,
  };
  rowData: any[] = [];
  colDefs: any[] = [];
  defaultColDef: any = {};
  isSearching = false;

  warningFormSearch: FormGroup = new FormGroup({
    warningName: new FormControl(''),
  });
  dateFormat = DateFormat;

  constructor(
    private translateService: TranslateService,
    public loadingService: LoadingService,
    private warningService: WarningService,
    private datePipe: DatePipe,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  get warningNameSearch(): FormControl {
    return this.warningFormSearch.get('warningName') as FormControl;
  }

  ngOnInit(): void {
    this.getVinID();
    this.initializeTable();
    this.getWarningList();
  }

  getVinID(): void {
    this.vin = this.route.snapshot.paramMap.get('id');
  }

  initializeTable(): void {
    this.colDefs = [
      {
        headerValueGetter: () => this.translateService.instant('vehicle.warningTab.header.warningName'),
        field: 'name',
        flex: 2,
        sortable: false,
        cellClass: 'cell-word-wrap can-click',
        onCellClicked: (event) => this.viewWarningDetail(event?.data?.id),
      }, {
        headerValueGetter: () => this.translateService.instant('vehicle.warningTab.header.relatedTicket'),
        field: 'relatedTicket',
        flex: 3,
        sortable: false,
        cellRenderer: (params: any) => {
          return params.data.relatedTicketId ? `${params.data.relatedTicketId} <br> ${params.data.relatedTicketTitle}` : '-';
        }
      }, {
        headerValueGetter: () => this.translateService.instant('vehicle.warningTab.header.priority'),
        field: 'priority',
        flex: 1,
        sortable: false,
        cellRenderer: (params: any) => {
          const code = params.data.priority ? params.data.priority.code : '';
          return this.warningService.getPriorityStyle(code);
        }
      }, {
        headerValueGetter: () => this.translateService.instant('vehicle.warningTab.header.status'),
        field: 'status',
        flex: 1,
        sortable: false,
        cellRenderer: (params: any) => {
          const code = params.data.status ? params.data.status.code : '';
          return this.warningService.getStatusStyle(code);
        }
      }, {
        headerValueGetter: () => this.translateService.instant('vehicle.warningTab.header.occurredTime'),
        field: 'timestamp',
        flex: 1.5,
        sortable: false,
      }
    ];
    this.defaultColDef = {
      resizable: false,
    };
  }

  onSearch() {
    this.isSearching = true;
    this.getWarningList();
  }

  onPageChange(newPage: number): void {
    this.pagingInfo.currentPage = newPage;
    this.getWarningList();
  }

  onResultsPerPageChange(event: number): void {
    this.pagingInfo = {
      ...this.pagingInfo,
      currentPage: 0,
      numberOfPage: event,
    };
    this.getWarningList();
  }

  getWarningList() {
    const dataSend: any = {
      currentPage: this.pagingInfo.currentPage,
      pageSize: this.pagingInfo.numberOfPage,
      name: this.warningFormSearch.value.warningName,
      vin: this.vin, // get from vehicle detail
    };
    this.loadingService.showLoader();
    this.warningService.getWarningList(dataSend).subscribe(
      (res: WarningList) => {
        if (res) {
          if (res.items) {
            this.rowData = res.items
          }

          if (res.pagination) {
            this.pagingInfo = {
              totalItems: res.pagination.totalResults,
              currentPage: res.pagination.currentPage,
              numberOfPage: res.pagination.pageSize,
            };
          }
        }
        this.loadingService.hideLoader();
      },
      (err) => {
        this.loadingService.hideLoader();
      }
    );
  }

  viewWarningDetail(id: string) {
    this.setWarningIdEvent.emit(id);
  }
}
