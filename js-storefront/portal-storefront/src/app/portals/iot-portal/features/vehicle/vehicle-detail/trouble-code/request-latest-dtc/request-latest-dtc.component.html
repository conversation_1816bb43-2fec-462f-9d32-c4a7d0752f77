<mat-icon svgIcon="ic-checked-red" class="icon-title"></mat-icon>
<h2 class="title-dialog">{{ "troubleCode.requestLastestDtc" | translate }}</h2>

<div class="container-dialog">
  <form [formGroup]="form">
    <app-dropdown-form-group
      [label]="'troubleCode.centerRequestType' | translate"
      [control]="form.controls.centerRequestType"
      [options]="centerRequestTypeOptions"
      [required]="true"
      (changeOption)="changeCenterRequestType($event)"
    ></app-dropdown-form-group>

    @if(form.value.centerRequestType === 1 || form.value.centerRequestType ===
    2) {
    <app-form-group
      [label]="'troubleCode.numberOfRetries' | translate"
      [control]="form.controls.numberRetries"
      [hasRange]="true"
      [maxLength]="2"
      [min]="0"
      [max]="10"
      [required]="
        form.value.centerRequestType === 1 || form.value.centerRequestType === 2
      "
    ></app-form-group>

    <app-form-group
      [label]="'troubleCode.timeOut' | translate"
      [control]="form.controls.timeOut"
      [hasRange]="true"
      [maxLength]="3"
      [min]="60"
      [max]="600"
      [required]="
        form.value.centerRequestType === 1 || form.value.centerRequestType === 2
      "
    ></app-form-group>
    } @if(form.value.centerRequestType === 3) {
    <!-- <app-form-group
      [label]="'troubleCode.expirationDate' | translate"
      [control]="form.controls.expirationTime"
      [required]="form.value.centerRequestType === 3"
      [hasRange]="true"
      [maxLength]="3"
    ></app-form-group> -->

    <app-date-time-picker-form-group
      [label]="'troubleCode.expirationDate' | translate"
      [control]="form.controls.expirationTime"
      [controlId]="'expirationTime'"
      [required]="true"
      [minDate]="minDate"
      [errorMessage]="
        'common.requiredField'
          | translate : { field: ('troubleCode.expirationDate' | translate) }
      "
    >
    </app-date-time-picker-form-group>
    }
  </form>
</div>

<div class="action-dialog">
  <button class="btn-quaternary" (click)="onCancel()">
    {{ "common.cancel" | translate }}
  </button>

  <button
    class="btn-primary btn-confirm"
    (click)="onConfirm()"
    [disabled]="form?.invalid"
  >
    {{ "common.confirm" | translate }}
  </button>
</div>
