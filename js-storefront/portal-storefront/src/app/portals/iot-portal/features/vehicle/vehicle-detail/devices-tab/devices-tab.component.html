<div class="device-tab">
  <div class="header-section">
    <div class="device-tab__title">
      <mat-icon svgIcon="ic-device-sim"></mat-icon>
      {{ "vehicle.deviceWidget.title" | translate }}
    </div>
  </div>

  @if (rowData?.length > 0) {
  <app-ag-grid-custom
    [rowData]="rowData"
    [colDefs]="colDefs"
    [defaultColDef]="defaultColDef"
    [onlyTable]="true"
    [pagingInfo]="pagingInfo"
    (onPageChange)="onPageChange($event)"
    (changeItemPerPage)="changeItemPerPage($event)"
  >
  </app-ag-grid-custom>
  } @else {
  <div class="no-data">
    {{ "vehicle.deviceWidget.noDataFound" | translate }}
  </div>
  }
</div>
