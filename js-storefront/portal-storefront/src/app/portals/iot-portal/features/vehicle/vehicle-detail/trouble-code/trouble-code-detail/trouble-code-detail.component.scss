:host {
  .trouble-code-detail {
    display: flex;
    gap: 30px;
    flex-direction: column;
    margin-top: 30px;

    p {
      margin-bottom: 0;
    }

    &__type {
      background-color: #eee;
      padding: 15px;

      p {
        &:first-child {
          font-weight: 600;
          font-size: 16px;
        }
      }
    }

    &__detail {
      app-ag-grid-custom {
        ::ng-deep {
          .highlight-column, .header-special {
            background-color: rgba(38, 110, 242, 0.1);
            color: #266ef2;
          }

          .header-col {
            font-weight: 600;
          }

          .header-signal {
            font-size: 16px;
          }
        }
      }
    }

    &__status {
      display: grid;
      grid-template-columns: auto auto;

      &__item {
        display: flex;
        flex-direction: column;
        gap: 15px;

        &--label {
          font-size: 16px;
          font-weight: 600;
        }
      }
    }
  }
}
