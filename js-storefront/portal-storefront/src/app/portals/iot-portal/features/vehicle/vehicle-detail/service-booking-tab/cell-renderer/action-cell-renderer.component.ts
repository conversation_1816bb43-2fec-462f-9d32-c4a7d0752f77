import { Component, EventEmitter, Input, Output } from '@angular/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { TranslateModule } from '@ngx-translate/core';
import { IconModule } from '../../../../../../../core/icon/icon.module';
import { ServiceBookingAction } from '../../../../../enums';

@Component({
  selector: 'app-action-cell-renderer',
  template: `
    <div class="action-buttons-cell-renderer">
      <button class="edit-button" (click)="onEdit()">
        <mat-icon svgIcon="ic-edit" class="small-icon"></mat-icon>
        {{ 'common.edit' | translate }}
      </button>
      <button class="cancel-button" (click)="onCancelBooking()">
        <mat-icon svgIcon="ic-cancel" class="small-icon"></mat-icon>
        {{ 'common.cancel' | translate }}
      </button>
    </div>
  `,
  standalone: true,
  imports: [IconModule, TranslateModule],
})
export class ActionCellRendererComponent implements ICellRendererAngularComp {
  @Output() edit = new EventEmitter<void>();
  @Output() cancel = new EventEmitter<void>();

  params!: any;
  @Input() rowIndex!: number;

  agInit(params: any): void {
    this.params = params;
    this.rowIndex = params.rowIndex;
  }

  refresh(params: any): boolean {
    return true;
  }

  onEdit(): void {
    this.params.onClick(ServiceBookingAction.Edit, this.params.data);
  }

  onCancelBooking(): void {
    this.params.onClick(ServiceBookingAction.Cancel, this.params.data);
  }
}
