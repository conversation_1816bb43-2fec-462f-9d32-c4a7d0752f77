import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  inject,
  <PERSON><PERSON><PERSON>roy,
  OnInit,
  Output,
} from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { Subscription } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute } from '@angular/router';
import { RequestLatestDtcComponent } from '../request-latest-dtc/request-latest-dtc.component';
import { ColDef } from 'ag-grid-community';
import { IconModule } from '../../../../../../../core/icon/icon.module';
import { AgGridCustomComponent, FormGroupComponent } from '../../../../../../../core/shared';
import { LoadingService, NotificationService } from '../../../../../../../core/services';
import { TroubleCodeService } from '../../../../../services';
import { DEFAULT_COL_DEF } from '../../../../../../../core/constants';
import { handleErrors } from '../../../../../../../core/helpers';
import { TroubleCodeItem } from '../../../../../../../core/interfaces/vehicle.interface';

@Component({
  selector: 'app-trouble-code-list',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    ReactiveFormsModule,
    IconModule,
    AgGridCustomComponent,
    FormGroupComponent,
  ],
  templateUrl: './trouble-code-list.component.html',
  styleUrl: './trouble-code-list.component.scss',
})
export class TroubleCodeListComponent implements OnInit, OnDestroy {
  @Output() setTroubleCodeId = new EventEmitter<string>();

  notificationService = inject(NotificationService);
  translateService = inject(TranslateService);
  loadingService = inject(LoadingService);
  troubleCodeService = inject(TroubleCodeService);
  activatedRoute = inject(ActivatedRoute);

  dialog = inject(MatDialog);

  subscription = new Subscription();

  rowData: TroubleCodeItem[] = [];
  vin: string;

  form = new FormGroup({
    nodeName: new FormControl(),
    systemName: new FormControl(),
    dtc: new FormControl(),
  });

  defaultColDef = {
    ...DEFAULT_COL_DEF,
    valueFormatter: (params) => (params.value ? params.value : '-'),
    wrapText: true,
    autoHeight: true,
    // cellClassRules: {
    //   'row-even': (params) => params.data?.index % 2 === 0,
    //   'row-odd': (params) => params.data?.index % 2 !== 0,
    // },
  };

  // colDefs = [
  //   {
  //     headerName: this.translateService.instant(
  //       'troubleCode.dtcList.warningOccurrenceTime'
  //     ),
  //     headerValueGetter: () =>
  //       this.translateService.instant(
  //         'troubleCode.dtcList.warningOccurrenceTime'
  //       ),
  //     field: 'warningOccurrenceTime',
  //     flex: 1,
  //     cellClass: 'cell-word-wrap border-bottom',
  //     spanRows: (param) => this.spanRowsCondition(param),
  //   },
  //   {
  //     headerName: this.translateService.instant(
  //       'troubleCode.dtcList.diagnosticTime'
  //     ),
  //     headerValueGetter: () =>
  //       this.translateService.instant('troubleCode.dtcList.diagnosticTime'),
  //     field: 'diagnosticTime',
  //     flex: 1,
  //     spanRows: (param) => this.spanRowsCondition(param),
  //     cellClass: 'border-bottom',
  //   },
  //   {
  //     headerName: this.translateService.instant(
  //       'troubleCode.dtcList.lastRecordReceivedTime'
  //     ),
  //     headerValueGetter: () =>
  //       this.translateService.instant(
  //         'troubleCode.dtcList.lastRecordReceivedTime'
  //       ),
  //     field: 'lastRecordReceivedTime',
  //     flex: 1,
  //     spanRows: (param) => this.spanRowsCondition(param),
  //     cellClass: 'border-bottom',
  //   },
  //   {
  //     headerName: this.translateService.instant(
  //       'troubleCode.dtcList.dataCollectionStatus'
  //     ),
  //     headerValueGetter: () =>
  //       this.translateService.instant(
  //         'troubleCode.dtcList.dataCollectionStatus'
  //       ),
  //     field: 'dataCollectionStatus',
  //     flex: 1,
  //     cellClass: 'border-right border-bottom',
  //     spanRows: (param) => this.spanRowsCondition(param),
  //   },
  //   {
  //     headerName: this.translateService.instant('troubleCode.dtcList.nodeName'),
  //     headerValueGetter: () =>
  //       this.translateService.instant('troubleCode.dtcList.nodeName'),
  //     field: 'dtcName',
  //     cellClass: 'border-bottom',
  //     flex: 1,
  //   },
  //   {
  //     headerName: this.translateService.instant(
  //       'troubleCode.dtcList.systemName'
  //     ),
  //     headerValueGetter: () =>
  //       this.translateService.instant('troubleCode.dtcList.systemName'),
  //     field: 'ecuName',
  //     cellClass: 'border-bottom',
  //     flex: 1,
  //   },
  //   {
  //     headerName: this.translateService.instant('troubleCode.dtcList.dtc'),
  //     headerValueGetter: () =>
  //       this.translateService.instant('troubleCode.dtcList.dtc'),
  //     field: 'dtc',
  //     cellClass: 'border-bottom',
  //     flex: 1,
  //   },
  //   {
  //     flex: 1,
  //     cellRenderer: ActionCellRendererComponent,
  //     cellRendererParams: {
  //       actionDetail: {
  //         icon: 'ic-view',
  //         text: 'troubleCode.viewFFD',
  //       },
  //       onClick: (data) => this.viewFFD(data),
  //     },
  //     cellClass: 'action-grid action-last-grid border-bottom',
  //     cellClassRules: {
  //       'hide-action': (params) => !params.data?.ssrID,
  //     },
  //   },
  // ];

  colDefs: ColDef[];

  ngOnInit(): void {
    this.colDefs = this.troubleCodeService.getColumnDefs(this.viewFFD.bind(this));
    this.vin = this.activatedRoute.snapshot.paramMap.get('id');
    this.getList();
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  customCellRenderer(params) {
    const currentIndex = params.node.rowIndex;
    const previousNode = params.api.getDisplayedRowAtIndex(currentIndex - 1);
  
    if (previousNode && previousNode.data?.index === params.data?.index) {
      return '-';
    }
    return params.value;
  }

  spanRowsCondition(param): boolean {
    const { valueA, valueB, nodeA, nodeB } = param;
    return (
      nodeA?.data?.dtc === nodeB?.data?.dtc &&
      nodeA?.data?.warningOccurrenceTime ===
        nodeB?.data?.warningOccurrenceTime &&
      nodeA?.data?.diagnosticTime === nodeB?.data?.diagnosticTime &&
      nodeA?.data?.lastRecordReceivedTime ===
        nodeB?.data?.lastRecordReceivedTime &&
      nodeA?.data?.dataCollectionStatus === nodeB?.data?.dataCollectionStatus &&
      nodeA?.data?.index === nodeB?.data?.index
    );
  }

  getList(): void {
    const params = {
      nodeName: this.form.value.nodeName,
      systemName: this.form.value.systemName,
      dtc: this.form.value.dtc,
    };
    this.loadingService.showLoader();
    this.subscription.add(
      this.troubleCodeService.getTroubleCodeList(this.vin, params).subscribe(
        (response) => {
          this.loadingService.hideLoader();
          this.rowData = response?.items;
        },
        (error) => {
          this.loadingService.hideLoader();
          handleErrors(error, this.notificationService);
        }
      )
    );
  }

  search(): void {
    this.getList();
  }

  viewFFD(data): void {
    if (data?.ssrID) {
      this.setTroubleCodeId.emit(data?.ssrID);
    }
  }

  requestLastestDtc(): void {
    this.dialog.open(RequestLatestDtcComponent, {
      width: '560px',
      data: { vin: this.vin },
      autoFocus: false,
      disableClose: true,
    });
  }
}
