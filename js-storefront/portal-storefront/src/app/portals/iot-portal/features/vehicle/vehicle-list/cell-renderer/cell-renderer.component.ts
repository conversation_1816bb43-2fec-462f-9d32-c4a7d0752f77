import { Component, Input } from '@angular/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { CommonModule } from '@angular/common';
import { IconModule } from '../../../../../../core/icon/icon.module';
import { environment } from '../../../../../../../environments/environment';

@Component({
  selector: 'app-action-cell-renderer',
  imports: [IconModule, CommonModule],
  templateUrl: './cell-renderer.component.html',
  styleUrls: ['./cell-renderer.component.scss'],
  standalone: true,
})
export class CellRendererComponent implements ICellRendererAngularComp {
  @Input() data: any;
  @Input() config: {
    icon?: string;
    fields: { key: string; format?: 'text' }[];
    totalKey?: string;
    id: string;
  };

  params!: any;

  prefix = `${environment.OCC_BASE_URL}`;

  agInit(params: any): void {
    this.params = params;
    this.data = params.data;
    this.config = { ...params.config };
  }

  refresh(params: any): boolean {
    this.data = params.data;
    return true;
  }
}
