import { CommonModule } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FilterVehicleComponent } from './search-vehicle';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';

import { CellRendererComponent } from './cell-renderer/cell-renderer.component';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { finalize, Subscription, take } from 'rxjs';
import { DecimalPipe } from '@angular/common';
import { CustomHeaderTicketComponent } from '../../ticket/custom-header-ticket/custom-header-ticket.component';
import { IconModule } from '../../../../../core/icon/icon.module';
import { IconCellRendererComponent } from '../../../../../core/shared/ag-grid-custom/icon-cell-renderer/icon-cell-renderer.component';
import { AgGridCustomComponent, WidgetSummaryComponent } from '../../../../../core/shared';
import { LoadingComponent } from '../../../../../layout/global/loading/loading.component';
import { LoadingService, NotificationService, UserService } from '../../../../../core/services';
import { Vehicle } from '../../../interfaces';
import { VehiclesTab } from '../../../enums/vehicles.enum';
import { PERMISSIONS_CODE } from '../../../../../core/constants';
import { ItemWidget, PagingInfo } from '../../../../../core/interfaces';
import { VehicleService } from '../../../services/vehicles/vehicles.service';
import { RouterLinkCellRendererComponent } from '../../../../../core/shared/router-link-cell-renderer/router-link-cell-renderer.component';
import { handleErrors } from '../../../../../core/helpers';
import { VEHICLES_WIDGETS } from '../../../constants';
import { VehicleRespon } from '../../../../../core/interfaces/vehicle.interface';

@Component({
  selector: 'app-vehicle-list',
  templateUrl: './vehicle-list.component.html',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IconModule,
    TranslateModule,
    FilterVehicleComponent,
    IconCellRendererComponent,
    WidgetSummaryComponent,
    AgGridCustomComponent,
    LoadingComponent,
  ],
  styleUrls: ['./vehicle-list.component.scss'],
  providers: [VehicleService, LoadingService, NotificationService, DecimalPipe],
  standalone: true,
})
export class VehicleListComponent implements OnInit {
  router = inject(Router);
  vehicleService = inject(VehicleService);
  vehicles: Vehicle[] = [];
  loadingService = inject(LoadingService);
  notificationService = inject(NotificationService);
  translateService = inject(TranslateService);
  decimalPipe = inject(DecimalPipe);
  userService = inject(UserService);
  activatedRoute = inject(ActivatedRoute);

  readonly VehiclesTab = VehiclesTab;
  countAPI: number = 0;

  filterForm = new FormGroup({
    vin: new FormControl(),
    nameOrPhone: new FormControl(),
    filterSSC: new FormControl(false),
    filterHighWarning: new FormControl(false),
    filterHighEmergency: new FormControl(false),
  });
  selectedTab: VehiclesTab;

  widgets: ItemWidget[] = [
    {
      id: '' as VehiclesTab,
      count: 0,
      description: '',
      icon: '',
    },
  ];

  defaultColDef = {
    resizable: false,
    valueFormatter: (params) => (params.value ? params.value : '-'),
    sortable: false,
    menuTabs: [],
    wrapHeaderText: true,
    autoHeaderHeight: true,
  };

  rowData: Vehicle[] = [];
  colDefs = [
    {
      headerName: `VIN / Plate No.`,
      headerValueGetter: () =>
        this.translateService.instant('vehicleManagement.vin'),
      field: 'vin',
      flex: 1,
      headerComponent: CustomHeaderTicketComponent,
      headerComponentParams: {
        secondHeader: this.translateService.instant(
          'vehicleManagement.plateNo'
        ),
      },
      autoHeight: true,
      cellClass: 'cell-word-wrap',
      cellRenderer: RouterLinkCellRendererComponent,
      cellRendererParams: {
        linkBuilder: (data: any) => this.userService.isHasPermission([
          PERMISSIONS_CODE.ECARE_VEHICLE_IOT_VIEW,
        ]) ? `/vehicles/${data?.vin}` : '',
        displayValue: (params) => {
          const vin = params?.vin || '-';
          const plateNo = params?.plateNo || '-';
          return `<span class="two-col">${vin}<br>${plateNo}</span>`;
        },
      },
    },
    {
      headerName: `Customer Name / Mobile Number`,
      headerValueGetter: () =>
        this.translateService.instant('vehicleManagement.customerName'),
      field: 'customerAndPhone',
      flex: 1,
      headerComponent: CustomHeaderTicketComponent,
      headerComponentParams: {
        secondHeader: this.translateService.instant(
          'vehicleManagement.mobileNumber'
        ),
      },
      cellRenderer: (params) => {
        const { customerName = '-', mobile = '-' } = params.data || {};
        return `<span class="two-col">${customerName}<br>${mobile}</span>`;
      },
      hide: !this.userService.isHasPermission([PERMISSIONS_CODE.ECARE_OWNER_VIEW])
    },
    {
      headerName: `Make / Model`,
      headerValueGetter: () =>
        this.translateService.instant('vehicleManagement.make'),
      field: 'makeAndModel',
      flex: 1,
      headerComponent: CustomHeaderTicketComponent,
      headerComponentParams: {
        secondHeader: this.translateService.instant('vehicleManagement.model'),
      },
      cellRenderer: (params) => {
        const { vehicleMake = '-', vehicleModel = '-' } = params.data || {};
        return `<span class="two-col">${vehicleMake}<br>${vehicleModel}</span>`;
      },
      tooltipValueGetter: (params) => {
        const { vehicleMake = '-', vehicleModel = '-' } = params.data || {};
        return `${vehicleMake} - ${vehicleModel}`;
      },
    },
    {
      headerName: this.translateService.instant(
        'vehicleManagement.emergencies'
      ),
      headerValueGetter: () =>
        this.translateService.instant('vehicleManagement.emergencies'),
      field: 'emergencyName',
      flex: 1.5,
      cellRenderer: CellRendererComponent,
      cellRendererParams: {
        config: {
          icon: 'ic-emergency-1',
          fields: [
            { key: 'emergencyName', format: 'text' },
            { key: 'emergencyCreateTime', format: 'text' },
          ],
          totalKey: 'numOfEmergency',
          id: 'emergency',
        },
      },
      tooltipValueGetter: (params) => {
        const { emergencyName = '', emergencyCreateTime = '' } =
          params.data || {};
        return emergencyName || emergencyCreateTime
          ? `${emergencyName} - ${emergencyCreateTime}`
          : null;
      },
    },
    {
      headerName: this.translateService.instant('vehicleManagement.warnings'),
      headerValueGetter: () =>
        this.translateService.instant('vehicleManagement.warnings'),
      field: 'warningName',
      flex: 1.5,
      cellRenderer: CellRendererComponent,
      cellRendererParams: {
        config: {
          icon: '',
          fields: [
            { key: 'warningName', format: 'text' },
            { key: 'warningOccurredTime', format: 'text' },
          ],
          totalKey: 'numOfWarning',
          id: 'warning',
        },
      },
      tooltipValueGetter: (params) => {
        const { warningName = '', warningOccurredTime = '' } =
          params.data || {};
        return warningName || warningOccurredTime
          ? `${warningName} - ${warningOccurredTime}`
          : null;
      },
    },
    {
      headerName: this.translateService.instant('vehicle.sscDue.title'),
      headerValueGetter: () =>
        this.translateService.instant('vehicle.sscDue.title'),
      field: 'sscDue',
      flex: 1,
      cellRenderer: CellRendererComponent,
      cellRendererParams: {
        config: {
          icon: '',
          fields: [
            { key: 'sscCategory', format: 'text' },
            { key: 'sscExpiryDate', format: 'text' },
          ],
          totalKey: 'numOfSSC',
          id: 'sscDue',
        },
      },
      tooltipValueGetter: (params) => {
        const { sscCategory = '', sscExpiryDate = '' } = params.data || {};
        return sscCategory || sscExpiryDate
          ? `${sscCategory} - ${sscExpiryDate}`
          : null;
      },
      hide: !this.userService.isHasPermission([PERMISSIONS_CODE.ECARE_SSC_VIEW])
    },
    {
      headerName: 'PMS Due',
      headerValueGetter: () =>
        this.translateService.instant('vehicleManagement.pmsDue'),
      field: 'nextMileage',
      flex: 1,
      cellRenderer: (params) => {
        const { nextMileage } = params.data;
        return nextMileage > 0
          ? `${this.decimalPipe.transform(
              nextMileage
            )}km ${this.translateService.instant('vehicleManagement.checkUp')}`
          : '-';
      },
      tooltipValueGetter: (params) => {
        const { nextMileage = null } = params.data || {};
        return nextMileage > 0
          ? `${this.decimalPipe.transform(
              nextMileage
            )}km ${this.translateService.instant('vehicleManagement.checkUp')}`
          : null;
      },
      hide: !this.userService.isHasPermission([PERMISSIONS_CODE.ECARE_PMS_VIEW])
    },
  ];

  pagingInfo: PagingInfo = {
    totalItems: 0,
    currentPage: 0,
    numberOfPage: 10,
  };

  subscription = new Subscription();

  ngOnInit(): void {
    this.countAPI = 2;
    this.getWidgets();

    this.activatedRoute.queryParams.pipe(take(1)).subscribe((params) => {
          const { tabId } = params;
    
          this.filterForm.patchValue({
            vin: '',
            nameOrPhone: '',
            filterSSC: false,
            filterHighWarning: false,
            filterHighEmergency: tabId === VehiclesTab.Emergency,
          });
    
          this.router.navigate([], {
            relativeTo: this.activatedRoute,
            queryParams: {
              tabId: null
            },
            queryParamsHandling: 'merge',
            replaceUrl: true,
          });
    
          this.getVehicleIdList();
        });
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  viewVehicleDetail(vehicleId: number): void {
    if (
      this.userService.isHasPermission([
        PERMISSIONS_CODE.ECARE_VEHICLE_IOT_VIEW,
      ])
    ) {
      this.router.navigate(['/vehicles', vehicleId]);
    }
  }

  getVehicleIdList(): void {
    this.loadingService.showLoader();

    const payload = {
      currentPage: this.pagingInfo?.currentPage,
      pageSize: this.pagingInfo?.numberOfPage,
      vinOrPlateNo: this.filterForm.value.vin,
      customerNameOrPhone: this.filterForm.value.nameOrPhone,
      filterActiveEmergency: this.filterForm.value.filterHighEmergency,
      filterHighWarning: this.filterForm.value.filterHighWarning,
      filterSSC: this.filterForm.value.filterSSC,
    };

    this.subscription.add(
      this.vehicleService
        .getVehicles(payload)
        .pipe(finalize(() => this.handleCountAPI()))
        .subscribe({
          next: (response: VehicleRespon) => {
            this.rowData = response.items;
            this.pagingInfo.totalItems = response?.pagination?.totalResults;
          },
          error: (err) => {
            this.handleCountAPI();
            this.rowData = [];
            handleErrors(err, this.notificationService);
          },
        })
    );
  }

  onPageChange(page: number): void {
    this.pagingInfo.currentPage = page;
    this.countAPI = 1;
    this.getVehicleIdList();
  }

  getWidgets(): void {
    this.loadingService.showLoader();
    this.subscription.add(
      this.vehicleService.getWidget().pipe(finalize(() => this.handleCountAPI())).subscribe((response) => {
        const { widgets } = response;

        this.widgets = widgets.map((item) => {
          const { description, icon } =
            VEHICLES_WIDGETS?.find((widget) => widget.id === item.type) || {};
          return {
            id: item?.type,
            count: item?.count,
            description,
            icon,
          };
        });
      }, (error) => {
        this.handleCountAPI();
      })
    );
  }
  onViewClick(tabId: VehiclesTab): void {
    this.selectedTab = tabId;

    switch (tabId) {
      case VehiclesTab.Warning:
        this.filterForm.patchValue({
          vin: '',
          nameOrPhone: '',
          filterSSC: false,
          filterHighWarning: true,
          filterHighEmergency: false,
        });
        break;

      case VehiclesTab.Emergency:
        this.filterForm.patchValue({
          vin: '',
          nameOrPhone: '',
          filterSSC: false,
          filterHighWarning: false,
          filterHighEmergency: true,
        });
        break;

      case VehiclesTab.SSCDue:
        this.filterForm.patchValue({
          vin: '',
          nameOrPhone: '',
          filterSSC: true,
          filterHighWarning: false,
          filterHighEmergency: false,
        });
        break;
    }
    this.countAPI = 1;
    this.getVehicleIdList();
  }

  changeItemPerPage(item) {
    this.pagingInfo.numberOfPage = item;
    this.pagingInfo.currentPage = 0;
    this.countAPI = 1;
    this.getVehicleIdList();
  }

  searchVehicle(): void {
    this.pagingInfo.currentPage = 0;
    this.countAPI = 1;
    this.filterForm.patchValue({
      filterSSC: false,
      filterHighWarning: false,
      filterHighEmergency: false,
    });
    this.getVehicleIdList();
  }

  handleCountAPI() {
    this.countAPI -= 1;
    if (this.countAPI === 0) {
      this.loadingService.hideLoader();
    }
  }
}
