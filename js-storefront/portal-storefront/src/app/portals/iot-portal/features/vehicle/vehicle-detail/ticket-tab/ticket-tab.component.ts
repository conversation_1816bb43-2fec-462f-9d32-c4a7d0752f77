import { CommonModule, DatePipe } from '@angular/common';
import { Component, inject, Input, OnInit } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { filter, Subscription } from 'rxjs';
import { CustomHeaderTicketComponent } from '../../../ticket/custom-header-ticket/custom-header-ticket.component';
import { CreateTicketComponent } from '../../../items-detail';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { VehicleModule } from '../vehicle-detail.module';
import { IconModule } from '../../../../../../core/icon/icon.module';
import { LoadingComponent } from '../../../../../../layout/global/loading/loading.component';
import { AgGridCustomComponent, FormGroupComponent, RouterLinkCellRendererComponent } from '../../../../../../core/shared';
import { NotificationService, UserService } from '../../../../../../core/services';
import { TypeTicketPipe } from '../../../../pipes';
import { VehicleService } from '../../../../services';
import { DEFAULT_COL_DEF, PERMISSIONS_CODE } from '../../../../../../core/constants';
import { Ticket, Vehicle } from '../../../../interfaces';
import { PagingInfo } from '../../../../../../core/interfaces';
import { PriorityTicket, StatusTicket } from '../../../../enums';
import { ActionModal, DateFormat } from '../../../../../../core/enums';
import { handleErrors } from '../../../../../../core/helpers';

@Component({
  selector: 'app-ticket-tab',
  standalone: true,
  imports: [
    CommonModule,
    IconModule,
    TranslateModule,
    LoadingComponent,
    AgGridCustomComponent,
    FormGroupComponent,
    ReactiveFormsModule,
    VehicleModule
  ],
  providers: [NotificationService, DatePipe, TypeTicketPipe],
  templateUrl: './ticket-tab.component.html',
  styleUrls: ['./ticket-tab.component.scss']
})
export class TicketTabComponent implements OnInit {
  @Input() vin: string;
  vehicleService = inject(VehicleService);
  notificationService = inject(NotificationService);
  translateService = inject(TranslateService);
  datePipe = inject(DatePipe)
  dialog = inject(MatDialog);
  router = inject(Router)

  userService = inject(UserService);
  typeTicketPipe = inject(TypeTicketPipe);
  
  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;

  isLoading: boolean = false;
  isSearch: boolean = false;
  subscription = new Subscription();

  rowData: Ticket[] = [];

  pagingInfo: PagingInfo = {
    totalItems: 0,
    currentPage: 0,
    numberOfPage: 10,
  };

  ticketFormSearch: FormGroup = new FormGroup({
    txtSearchId: new FormControl(''),
    txtSearchTitle: new FormControl(''),
  });

  defaultColDef = {
    ...DEFAULT_COL_DEF,
    valueFormatter: (params) => (params.value ? params.value : '-'),
  };
  colDefs = [
    {
      headerName: this.translateService.instant('tickets.ticketList.ticketId'),
      headerValueGetter: () => this.translateService.instant('tickets.ticketList.ticketId'),
      wrapText: true,
      flex: 1,
      headerComponent: CustomHeaderTicketComponent,
      headerComponentParams: {
        secondHeader: this.translateService.instant(
          'tickets.ticketList.ticketType'
        ),
      },
      autoHeight: true,
      cellClass: 'cell-word-wrap',
      cellRenderer: RouterLinkCellRendererComponent,
      cellRendererParams: {
        linkBuilder: (data: any) => this.userService.isHasPermission([
          PERMISSIONS_CODE.ECARE_TICKET_DETAIL_VIEW,
        ]) ? `tickets/${data?.ticketID}` : '',
        displayValue: (params) => {
          return `<p>${params?.ticketID}</p><p>${this.typeTicketPipe.transform(params?.type)}</p>`;
        },
      },
    },
    {
      headerName: this.translateService.instant('tickets.ticketList.title'),
      headerValueGetter: () => this.translateService.instant('tickets.ticketList.title'),
      field: 'title',
      flex: 3,
      autoHeight: true,
      cellClass: 'cell-word-wrap',
    },
    {
      headerName: this.translateService.instant('tickets.ticketList.priority'),
      headerValueGetter: () => this.translateService.instant('tickets.ticketList.priority'),
      field: 'priority',
      flex: 1,
      cellRenderer: (params) => {
        return params?.value ? `<span>${params?.value?.name}</span>` : '';
      },
      cellClassRules: {
        'value-tag': (params) => !!params.value,
        'red-tag': (params) => params.value?.code === PriorityTicket.High,
        'yellow-tag': (params) => params.value?.code === PriorityTicket.Mid,
      },
    },
    {
      headerName: this.translateService.instant('tickets.ticketList.status'),
      headerValueGetter: () => this.translateService.instant('tickets.ticketList.status'),
      field: 'status',
      flex: 1,
      cellRenderer: (params) => {
        return params?.value ? `<span>${params?.value?.name}</span>` : '';
      },
      cellClassRules: {
        'value-tag': (params) => !!params.value,
        'yellow-tag': (params) =>
          params.value?.code === StatusTicket.InProgress,
      },
    },
    {
      headerName: this.translateService.instant(
        'tickets.ticketList.createDate'
      ),
      headerValueGetter: () => this.translateService.instant('tickets.ticketList.createDate'),
      field: 'createdDate',
      flex: 1,
      cellRenderer: (params) => {
        return params?.value
          ? `<span>${this.datePipe.transform(
            params?.value,
            DateFormat.FullDate
          )}</span>`
          : '';
      },
      tooltipValueGetter: (params) => {
        return this.datePipe.transform(
          params?.data?.createdDate,
          DateFormat.FullDate
        );
      },
    },
    {
      headerName: this.translateService.instant('tickets.ticketList.assignee'),
      headerValueGetter: () => this.translateService.instant('tickets.ticketList.assignee'),
      field: 'assignee',
      cellRenderer: (params) => {
        return params?.value ? `<span>${params?.value?.name}</span>` : '';
      },
      flex: 1,
      autoHeight: true,
      cellClass: 'cell-word-wrap',
    },
  ];

  vehicle: Vehicle;

  ngOnInit(): void {
    this.getTicketList();
    this.subscription.add(
      this.vehicleService.$vehicleDetail.subscribe((vehicleDetail) => {
        this.vehicle = vehicleDetail;
      })
    );
  }

  ngOnDestroy() {
    this.subscription && this.subscription.unsubscribe();
  }

  public get txtSearchId(): FormControl {
    return this.ticketFormSearch.get('txtSearchId') as FormControl;
  }

  public get txtSearchTitle(): FormControl {
    return this.ticketFormSearch.get('txtSearchTitle') as FormControl;
  }

  onSearch(): void {
    this.pagingInfo.currentPage = 0;
    this.isSearch = true;
    this.getTicketList();
  }

  getTicketList(): void {
    const params = {
      vin: this.vin,
      title: this.txtSearchTitle?.value || '',
      ticketId: this.txtSearchId?.value || '',
      currentPage: this.pagingInfo.currentPage,
      pageSize: this.pagingInfo.numberOfPage,
    };
    this.isLoading = true;
    this.subscription.add(
      this.vehicleService.getTicketList(params).subscribe(
        (response) => {
          this.isLoading = false;
          this.rowData = response?.items;
          this.pagingInfo.totalItems = response?.pagination?.totalResults;
        },
        (error) => {
          this.isLoading = false;
          handleErrors(error, this.notificationService);
        }
      )
    );
  }

  onPageChange(value: number): void {
    this.pagingInfo.currentPage = value;
    this.getTicketList();
  }

  changeItemPerPage(item) {
    this.pagingInfo.numberOfPage = item;
    this.pagingInfo.currentPage = 0;
    this.getTicketList();
  }

  createTicket(): void {
    const dialogRef = this.dialog.open(CreateTicketComponent, {
      width: '850px',
      maxHeight: '90vh',
      disableClose: true,
      autoFocus: false,
      data: {
        createFromWidget: true,
        vehicle: this.vehicle,
        vin: this.vin
      },
    });

    dialogRef.afterClosed()
    .pipe(filter((result: any) => result?.action === ActionModal.Submit))
    .subscribe((() => {
      this.pagingInfo.currentPage = 0;
      this.getTicketList();
    }));
  }

  viewTicketDetail(id) {
    if (
      this.userService.isHasPermission([
        PERMISSIONS_CODE.ECARE_TICKET_DETAIL_VIEW,
      ])
    ) {
      this.router.navigate(['tickets/' + id]);
    }
  }
}
