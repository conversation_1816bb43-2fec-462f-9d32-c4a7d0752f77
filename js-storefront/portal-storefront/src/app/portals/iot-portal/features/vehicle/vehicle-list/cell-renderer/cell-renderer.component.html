<div *ngIf="data && config && data[config.totalKey] > 0">
  <div class="col-have-icon">
    <div class="col-have-icon__icon">
      <mat-icon *ngIf="config.icon" [svgIcon]="config.icon"></mat-icon>
    </div>
    @if(config?.id === 'warning' && data?.warningIcon) {
      <img class="img-icon" [src]="prefix + data?.warningIcon" />
    }
    <div class="col-have-icon__text">
      <ng-container *ngFor="let field of config.fields">
        <div>
          {{ data[field.key] ? data[field.key] : "-" }}
          <br />
        </div>
      </ng-container>
    </div>
    <div *ngIf="config.totalKey && data[config.totalKey] > 1" class="col-have-icon__total">
    +{{ data[config.totalKey] - 1 }}
    </div>
  </div>
</div>
<span *ngIf="!data || !config || data[config.totalKey] <= 0">-</span>
