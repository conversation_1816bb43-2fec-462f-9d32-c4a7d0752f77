@import "../../../../../../../styles/abstracts/variables";
@import "../../../../../../../styles/abstracts/mixins";

:host {
    .ticket-tab {
        @include box-wrapper;

        .header-section {
            display: flex;
            gap: 20px;
            flex: 1 0;
            margin-bottom: 30px;
        }

        &__title {
            margin-right: auto;
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 22px;
            font-weight: 600;

            mat-icon {
                width: 32px;
                height: 32px;
            }
        }

        .no-data {
            color: $text-color-4;
            font-size: 20px;
            font-weight: 600;
            text-align: center;
            padding: 30px;
        }

        app-ag-grid-custom {
            ::ng-deep {

                .ag-cell {
                    display: flex;
                    align-items: center;
                    line-height: 20px;
                    padding-top: 10px;
                    padding-bottom: 10px;
                    word-wrap: break-word;
                    white-space: normal;
                }

                .ag-header-cell:last-child {
                    .ag-header-cell-label {
                        justify-content: start;
                    }
                }
            }
        }
    }
}