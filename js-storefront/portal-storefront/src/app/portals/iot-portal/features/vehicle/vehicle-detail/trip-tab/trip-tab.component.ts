import { CommonModule, DatePipe } from '@angular/common';
import { Component, inject, Input, OnInit } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { filter, Subscription } from 'rxjs';

import { ModalCloseComponent } from '../emergency-tab/modal-close/modal-close.component';
import { ModalTripDetailsComponent } from './modal-trip-details/modal-trip-details.component';
import { IconModule } from '../../../../../../core/icon/icon.module';
import { ActionCellRendererComponent, AgGridCustomComponent, DateFormGroupComponent } from '../../../../../../core/shared';
import { VehicleService } from '../../../../services';
import { LoadingService, NotificationService, UserService } from '../../../../../../core/services';
import { DEFAULT_COL_DEF, PERMISSIONS_CODE } from '../../../../../../core/constants';
import { ActionModal, DateFormat, TimeZone } from '../../../../../../core/enums';
import { handleErrors } from '../../../../../../core/helpers';
import { Trip } from '../../../../../../core/interfaces/vehicle.interface';

@Component({
  selector: 'app-trip-tab',
  standalone: true,
  imports: [
    CommonModule,
    IconModule,
    TranslateModule,
    AgGridCustomComponent,
    ReactiveFormsModule,
    DateFormGroupComponent,
  ],
  providers: [VehicleService, NotificationService, DatePipe],
  templateUrl: './trip-tab.component.html',
  styleUrls: ['./trip-tab.component.scss'],
})
export class TripTabComponent implements OnInit {
  @Input() vin: string;
  vehicleService = inject(VehicleService);
  notificationService = inject(NotificationService);
  translateService = inject(TranslateService);
  loadingService = inject(LoadingService);
  dialog = inject(MatDialog);
  datePipe = inject(DatePipe);
  userService = inject(UserService);

  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;

  tripFormSearch: FormGroup = new FormGroup({
    txtSearch: new FormControl(),
  });
  isSearch: boolean = false;
  subscription = new Subscription();
  deviceType: string = '';
  rowData: Trip[] = [];

  defaultColDef = {
    ...DEFAULT_COL_DEF,
    valueFormatter: (params) => (params.value ? params.value : '-'),
  };
  colDefs = [
    {
      headerName: this.translateService.instant('vehicle.trip.id'),
      headerValueGetter: () => this.translateService.instant('vehicle.trip.id'),
      field: 'id',
      valueGetter: (params) =>
        this.translateService.instant('vehicle.trip.tripIndex', {
          index: (params?.node?.rowIndex || 0) + 1,
        }),
    },
    {
      headerName: this.translateService.instant('vehicle.trip.start'),
      headerValueGetter: () =>
        this.translateService.instant('vehicle.trip.start'),
      field: 'startTime',
      flex: 1,
      cellRenderer: (params) => {
        return params?.value
          ? `<span>${this.datePipe.transform(
              params?.value,
              DateFormat.FullDate,
              TimeZone.UTC8
            )}</span>`
          : '-';
      },
    },
    {
      headerName: this.translateService.instant('vehicle.trip.end'),
      headerValueGetter: () =>
        this.translateService.instant('vehicle.trip.end'),
      field: 'endTime',
      flex: 1,
      cellRenderer: (params) => {
        return params?.value
          ? `<span>${this.datePipe.transform(
              params?.value,
              DateFormat.FullDate,
              TimeZone.UTC8
            )}</span>`
          : '-';
      },
    },
    {
      headerName: this.translateService.instant('vehicle.trip.duration'),
      headerValueGetter: () =>
        this.translateService.instant('vehicle.trip.duration'),
      field: 'totalTravelTime',
      flex: 1,
    },
    {
      headerName: this.translateService.instant('vehicle.trip.distance'),
      headerValueGetter: () =>
        this.translateService.instant('vehicle.trip.distance'),
      field: 'totalDistance',
      flex: 1,
      cellRenderer: (params) => {
        return params?.value
          ? `${params?.value} km`
          : '-';
      },
    },
    {
      headerName: this.translateService.instant('vehicle.trip.fuelConsumption'),
      headerValueGetter: () =>
        this.translateService.instant('vehicle.trip.fuelConsumption'),
      field: 'fuelConsumption',
      flex: 1,
    },
    {
      maxWidth: 130,
      minWidth: 130,
      cellRenderer: ActionCellRendererComponent,
      cellRendererParams: (params: any) => ({
        hasViewMap: true,
        onClick: (type: string, data: Trip) =>
          this.openModalTripDetails(data, params?.node?.rowIndex),
      }),
      cellClass: 'action-grid',
      sortable: false,
    },
  ];

  public get txtSearch(): FormControl {
    return this.tripFormSearch.get('txtSearch') as FormControl;
  }

  ngOnInit(): void {
    this.isSearch = true;
    this.getTripList();
  }

  ngOnDestroy() {
    this.subscription && this.subscription.unsubscribe();
  }

  getTripList(): void {
    const params: any = {
      vin: this.vin,
      tripDate: this.tripFormSearch?.value?.txtSearch
        ? this.datePipe.transform(
            this.tripFormSearch.value.txtSearch,
            DateFormat.ShortDate
          )
        : '',
    };
    this.loadingService.showLoader();
    this.subscription.add(
      this.vehicleService.getTripList(params).subscribe(
        (response) => {
          this.loadingService.hideLoader();
          this.rowData = response?.items;
        },
        (error) => {
          this.loadingService.hideLoader();
          handleErrors(error, this.notificationService);
        }
      )
    );
  }

  onSearch(): void {
    this.getTripList();
  }

  openModalTripDetails(data: Trip, rowIndex: number) {
    if (
      this.userService.isHasPermission([
        PERMISSIONS_CODE.ECARE_TRIP_DETAIL_VIEW,
      ])
    ) {
      const dialogRef = this.dialog.open(ModalTripDetailsComponent, {
        maxWidth: '1200px',
        width: '100%',
        data: {
          vin: this.vin,
          ...data,
          rowIndex
        },
      });
      dialogRef
        .afterClosed()
        .pipe(filter((result) => result?.action === ActionModal.Submit))
        .subscribe((result) => {});
    }
  }
}
