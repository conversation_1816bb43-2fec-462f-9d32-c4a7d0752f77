<app-loading *ngIf="loadingService.isLoading"></app-loading>


<div class="header-section">
  <h2 class="title-page">
    {{ "vehicleManagement.vehiclesTitle" | translate }}
  </h2>
</div>

<app-widget-summary
  [widgets]="widgets"
  (changeTab)="onViewClick($event)"
></app-widget-summary>

<app-filter-vehicle
  [form]="filterForm"
  (searchVehicle)="searchVehicle()"
></app-filter-vehicle>

<div class="vehicle-list">
  @if (rowData?.length > 0 ) {
  <app-ag-grid-custom
    class="vehicle-list-table"
    [rowData]="rowData"
    [colDefs]="colDefs"
    [defaultColDef]="defaultColDef"
    [isPaging]="true"
    [pagingInfo]="pagingInfo"
    [isShowActionExport]="false"
    (onPageChange)="onPageChange($event)"
    (changeItemPerPage)="changeItemPerPage($event)"
  >
  </app-ag-grid-custom>
  } @else {
  <div class="no-data">{{ "vehicleManagement.noVehicle" | translate }}</div>
  }
</div>
