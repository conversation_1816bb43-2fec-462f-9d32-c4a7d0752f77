import { CommonModule } from '@angular/common';
import { Component, inject, Input, OnInit } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Subscription, filter } from 'rxjs';
import { Router } from '@angular/router';
import { IconModule } from '../../../../../../core/icon/icon.module';
import { AgGridCustomComponent, RouterLinkCellRendererComponent } from '../../../../../../core/shared';
import { DeviceService, VehicleService } from '../../../../services';
import { LoadingService, NotificationService, UserService } from '../../../../../../core/services';
import { DataStoreService } from '../../../../../../core/services/data-store.service';
import { DEFAULT_COL_DEF, PERMISSIONS_CODE } from '../../../../../../core/constants';
import { ItemDevice, PayloadGetDevice } from '../../../../interfaces';
import { PagingInfo } from '../../../../../../core/interfaces';
import { DeviceManagementTab } from '../../../../enums';
import { handleErrors } from '../../../../../../core/helpers';
@Component({
  selector: 'app-devices-tab',
  standalone: true,
  imports: [
    CommonModule,
    IconModule,
    TranslateModule,
    AgGridCustomComponent,
    ReactiveFormsModule,
  ],
  providers: [VehicleService, NotificationService, DeviceService],
  templateUrl: './devices-tab.component.html',
  styleUrls: ['./devices-tab.component.scss']
})
export class DevicesTabComponent implements OnInit {
  @Input() vin: string;
  vehicleService = inject(VehicleService);
  notificationService = inject(NotificationService);
  translateService = inject(TranslateService);
  loadingService = inject(LoadingService);
  deviceService = inject(DeviceService)
  dialog = inject(MatDialog);
  router = inject(Router);
  dataStoreService = inject(DataStoreService);
  userService = inject(UserService);
    
  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;
  subscription = new Subscription();
  deviceType: string = '';
  rowData: ItemDevice[] = [];

  pagingInfo: PagingInfo = {
    totalItems: 0,
    currentPage: 0,
    numberOfPage: 10,
  };
  defaultColDef = {
    ...DEFAULT_COL_DEF,
    valueFormatter: (params) => (params.value ? params.value : '-'),
  };
  colDefs = [
    {
      headerName: this.translateService.instant('deviceManagement.deviceID'),
      headerValueGetter: () => this.translateService.instant('deviceManagement.deviceID'),
      field: 'deviceId',
      wrapText: true,
      autoHeight: true,
      cellRenderer: RouterLinkCellRendererComponent,
      cellRendererParams: {
        linkBuilder: (data: any) => this.userService.isHasPermission([
          PERMISSIONS_CODE.IOT_DEVICE_DETAIL_VIEW,
        ]) ? `/devices/${data?.deviceId}` : '',
      },
      tooltipField: 'deviceId',
    },
    {
      headerName: this.translateService.instant(
        'deviceManagement.signalTestingStatus'
      ),
      headerValueGetter: () => this.translateService.instant('deviceManagement.signalTestingStatus'),
      field: 'signalTestingStatus',
      flex: 1,
      autoHeaderHeight: true,
      cellRenderer: (params) => {
        return params?.value ? `<span>${params?.value}</span>` : 'N/A';
      },
      tooltipValueGetter: (params) => {
        return params?.data?.signalTestingStatus;
      },
    },
    {
      headerName: this.translateService.instant('deviceManagement.deviceStaus'),
      headerValueGetter: () => this.translateService.instant('deviceManagement.deviceStaus'),
      field: 'deviceStatus',
      flex: 1,
      cellRenderer: (params) => {
        return params?.value ? `<span>${params?.value?.name}</span>` : 'N/A';
      },
      tooltipValueGetter: (params) => {
        return params?.data?.deviceStatus?.name;
      },
    },
    {
      headerName: this.translateService.instant(
        'deviceManagement.activationStatus'
      ),
      headerValueGetter: () => this.translateService.instant('deviceManagement.activationStatus'),
      field: 'activationStatus',
      flex: 1,
      cellRenderer: (params) => {
        return params?.value ? `<span>${params?.value?.name}</span>` : 'N/A';
      },
      tooltipValueGetter: (params) => {
        return params?.data?.activationStatus?.name;
      },
    },
    {
      headerName: this.translateService.instant('deviceManagement.simStatus'),
      headerValueGetter: () => this.translateService.instant('deviceManagement.simStatus'),
      field: 'simStatus',
      flex: 1,
      cellRenderer: (params) => {
        return params?.value ? `<span>${params?.value?.name}</span>` : 'N/A';
      },
      headerComponentParams: {
        id: 'simStatus',
      },
      tooltipValueGetter: (params) => {
        return params?.data?.simStatus?.name;
      },
    },
  ];

  ngOnInit(): void {
    this.subscription.add(this.dataStoreService.deviceType.subscribe(deviceType => {
      if (deviceType) {
        this.deviceType = deviceType;
        this.getDeviceList();
      }
    }));
  }

  ngOnDestroy() {
    this.subscription && this.subscription.unsubscribe();
  }

  getDeviceList(): void {
    const params: PayloadGetDevice = {
      currentPage: this.pagingInfo.currentPage,
      pageSize: this.pagingInfo.numberOfPage,
      idOrVin: this.vin,
      includeHistory: true
    };
    this.loadingService.showLoader();
    this.subscription.add(
      this.deviceService.getDeviceList(params, this.deviceType as DeviceManagementTab).subscribe(
        (response) => {
          this.loadingService.hideLoader();
          this.rowData = response?.items;
          this.pagingInfo.totalItems = response?.pagination?.totalPages;
        },
        (error) => {
          this.loadingService.hideLoader();
          handleErrors(error, this.notificationService);
        }
      )
    );
  }

  onPageChange(value: number): void {
    this.pagingInfo.currentPage = value;
    this.getDeviceList();
  }

  changeItemPerPage(item) {
    this.pagingInfo.numberOfPage = item;
    this.pagingInfo.currentPage = 0;
    this.getDeviceList();
  }

  viewDeviceDetail(deviceId: number) {
    if (
      this.userService.isHasPermission([
        PERMISSIONS_CODE.IOT_DEVICE_DETAIL_VIEW,
      ])
    ) {
      this.router.navigate(['/devices', deviceId]);
    }
  }
}
