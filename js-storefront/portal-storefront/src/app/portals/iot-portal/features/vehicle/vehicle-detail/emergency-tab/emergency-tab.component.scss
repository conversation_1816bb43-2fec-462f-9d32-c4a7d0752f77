@import "../../../../../../../styles/abstracts/variables";
@import "../../../../../../../styles/abstracts/mixins";

:host {
  .emergency-tab {
    @include box-wrapper;

    &__title {
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: 22px;
      font-weight: 600;
      margin-bottom: 30px;

      mat-icon {
        width: 32px;
        height: 32px;
      }
    }

    .no-data {
      color: $text-color-4;
      font-size: 20px;
      font-weight: 600;
      text-align: center;
    }

    app-ag-grid-custom {
      ::ng-deep {
        .red-tag {
          span {
            background-color: #eb0a1e;
          }
        }

        .ag-cell {
          display: flex;
          align-items: center;
          line-height: 20px;
          padding-top: 10px;
          padding-bottom: 10px;
        }

        .ag-header-cell:last-child {
          .ag-header-cell-label {
            justify-content: start;
          }
        }
      }
    }
  }
}