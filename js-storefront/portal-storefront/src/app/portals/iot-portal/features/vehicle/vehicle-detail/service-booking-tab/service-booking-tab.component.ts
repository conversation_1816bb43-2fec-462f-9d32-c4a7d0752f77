import { CommonModule, DatePipe } from '@angular/common';
import { Component, inject, Input, OnDestroy, OnInit } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs';


import { MatDialog } from '@angular/material/dialog';
import { AddServiceBookingComponent } from '../../../items-detail/service-booking/add-service-booking/add-service-booking.component';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { ActionCellRendererComponent } from './cell-renderer/action-cell-renderer.component';
import { CancelBookingComponent } from '../../../items-detail/service-booking/cancel-booking/cancel-booking.component';
import { VehicleModule } from '../vehicle-detail.module';
import { ServiceBookingDetailsComponent } from '../../../items-detail/service-booking/service-booking-details/service-booking-details.component';
import { IconModule } from '../../../../../../core/icon/icon.module';
import { AgGridCustomComponent, DropdownFormGroupComponent, FormGroupComponent } from '../../../../../../core/shared';
import { ServiceBookingService, ServiceBookingShareDataService, VehicleService } from '../../../../services';
import { LoadingService, NotificationService, UserService } from '../../../../../../core/services';
import { DEFAULT_COL_DEF, PERMISSIONS_CODE } from '../../../../../../core/constants';
import { ItemServiceBooking, Vehicle } from '../../../../interfaces';
import { OptionDropdown, PagingInfo } from '../../../../../../core/interfaces';
import { ActionModal, DateFormat, TimeZone } from '../../../../../../core/enums';
import { ServiceBookingAction, ServiceBookingStatus } from '../../../../enums';
import { handleErrors } from '../../../../../../core/helpers';

@Component({
  selector: 'app-service-booking-tab',
  standalone: true,
  imports: [
    CommonModule,
    IconModule,
    TranslateModule,
    ReactiveFormsModule,
    VehicleModule,
    AgGridCustomComponent,
    FormGroupComponent,
    DropdownFormGroupComponent,
  ],
  templateUrl: './service-booking-tab.component.html',
  styleUrl: './service-booking-tab.component.scss',
  providers: [DatePipe, ServiceBookingService, NotificationService],
})
export class ServiceBookingTabComponent implements OnInit, OnDestroy {
  @Input() set vin(value: string) {
    if (value) {
      this.vinValue = value;
      this.getList();
    }
  }

  serviceBookingService = inject(ServiceBookingService);
  notificationService = inject(NotificationService);
  translateService = inject(TranslateService);
  loadingService = inject(LoadingService);
  datePipe = inject(DatePipe);
  dialog = inject(MatDialog);
  vehicleService = inject(VehicleService);
  userService = inject(UserService);
  serviceBookingShareDataService = inject(ServiceBookingShareDataService);

  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;

  subscription = new Subscription();

  vinValue: string;
  rowData: ItemServiceBooking[];

  pagingInfo: PagingInfo = {
    totalItems: 0,
    currentPage: 0,
    numberOfPage: 10,
  };

  defaultColDef = {
    ...DEFAULT_COL_DEF,
    valueFormatter: (params) => (params.value ? params.value : '-'),
  };
  colDefs = [
    {
      headerName: this.translateService.instant(
        'serviceBookingTab.serviceBookingList.id'
      ),
      headerValueGetter: () =>
        this.translateService.instant(
          'serviceBookingTab.serviceBookingList.id'
        ),
      flex: 1,
      autoHeight: true,
      wrapText: true,
      cellClass: 'cell-word-wrap can-click',
      field: 'dbmBookingNo',
      onCellClicked: (event) => this.viewDetail(event?.data),
    },
    {
      headerName: this.translateService.instant(
        'serviceBookingTab.serviceBookingList.reason'
      ),
      headerValueGetter: () =>
        this.translateService.instant(
          'serviceBookingTab.serviceBookingList.reason'
        ),
      flex: 1,
      field: 'bookingReasons',
      cellRenderer: (params) => {
        return params?.data?.bookingReason?.name
          ? `<span>${params?.data?.bookingReason?.name}</span>`
          : '-';
      },
    },
    {
      headerName: this.translateService.instant(
        'serviceBookingTab.serviceBookingList.status'
      ),
      headerValueGetter: () =>
        this.translateService.instant(
          'serviceBookingTab.serviceBookingList.status'
        ),
      flex: 1,
      field: 'status',
      cellRenderer: (params) => {
        return params?.value?.name
          ? `<span>${params?.value?.name}</span>`
          : '-';
      },
    },
    {
      headerName: this.translateService.instant(
        'serviceBookingTab.serviceBookingList.dealer'
      ),
      headerValueGetter: () =>
        this.translateService.instant(
          'serviceBookingTab.serviceBookingList.dealer'
        ),
      flex: 1,
      field: 'dealerText',
    },
    {
      headerName: this.translateService.instant(
        'serviceBookingTab.serviceBookingList.date'
      ),
      headerValueGetter: () =>
        this.translateService.instant(
          'serviceBookingTab.serviceBookingList.date'
        ),
      flex: 1,
      cellRenderer: (params) => {
        return `<span>${this.datePipe.transform(
          params?.data?.bookingDate,
          DateFormat.ShortDate, TimeZone.UTC8
        )} ${params?.data?.bookingTime}</span>`;
      },
    },
    {
      headerName: '',
      field: 'actions',
      flex: 1.2,
      cellRenderer: ActionCellRendererComponent,
      cellRendererParams: {
        onClick: (type: ServiceBookingAction, data: ItemServiceBooking) =>
          this.handleActionTable(type, data),
      },
      cellClassRules: {
        'hide-action': (params) =>
          params.data?.status?.code !== ServiceBookingStatus.Confirmed ||
          !this.userService.isHasPermission([
            PERMISSIONS_CODE.ECARE_SERVICE_BOOKING_MANAGE,
          ]),
      },
      cellClass: 'action-grid',
      sortable: false,
    },
  ];

  form = new FormGroup({
    id: new FormControl(),
    status: new FormControl(''),
  });

  statusServiceBooking: OptionDropdown[] = [];
  vehicleMileage: number;
  vehicleDetail: Vehicle;

  ngOnInit(): void {
    this.getStatus();
    this.subscription.add(
      this.vehicleService.$vehicleDetail.subscribe((vehicleDetail) => {
        this.vehicleMileage = vehicleDetail?.odo;
        this.vehicleDetail = vehicleDetail;
      })
    );

    this.subscription.add(
      this.serviceBookingShareDataService.isReloadList$.subscribe((value) => {
        if (value) {
          this.getList();
        }
      })
    )
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  getList(): void {
    const params = {
      vin: this.vinValue,
      currentPage: this.pagingInfo.currentPage,
      pageSize: this.pagingInfo.numberOfPage,
      status: this.form.value.status,
      id: this.form.value.id,
    };
    this.loadingService.showLoader();
    this.subscription.add(
      this.serviceBookingService.getAllServiceBookings(params).subscribe(
        (response) => {
          this.loadingService.hideLoader();
          this.rowData = response?.items;
          this.pagingInfo.totalItems = response?.pagination?.totalResults;
        },
        (error) => {
          this.loadingService.hideLoader();
          handleErrors(error, this.notificationService);
        }
      )
    );
  }

  getStatus(): void {
    this.loadingService.showLoader();
    this.subscription.add(
      this.serviceBookingService.getServiceBookingsSatus().subscribe(
        (response) => {
          this.loadingService.hideLoader();
          this.statusServiceBooking = response;
        },
        (error) => {
          this.loadingService.hideLoader();
          handleErrors(error, this.notificationService);
        }
      )
    );
  }

  onPageChange(value: number): void {
    this.pagingInfo.currentPage = value;
    this.getList();
  }

  changeItemPerPage(item) {
    this.pagingInfo.numberOfPage = item;
    this.pagingInfo.currentPage = 0;
    this.getList();
  }

  addServiceBooking(): void {
    if (!this.vehicleDetail?.hasActiveCustomerRelation) {
      this.serviceBookingService.activeCustomerRelation(this.vinValue);
      return;
    }
    const dialogRef = this.dialog.open(AddServiceBookingComponent, {
      width: '850px',
      maxHeight: '90vh',
      autoFocus: false,
      data: {
        isEdit: false,
        vin: this.vinValue,
        vehicleMileage: this.vehicleMileage,
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result?.action === ActionModal.Submit) {
        this.pagingInfo.currentPage = 0;
        this.getList();
      }
    });
  }

  search(): void {
    this.pagingInfo.currentPage = 0;
    this.getList();
  }

  handleActionTable(type: string, data: ItemServiceBooking) {
    if (type === ServiceBookingAction.Edit) {
      this.onEdit(data);
    } else {
      this.onCancelBooking(data);
    }
  }

  onEdit(data: ItemServiceBooking): void {
    if (!this.vehicleDetail?.hasActiveCustomerRelation) {
      this.serviceBookingService.activeCustomerRelation(this.vinValue);
      return;
    }

    this.loadingService.showLoader();

    this.subscription.add(
      this.serviceBookingService
        .getServiceBookingsDetail(data?.bookingNo)
        .subscribe(
          (response) => {
            this.loadingService.hideLoader();
            const dialogRef = this.dialog.open(AddServiceBookingComponent, {
              width: '850px',
              maxHeight: '90vh',
              autoFocus: false,
              data: {
                isEdit: true,
                vin: this.vinValue,
                serviceBookingId: data?.bookingNo,
                bookingDetail: response,
              },
            });

            dialogRef.afterClosed().subscribe((result) => {
              if (result?.action === ActionModal.Submit) {
                this.getList();
              }
            });
          },
          (error) => {
            this.loadingService.hideLoader();
            handleErrors(error, this.notificationService);
          }
        )
    );
  }

  onCancelBooking(data: ItemServiceBooking): void {
    if (!this.vehicleDetail?.hasActiveCustomerRelation) {
      this.serviceBookingService.activeCustomerRelation(this.vinValue);
      return;
    }

    const dialogRef = this.dialog.open(CancelBookingComponent, {
      width: '660px',
      autoFocus: false,
      data: {
        vin: this.vinValue,
        serviceBookingId: data?.bookingNo,
        dbmBookingNo: data?.dbmBookingNo
      },
    });

    dialogRef.afterClosed().subscribe((result) => {});
  }

  viewDetail(data: ItemServiceBooking): void {
    const dialogRef = this.dialog.open(ServiceBookingDetailsComponent, {
      width: '600px',
      maxHeight: '90vh',
      data: {
        serviceBookingId: data?.bookingNo,
        vin: this.vinValue,
        dbmBookingNo: data?.dbmBookingNo,
        hasActiveCustomerRelation: this.vehicleDetail?.hasActiveCustomerRelation
      },
    });

    dialogRef.afterClosed().subscribe();
  }
}
