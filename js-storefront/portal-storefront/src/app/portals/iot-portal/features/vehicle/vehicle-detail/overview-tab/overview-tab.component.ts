import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  inject,
  Input,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { CurrentLocationComponent } from '../../../items-detail/current-location/current-location.component';

import { DeviceWidgetComponent } from '../../../items-detail/device-widget/device-widget.component';
import { PmsDueComponent } from '../../../items-detail/pms-due/pms-due.component';
import { SscDueComponent } from '../../../items-detail/ssc-due/ssc-due.component';

import { TowingServiceComponent } from '../../../items-detail/towing-service/towing-service.component';
import {
  MaintenanceModeComponent,
  ServiceBookingComponent,
} from '../../../items-detail';
import { WarningComponent } from '../../../items-detail/warning/warning.component';
import { EmergencyComponent } from '../../../items-detail/emergency/emergency.component';
import { SnapShotRecordComponent } from '../../../items-detail/snap-shot-record/snap-shot-record.component';
import { DiagnosticTroubleCodeComponent } from '../../../items-detail/diagnostic-trouble-code/diagnostic-trouble-code.component';
import { TicketWidgetComponent } from '../../../items-detail/ticket-widget/ticket-widget.component';
import { LoanWidgetComponent } from '../../../items-detail/loan-widget/loan-widget.component';
import { OwnerWidgetComponent } from '../../../items-detail/owner-widget/owner-widget.component';
import { VehicleInfoComponent } from '../../../items-detail/vehicle-info/vehicle-info.component';

import { finalize, Subscription } from 'rxjs';
import { SubscriptionWidgetComponent } from '../../../items-detail/subscription-widget/subscription-widget.component';
import { VehicleModule } from '../vehicle-detail.module';
import { HistoryLogWidgetComponent } from '../../../items-detail/history-log-widget/history-log-widget.component';
import { OwnershipVerificationWidgetComponent } from '../../../items-detail/ownership-verification-widget/ownership-verification-widget.component';
import { DtcWidgetComponent } from '../../../items-detail/dtc-widget/dtc-widget.component';
import { LoadingService, NotificationService, UserService } from '../../../../../../core/services';
import { ServiceCampaignsService, VehicleService } from '../../../../services';
import { DTCWidget, ItemDevice, ItemServiceCampaign, ItemSubscription, OwnershipVerificationWidget, OwnerWidget, Ticket, TicketWidget, Vehicle } from '../../../../interfaces';
import { DataStoreService } from '../../../../../../core/services/data-store.service';
import { PERMISSIONS_CODE } from '../../../../../../core/constants';
import { DeviceManagementTab } from '../../../../enums';
import { handleErrors } from '../../../../../../core/helpers';
import { TicketsDetailService } from '../../../../services/tickets/ticket-detail.service';
import { Emergency, HistoryLog, LoanItem, PMSWidget, Warning } from '../../../../../../core/interfaces/vehicle.interface';

@Component({
  selector: 'app-overview-tab',
  standalone: true,
  imports: [
    CommonModule,
    VehicleModule,
    CurrentLocationComponent,
    DeviceWidgetComponent,
    PmsDueComponent,
    SscDueComponent,
    TowingServiceComponent,
    ServiceBookingComponent,
    WarningComponent,
    EmergencyComponent,
    SnapShotRecordComponent,
    DiagnosticTroubleCodeComponent,
    TicketWidgetComponent,
    LoanWidgetComponent,
    OwnerWidgetComponent,
    VehicleInfoComponent,
    SubscriptionWidgetComponent,
    HistoryLogWidgetComponent,
    MaintenanceModeComponent,
    OwnershipVerificationWidgetComponent,
    DtcWidgetComponent
  ],
  providers: [NotificationService, TicketsDetailService, ServiceCampaignsService],
  templateUrl: './overview-tab.component.html',
  styleUrls: ['./overview-tab.component.scss'],
})
export class OverviewTabComponent implements OnInit {
  @Input() vin: string = '';
  @Output() waringId = new EventEmitter<string>();
  @Output() viewFFD = new EventEmitter();

  @Output() vehilceInfo = new EventEmitter<Vehicle>();

  rowData: ItemDevice[];
  sscData: ItemServiceCampaign[];
  pmsData: PMSWidget;
  towingServiceData: any = null;
  emergencyData: Emergency[] = [];
  warningData: Warning[] = [];
  vehicle: Vehicle;
  ticketData: Ticket[] = [];
  rowDataSub: ItemSubscription[];
  mainSubscription = new Subscription();
  dtcData: DTCWidget[] = [];
  ownerData: OwnerWidget;
  ownershipVerificationData: {enabledOwnership: boolean, ownerVerificationHistory: OwnershipVerificationWidget[]};
  rowLoanData: LoanItem[] = [];
  historyLogData: HistoryLog[];
  countAPI: number = 0;

  private notificationService = inject(NotificationService);
  private vehicleService = inject(VehicleService);
  private ticketsService = inject(TicketsDetailService);
  private loadingService = inject(LoadingService);
  private dataStoreService = inject(DataStoreService);

  userService = inject(UserService);

  serviceCampaignsService = inject(ServiceCampaignsService);

  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;
  readonly DeviceManagementTab = DeviceManagementTab;

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['vin'] && changes['vin'].currentValue) {
      

      this.userService.allPermission.forEach((item) => {
        if (
          [
            PERMISSIONS_CODE.ECARE_VEHICLE_IOT_VIEW,
            PERMISSIONS_CODE.ECARE_OWNER_VIEW,
          ]?.includes(item)
        ) {
          this.countAPI = this.countAPI + 2;
        } else if ([
            PERMISSIONS_CODE.ECARE_TICKET_LIST_VIEW,
            PERMISSIONS_CODE.IOT_DEVICE_SIM_LIST_VIEW,
            PERMISSIONS_CODE.IOT_SUBSCRIPTION_LIST_VIEW,
            PERMISSIONS_CODE.ECARE_TOWING_VIEW,
            PERMISSIONS_CODE.ECARE_SSC_VIEW,
            PERMISSIONS_CODE.ECARE_PMS_VIEW,
            PERMISSIONS_CODE.ECARE_EMERGENCY_VIEW,
          ]?.includes(item)
        ) {
          this.countAPI++;
        }
      });

      this.loadingService.showLoader();
      if (
        this.userService.isHasPermission([
          PERMISSIONS_CODE.ECARE_VEHICLE_IOT_VIEW,
        ])
      ) {
        this.getVehicleWidget();
        this.getHistoryLogWidget();
      }

      if (
        this.userService.isHasPermission([
          PERMISSIONS_CODE.ECARE_TICKET_LIST_VIEW,
        ])
      ) {
        this.getTicketWidget();
      }

      if (
        this.userService.isHasPermission([
          PERMISSIONS_CODE.IOT_DEVICE_SIM_LIST_VIEW,
        ])
      ) {
        this.getDeviceWidget();
      }

      if (
        this.userService.isHasPermission([
          PERMISSIONS_CODE.IOT_SUBSCRIPTION_LIST_VIEW,
        ])
      ) {
        this.getSubscriptionWidget();
      }

      if (
        this.userService.isHasPermission([PERMISSIONS_CODE.ECARE_TOWING_VIEW])
      ) {
        this.getTowingServiceDetails();
      }

      if (this.userService.isHasPermission([PERMISSIONS_CODE.ECARE_SSC_VIEW])) {
        this.getSSCWidget();
      }

      if (this.userService.isHasPermission([PERMISSIONS_CODE.ECARE_PMS_VIEW])) {
        this.getPMSWidget();
      }

      if (
        this.userService.isHasPermission([
          PERMISSIONS_CODE.ECARE_EMERGENCY_VIEW,
          PERMISSIONS_CODE.ECARE_WARNING_VIEW,
        ])
      ) {
        this.getNotificationList();
      }

      if (
        this.userService.isHasPermission([PERMISSIONS_CODE.ECARE_OWNER_VIEW])
      ) {
        this.getOwnerWidget();
        this.getOwnershipVerificationWidget();
      }
    }
  }

  ngOnInit() {}

  ngOnDestroy() {
    this.mainSubscription && this.mainSubscription.unsubscribe();
  }

  handleCountAPI() {
    this.countAPI -= 1;
    if (this.countAPI === 0) {
      this.loadingService.hideLoader();
    }
  }

  getLoanWidget(isRefresh: boolean = false) {
    isRefresh && this.loadingService.showLoader();
    this.mainSubscription.add(
      this.vehicleService
        .getLoanWidget(this.vin)
        .pipe(finalize(() => this.handleCountAPI()))
        .subscribe(
          (res: LoanItem) => {
            if (res) {
              this.rowLoanData = [{ ...res }];
            }
            isRefresh && this.loadingService.hideLoader();
          },
          (err) => {
            handleErrors(err, this.notificationService);
            isRefresh && this.loadingService.hideLoader();
          }
        )
    );
  }

  getVehicleWidget() {
    this.mainSubscription.add(
      this.vehicleService.getVehicleWidget(this.vin).subscribe(
        (res: Vehicle) => {
          if (res) {
            this.vehicle = res;
            this.vehilceInfo.emit(this.vehicle);
            this.dataStoreService.setDeviceType(res.deviceType);
            this.vehicleService.$vehicleDetail.next(res);
            if (
              res?.vehicleType === 'B2B' &&
              res?.vehicleSubType === 'CAL' &&
              this.userService.isHasPermission([
                PERMISSIONS_CODE.IOT_B2B_CAL_VEHICLE_LIST_VIEW,
              ])
            ) {
              this.countAPI += 1;
              this.getLoanWidget();
            }
          }
          this.handleCountAPI();
        },
        (err) => {
          handleErrors(err, this.notificationService);
          this.handleCountAPI();
        }
      )
    );
  }

  getTicketWidget(isRefresh: boolean = false) {
    isRefresh && this.loadingService.showLoader();
    this.mainSubscription.add(
      this.vehicleService
        .getTicketWidget(this.vin)
        .pipe(finalize(() => this.handleCountAPI()))
        .subscribe(
          (res: TicketWidget) => {
            if (res) {
              this.ticketData = res.tickets;
            }
            isRefresh && this.loadingService.hideLoader();
          },
          (err) => {
            handleErrors(err, this.notificationService);
            isRefresh && this.loadingService.hideLoader();
          }
        )
    );
  }

  getDeviceWidget() {
    this.mainSubscription.add(
      this.vehicleService
        .getDeviceWidget(this.vin)
        .pipe(finalize(() => this.handleCountAPI()))
        .subscribe(
          (res: ItemDevice) => {
            if (res) {
              this.rowData = [{ ...res }];
            }
          },
          (err) => {
            handleErrors(err, this.notificationService);
          }
        )
    );
  }

  getSubscriptionWidget() {
    this.mainSubscription.add(
      this.vehicleService
        .getSubscriptionWidget(this.vin)
        .pipe(finalize(() => this.handleCountAPI()))
        .subscribe(
          (res: ItemSubscription) => {
            if (res) {
              const rowData = {
                ...res,
                package: {
                  isFreeSubscription: res?.isFreeSubscription,
                  packageType: res?.packageType,
                },
                period: {
                  endDate: res?.endDate,
                  startDate: res?.startDate,
                },
              };
              this.rowDataSub = [rowData];
            }
          },
          (err) => {
            handleErrors(err, this.notificationService);
          }
        )
    );
  }

  getTowingServiceDetails(isRefresh: boolean = false) {
    isRefresh && this.loadingService.showLoader();
    this.mainSubscription.add(
      this.ticketsService
        .getTowingServiceDetails(this.vin)
        .pipe(finalize(() => !isRefresh && this.handleCountAPI()))
        .subscribe(
          (res) => {
            if (res && Object.keys(res).length > 0) {
              this.towingServiceData = res;
            }
            isRefresh && this.loadingService.hideLoader();
          },
          (error) => {
            handleErrors(error, this.notificationService);
            isRefresh && this.loadingService.hideLoader();
          }
        )
    );
  }

  getSSCWidget() {
    const payload = {
      vin: this.vin,
      currentPage: 0,
      pageSize: 5,
    };
    this.mainSubscription.add(
      this.serviceCampaignsService
        .getServiceCampaigns(payload)
        .pipe(finalize(() => this.handleCountAPI()))
        .subscribe(
          (res) => {
            if (res) {
              this.sscData = res?.items;
            }
          },
          (err) => {
            handleErrors(err, this.notificationService);
          }
        )
    );
  }

  getPMSWidget() {
    this.mainSubscription.add(
      this.vehicleService
        .getPMSWidget(this.vin)
        .pipe(finalize(() => this.handleCountAPI()))
        .subscribe(
          (res: PMSWidget) => {
            if (res) {
              this.pmsData = res;
            }
          },
          (err) => {
            handleErrors(err, this.notificationService);
          }
        )
    );
  }

  getNotificationList() {
    this.mainSubscription.add(
      this.vehicleService
        .getWarningListForVehicle(this.vin)
        .pipe(finalize(() => this.handleCountAPI()))
        .subscribe(
          (res) => {
            if (res) {
              this.warningData = res.warnings?.items;
              this.emergencyData = res.emergencies?.items;
            }
          },
          (err) => {
            handleErrors(err, this.notificationService);
          }
        )
    );
  }

  getOwnerWidget() {
    this.mainSubscription.add(
      this.vehicleService
        .getOwnerWidget(this.vin, null)
        .pipe(finalize(() => this.handleCountAPI()))
        .subscribe(
          (res: OwnerWidget) => {
            if (res) {
              this.ownerData = res;
            }
          },
          (err) => {
            handleErrors(err, this.notificationService);
          }
        )
    );
  }

  getOwnershipVerificationWidget() {
    this.mainSubscription.add(
      this.vehicleService
        .getOwnershipVerificationWidget(this.vin)
        .pipe(finalize(() => this.handleCountAPI()))
        .subscribe(
          (res) => {
            if (res) {
              this.ownershipVerificationData = res;
            }
          },
          (err) => {
            handleErrors(err, this.notificationService);
          }
        )
    );
  }

  refreshCurrentLocation() {
    this.loadingService.showLoader();
    this.mainSubscription.add(
      this.vehicleService
        .refreshCurrentLocation(this.vin)
        .pipe(finalize(() => this.loadingService.hideLoader()))
        .subscribe(
          (res: Vehicle) => {
            if (res) {
              this.vehicle = { ...this.vehicle, ...res };
            }
          },
          (err) => {
            handleErrors(err, this.notificationService);
          }
        )
    );
  }

  onWarningDetails(warningId: string) {
    this.waringId.emit(warningId);
  }

  getHistoryLogWidget() {
    this.mainSubscription.add(
      this.vehicleService
        .getHistoryLogWidget(this.vin)
        .pipe(finalize(() => this.handleCountAPI()))
        .subscribe(
          (res: any) => {
            if (res) {
              this.historyLogData = res;
            }
          },
          (err) => {
            handleErrors(err, this.notificationService);
          }
        )
    );
  }
}
