@import "../../../../../../../styles/abstracts/variables";
@import "../../../../../../../styles/abstracts/mixins";

:host {
  .subscription-tab {
    @include box-wrapper;

    &__title {
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: 22px;
      font-weight: 600;
      margin-bottom: 30px;

      mat-icon {
        width: 32px;
        height: 32px;
      }
    }

    .no-data {
      color: $text-color-4;
      font-size: 20px;
      font-weight: 600;
      text-align: center;
    }

    app-ag-grid-custom {
      ::ng-deep {
        .ag-header-cell:last-child {
          .ag-header-cell-label {
            justify-content: start;
          }
        }

        .ag-cell p {
          margin: 0;
        }

        .value-tag {
          .ag-cell-value {
            @include value-tag;
          }
        }
      }
    }
  }
}
