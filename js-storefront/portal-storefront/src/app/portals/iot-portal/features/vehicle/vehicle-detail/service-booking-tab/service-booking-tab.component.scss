@import "../../../../../../../styles/abstracts/variables";
@import "../../../../../../../styles/abstracts/mixins";

:host {
  .service-booking-tab {
    @include box-wrapper;

    &__title {
      display: flex;
      justify-content: space-between;
      margin-bottom: 30px;

      > div {
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 22px;
        font-weight: 600;

        mat-icon {
          width: 32px;
          height: 32px;
        }
      }

      .btn-primary {
        text-transform: uppercase;
      }
    }

    &__filter {
      display: grid;
      grid-template-columns: 2fr 1fr auto;
      gap: 20px;

      .search {
        height: 40px;
        margin-top: 20px;
      }
    }

    .no-data {
      color: $text-color-4;
      font-size: 20px;
      font-weight: 600;
      text-align: center;
      padding: 30px;
    }

    app-ag-grid-custom {
      ::ng-deep {
        .ag-header-cell:last-child {
          .ag-header-cell-label {
            justify-content: start;
          }
        }

        .action-buttons-cell-renderer {
          display: inline-flex;
          gap: 25px;

          button {
            display: flex;
            align-items: center;
            gap: 5px;

            &:last-child {
              color: inherit;
            }
          }
        }

        .custom-grid {
          .hide-action {
            display: none;
          }
        }
      }
    }
  }
}
