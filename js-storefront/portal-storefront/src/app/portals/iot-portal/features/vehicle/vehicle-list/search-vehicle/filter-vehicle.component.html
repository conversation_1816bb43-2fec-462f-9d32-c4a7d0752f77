<div *ngIf="form" [formGroup]="form">
    <form [formGroup]="form" (ngSubmit)="search()">
        <app-form-group [label]="'vehicleManagement.vinOrPlateNo' | translate" [control]="getControl('vin')"
            [placeholder]="'vehicleManagement.vinPlaceholder' | translate" controlId="vin"
            class="vehicle-model-section__input"></app-form-group>
        <app-form-group *ngIf="userService.isHasPermission([PERMISSIONS_CODE.ECARE_OWNER_VIEW])" [label]="'vehicleManagement.customerNameOrPhone' | translate"
            [control]="getControl('nameOrPhone')"
            [placeholder]="'vehicleManagement.customerNameOrPhonePlaceholder' | translate" controlId="nameOrPhone"
            class="vehicle-model-section__input"></app-form-group>

        <button type="submit" class="btn-tertiary search">
            {{ 'common.search' | translate }}
        </button>
    </form>
</div>