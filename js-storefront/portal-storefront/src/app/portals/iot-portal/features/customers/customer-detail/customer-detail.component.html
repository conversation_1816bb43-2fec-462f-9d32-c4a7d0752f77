<div class="title">
  <div class="title__left">
    <app-breadcrumb-with-label
      [breadcrumbs]="[
        'customers.customerDetail.customers',
        ownerData?.name
      ]"
      [breadcrumbLinks]="[
        '/customers',
        '/customers/' + (customerId || '')
      ]"
      [label]="'customers.customerDetail.title' | translate"
    >
    </app-breadcrumb-with-label>
  </div>
</div>

<div class="customer-detail">
  <div class="customer-detail__owner-detail">
    <div class="customer-detail__box">
      <app-owner-widget [data]="ownerData" [vin]="vin"></app-owner-widget>
    </div>
  </div>
  <div *ngIf="vehicleList?.length > 0" class="customer-detail__vehicle-detail">
    @for (item of vehicleList; track $index) {
    <div class="customer-detail__box">
      <app-vehicle-info
        class="vehicle-info"
        [vehicle]="item"
        [vin]="item?.vin"
        [isAction]="true"
        (viewDetailVehicle)="viewDetailVehicle(item?.vin)"
        (createTicket)="createTicket(item?.vin)"
      ></app-vehicle-info>
    </div>
    }
  </div>
</div>
