@import "../../../../../styles/abstracts/mixins";
@import "../../../../../styles/abstracts/variables";

:host {
  min-height: calc(100vh - 75px);
  display: block;

  .header-section {
    background-color: $bg-color-9;
    padding: 20px 16px 100px;

    display: flex;
    justify-content: space-between;

    @include md {
      padding: 30px 30px 135px;
    }

    .title-page {
      color: $text-color-white;
      font-size: $fs38;
      margin: 0;
    }
  }

  .customer-list {
    padding: 0 16px 16px;

    @include md {
      padding: 0 30px;
    }

    .no-data {
      font-size: $fs18;
      font-weight: $fw600;
      text-align: center;
      padding: 20px;
    }

    .customer-table {
      ::ng-deep {
        .custom-grid {
          .ag-cell {
            line-height: 24px;
            padding: 15px;

            span {
              p {
                margin: 0;
              }
            }
          }

          .isOddMergeRow {
            background-color: #f5f5f5!important;
            &::before {
              background-color: #f5f5f5!important;
            }
          }

          .isOddMergeRow:nth-child(even), .isEvenMergeRow:nth-child(odd) {
            border-top: 1px solid #cccccc;
            border-bottom: 1px solid #cccccc;
          }

          .isEvenMergeRow {
            background-color: #ffffff!important;
            &::before  {
              background-color: #ffffff!important;
            }
          }

          .border-right {
            border-right: 1px solid #cccccc;
            pointer-events: none;
          }
        }

        .row-odd {
          background-color: #ffffff!important;
        }

        .row-even {
          background-color: #f5f5f5!important;
        }
      }
    }
  }
}
