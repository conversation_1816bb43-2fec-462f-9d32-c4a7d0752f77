@import "../../../../../../styles/abstracts/mixins";
@import "../../../../../../styles/abstracts/variables";

:host {
  min-height: calc(100vh - 75px);
  display: block;
  padding: 30px 30px 0;

  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .customer-detail {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    padding: 15px 30px 40px 30px;
    background-color: #f5f5f5;

    @include lg-max {
      flex-direction: column;
      > * {
        width: 100%;
      }
    }

    &__box {
      background-color: $main-bg-color;
      padding: 25px;
      box-shadow: 0px 4px 5px 5px #00000008;
    }

    &__owner-detail {
      flex: 1;
    }

    &__vehicle-detail {
      flex: 2;
      display: flex;
      flex-direction: column;
      gap: 15px;

      .vehicle-info {
        ::ng-deep {
          .vehicle-info {
            display: flex;
            gap: 35px;

            > :first-child {
                flex: 1;
            }

            > :last-child {
                flex: 2;
            }

            &__right {
                display: flex;
                justify-content: space-between;
                flex-direction: column;
            }
          }
        }
      }
    }
  }
}
