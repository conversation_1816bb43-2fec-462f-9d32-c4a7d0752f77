<div class="header-section">
  <h2 class="title-page">
    {{ "customers.customers" | translate }}
  </h2>
</div>

<app-widget-summary
  [widgets]="widgets"
  (changeTab)="changeSummaryTab($event)"
></app-widget-summary>

<app-filter-customers
  [form]="filterForm"
  (search)="onSearch()"
></app-filter-customers>

<div class="customer-list">
  @if (rowData?.length > 0 ) {
  <app-ag-grid-custom
    class="customer-table"
    [rowData]="rowData"
    [colDefs]="colDefs"
    [defaultColDef]="defaultColDef"
    [isPaging]="true"
    [pagingInfo]="pagingInfo"
    [isShowActionExport]="false"
    (onPageChange)="onPageChange($event)"
    (changeItemPerPage)="changeItemPerPage($event)"
    [rowClassRules]="rowClassRules"
  >
  </app-ag-grid-custom>
  } @else {
  <div class="no-data">{{ "customers.noData" | translate }}</div>
  }
</div>
