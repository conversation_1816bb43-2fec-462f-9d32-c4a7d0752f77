import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { FormGroupComponent } from '../../../../../core/shared';

@Component({
  selector: 'app-filter-customers',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    FormGroupComponent,
  ],
  templateUrl: './filter-customers.component.html',
  styleUrl: './filter-customers.component.scss',
})
export class FilterCustomersComponent {
  @Input() form: any;

  @Output() search = new EventEmitter();
}
