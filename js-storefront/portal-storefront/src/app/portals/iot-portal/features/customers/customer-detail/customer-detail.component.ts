import { CommonModule } from '@angular/common';
import { Component, inject, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ActivatedRoute, Router } from '@angular/router';
import { finalize, Subscription } from 'rxjs';

import { OwnerWidgetComponent } from '../../items-detail/owner-widget/owner-widget.component';
import { VehicleInfoComponent } from '../../items-detail/vehicle-info/vehicle-info.component';
import { VehicleModule } from '../../vehicle/vehicle-detail/vehicle-detail.module';
import { BreadcrumbWithLabelComponent } from '../../../../../core/shared';
import { LoadingService, NotificationService, UserService } from '../../../../../core/services';
import { CustomersService, VehicleService } from '../../../services';
import { PERMISSIONS_CODE } from '../../../../../core/constants';
import { OwnerWidget, Vehicle } from '../../../interfaces';
import { handleErrors } from '../../../../../core/helpers';

@Component({
  selector: 'app-customer-detail',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    BreadcrumbWithLabelComponent,
    OwnerWidgetComponent,
    VehicleInfoComponent,
    VehicleModule,
  ],
  templateUrl: './customer-detail.component.html',
  styleUrl: './customer-detail.component.scss',
  providers: [NotificationService, CustomersService],
})
export class CustomerDetailComponent implements OnInit, OnDestroy {
  activatedRoute = inject(ActivatedRoute);
  loadingService = inject(LoadingService);
  translateService = inject(TranslateService);
  notificationService = inject(NotificationService);
  router = inject(Router);

  userService = inject(UserService);
  customersService = inject(CustomersService);
  vehicleService = inject(VehicleService);

  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;

  subscription = new Subscription();

  customerDetail: any;
  customerId: string;
  ownerData: OwnerWidget;
  vin: string;
  vehicleList: Vehicle[];

  countAPI = 2;

  ngOnInit(): void {
    this.customerId = this.activatedRoute.snapshot.paramMap.get('id');
    this.getOwnerDetail();
    this.getVehicleList();
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  getOwnerDetail(): void {
    this.loadingService.showLoader();
    this.vehicleService.getOwnerWidget(null, this.customerId)
    .pipe(finalize(() => this.handleCountAPI()))
    .subscribe(
      (response) => {
        this.ownerData = response;
      },
      (errors) => {
        handleErrors(errors, this.notificationService);
      }
    );
  }

  getVehicleList(): void {
    this.loadingService.showLoader();
    this.customersService.getVehicleListByCustomer(this.customerId)
    .pipe(finalize(() => this.handleCountAPI()))
    .subscribe(
      (response) => {
        this.loadingService.hideLoader();
        this.vehicleList = response;
      },
      (errors) => {
        this.loadingService.hideLoader();
        handleErrors(errors, this.notificationService);
      }
    );
  }
  viewDetailVehicle(vin: string) {
    this.router.navigate(['/vehicles', vin]);
  }

  createTicket(vin: string): void {
    this.router.navigate(['/vehicles', vin], {
      queryParams: { openCreateTicket: true },
    });
  }

  handleCountAPI() {
    this.countAPI -= 1;
    if (this.countAPI === 0) {
      this.loadingService.hideLoader();
    }
  }
}
