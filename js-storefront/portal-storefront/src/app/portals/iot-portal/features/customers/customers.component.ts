import { CommonModule, DecimalPipe } from '@angular/common';
import { Component, inject, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { FilterCustomersComponent } from './filter-customers/filter-customers.component';
import { CustomHeaderTicketComponent } from '../ticket/custom-header-ticket/custom-header-ticket.component';
import { CellRendererComponent } from '../vehicle/vehicle-list/cell-renderer/cell-renderer.component';
import { RowClassRules } from 'ag-grid-community';
import { AgGridCustomComponent, RouterLinkCellRendererComponent, WidgetSummaryComponent } from '../../../../core/shared';
import { CustomersService } from '../../services';
import { LoadingService, NotificationService, UserService } from '../../../../core/services';
import { PERMISSIONS_CODE } from '../../../../core/constants';
import { ItemWidget, PagingInfo } from '../../../../core/interfaces';
import { CUSTOMERS_WIDGETS } from '../../constants';
import { handleErrors } from '../../../../core/helpers';
import { CustomerSummaryTab } from '../../enums';

@Component({
  selector: 'app-customers',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    ReactiveFormsModule,
    MatIconModule,
    WidgetSummaryComponent,
    AgGridCustomComponent,
    FilterCustomersComponent,
  ],
  templateUrl: './customers.component.html',
  styleUrl: './customers.component.scss',
  providers: [CustomersService, NotificationService, DecimalPipe],
})
export class CustomersComponent implements OnInit, OnDestroy {
  loadingService = inject(LoadingService);
  translateService = inject(TranslateService);
  router = inject(Router);
  notificationService = inject(NotificationService);

  userService = inject(UserService);
  customersService = inject(CustomersService);
  decimalPipe = inject(DecimalPipe);

  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;
  subscription = new Subscription();

  pagingInfo: PagingInfo = {
    totalItems: 0,
    currentPage: 0,
    numberOfPage: 10,
  };

  widgets: ItemWidget[] = [
    {
      id: '',
      count: 0,
      description: '',
      icon: '',
    },
  ];
  selectedSummaryTab: any;

  rowData: any[];
  defaultColDef = {
    resizable: false,
    sortable: false,
    menuTabs: [],
    wrapHeaderText: true,
    autoHeaderHeight: true,
  };

  colDefs: any = [
    {
      headerName: this.translateService.instant('customers.table.customerName'),
      headerValueGetter: () =>
        this.translateService.instant('customers.table.customerName'),
      field: 'customerName',
      flex: 1,
      autoHeight: true,
      wrapText: true,
      cellClass: 'cell-word-wrap',
      spanRows: (param) => {
        const {valueA, valueB, nodeA, nodeB} = param;
        return valueA === valueB && nodeA?.data?.mobile === nodeB?.data?.mobile
        && nodeA?.data?.email === nodeB?.data?.email;
      },
      cellClassRules: {
        'row-even': (params) => params.data?.isOddRow,
        'row-odd': (params) => !params.data?.isOddRow
      },
      cellRenderer: RouterLinkCellRendererComponent,
      cellRendererParams: {
        linkBuilder: (data: any) => `customers/${data?.customerId}`,
      }
    },
    {
      headerName: this.translateService.instant('customers.table.mobileNumber'),
      headerValueGetter: () =>
        this.translateService.instant('customers.table.mobileNumber'),
      field: 'mobile',
      flex: 1,
      autoHeight: true,
      cellClass: 'cell-word-wrap',
      cellClassRules: {
        'row-even': (params) => params.data?.isOddRow,
        'row-odd': (params) => !params.data?.isOddRow
      },
      wrapText: true,
      spanRows: (param) => {
        const {valueA, valueB, nodeA, nodeB} = param;
        return valueA === valueB && nodeA?.data?.customerName === nodeB?.data?.customerName
        && nodeA?.data?.email === nodeB?.data?.email;
      },
    },
    {
      headerName: this.translateService.instant('customers.table.email'),
      headerValueGetter: () =>
        this.translateService.instant('customers.table.email'),
      field: 'email',
      flex: 1,
      autoHeight: true,
      cellClass: 'cell-word-wrap border-right',
      spanRows: (param) => {
        const {valueA, valueB, nodeA, nodeB} = param;
        return valueA === valueB && nodeA?.data?.customerName === nodeB?.data?.customerName
        && nodeA?.data?.mobile === nodeB?.data?.mobile;
      },
      cellClassRules: {
        'row-even': (params) => params.data?.isOddRow,
        'row-odd': (params) => !params.data?.isOddRow
      },
      wrapText: true,
    },
    {
      headerName: `VIN / Plate No.`,
      headerValueGetter: () =>
        this.translateService.instant('customers.table.vin'),
      field: 'vin',
      flex: 1,
      headerComponent: CustomHeaderTicketComponent,
      headerComponentParams: {
        secondHeader: this.translateService.instant('customers.table.plateNo'),
      },
      cellRenderer: (params) => {
        const vin = params.data?.vin || '-';
        const plateNo = params.data?.plateNo || '-';
        return `<span class="two-col">${vin}<br>${plateNo}</span>`;
      },
      autoHeight: true,
      cellClass: 'cell-word-wrap',
      wrapText: true,
    },
    {
      headerName: `Make / Model`,
      headerValueGetter: () =>
        this.translateService.instant('customers.table.make'),
      field: 'makeAndModel',
      flex: 1,
      headerComponent: CustomHeaderTicketComponent,
      headerComponentParams: {
        secondHeader: this.translateService.instant('customers.table.model'),
      },
      cellRenderer: (params) => {
        const { vehicleMake = '-', vehicleModel = '-' } = params.data || {};
        return `<span class="two-col">${vehicleMake}<br>${vehicleModel}</span>`;
      },
      tooltipValueGetter: (params) => {
        const { vehicleMake = '-', vehicleModel = '-' } = params.data || {};
        return `${vehicleMake} - ${vehicleModel}`;
      },
      autoHeight: true,
      wrapText: true,
      cellClass: 'cell-word-wrap',
    },
    {
      headerName: this.translateService.instant('customers.table.emergencies'),
      headerValueGetter: () =>
        this.translateService.instant('customers.table.emergencies'),
      field: 'emergencyName',
      flex: 1.5,

      cellRenderer: CellRendererComponent,
      cellRendererParams: {
        config: {
          icon: 'ic-emergency-1',
          fields: [
            { key: 'emergencyName', format: 'text' },
            { key: 'emergencyCreateTime', format: 'text' },
          ],
          totalKey: 'numOfEmergency',
          id: 'emergency',
        },
      },
      tooltipValueGetter: (params) => {
        const { emergencyName = '', emergencyCreateTime = '' } =
          params.data || {};
        return emergencyName || emergencyCreateTime
          ? `${emergencyName} - ${emergencyCreateTime}`
          : null;
      },
      autoHeight: true,
      wrapText: true,
      cellClass: 'cell-word-wrap',
    },
    {
      headerName: this.translateService.instant('customers.table.warnings'),
      headerValueGetter: () =>
        this.translateService.instant('customers.table.warnings'),
      field: 'warningName',
      flex: 1.5,
      cellRenderer: CellRendererComponent,
      cellRendererParams: {
        config: {
          icon: '',
          fields: [
            { key: 'warningName', format: 'text' },
            { key: 'warningOccurredTime', format: 'text' },
          ],
          totalKey: 'numOfWarning',
          id: 'warning',
        },
      },
      tooltipValueGetter: (params) => {
        const { warningName = '', warningOccurredTime = '' } =
          params.data || {};
        return warningName || warningOccurredTime
          ? `${warningName} - ${warningOccurredTime}`
          : null;
      },
      autoHeight: true,
      wrapText: true,
      cellClass: 'cell-word-wrap',
    },
    {
      headerName: this.translateService.instant('customers.table.sscDue'),
      headerValueGetter: () =>
        this.translateService.instant('customers.table.sscDue'),
      field: 'sscDue',
      flex: 1,
      cellRenderer: CellRendererComponent,
      cellRendererParams: {
        config: {
          icon: '',
          fields: [
            { key: 'sscCategory', format: 'text' },
            { key: 'sscExpiryDate', format: 'text' },
          ],
          totalKey: 'numOfSSC',
          id: 'sscDue',
        },
      },
      tooltipValueGetter: (params) => {
        const { sscCategory = '', sscExpiryDate = '' } = params.data || {};
        return sscCategory || sscExpiryDate
          ? `${sscCategory} - ${sscExpiryDate}`
          : null;
      },
      autoHeight: true,
      wrapText: true,
      cellClass: 'cell-word-wrap',
    },
    {
      headerName: 'PMS Due',
      headerValueGetter: () =>
        this.translateService.instant('customers.table.pmsDue'),
      field: 'nextMileage',
      flex: 1,
      cellRenderer: (params) => {
        const { nextMileage } = params.data;
        return nextMileage > 0
          ? `${this.decimalPipe.transform(
              nextMileage
            )}km ${this.translateService.instant('vehicleManagement.checkUp')}`
          : '-';
      },
      autoHeight: true,
      wrapText: true,
      cellClass: 'cell-word-wrap',
    },
  ];


  filterForm = new FormGroup({
    customerName: new FormControl(),
    mobileNumber: new FormControl(),
    email: new FormControl(),
    vin: new FormControl(),
    filterSSC: new FormControl(false),
    filterHighWarning: new FormControl(false),
    filterHighEmergency: new FormControl(false),
  });

  rowClassRules: RowClassRules = {
    isOddMergeRow: (params) => {
      return params.data.isOddRow;
    },
    isEvenMergeRow: (params) => {
      return !params.data.isOddRow;
    },
  };

  ngOnInit(): void {
    this.getWidgets();
    this.getList();
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  getWidgets(): void {
    this.loadingService.showLoader();
    this.subscription.add(
      this.customersService.getWidget().subscribe((response) => {
        this.loadingService.hideLoader();
        const { widgets } = response;

        this.widgets = widgets.map((item) => {
          const { description, icon } =
            CUSTOMERS_WIDGETS?.find((widget) => widget.id === item.type) || {};
          return {
            id: item?.type,
            count: item?.count,
            description,
            icon,
          };
        });
      })
    );
  }

  getList(): void {
    this.loadingService.showLoader();

    const payload = {
      currentPage: this.pagingInfo?.currentPage,
      pageSize: this.pagingInfo?.numberOfPage,
      customerName: this.filterForm.value.customerName,
      mobileNumber: this.filterForm.value.mobileNumber,
      email: this.filterForm.value.email,
      vinOrPlateNo: this.filterForm.value.vin,

      filterActiveEmergency: this.filterForm.value.filterHighEmergency,
      filterHighWarning: this.filterForm.value.filterHighWarning,
      filterSSC: this.filterForm.value.filterSSC,
    };

    this.subscription.add(
      this.customersService.getCustomerList(payload).subscribe({
        next: (response) => {
          this.loadingService.hideLoader();

          let mapMerge: any = [];

          if (response.items.length > 0) {
            response.items.forEach((itm: any) => {
              let existInMapMergeIndex = mapMerge.findIndex(
                (mapItm: any) =>
                  mapItm.customerName === itm.customerName &&
                  mapItm.email === itm.email &&
                  mapItm.mobile === itm.mobile
              );

              if (existInMapMergeIndex < 0) {
                mapMerge.push({
                  customerName: itm.customerName,
                  email: itm.email,
                  mobile: itm.mobile,
                  rowData: [itm],
                });
              } else {
                mapMerge[existInMapMergeIndex].rowData.push(itm);
              }
            });
          }

          mapMerge.forEach((mainItm: any, indexMain: number) => {
            mainItm.rowData.forEach((subItm: any, indexSub: number) => {
              let dataRow: any = {};

              dataRow = subItm;

              dataRow.isOddRow = indexMain % 2 === 0 ? true : false;
            });
          });

          this.rowData = response.items;
          this.pagingInfo.totalItems = response?.pagination?.totalResults;
        },
        error: (err) => {
          this.loadingService.hideLoader();
          this.rowData = [];
          handleErrors(err, this.notificationService);
        },
      })
    );
  }

  changeSummaryTab(id: any) {
    this.selectedSummaryTab = id;
    this.pagingInfo.currentPage = 0;

    this.mapWidgetFilter(id);
    this.getList();
  }

  mapWidgetFilter(id): void {
    switch (id) {
      case CustomerSummaryTab.HighPriorityWarningVehicles:
        this.filterForm.patchValue({
          customerName: '',
          mobileNumber: '',
          email: '',
          vin: '',
          filterSSC: false,
          filterHighWarning: true,
          filterHighEmergency: false,
        });
        break;

      case CustomerSummaryTab.VehiclesWithEmergencyEvents:
        this.filterForm.patchValue({
          customerName: '',
          mobileNumber: '',
          email: '',
          vin: '',
          filterSSC: false,
          filterHighWarning: false,
          filterHighEmergency: true,
        });
        break;

      case CustomerSummaryTab.VehicleWithSSCDue:
        this.filterForm.patchValue({
          customerName: '',
          mobileNumber: '',
          email: '',
          vin: '',
          filterSSC: true,
          filterHighWarning: false,
          filterHighEmergency: false,
        });
        break;
    }
  }

  onPageChange(value: number): void {
    this.pagingInfo.currentPage = value;
    this.getList();
  }

  changeItemPerPage(item): void {
    this.pagingInfo.numberOfPage = item;
    this.pagingInfo.currentPage = 0;
    this.getList();
  }

  onSearch(): void {
    this.pagingInfo.currentPage = 0;
    this.filterForm.patchValue({
      filterSSC: false,
      filterHighWarning: false,
      filterHighEmergency: false,
    });
    this.getList();
  }
}
