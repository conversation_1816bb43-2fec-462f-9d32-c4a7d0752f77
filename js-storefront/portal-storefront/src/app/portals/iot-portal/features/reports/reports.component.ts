import { CommonModule } from '@angular/common';
import { Component, inject, OnD<PERSON>roy, OnInit } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs/internal/Subscription';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { DateFormGroupComponent, DropdownFormGroupComponent, FormGroupComponent } from '../../../../core/shared';
import { LoadingService, NotificationService, ReportService } from '../../../../core/services';
import { OptionDropdown } from '../../../../core/interfaces';
import { ReportDataType } from '../../../../core/enums';
import { handleErrors } from '../../../../core/helpers';

@Component({
  selector: 'app-reports',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    ReactiveFormsModule,
    DropdownFormGroupComponent,
    DateFormGroupComponent,
    FormGroupComponent,
  ],
  templateUrl: './reports.component.html',
  styleUrl: './reports.component.scss',
  providers: [NotificationService, ReportService],
})
export class ReportsComponent implements OnInit, OnDestroy {
  loadingService = inject(LoadingService);
  translateService = inject(TranslateService);
  reportService = inject(ReportService)
  notificationService = inject(NotificationService);

  subscription = new Subscription();

  form = new FormGroup({
    dataType: new FormControl(''),

    //Subscription
    vehicleType: new FormControl('', {
      nonNullable: true,
    }),
    subscriptionStatus: new FormControl('', {
      nonNullable: true,
    }),
    paymentStatus: new FormControl('', {
      nonNullable: true,
    }),
    subscriptionEndDate: new FormControl('', {
      nonNullable: true,
    }),
    startDate: new FormControl(),
    endDate: new FormControl(),

    //Tickets Management
    ticketType: new FormControl('', {
      nonNullable: true,
    }),
    ticketPriority: new FormControl('', {
      nonNullable: true,
    }),
    ticketStatus: new FormControl('', {
      nonNullable: true,
    }),

    //Warning Reports
    warningCode: new FormControl('', {
      nonNullable: true,
    }),
    warningType: new FormControl('', {
      nonNullable: true,
    }),
    warningPriority: new FormControl('', {
      nonNullable: true,
    }),
    warningStatus: new FormControl('', {
      nonNullable: true,
    })
  });

  dataVehicleTypeOption: OptionDropdown[] = [
    {
      code: '',
      name: 'manageSub.vehicleType.all'
    },
    {
      code: 'B2C',
      name: 'manageSub.vehicleType.b2c'
    },
    {
      code: 'B2B',
      name: 'manageSub.vehicleType.b2b'
    },
    {
      code: 'B2B - CAL',
      name: 'manageSub.vehicleType.b2bCal'
    },
    {
      code: 'B2B - FLEET',
      name: 'manageSub.vehicleType.b2bFleet'
    },
  ]

  dataTypeOption: OptionDropdown[] = [
    {
      code: ReportDataType.SUBSCRIPTION,
      name: 'Subscription',
    },
    {
      code: ReportDataType.TICKET,
      name: 'Tickets Management',
    },
    {
      code: ReportDataType.WARNING,
      name: 'Warnings',
    },
  ];

  defaultOption: OptionDropdown[] = [
    {
      code: '',
      name: 'manageSub.paymentStatus.all'
    }
  ]

  dataSubscriptionStatusOption: OptionDropdown[] = []

  dataPaymentStatusOption: OptionDropdown[] = []

  dataSubscriptionEndDateOption: OptionDropdown[] = [
    {
      code: '',
      name: 'manageSub.endDateOpt.all'
    },
    {
      code: 'THIS_MONTH',
      name: 'manageSub.endDateOpt.thisMonth'
    },
    {
      code: 'NEXT_MONTH',
      name: 'manageSub.endDateOpt.nextMonth'
    },
    {
      code: 'NEXT_3_DAYS',
      name: 'manageSub.endDateOpt.next3Days'
    },
    {
      code: 'LAST_3_DAYS',
      name: 'manageSub.endDateOpt.last3Days'
    },
    {
      code: 'CUSTOM',
      name: 'manageSub.endDateOpt.custom'
    }
  ]

  dataTicketTypeOption: OptionDropdown[] = []

  dataTicketPriorityOption: OptionDropdown[] = []

  dataTicketStatusOption: OptionDropdown[] = []

  dataWarningTypeOption: OptionDropdown[] = []

  dataWarningPriorityOption: OptionDropdown[] = []

  dataWarningStatusOption: OptionDropdown[] = []

  validSubscriptionStatus = [
    'active', 
    'AWAITING_ACTIVATION',
    'cancelled',
    'CANCELLING',
    'FOR_CANCELLATION',
    'expired',
    'EXPIRING',
    'PENDING_ACTIVATION',
    'FAILED',
    'SCHEDULED'
  ]

  validPaymentStatus = [
    'PAID',
    'PENDING_CONFIRMATION',
    'PAYMENT_FAIL'
  ]

  validWarningStatus = [
    'MALFUNCTION',
    'MAINTENANCE'
  ]

  readonly ReportDataType = ReportDataType;

  ngOnInit(): void {}

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  onChangeOption(e, field) {
    let formValue = e.value
    this.form.controls[field].setValue(formValue)

    if(field === 'subscriptionEndDate') {
      if(formValue === 'CUSTOM') {
        this.form?.controls['startDate']?.addValidators(Validators.required);
        this.form?.controls['endDate']?.addValidators(Validators.required);
        this.form?.controls['startDate']?.updateValueAndValidity();
        this.form?.controls['endDate']?.updateValueAndValidity();
      }
      else {
        this.form?.controls['startDate']?.clearValidators();
        this.form?.controls['endDate']?.clearValidators();
        this.form?.controls['startDate']?.updateValueAndValidity();
        this.form?.controls['endDate']?.updateValueAndValidity();
      }
    }
  }

  onChangeDataType(e) {
    let dataType = e.value
    this.form.reset()
    this.form.patchValue({
      dataType: dataType
    })

    this.form?.controls['startDate']?.clearValidators();
    this.form?.controls['endDate']?.clearValidators();
    this.form?.controls['startDate']?.updateValueAndValidity();
    this.form?.controls['endDate']?.updateValueAndValidity();

    switch(dataType) {
      case ReportDataType.SUBSCRIPTION:
        this.dataSubscriptionStatusOption = [...this.defaultOption]
        this.dataPaymentStatusOption = [...this.defaultOption]
        this.getSubscriptionStatusList()
        this.getSubscriptionPaymentStatusList()
      break
      case ReportDataType.TICKET:
        this.dataTicketTypeOption = [...this.defaultOption]
        this.dataTicketPriorityOption = [...this.defaultOption]
        this.dataTicketStatusOption = [...this.defaultOption]
        this.getTicketTypeList()
        this.getTicketPriorityList()
        this.getTicketStatusList()
      break
      case ReportDataType.WARNING:
        this.dataWarningTypeOption = [...this.defaultOption]
        this.dataWarningPriorityOption = [...this.defaultOption]
        this.dataWarningStatusOption = [...this.defaultOption]
        this.getWarningTypeList()
        this.getTicketPriorityList()
        this.getWarningStatusList()
      break
    }
  }

  getSubscriptionStatusList() {
    this.reportService.getSubscriptionStatusOp().subscribe(
      (data: any) => {
        if(data.length > 0 && data !== undefined) {
          let subscriptionStatusOption = [...data]
          subscriptionStatusOption = subscriptionStatusOption.filter((itm: any) => this.validSubscriptionStatus.includes(itm.code))
          subscriptionStatusOption = subscriptionStatusOption.map((itm: any) => {
            itm.name = itm.code === 'EXPIRING' ? 'reports.expiringProgress' : itm.name
            return itm
          })
          let dataSubscriptionStatusOption = [...this.dataSubscriptionStatusOption, ...subscriptionStatusOption]
          this.dataSubscriptionStatusOption = dataSubscriptionStatusOption
        }
      },
      error => {
        this.loadingService.hideLoader();
        handleErrors(error, this.notificationService)
      }
    )
  }

  getSubscriptionPaymentStatusList() {
    this.reportService.getSubscriptionPaymentStatusOp().subscribe(
      (data: any) => {
        if(data.length > 0 && data !== undefined) {
          let paymentStatusOption = [...data]
          paymentStatusOption = paymentStatusOption.filter((itm: any) => this.validPaymentStatus.includes(itm.code))
          let dataPaymentStatusOption = [...this.dataPaymentStatusOption, ...paymentStatusOption]
          this.dataPaymentStatusOption = dataPaymentStatusOption
        }
      },
      error => {
        this.loadingService.hideLoader();
        handleErrors(error, this.notificationService)
      }
    )
  }

  getTicketTypeList() {
    this.reportService.getTicketTypeOp().subscribe(
      (data: any) => {
        if(data.length > 0 && data !== undefined) {
          let ticketTypeOption = [...data]
          let dataTicketTypeOption = [...this.dataTicketTypeOption, ...ticketTypeOption]
          this.dataTicketTypeOption = dataTicketTypeOption
        }
      },
      error => {
        this.loadingService.hideLoader();
        handleErrors(error, this.notificationService)
      }
    )
  }

  getTicketPriorityList() {
    this.reportService.getTicketPriorityOp().subscribe(
      (data: any) => {
        if(data.length > 0 && data !== undefined) {
          let ticketPriorityOption = [...data]
          let dataTicketPriorityOption = [...this.dataTicketPriorityOption, ...ticketPriorityOption]
          this.dataTicketPriorityOption = dataTicketPriorityOption

          let warningPriorityOption = [...data]
          let dataWarningPriorityOption = [...this.dataWarningPriorityOption, ...warningPriorityOption]
          this.dataWarningPriorityOption = dataWarningPriorityOption
        }
      },
      error => {
        this.loadingService.hideLoader();
        handleErrors(error, this.notificationService)
      }
    )
  }

  getTicketStatusList() {
    this.reportService.getTicketStateOp().subscribe(
      (data: any) => {
        if(data.length > 0 && data !== undefined) {
          let ticketStatusOption = [...data]
          let dataTicketStatusOption = [...this.dataTicketStatusOption, ...ticketStatusOption]
          this.dataTicketStatusOption = dataTicketStatusOption
        }
      },
      error => {
        this.loadingService.hideLoader();
        handleErrors(error, this.notificationService)
      }
    )
  }

  getWarningTypeList() {
    this.reportService.getWarningTypeOp().subscribe(
      (data: any) => {
        if(data.length > 0 && data !== undefined) {
          let warningTypeOption = [...data]
          warningTypeOption = warningTypeOption.filter((itm: any) => this.validWarningStatus.includes(itm.code))
          let dataWarningTypeOption = [...this.dataWarningTypeOption, ...warningTypeOption]
          this.dataWarningTypeOption = dataWarningTypeOption
        }
      },
      error => {
        this.loadingService.hideLoader();
        handleErrors(error, this.notificationService)
      }
    )
  }

  getWarningStatusList() {
    this.reportService.getWarningStatusOp().subscribe(
      (data: any) => {
        if(data.length > 0 && data !== undefined) {
          let warningStatusOption = [...data]
          let dataWarningStatusOption = [...this.dataWarningStatusOption, ...warningStatusOption]
          this.dataWarningStatusOption = dataWarningStatusOption
        }
      },
      error => {
        this.loadingService.hideLoader();
        handleErrors(error, this.notificationService)
      }
    )
  }

  generateReportSubscription(body: any) {
    this.reportService.generateReportSubscription(body).subscribe(
      (data: any) => {
        this.generateFileExcel(data, 'subscription-report')
      },
      error => {
        this.loadingService.hideLoader();
        handleErrors(error, this.notificationService)
      }
    )
  }

  generateReportTicket(body: any) {
    this.reportService.generateReportTicket(body).subscribe(
      (data: any) => {
        this.generateFileExcel(data, 'ticketsmgmt-report')
      },
      error => {
        this.loadingService.hideLoader();
        handleErrors(error, this.notificationService)
      }
    )
  }

  generateReportWarning(body: any) {
    this.reportService.generateReportWarning(body).subscribe(
      (data: any) => {
        this.generateFileExcel(data, 'warnings-report')
      },
      error => {
        this.loadingService.hideLoader();
        handleErrors(error, this.notificationService)
      }
    )
  }

  generateReport() {
    let dataSend: any = {}
    let dataForm = this.form.value
    let dataType = dataForm.dataType

    this.loadingService.showLoader();

    switch(dataType) {
      case ReportDataType.SUBSCRIPTION:
        dataSend = {
          vehicleType: dataForm.vehicleType,
          subscriptionStatus: dataForm.subscriptionStatus,
          subscriptionPaymentStatus: dataForm.paymentStatus,
          subscriptionStartDate: dataForm.subscriptionEndDate !== '' ? this.convertStartEndDate(dataForm.startDate, dataForm.subscriptionEndDate, true) : '',
          subscriptionEndDate: dataForm.subscriptionEndDate !== '' ? this.convertStartEndDate(dataForm.endDate, dataForm.subscriptionEndDate, false) : ''
        }

        this.generateReportSubscription(dataSend)
        break
      case ReportDataType.TICKET:
        dataSend = {
          ticketType: dataForm.ticketType,
          ticketPriority: dataForm.ticketPriority,
          ticketStatus: dataForm.ticketStatus
        }

        this.generateReportTicket(dataSend)
        break
      case ReportDataType.WARNING:
        dataSend = {
          warningCode: dataForm.warningCode,
          warningType: dataForm.warningType,
          warningStatus: dataForm.warningStatus
        }

        this.generateReportWarning(dataSend)
        break
    }
  }

  convertStartEndDate(date: any, endDateType: any, isStart: boolean) {
    switch(endDateType) {
      case 'THIS_MONTH':
        const now = new Date();
        const startDate = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0, 0).getTime();
        const endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999).getTime()
        return isStart ? startDate : endDate
      case 'NEXT_MONTH':
        const nowNext = new Date();
        const startDateNext = new Date(nowNext.getFullYear(), nowNext.getMonth() + 1, 1, 0, 0, 0, 0).getTime();
        const endDateNext = new Date(nowNext.getFullYear(), nowNext.getMonth() + 2, 0, 23, 59, 59, 999).getTime()
        return isStart ? startDateNext : endDateNext
      case 'NEXT_3_DAYS':
        const nowNext3 = new Date();
        const startDateNext3 = new Date(nowNext3.getFullYear(), nowNext3.getMonth(), nowNext3.getDate(), 0, 0, 0, 0).getTime()
        const endDateNext3 = new Date(nowNext3.getFullYear(), nowNext3.getMonth(), nowNext3.getDate() + 3, 23, 59, 59, 999).getTime();
        return isStart ? startDateNext3 : endDateNext3
      case 'LAST_3_DAYS':
        const nowLast3 = new Date();
        const startDateLast3 = new Date(nowLast3.getFullYear(), nowLast3.getMonth(), nowLast3.getDate() - 3, 0, 0, 0, 0).getTime();
        const endDateLast3 = new Date(nowLast3.getFullYear(), nowLast3.getMonth(), nowLast3.getDate(), 23, 59, 59, 999).getTime();
        return isStart ?  startDateLast3 : endDateLast3
      case 'CUSTOM':
        let endDateConvert = date.toString().replace('00:00:00', '23:59:59.999')
        const startDateCustom = new Date(date).getTime()
        const endDateCustom = new Date(endDateConvert).getTime()
        return isStart ? startDateCustom : endDateCustom
      default:
        return ''
    }
  }

  generateFileExcel(data: any, filename: string) {
    const url = window.URL.createObjectURL(data);
    let exportDate = new Date().toLocaleString('en-US', { 
      month: '2-digit', day: '2-digit', year: 'numeric', 
      hour: '2-digit', minute: '2-digit', second: '2-digit', 
      hour12: false 
    }).replace(/\D/g, '')

    // Create an anchor element
    const a = document.createElement('a');
    a.href = url;
    a.download = filename + '-' + exportDate + '.xlsx'; // Specify the filename here
    document.body.appendChild(a);

    // Trigger the download
    a.click();

    // Clean up
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);

    this.loadingService.hideLoader();
    this.notificationService.showSuccess('reports.exportSuccesfully');
  }
}
