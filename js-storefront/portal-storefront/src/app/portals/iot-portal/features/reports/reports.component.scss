@import "../../../../../styles/abstracts/mixins";
@import "../../../../../styles/abstracts/variables";

:host {
  min-height: calc(100vh - 60px);
  display: block;

  padding: 30px;

  .title {
    font-size: 38px;
    font-weight: 600;
    margin-bottom: 30px;
  }

  .generate-report {
    display: flex;
    justify-content: end;

    > button {
        text-transform: uppercase;
    }
  }

  .report-item-container {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;

    .report-item {
      flex: 1
    }
  }
}