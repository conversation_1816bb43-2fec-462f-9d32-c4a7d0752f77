<h2 class="title">
  {{ "reports.reports" | translate }}
</h2>

<form [formGroup]="form">
  <div class="report-item-container">
    <div class="report-item">
      <app-dropdown-form-group
        [label]="'reports.dataType' | translate"
        [control]="form.controls.dataType"
        [options]="dataTypeOption"
        [placeholder]="'reports.selectDataType' | translate"
        class="vehicle-model-section__input"
        (changeOption)="onChangeDataType($event)"
      ></app-dropdown-form-group>
    </div>

    @if(form.value.dataType === ReportDataType.SUBSCRIPTION) {
      <div class="report-item">
        <app-dropdown-form-group
          [label]="'reports.vehicleType' | translate"
          [control]="form.controls.vehicleType"
          [options]="dataVehicleTypeOption"
          class="vehicle-model-section__input"
          (changeOption)="onChangeOption($event, 'vehicleType')"
        ></app-dropdown-form-group>
      </div>
      <div class="report-item">
        <app-dropdown-form-group
          [label]="'reports.subscriptionStatus' | translate"
          [control]="form.controls.subscriptionStatus"
          [options]="dataSubscriptionStatusOption"
          class="vehicle-model-section__input"
          (changeOption)="onChangeOption($event, 'subscriptionStatus')"
        ></app-dropdown-form-group>
      </div>
      <div class="report-item">
        <app-dropdown-form-group
          [label]="'reports.paymentStatus' | translate"
          [control]="form.controls.paymentStatus"
          [options]="dataPaymentStatusOption"
          class="vehicle-model-section__input"
          (changeOption)="onChangeOption($event, 'paymentStatus')"
        ></app-dropdown-form-group>
      </div>
    }

    @if(form.value.dataType === ReportDataType.TICKET){
      <div class="report-item">
        <app-dropdown-form-group
          [label]="'reports.ticketType' | translate"
          [control]="form.controls.ticketType"
          [options]="dataTicketTypeOption"
          class="vehicle-model-section__input"
          (changeOption)="onChangeOption($event, 'vehicleType')"
        ></app-dropdown-form-group>
      </div>
      <div class="report-item">
        <app-dropdown-form-group
          [label]="'reports.priority' | translate"
          [control]="form.controls.ticketPriority"
          [options]="dataTicketPriorityOption"
          class="vehicle-model-section__input"
          (changeOption)="onChangeOption($event, 'subscriptionStatus')"
        ></app-dropdown-form-group>
      </div>
      <div class="report-item">
        <app-dropdown-form-group
          [label]="'reports.status' | translate"
          [control]="form.controls.ticketStatus"
          [options]="dataTicketStatusOption"
          class="vehicle-model-section__input"
          (changeOption)="onChangeOption($event, 'paymentStatus')"
        ></app-dropdown-form-group>
      </div>
    }


    @if(form.value.dataType === ReportDataType.WARNING){
      <div class="report-item">
        <app-form-group
          [label]="'reports.warningCode' | translate"
          [control]="form.controls.warningCode"
          [placeholder]="'enterHereToSearch' | translate"
        ></app-form-group>
      </div>
      <div class="report-item">
        <app-dropdown-form-group
          [label]="'reports.warningType' | translate"
          [control]="form.controls.warningType"
          [options]="dataWarningTypeOption"
          class="vehicle-model-section__input"
          (changeOption)="onChangeOption($event, 'subscriptionStatus')"
        ></app-dropdown-form-group>
      </div>
      <div class="report-item">
        <app-dropdown-form-group
          [label]="'reports.priority' | translate"
          [control]="form.controls.warningPriority"
          [options]="dataWarningPriorityOption"
          class="vehicle-model-section__input"
          (changeOption)="onChangeOption($event, 'paymentStatus')"
        ></app-dropdown-form-group>
      </div>
    }

  </div>

  @if(form.value.dataType === ReportDataType.SUBSCRIPTION) {
    <div class="report-item-container">
      <div class="report-item">
        <app-dropdown-form-group
          [label]="'reports.subscriptionEndDate' | translate"
          [control]="form.controls.subscriptionEndDate"
          [options]="dataSubscriptionEndDateOption"
          class="vehicle-model-section__input"
          (changeOption)="onChangeOption($event, 'subscriptionEndDate')"
        ></app-dropdown-form-group>
      </div>
      @if(form.value.subscriptionEndDate === 'CUSTOM') {
        <div class="report-item">
          <app-date-form-group
            [label]="'manageLoan.startDate' | translate"
            [control]="form.controls.startDate"
            controlId="startDate"
            [required]="true"
            [maxDate]="form.controls.endDate?.value"
            [errorMessage]="
              'common.requiredField'
                | translate : { field: ('manageLoan.startDate' | translate) }
            "
          ></app-date-form-group>
        </div>
        <div class="report-item">
          <app-date-form-group
            [label]="'manageLoan.endDate' | translate"
            [control]="form.controls.endDate"
            controlId="endDate"
            [required]="true"
            [minDate]="form.controls.startDate?.value"
            [errorMessage]="
              'common.requiredField'
                | translate : { field: ('manageLoan.endDate' | translate) }
            "
          ></app-date-form-group>
        </div>
      }
    </div>
  }
  @if(form.value.dataType === ReportDataType.WARNING) {
    <div class="report-item-container">
      <div class="report-item">
        <app-dropdown-form-group
          [label]="'reports.status' | translate"
          [control]="form.controls.warningStatus"
          [options]="dataWarningStatusOption"
          class="vehicle-model-section__input"
          (changeOption)="onChangeOption($event, 'subscriptionEndDate')"
        ></app-dropdown-form-group>
      </div>
    </div>
  }
</form>

<div class="generate-report">
  <button class="btn-primary" [disabled]="form.value.dataType === '' || !form.valid" (click)="generateReport()">
    {{ "reports.generateReport" | translate }}
  </button>
</div>
