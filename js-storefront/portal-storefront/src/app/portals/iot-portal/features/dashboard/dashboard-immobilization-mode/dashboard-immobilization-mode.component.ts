import { Component, HostListener, inject, Input } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { CurrentLocationComponent } from '../../items-detail/current-location/current-location.component';
import { LoadingComponent } from '../../../../../layout/global/loading/loading.component';
import { IconModule } from '../../../../../core/icon/icon.module';
import { DashboardService, VehicleService } from '../../../services';
import { LoadingService, NotificationService, UserService } from '../../../../../core/services';
import { Vehicle } from '../../../interfaces';
import { ResponsePaging } from '../../../../../core/interfaces';
import { PERMISSIONS_CODE } from '../../../../../core/constants';
import { handleErrors, isInViewport } from '../../../../../core/helpers';
import { VehicleMode } from '../../../enums';


@Component({
  selector: 'app-dashboard-immobilization-mode',
  standalone: true,
  imports: [
    TranslateModule,
    LoadingComponent,
    CommonModule,
    CurrentLocationComponent,
    IconModule,
  ],
  templateUrl: './dashboard-immobilization-mode.component.html',
  styleUrl: './dashboard-immobilization-mode.component.scss',
  providers: [DashboardService, VehicleService, NotificationService],
})
export class DashboardImmobilizationModeComponent {
  @Input() index;

  dashboardService = inject(DashboardService);
  loadingService = inject(LoadingService);
  translateService = inject(TranslateService);
  userService = inject(UserService);
  router = inject(Router);

  vehicleService = inject(VehicleService);
  notificationService = inject(NotificationService);

  isLoaded: boolean = false;
  vehicle: Vehicle;

  @HostListener('window:scroll', ['$event'])
  onScroll(event) {
    this.checkInView();
  }

  rowData = [];
  pagingInfoRight: ResponsePaging = {
    currentPage: 0,
    pageSize: 5,
  };

  rowDataLeft = [];
  pagingInfoLeft: ResponsePaging = {
    currentPage: 0,
    pageSize: 5,
  };

  viewTicketDetail(id) {
    if (
      this.userService.isHasPermission([
        PERMISSIONS_CODE.ECARE_TICKET_DETAIL_VIEW,
      ])
    ) {
      this.router.navigate(['tickets/' + id]);
    }
  }

  ngOnInit(): void {
    if (this.index < 3) {
      this.isLoaded = true;
      this.getImmobilizerVehicle(true);
      this.getSvtVehicle(true);
    }
  }

  checkInView() {
    const box = document.querySelector(`.dashboard-immo`);

    if (box && isInViewport(box) && !this.isLoaded) {
      this.isLoaded = true;
      this.getImmobilizerVehicle(false);
      this.getSvtVehicle(false);
    }
  }

  getImmobilizerVehicle(isReset): void {
    const param = {
      currentPage: this.pagingInfoRight?.currentPage,
      pageSize: this.pagingInfoRight?.pageSize,
    };
    this.loadingService.startLoading();
    this.dashboardService.getVehicle(param, VehicleMode.Immobilization).subscribe(
      (response) => {
        this.loadingService.stopLoading();
        this.rowData = isReset
          ? response?.items
          : [...this.rowData, ...response?.items];
        this.pagingInfoRight = response?.pagination;
      },
      () => {
        this.loadingService.stopLoading();
      }
    );
  }

  getSvtVehicle(isReset): void {
    const param = {
      currentPage: this.pagingInfoLeft?.currentPage,
      pageSize: this.pagingInfoLeft?.pageSize,
    };
    this.loadingService.startLoading();
    this.dashboardService.getVehicle(param, VehicleMode.SVT).subscribe(
      (response) => {
        this.loadingService.stopLoading();
        this.rowDataLeft = isReset
          ? response?.items
          : [...this.rowDataLeft, ...response?.items];

        this.vehicle = response?.items?.[0] || {};
        this.pagingInfoLeft = response?.pagination;
      },
      () => {
        this.loadingService.stopLoading();
      }
    );
  }

  viewMoreImmobilizer(): void {
    this.pagingInfoRight.currentPage += 1;
    this.getImmobilizerVehicle(false);
  }

  viewLessImmobilizer(): void {
    this.pagingInfoRight.currentPage = 0;
    this.getImmobilizerVehicle(true);
  }

  onActionClick(data: Vehicle) {
    this.router.navigate(['/vehicles', data?.vin]);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  viewMoreSvt(): void {
    this.pagingInfoLeft.currentPage += 1;
    this.getSvtVehicle(false);
  }

  viewLessSvt(): void {
    this.pagingInfoLeft.currentPage = 0;
    this.getSvtVehicle(true);
  }

  viewMap(data: Vehicle): void {
    this.vehicle = data;
  }

  onRefresh() {
    this.loadingService.showLoader();
    this.vehicleService.refreshCurrentLocation(this.vehicle?.vin).subscribe(
      (res: Vehicle) => {
        if (res) {
          this.loadingService.hideLoader();
          this.vehicle = { ...this.vehicle, ...res };
        }
      },
      (err) => {
        this.loadingService.hideLoader();
        handleErrors(err, this.notificationService);
      }
    );
  }
}
