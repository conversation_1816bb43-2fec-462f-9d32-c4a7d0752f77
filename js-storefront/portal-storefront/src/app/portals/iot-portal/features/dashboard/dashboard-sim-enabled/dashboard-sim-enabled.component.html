<div
  class="dashboard-content__box dashboard-sim-enabled"
  [ngStyle]="{ 'padding-right': rowData.length > 5 ? '0' : '15px' }"
>
  <ng-container *ngIf="isLoaded">
    <app-loading *ngIf="!isLoaded"></app-loading>

    <h2>SIM to be Enabled</h2>

    <div class="table-container">
      <div class="content-table">
        <table *ngIf="rowData.length > 0">
          <tr>
            <th>
              {{ "deviceManagement.deviceId" | translate }}
            </th>
            <th>
              {{ "deviceManagement.deviceStaus" | translate }}
            </th>
            <th>
              {{ "deviceManagement.activationStatus" | translate }}
            </th>
            <th>
              {{ "deviceManagement.pairingStatus" | translate }}
            </th>
            <th>
              {{ "deviceManagement.vin" | translate }}
            </th>
            <th>
              {{ "deviceManagement.simStatus" | translate }}
            </th>
            <th></th>
          </tr>
          <tr *ngFor="let item of rowData">
            <td>
              <span class="header-mobile">
                {{ "deviceManagement.deviceId" | translate }}:
              </span>
              <a [routerLink]="['/devices', item.deviceId]">{{
                item.deviceId
              }}</a>
            </td>
            <td>
              <span class="header-mobile">
                {{ "deviceManagement.deviceStaus" | translate }}:
              </span>
              {{ item.deviceStatus?.name }}
            </td>
            <td>
              <span class="header-mobile">
                {{ "deviceManagement.activationStatus" | translate }}:
              </span>
              {{ item.activationStatus?.name }}
            </td>
            <td>
              <span class="header-mobile">
                {{ "deviceManagement.pairingStatus" | translate }}:
              </span>
              {{ item.pairingStatus?.name }}
            </td>
            <td>
              <span class="header-mobile">
                {{ "deviceManagement.vin" | translate }}:
              </span>
              {{ item.vin }}
            </td>
            <td>
              <span class="header-mobile">
                {{ "deviceManagement.simStatus" | translate }}:
              </span>
              {{ item.simStatus?.name }}
            </td>
            <td class="action">
              <span (click)="onActionClick(item)">
                <mat-icon svgIcon="ic-enable-sim-red"></mat-icon>
                {{ "deviceAction.enableSIM" | translate }}
              </span>
            </td>
          </tr>
        </table>

        @if(pagingInfo?.totalPages > 1) { @if(rowData?.length <
        pagingInfo?.totalResults) {
        <div class="view-more" (click)="viewMore()">
          <mat-icon svgIcon="ic-view-more"></mat-icon>
          {{ "dashboard.viewMore" | translate }}
        </div>
        }@else {
        <div class="view-less" (click)="viewLess()">
          <mat-icon svgIcon="ic-view-more"></mat-icon>
          {{ "dashboard.viewLess" | translate }}
        </div>
        } }
      </div>
    </div>
  </ng-container>
</div>
