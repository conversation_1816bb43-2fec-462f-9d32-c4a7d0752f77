import { Component, HostListener, inject, Input, OnInit } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { AddServiceBookingComponent } from '../../items-detail/service-booking/add-service-booking/add-service-booking.component';
import { MatDialog } from '@angular/material/dialog';
import { LoadingComponent } from '../../../../../layout/global/loading/loading.component';
import { IconModule } from '../../../../../core/icon/icon.module';
import { DashboardService, ServiceBookingService } from '../../../services';
import { LoadingService, NotificationService, UserService } from '../../../../../core/services';
import { PERMISSIONS_CODE } from '../../../../../core/constants';
import { ServiceBookingStatus } from '../../../enums';
import { ResponsePaging } from '../../../../../core/interfaces';
import { handleErrors, isInViewport } from '../../../../../core/helpers';
import { ServiceBookingDashboard } from '../../../interfaces';
import { ActionModal } from '../../../../../core/enums';

@Component({
  selector: 'app-dashboard-due',
  standalone: true,
  imports: [LoadingComponent, CommonModule, IconModule, TranslateModule],
  templateUrl: './dashboard-due.component.html',
  styleUrl: './dashboard-due.component.scss',
  providers: [DashboardService, ServiceBookingService, NotificationService],
})
export class DashboardDueComponent implements OnInit {
  @Input() index;

  translateService = inject(TranslateService);
  dashboardService = inject(DashboardService);
  router = inject(Router);
  loadingService = inject(LoadingService);
  serviceBookingService = inject(ServiceBookingService);
  dialog = inject(MatDialog);
  notificationService = inject(NotificationService);
  userService = inject(UserService);

  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;
  readonly ServiceBookingStatus = ServiceBookingStatus;

  isLoaded: boolean = false;

  @HostListener('window:scroll', ['$event'])
  onScroll(event) {
    this.checkInView();
  }

  rowDataService = [];
  pagingInfoServiceBooking: ResponsePaging = {
    currentPage: 0,
    pageSize: 5,
  };

  rowDataPMS = [];
  pagingInfoPMS: ResponsePaging = {
    currentPage: 0,
    pageSize: 5,
  };

  ngOnInit(): void {
    if (this.index < 3) {
      this.isLoaded = true;
      this.getServiceBooking(true);
      this.getPMS(true);
    }
  }

  checkInView() {
    const box = document.querySelector(`.dashboard-due`);

    if (box && isInViewport(box) && !this.isLoaded) {
      this.isLoaded = true;
      this.getServiceBooking(true);
      this.getPMS(true);
    }
  }

  onViewServiceBooking(data: ServiceBookingDashboard) {
    if (!data?.hasActiveCustomerRelation) {
      this.serviceBookingService.activeCustomerRelation(data?.vin);
      return;
    }
    this.serviceBookingService
      .getServiceBookingsDetail(data?.serviceBookingNo)
      .subscribe(
        (response) => {
          this.loadingService.hideLoader();
          const dialogRef = this.dialog.open(AddServiceBookingComponent, {
            width: '850px',
            maxHeight: '90vh',
            autoFocus: false,
            data: {
              isEdit: true,
              vin: data?.vin,
              serviceBookingId: data?.serviceBookingNo,
              bookingDetail: response,
            },
          });

          dialogRef.afterClosed().subscribe((result) => {
            if (result?.action === ActionModal.Submit) {
              this.getServiceBooking(true);
            }
          });
        },
        (error) => {
          this.loadingService.hideLoader();
          handleErrors(error, this.notificationService);
        }
      );
  }

  getServiceBooking(isReset: boolean): void {
    const param = {
      currentPage: this.pagingInfoServiceBooking?.currentPage,
      pageSize: this.pagingInfoServiceBooking?.pageSize,
    };
    this.loadingService.startLoading();
    this.dashboardService.getServiceBooking(param).subscribe(
      (response) => {
        this.loadingService.stopLoading();
        this.rowDataService = isReset
          ? response?.items
          : [...this.rowDataService, ...response?.items];
        this.pagingInfoServiceBooking = response?.pagination;
      },
      () => {
        this.loadingService.stopLoading();
      }
    );
  }

  getPMS(isReset: boolean): void {
    const param = {
      currentPage: this.pagingInfoPMS?.currentPage,
      pageSize: this.pagingInfoPMS?.pageSize,
    };
    this.loadingService.startLoading();
    this.dashboardService.getPMS(param).subscribe(
      (response) => {
        this.loadingService.stopLoading();
        this.rowDataPMS = isReset
          ? response?.items
          : [...this.rowDataPMS, ...response?.items];
        this.pagingInfoPMS = response?.pagination;
      },
      () => {
        this.loadingService.stopLoading();
      }
    );
  }

  viewMoreServiceBooking(): void {
    this.pagingInfoServiceBooking.currentPage += 1;
    this.getServiceBooking(false);
  }

  viewLessServiceBooking(): void {
    this.pagingInfoServiceBooking.currentPage = 0;
    this.getServiceBooking(true);
  }

  viewMorePMS(): void {
    this.pagingInfoPMS.currentPage += 1;
    this.getPMS(false);
  }

  viewLessPMS(): void {
    this.pagingInfoPMS.currentPage = 0;
    this.getPMS(true);
  }
}
