<div class="dashboard-row dashboard-immo">
  <ng-container *ngIf="isLoaded">
    <div class="col-6">
      <div class="dashboard-content__box" [ngStyle]="{ 'padding-right': '0' }">
        <h2>Vehicles in SVT Mode</h2>

        <div class="table-container">
          <div class="content-table">
            <table *ngIf="rowDataLeft?.length > 0">
              <tr>
                <th>
                  {{ "manageSub.table.vehicle" | translate }}
                </th>
                <th>
                  {{ "tickets.ticketDetail.plate" | translate }}
                </th>
                <th>
                  {{ "Latitude / Longitude" | translate }}
                </th>
                <th></th>
              </tr>
              <tr *ngFor="let item of rowDataLeft">
                <td>
                  <span class="header-mobile">
                    {{ "manageSub.table.vehicle" | translate }}:
                  </span>
                  {{ item?.vehicleModel }}
                </td>
                <td>
                  <span class="header-mobile">
                    {{ "tickets.ticketDetail.plate" | translate }}:
                  </span>
                  {{ item?.plateNumber }}
                </td>
                <td>
                  <span class="header-mobile">
                    {{ "vehicle.immobilizedSince" | translate }}:
                  </span>
                  @if(item?.latitude || item?.longitude) {
                    <p>{{ item?.latitude }} / {{ item?.longitude }}</p>
                  }
                  @else  {
                    -
                  }
                </td>
                <td class="action">
                  <span (click)="onActionClick(item)">
                    <mat-icon svgIcon="ic-eyes"></mat-icon>
                    {{ "common.view" | translate }}
                  </span>
                </td>
              </tr>
            </table>
            @if(pagingInfoLeft?.totalPages > 1) { 
              @if(rowDataLeft?.length <
              pagingInfoLeft?.totalResults) {
              <div class="view-more" (click)="viewMoreSvt()">
                <mat-icon svgIcon="ic-view-more"></mat-icon>
                {{ "dashboard.viewMore" | translate }}
              </div>
              }@else {
              <div class="view-less" (click)="viewLessSvt()">
                <mat-icon svgIcon="ic-view-more"></mat-icon>
                {{ "dashboard.viewLess" | translate }}
              </div>
              } }
          </div>
        </div>
        <div class="map-content">
          <!-- <app-current-location
            [mapId]="'map1'"
            [vehicle]="vehicle"
            (refresh)="onRefresh()"
          ></app-current-location> -->

          <!-- @if(pagingInfoLeft?.totalPages > 1) { 
          @if(rowDataLeft?.length <
          pagingInfoLeft?.totalResults) {
          <div class="view-more" (click)="viewMoreSvt()">
            <mat-icon svgIcon="ic-view-more"></mat-icon>
            {{ "dashboard.viewMore" | translate }}
          </div>
          }@else {
          <div class="view-less" (click)="viewLessSvt()">
            <mat-icon svgIcon="ic-view-more"></mat-icon>
            {{ "dashboard.viewLess" | translate }}
          </div>
          } } -->

          <!-- <div class="map-list">
            <div class="item" *ngFor="let item of rowDataLeft">
              <p (click)="viewMap(item)">
                <strong> {{ item?.vehicleModel }} </strong>
              </p>
              <p>{{ item?.plateNumber }}</p>

              <button class="view-btn" (click)="onActionClick(item)">
                <mat-icon svgIcon="ic-eyes"></mat-icon>
                {{ "common.view" | translate }}
              </button>
            </div>
           
            @if(pagingInfoLeft?.totalPages > 1) { 
            @if(rowDataLeft?.length <
            pagingInfoLeft?.totalResults) {
            <div class="view-more" (click)="viewMoreSvt()">
              <mat-icon svgIcon="ic-view-more"></mat-icon>
              {{ "dashboard.viewMore" | translate }}
            </div>
            }@else {
            <div class="view-less" (click)="viewLessSvt()">
              <mat-icon svgIcon="ic-view-more"></mat-icon>
              {{ "dashboard.viewLess" | translate }}
            </div>
            } }
          </div> -->
        </div>
      </div>
    </div>

    <div class="col-6">
      <div class="dashboard-content__box" [ngStyle]="{ 'padding-right': '0' }">
        <app-loading *ngIf="!isLoaded"></app-loading>

        <h2>Vehicles in Immobilization Mode</h2>

        <div class="table-container">
          <div class="content-table">
            <table *ngIf="rowData.length > 0">
              <tr>
                <th>
                  {{ "manageSub.table.vehicle" | translate }}
                </th>
                <th>
                  {{ "tickets.ticketDetail.plate" | translate }}
                </th>
                <th>
                  {{ "vehicle.immobilizedSince" | translate }}
                </th>
                <th></th>
              </tr>
              <tr *ngFor="let item of rowData">
                <td>
                  <span class="header-mobile">
                    {{ "manageSub.table.vehicle" | translate }}:
                  </span>
                  {{ item?.vehicleModel }}
                </td>
                <td>
                  <span class="header-mobile">
                    {{ "tickets.ticketDetail.plate" | translate }}:
                  </span>
                  {{ item?.plateNumber }}
                </td>
                <td>
                  <span class="header-mobile">
                    {{ "vehicle.immobilizedSince" | translate }}:
                  </span>
                  {{
                    item?.iotVehicleProperty?.remoteImmobilizerDetail
                      ?.remoteImmobilizerDateTime
                  }}
                </td>
                <td class="action">
                  <span (click)="onActionClick(item)">
                    <mat-icon svgIcon="ic-eyes"></mat-icon>
                    {{ "common.view" | translate }}
                  </span>
                </td>
              </tr>
            </table>

            @if(pagingInfoRight?.totalPages > 1) { @if(rowData?.length <
            pagingInfoRight?.totalResults) {
            <div class="view-more" (click)="viewMoreImmobilizer()">
              <mat-icon svgIcon="ic-view-more"></mat-icon>
              {{ "dashboard.viewMore" | translate }}
            </div>
            }@else {
            <div class="view-less" (click)="viewLessImmobilizer()">
              <mat-icon svgIcon="ic-view-more"></mat-icon>
              {{ "dashboard.viewLess" | translate }}
            </div>
            } }
          </div>
        </div>
      </div>
    </div>
  </ng-container>
</div>
