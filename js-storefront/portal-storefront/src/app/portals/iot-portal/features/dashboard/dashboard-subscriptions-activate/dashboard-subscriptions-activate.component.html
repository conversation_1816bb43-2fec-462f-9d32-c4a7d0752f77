<div
  class="dashboard-content__box dashboard-subscriptions-activate"
  [ngStyle]="{ 'padding-right': '0' }"
>
  <ng-container *ngIf="isLoaded">
    <h2 [ngStyle]="{ 'padding-right': '25px' }">
      {{ 'dashboard.widgets.subscriptionToActivate' | translate }}

      <a (click)="viewSubsciptions()">
        <mat-icon svgIcon="ic-eyes"></mat-icon>
        {{ "common.viewList" | translate }}
      </a>
    </h2>

    <div class="dashboard-row">
      <div class="col-3 flex-block">
        <app-pie-chart
          *ngIf="dataChart?.length > 0"
          [data]="dataChart"
          [colors]="colors"
          [isShowTitle]="false"
          [isShowTotla]="false"
          [isLoading]="!isLoaded"
          (onClick)="viewSubsciptions()"
          (onLegendClick)="onLegendClick($event)"
        ></app-pie-chart>
      </div>

      <div class="col-9">
        <app-loading *ngIf="!isLoaded"></app-loading>

        <div class="table-container">
          <div class="content-table">
            @if(rowData?.length > 0 && statusSelected) {
            <table>
              <tr>
                <th>
                  {{ "manageSub.table.vehicle" | translate }}
                </th>
                <th>
                  {{ "manageSub.table.plateNumber" | translate }}
                </th>
                <th>
                  {{ "manageSub.table.subscriptionEndDate" | translate }}
                </th>
                <th>
                  {{ "manageSub.table.package" | translate }}
                </th>
              </tr>
              <tr *ngFor="let item of rowData">
                <td>
                  <span class="header-mobile">
                    {{ "manageSub.table.vehicle" | translate }}:
                  </span>
                  {{ item.vehicleModel }}
                </td>
                <td>
                  <span class="header-mobile">
                    {{ "manageSub.table.plateNumber" | translate }}:
                  </span>
                  {{ item.plateNumber }}
                </td>
                <td>
                  <span class="header-mobile">
                    {{ "manageSub.table.subscriptionEndDate" | translate }}:
                  </span>
                  {{ item.subscriptionEndDate }}
                </td>
                <td>
                  <span class="header-mobile">
                    {{ "manageSub.table.package" | translate }}:
                  </span>
                  {{ item.subscriptionPackage }}
                </td>
              </tr>
            </table>
            @if(pagingInfo?.totalPages > 1) { @if(rowData?.length <
            pagingInfo?.totalResults) {
            <div class="view-more" (click)="viewMore()">
              <mat-icon svgIcon="ic-view-more"></mat-icon>
              {{ "dashboard.viewMore" | translate }}
            </div>
            }@else {
            <div class="view-less" (click)="viewLess()">
              <mat-icon svgIcon="ic-view-more"></mat-icon>
              {{ "dashboard.viewLess" | translate }}
            </div>
            } }
            }@else {
            <div class="no-data">
              {{ "manageSub.noSubscription" | translate }}
            </div>
            }
          </div>
        </div>
      </div>
    </div>
  </ng-container>
</div>
