import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DashboardNonDeviceVehicleComponent } from './dashboard-non-device-vehicle.component';

describe('DashboardNonDeviceVehicleComponent', () => {
  let component: DashboardNonDeviceVehicleComponent;
  let fixture: ComponentFixture<DashboardNonDeviceVehicleComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DashboardNonDeviceVehicleComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(DashboardNonDeviceVehicleComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
