@import "../../../../../styles/abstracts/mixins";
@import "../../../../../styles/abstracts/variables";

:host {
  min-height: calc(100vh - 60px);
  display: block;

  .header-section {
    background-color: $bg-color-9;
    padding: 20px 16px 100px;

    display: flex;
    justify-content: space-between;

    @include md {
      padding: 30px 30px 135px;
    }

    .title-page {
      color: $text-color-white;
      font-size: $fs38;
      margin: 0;
    }
  }
}
