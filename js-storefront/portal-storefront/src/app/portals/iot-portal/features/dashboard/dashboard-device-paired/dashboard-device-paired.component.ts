import { Component, HostListener, inject, Input } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { PieChartComponent } from '../../../../../core/shared/pie-chart/pie-chart.component';
import { LoadingComponent } from '../../../../../layout/global/loading/loading.component';
import { IconModule } from '../../../../../core/icon/icon.module';
import { Router, RouterModule } from '@angular/router';
import { DashboardService } from '../../../services';
import { LoadingService, UserService } from '../../../../../core/services';
import { ItemChart } from '../../../interfaces';
import { ResponsePaging } from '../../../../../core/interfaces';
import { PERMISSIONS_CODE } from '../../../../../core/constants';
import { isInViewport } from '../../../../../core/helpers';
import { ChartType } from '../../../enums';

@Component({
  selector: 'app-dashboard-device-paired',
  standalone: true,
  imports: [
    PieChartComponent,
    TranslateModule,
    LoadingComponent,
    CommonModule,
    IconModule,
    RouterModule,
  ],
  providers: [DashboardService],
  templateUrl: './dashboard-device-paired.component.html',
  styleUrl: './dashboard-device-paired.component.scss',
})
export class DashboardDevicePairedComponent {
  @Input() index;

  translateService = inject(TranslateService);
  userService = inject(UserService);
  router = inject(Router);
  dashboardService = inject(DashboardService);
  loadingService = inject(LoadingService);

  isLoaded: boolean = false;

  @HostListener('window:scroll', ['$event'])
  onScroll(event) {
    this.checkInView();
  }

  // device = DeviceManagementTab;
  // deviceType = DeviceManagementTab.LDCM;

  dataChart: ItemChart[] = [];

  colors = ['#099ec6', '#808080', '#2aba6c'];

  // onChangeType(type: any) {
  //   this.deviceType = type;
  // }

  rowData = [];
  pagingInfo: ResponsePaging = {
    currentPage: 0,
    pageSize: 5,
  };
  statusSelected: string;

  onActionClick(data: any) {}

  viewDeviceDetail(deviceId: number) {
    if (
      this.userService.isHasPermission([
        PERMISSIONS_CODE.IOT_DEVICE_DETAIL_VIEW,
      ])
    ) {
      this.router.navigate(['/devices', deviceId]);
    }
  }

  ngOnInit(): void {
    if (this.index < 3) {
      this.isLoaded = true;
      this.getChart();
    }
  }

  checkInView() {
    const box = document.querySelector(`.dashboard-device-paired`);

    if (box && isInViewport(box) && !this.isLoaded) {
      this.isLoaded = true;
      this.getChart();
    }
  }

  getChart(): void {
    this.dashboardService.fetchChartData(
      ChartType.DevicePairedThisMonthByDeviceType,
      (items) => {
        this.dataChart = items;
        this.statusSelected = items?.map(item => item?.code)?.join(',');
        this.getTableByChart(true);
      }
    );
  }

  getTableByChart(isReset: boolean): void {
    const param = {
      currentPage: this.pagingInfo?.currentPage,
      pageSize: this.pagingInfo?.pageSize,
      deviceType: this.statusSelected,
    };
    this.loadingService.startLoading();
    this.dashboardService.getDeviceByStatusThisMonth(param).subscribe(
      (response) => {
        this.loadingService.stopLoading();
        this.rowData = isReset
          ? response?.items
          : [...this.rowData, ...response?.items];
        this.pagingInfo = response?.pagination;
      },
      () => {
        this.loadingService.stopLoading();
      }
    );
  }

  viewMore(): void {
    this.pagingInfo.currentPage += 1;
    this.getTableByChart(false);
  }

  viewLess(): void {
    this.pagingInfo.currentPage = 0;
    this.getTableByChart(true);
  }

  viewList(): void {
    this.router.navigate(['/devices']);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  onLegendClick(item): void {
    this.statusSelected =
      item?.length !== this.dataChart?.length
        ? item
            ?.filter((item) => item?.code)
            ?.map((item) => item?.code)
            ?.join(',')
        : item?.map((item) => item?.code)?.join(',');

    if (this.statusSelected === '') {
      this.rowData = []
    } else {
      this.viewLess();
    }
  }
}
