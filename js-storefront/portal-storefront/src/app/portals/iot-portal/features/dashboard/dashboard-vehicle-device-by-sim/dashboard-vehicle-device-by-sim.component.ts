import { Component, HostListener, inject, Input, OnInit } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { PieChartComponent } from '../../../../../core/shared/pie-chart/pie-chart.component';
import { IconModule } from '../../../../../core/icon/icon.module';
import { LoadingComponent } from '../../../../../layout/global/loading/loading.component';
import { DashboardService } from '../../../services';
import { LoadingService, UserService } from '../../../../../core/services';
import { PERMISSIONS_CODE } from '../../../../../core/constants';
import { ItemChart, Vehicle } from '../../../interfaces';
import { ResponsePaging } from '../../../../../core/interfaces';
import { ChartType } from '../../../enums';
import { isInViewport } from '../../../../../core/helpers';

@Component({
  selector: 'app-dashboard-vehicle-device-by-sim',
  standalone: true,
  imports: [
    PieChartComponent,
    TranslateModule,
    CommonModule,
    LoadingComponent,
    IconModule,
  ],
  templateUrl: './dashboard-vehicle-device-by-sim.component.html',
  styleUrl: './dashboard-vehicle-device-by-sim.component.scss',
  providers: [DashboardService],
})
export class DashboardVehicleDeviceBySimComponent implements OnInit {
  translateService = inject(TranslateService);
  userService = inject(UserService);
  
  dashboardService = inject(DashboardService);
  router = inject(Router);
  loadingService = inject(LoadingService);

  @Input() index;
  
  isLoaded: boolean = false;

  @HostListener('window:scroll', ['$event'])
  onScroll(event) {
    this.checkInView();
	}

  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;

  dataChart: ItemChart[] = [];
  colors = ['#df6969', '#564c9d', '#faa627', '#4fcd82', '#5C9cd8'];

  rowData: Vehicle[] = [];
  pagingInfoVehicleSos: ResponsePaging = {
    currentPage: 0,
    pageSize: 5,
  };

  ngOnInit(): void {
    if (this.index < 3) {
			this.isLoaded = true;
      this.getLdcmDeviceBySim();
      this.getVehicleSos(true);
		} 
      }

  checkInView() {
    const box = document.querySelector(`.ticket`);

    if (box && isInViewport(box) && !this.isLoaded) {
      this.isLoaded = true;
      this.getLdcmDeviceBySim();
      this.getVehicleSos(true);
    }
  }

  getLdcmDeviceBySim(): void {
    this.dashboardService.fetchChartData(
      ChartType.LdcmDevicesBySimStatus,
      (items) => {
        this.dataChart = items;
}
    );
  }

  getVehicleSos(isReset: boolean): void {
    const param = {
      currentPage: this.pagingInfoVehicleSos?.currentPage,
      pageSize: this.pagingInfoVehicleSos?.pageSize,
    };
    this.loadingService.startLoading();
    this.dashboardService.getVehicleWithSos(param).subscribe(
      (response) => {
        this.loadingService.stopLoading();
        this.rowData = isReset
          ? response?.items
          : [...this.rowData, ...response?.items];
        this.pagingInfoVehicleSos = response?.pagination;
      },
      () => {
        this.loadingService.stopLoading();
      }
    );
  }

  viewVehicle(data: Vehicle): void {
    this.router.navigate(['/vehicles', data?.vin]);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  viewDeviceList(): void {
    this.router.navigate(['/devices']);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  viewMore(): void {
    this.pagingInfoVehicleSos.currentPage += 1;
    this.getVehicleSos(false);
  }

  viewLess(): void {
    this.pagingInfoVehicleSos.currentPage = 0;
    this.getVehicleSos(true);
  }
}
