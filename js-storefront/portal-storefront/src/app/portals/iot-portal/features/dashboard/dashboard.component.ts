import { CommonModule } from '@angular/common';
import { Component, inject, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { Subscription } from 'rxjs';
import { DashboardViewComponent } from './dashboard-view/dashboard-view.component';

import { Router } from '@angular/router';
import { IconModule } from '../../../../core/icon/icon.module';
import { WidgetSummaryComponent } from '../../../../core/shared';
import { DashboardService } from '../../services';
import { LoadingService, UserService } from '../../../../core/services';
import { PERMISSIONS_CODE } from '../../../../core/constants';
import { ItemWidget, UserInfo } from '../../../../core/interfaces';
import { DASHBOARD_WIDGET, TICKET_ASSIGNEE } from '../../constants';
import { ActivationStatus, DashboardWidget, LoanSummaryTab, PairingStatus, PriorityTicket, SimStatus, StatusTicket, TypeTicket } from '../../enums';
import { VehiclesTab } from '../../enums/vehicles.enum';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.scss',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    IconModule,
    WidgetSummaryComponent,
    DashboardViewComponent,
  ],
  providers: [DashboardService],
})
export class DashboardComponent implements OnInit, OnDestroy {
  userService = inject(UserService);
  dashboardService = inject(DashboardService);
  loadingService = inject(LoadingService);
  router = inject(Router);

  subscription = new Subscription();

  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;

  widgets: ItemWidget[] = [];
  userInfo: UserInfo;
  ngOnInit(): void {
    this.userService.userRoles$.subscribe((value) => {
      this.userInfo = value;
    });

    this.getWidgets();
  }

  getWidgets(): void {
    this.loadingService.startLoading();

    this.dashboardService.getWidget().subscribe(
      (response) => {
        this.loadingService.stopLoading();
        const widgets = response || [];

        this.widgets = widgets?.map((item) => {
          const { description, icon } =
            DASHBOARD_WIDGET?.find((widget) => widget.id === item.type) || {};
          return {
            id: item?.type,
            count: item?.count,
            description,
            icon,
          };
        });
      },
      (error) => {
        this.loadingService.stopLoading();
      }
    );
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  changeSummaryTab(id: any): void {
    switch (id) {
      case DashboardWidget.HighPriorityTickets:
        this.router.navigate(['/tickets'], {
          queryParams: {
            priority: PriorityTicket.High,
            status: StatusTicket.Open,
          },
        });

        break;
      case DashboardWidget.UnassignedTicket:
        this.router.navigate(['/tickets'], {
          queryParams: {
            assignee: TICKET_ASSIGNEE[1].code,
          },
        });
        break;
      case DashboardWidget.AssignedWarningTickets:
        this.router.navigate(['/tickets'], {
          queryParams: {
            type: TypeTicket.WARNING,
            assignee: this.userService.userInfo?.username,
          },
        });
        break;
      case DashboardWidget.VehicleWithSos:
        this.router.navigate(['/vehicles'], {
          queryParams: {
            tabId: VehiclesTab.Emergency,
          },
        });
        break;
      case DashboardWidget.AssignedTickets:
        this.router.navigate(['/tickets']);
        break;

      case DashboardWidget.VehiclesSOS:
        this.router.navigate(['/vehicles'], {
          queryParams: {
            tabId: VehiclesTab.Emergency,
          },
        });
        break;
      case DashboardWidget.CalPendingPaymentConfirmation:
        this.router.navigate(['/b2b-cal']);
        break;
      case DashboardWidget.CalVehicleDueForRenewal:
        this.router.navigate(['/b2b-cal']);
        break;
      case DashboardWidget.CalLoansAboutToClose:
        this.router.navigate(['/b2b-cal'], {
          queryParams: {
            tabId: LoanSummaryTab.CAL_VEHICLES_WITH_LOANS_EXPIRING_SOON,
          },
        });
        break;
      case DashboardWidget.CalWithoutLoanInfo:
        this.router.navigate(['/b2b-cal'], {
          queryParams: {
            tabId: LoanSummaryTab.CAL_VEHICLES_WITHOUT_LOAN_INFO,
          },
        });
        break;
      case DashboardWidget.LdcmSimPendingForActivation:
        this.router.navigate(['/devices']);
        break;
      case DashboardWidget.LdcmDevicePendingForActivation:
        this.router.navigate(['/devices']);
        break;
      case DashboardWidget.LdcmDevicePendingForCancellation:
        this.router.navigate(['/devices']);
        break;
      case DashboardWidget.WaitingForPairing:
        this.router.navigate(['/devices'], {
          queryParams: {
            pairingStatus: PairingStatus.NotPaired,
          },
        });
        break;
      case DashboardWidget.WaitingForEnable:
        this.router.navigate(['/devices'], {
          queryParams: {
            simStatus: SimStatus.Pending,
          },
        });
        break;
      case DashboardWidget.NonOperationalDevices:
        this.router.navigate(['/devices'], {
          queryParams: {
            activationStatus: ActivationStatus.NonOperational,
          },
        });
        break;
    }
  }
}
