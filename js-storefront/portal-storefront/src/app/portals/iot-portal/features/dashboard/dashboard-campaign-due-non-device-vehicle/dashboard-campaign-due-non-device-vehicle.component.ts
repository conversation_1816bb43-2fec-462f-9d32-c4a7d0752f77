import { Component, HostListener, inject, Input, OnInit } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { LoadingComponent } from '../../../../../layout/global/loading/loading.component';
import { IconModule } from '../../../../../core/icon/icon.module';
import { DashboardService } from '../../../services';
import { LoadingService } from '../../../../../core/services';
import { ResponsePaging } from '../../../../../core/interfaces';
import { isInViewport } from '../../../../../core/helpers';

@Component({
  selector: 'app-dashboard-campaign-due-non-device-vehicle',
  standalone: true,
  imports: [LoadingComponent, CommonModule, IconModule, TranslateModule],
  templateUrl: './dashboard-campaign-due-non-device-vehicle.component.html',
  styleUrl: './dashboard-campaign-due-non-device-vehicle.component.scss',
  providers: [DashboardService],
})
export class DashboardCampaignDueNonDeviceVehicleComponent implements OnInit {
  @Input() index;

  translateService = inject(TranslateService);
  dashboardService = inject(DashboardService);
  loadingService = inject(LoadingService);
  router = inject(Router);

  isLoaded: boolean = false;
  rowDataService = [];
  rowDataNon = [];

  pagingInfoServiceCampaign: ResponsePaging = {
    currentPage: 0,
    pageSize: 10,
  };

  pagingInfoNonOperational: ResponsePaging = {
    currentPage: 0,
    pageSize: 10,
  };

  @HostListener('window:scroll', ['$event'])
  onScroll(event) {
    this.checkInView();
  }

  ngOnInit(): void {
    if (this.index < 3) {
      this.isLoaded = true;
      this.getSpecialServiceCampaignDue(true);
      this.getNonOperationalDevice(true);
    }
  }

  checkInView() {
    const box = document.querySelector(`.dashboard-campaign`);

    if (box && isInViewport(box) && !this.isLoaded) {
      this.isLoaded = true;
      this.getSpecialServiceCampaignDue(true);
      this.getNonOperationalDevice(true);
    }
  }

  getSpecialServiceCampaignDue(isReset): void {
    const param = {
      currentPage: this.pagingInfoServiceCampaign?.currentPage,
      pageSize: this.pagingInfoServiceCampaign?.pageSize,
    };
    this.loadingService.startLoading();
    this.dashboardService.getSpecialServiceCampaignDue(param).subscribe(
      (response) => {
        this.loadingService.stopLoading();
        this.rowDataService = isReset
          ? response?.items
          : [...this.rowDataService, ...response?.items];
        this.pagingInfoServiceCampaign = response?.pagination;
      },
      () => {
        this.loadingService.stopLoading();
      }
    );
  }

  getNonOperationalDevice(isReset): void {
    const param = {
      currentPage: this.pagingInfoNonOperational?.currentPage,
      pageSize: this.pagingInfoNonOperational?.pageSize,
    };
    this.loadingService.startLoading();
    this.dashboardService.getNonOperationalVehicle(param).subscribe(
      (response) => {
        this.loadingService.stopLoading();
        this.rowDataNon = isReset
          ? response?.items
          : [...this.rowDataNon, ...response?.items];
        this.pagingInfoNonOperational = response?.pagination;
      },
      () => {
        this.loadingService.stopLoading();
      }
    );
  }

  viewMoreServiceCampaign(): void {
    this.pagingInfoServiceCampaign.currentPage += 1;
    this.getSpecialServiceCampaignDue(false);
  }

  viewLessServiceCampaign(): void {
    this.pagingInfoServiceCampaign.currentPage = 0;
    this.getSpecialServiceCampaignDue(true);
  }

  viewMoreNonOperational(): void {
    this.pagingInfoNonOperational.currentPage += 1;
    this.getNonOperationalDevice(false);
  }

  viewLessNonOperational(): void {
    this.pagingInfoNonOperational.currentPage = 0;
    this.getNonOperationalDevice(true);
  }

  onActionClick(data) {
    this.router.navigate(['/vehicles', data?.vin]);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }
}
