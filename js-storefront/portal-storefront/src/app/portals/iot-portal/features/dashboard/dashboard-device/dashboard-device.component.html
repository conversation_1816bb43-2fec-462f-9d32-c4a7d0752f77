<div class="dashboard-row dashboard-device">
    <ng-container *ngIf="isLoaded">
        <div class="col-6" *ngIf="!userService.isHasPermission([PERMISSIONS_CODE.TMTGROUP, PERMISSIONS_CODE.VLDGROUP])">
            <div class="dashboard-content__box">
                <app-pie-chart 
                    *ngIf="deviceByActivation?.length > 0"
                    [data]="deviceByActivation" 
                    [title]="'LDCM Devices by Activation Status'" 
                    [colors]="deviceByActivationColors" 
                    [isLoading]="!isLoaded"
                    [totalLabel]="'common.totalDevices'"
                    (onClick)="viewList()"
                ></app-pie-chart>
            </div>
        </div>
    
        <div class="col-6">
            <div class="dashboard-content__box">
                <app-pie-chart 
                    *ngIf="deviceByPairing?.length > 0"
                    [data]="deviceByPairing" 
                    [title]="'LDCM Devices by Pairing Status'" 
                    [colors]="deviceByPairingColors" 
                    [isLoading]="!isLoaded"
                    [totalLabel]="'common.totalDevices'"
                    (onClick)="viewList()"
                ></app-pie-chart>
            </div>
        </div>
    </ng-container>
</div>