import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DashboardSimEnabledComponent } from './dashboard-sim-enabled.component';

describe('DashboardSimEnabledComponent', () => {
  let component: DashboardSimEnabledComponent;
  let fixture: ComponentFixture<DashboardSimEnabledComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DashboardSimEnabledComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(DashboardSimEnabledComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
