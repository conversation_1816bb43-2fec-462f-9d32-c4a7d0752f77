import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DashboardDevicePairedComponent } from './dashboard-device-paired.component';

describe('DashboardDevicePairedComponent', () => {
  let component: DashboardDevicePairedComponent;
  let fixture: ComponentFixture<DashboardDevicePairedComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DashboardDevicePairedComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(DashboardDevicePairedComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
