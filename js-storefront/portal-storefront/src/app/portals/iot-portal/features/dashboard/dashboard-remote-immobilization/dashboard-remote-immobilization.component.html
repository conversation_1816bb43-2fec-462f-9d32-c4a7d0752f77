<div class="dashboard-row dashboard-remote">
    <ng-container *ngIf="isLoaded">
        <div class="col-6">
            <div class="dashboard-content__box" 
            [ngStyle]="{'padding-right': rowData.length > 5 ? '0' : '15px'}"
            >
                <app-loading *ngIf="!isLoaded"></app-loading>
                
                <h2>
                    Remote Immobilization
                </h2>
            
                <div class="table-container">
                    <div class="content-table">
                        <table *ngIf="rowData.length > 0">
                            <tr>
                                <th>
                                    {{ 'vehicle.ticket.ticketId' | translate }}
                                </th>
                                <th>
                                    {{ 'manageSub.table.vehicle' | translate }}
                                </th>
                                <th>
                                    {{ 'tickets.ticketDetail.plate' | translate }}
                                </th>
                                <th>
                                    {{ 'tickets.ticketList.status' | translate }}
                                </th>
                                <th>
                                </th>
                            </tr>
                            <tr *ngFor="let item of rowData">
                                <td>
                                    <span class="header-mobile">
                                        {{ 'vehicle.ticket.ticketId' | translate }}:
                                    </span>
                                    <a>{{ item.ticketID }}</a>
                                </td>
                                <td>
                                    <span class="header-mobile">
                                        {{ 'manageSub.table.vehicle' | translate }}:
                                    </span>
                                    {{ item.vehicle }}
                                </td>
                                <td>
                                    <span class="header-mobile">
                                        {{ 'tickets.ticketDetail.plate' | translate }}:
                                    </span>
                                    {{ item.plateNo }}
                                </td>
                                <td>
                                    <span class="header-mobile">
                                        {{ 'tickets.ticketList.status' | translate }}:
                                    </span>
                                    {{ item.status.name }}
                                </td>
                                <td class="action">
                                    <span (click)="onActionClick(item)">
                                        <mat-icon svgIcon="ic-eyes"></mat-icon>
                                        {{ 'common.view' | translate }}
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    
        <div class="col-6">
            <div class="dashboard-content__box">
                <h2>
                    Vehicles in SVT Mode
                </h2>
                
                <div class="map-content">
                    <app-current-location
                        [mapId]="'map2'"
                        [vehicle]="vehicle"
                    ></app-current-location>
    
                    <div class="map-list">
                        <div class="item" *ngFor="let item of [1,2,3,4,5,6,7,8,9]">
                            <p>
                                <strong>
                                    Land Cruiser 300 
                                </strong>
                            </p>
                            <p>
                                NBA 1234
                            </p>
    
                            <button class="view-btn"> 
                                <mat-icon svgIcon="ic-eyes"></mat-icon> 
                                {{ 'common.view' | translate }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </ng-container>
</div>

