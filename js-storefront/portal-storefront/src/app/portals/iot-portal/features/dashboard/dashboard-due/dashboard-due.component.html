<div class="dashboard-row dashboard-due">
  <ng-container *ngIf="isLoaded">
    <div class="col-6">
      <div
        class="dashboard-content__box"
        [ngStyle]="{
          'padding-right': '0'
        }"
      >
        <app-loading *ngIf="!isLoaded"></app-loading>

        <h2>Service Booking Due</h2>

        <div class="table-container">
          <div class="content-table">
            @if(rowDataService.length > 0) {
            <table *ngIf="rowDataService.length > 0">
              <tr>
                <th>
                  {{ "vehicleManagement.make" | translate }} <br />
                  {{ "vehicleManagement.plateNumber" | translate }}
                </th>
                <th>
                  {{ "vehicleManagement.reason" | translate }} <br />
                  {{ "vehicleManagement.bookingOn" | translate }}
                </th>
                <th>
                  {{ "vehicleManagement.dealerName/" | translate }} <br />
                  {{ "vehicleManagement.confirmation" | translate }}
                </th>
                <th></th>
              </tr>
              <tr *ngFor="let item of rowDataService">
                <td>
                  <span class="header-mobile">
                    {{ "vehicleManagement.make" | translate }} <br />
                    {{ "vehicleManagement.plateNumber" | translate }}
                  </span>
                  {{ item.vehicleModel }} <br />
                  {{ item.plateNumber }}
                </td>
                <td>
                  <span class="header-mobile">
                    {{ "vehicleManagement.reason" | translate }} <br />
                    {{ "vehicleManagement.bookingOn" | translate }}
                  </span>
                  {{ item.serviceBookingReason }} <br />
                  {{ item.serviceBookingOn }}
                </td>
                <td>
                  <span class="header-mobile">
                    {{ "vehicleManagement.dealerName/" | translate }} <br />
                    {{ "vehicleManagement.confirmation" | translate }}
                  </span>
                  {{ item.serviceBookingDealer }} <br />
                  {{ item.serviceBookingStatus }}
                </td>
                <td class="action">
                  @if
                  (userService.isHasPermission([PERMISSIONS_CODE.ECARE_SERVICE_BOOKING_MANAGE]))
                  {
                  <span (click)="onViewServiceBooking(item)">
                    <mat-icon svgIcon="ic-book-red"></mat-icon>
                    {{ "serviceBooking.book" | translate }}
                  </span>
                  }
                </td>
              </tr>
            </table>
            @if(pagingInfoServiceBooking?.totalPages > 1) {
            @if(rowDataService?.length < pagingInfoServiceBooking?.totalResults)
            {
            <div class="view-more" (click)="viewMoreServiceBooking()">
              <mat-icon svgIcon="ic-view-more"></mat-icon>
              {{ "dashboard.viewMore" | translate }}
            </div>
            }@else {
            <div class="view-less" (click)="viewLessServiceBooking()">
              <mat-icon svgIcon="ic-view-more"></mat-icon>
              {{ "dashboard.viewLess" | translate }}
            </div>
            } }} @else {
            <div class="no-data">
              {{ "serviceBookingTab.noData" | translate }}
            </div>
            }
          </div>
        </div>
      </div>
    </div>

    <div class="col-6">
      <div
        class="dashboard-content__box"
        [ngStyle]="{ 'padding-right':'0' }"
      >
        <app-loading *ngIf="!isLoaded"></app-loading>

        <h2>PMS Due</h2>

        <div class="table-container">
          <div class="content-table">
            @if(rowDataPMS.length > 0) {
            <table *ngIf="rowDataPMS.length > 0">
              <tr>
                <th>
                  {{ "vehicleManagement.make" | translate }} <br />
                  {{ "vehicleManagement.plateNumber" | translate }}
                </th>
                <th>
                  {{ "vehicleManagement.servicePackage" | translate }}
                </th>
              </tr>
              <tr *ngFor="let item of rowDataPMS">
                <td>
                  <span class="header-mobile">
                    {{ "vehicleManagement.make" | translate }} <br />
                    {{ "vehicleManagement.plateNumber" | translate }}
                  </span>
                  {{ item.vehicleModel }} <br />
                  {{ item.plateNumber }}
                </td>
                <td>
                  <span class="header-mobile">
                    {{ "vehicleManagement.servicePackage" | translate }}
                  </span>
                  {{
                    item.pmsServicePackage
                      ? item.pmsServicePackage + "km Checkup"
                      : "-"
                  }}
                </td>
              </tr>
            </table>
            @if(pagingInfoPMS?.totalPages > 1) { @if(rowDataPMS?.length <
            pagingInfoPMS?.totalResults) {
            <div class="view-more" (click)="viewMorePMS()">
              <mat-icon svgIcon="ic-view-more"></mat-icon>
              {{ "dashboard.viewMore" | translate }}
            </div>
            }@else {
            <div class="view-less" (click)="viewLessPMS()">
              <mat-icon svgIcon="ic-view-more"></mat-icon>
              {{ "dashboard.viewLess" | translate }}
            </div>
            } } } @else {
            <div class="no-data">
              {{ "common.noService" | translate }}
            </div>
            }
          </div>
        </div>
      </div>
    </div>
  </ng-container>
</div>
