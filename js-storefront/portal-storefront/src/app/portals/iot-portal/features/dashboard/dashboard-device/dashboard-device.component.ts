import { Component, HostListener, inject, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { PieChartComponent } from '../../../../../core/shared/pie-chart/pie-chart.component';
import { DashboardService } from '../../../services';
import { LoadingService, UserService } from '../../../../../core/services';
import { PERMISSIONS_CODE } from '../../../../../core/constants';
import { ItemChart } from '../../../interfaces';
import { isInViewport } from '../../../../../core/helpers';
import { ChartType } from '../../../enums';

@Component({
  selector: 'app-dashboard-device',
  standalone: true,
  imports: [PieChartComponent, CommonModule],
  templateUrl: './dashboard-device.component.html',
  styleUrl: './dashboard-device.component.scss',
  providers: [DashboardService],
})
export class DashboardDeviceComponent implements OnInit {
  @Input() index;

  dashboardService = inject(DashboardService);
  router = inject(Router);
  loadingService = inject(LoadingService);
  userService = inject(UserService);

  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;

  isLoaded: boolean = false;

  @HostListener('window:scroll', ['$event'])
  onScroll(event) {
    this.checkInView();
  }

  deviceByActivation: ItemChart[] = [];

  deviceByActivationColors = ['#df6969', '#faa627', '#5c9cd8'];

  deviceByPairing: ItemChart[] = [];

  deviceByPairingColors = ['#2aba6c', '#6b7cfe'];

  ngOnInit(): void {
    if (this.index < 3) {
      this.isLoaded = true;
      this.getDeviceByActivation();
      this.getDeviceByPairing();
    }
  }

  checkInView() {
    const box = document.querySelector(`.dashboard-device`);

    if (box && isInViewport(box) && !this.isLoaded) {
      this.isLoaded = true;
      this.getDeviceByActivation();
      this.getDeviceByPairing();
    }
  }

  getDeviceByActivation(): void {
    this.dashboardService.fetchChartData(
      ChartType.LdcmDevicesByActivationStatus,
      (items) => {
        this.deviceByActivation = items;
      }
    );
  }

  getDeviceByPairing(): void {
    this.dashboardService.fetchChartData(
      ChartType.LdcmDevicesByPairingStatus,
      (items) => {
        this.deviceByPairing = items;
      }
    );
  }

  viewList(): void {
    this.router.navigate(['/devices']);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }
}
