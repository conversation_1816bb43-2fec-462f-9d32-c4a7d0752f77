<div class="dashboard-row dashboard-campaign">
  <ng-container *ngIf="isLoaded">
    <div class="col-6">
      <div
        class="dashboard-content__box"
        [ngStyle]="{ 'padding-right': '0' }"
      >
        <app-loading *ngIf="!isLoaded"></app-loading>

        <h2>Special Service Campaign Due</h2>

        <div class="table-container">
          <div class="content-table">
            <table *ngIf="rowDataService.length > 0">
              <tr>
                <th>
                  {{ "vehicleManagement.make" | translate }} <br />
                  {{ "vehicleManagement.plateNumber" | translate }}
                </th>
                <th>SSC</th>
                <th>
                  {{ "vehicleManagement.validFrom" | translate }}
                </th>
                <th style="white-space: nowrap">
                  {{ "tickets.ticketList.status" | translate }}
                </th>
              </tr>
              <tr *ngFor="let item of rowDataService">
                <td>
                  <span class="header-mobile">
                    {{ "vehicleManagement.make" | translate }} <br />
                    {{ "vehicleManagement.plateNumber" | translate }}
                  </span>
                  {{ item.vehicleModel }} <br />
                  {{ item.plateNumber }}
                </td>
                <td style="width: 150px">
                  <span class="header-mobile"> SSC </span>
                  {{ item.sscCategory }} <br/>
                  {{ item.sscExpiryDate}}
                </td>
                <td style="white-space: nowrap">
                  <span class="header-mobile">
                    {{ "vehicleManagement.validFrom" | translate }}
                  </span>
                  {{ item.sscValidFrom }}
                </td>
                <td>
                  <span class="header-mobile">
                    {{ "tickets.ticketList.status" | translate }}
                  </span>
                  {{ item.sscStatus }}
                </td>
              </tr>
            </table>

            @if(pagingInfoServiceCampaign?.totalPages > 1) {
            @if(rowDataService?.length <
            pagingInfoServiceCampaign?.totalResults) {
            <div class="view-more" (click)="viewMoreServiceCampaign()">
              <mat-icon svgIcon="ic-view-more"></mat-icon>
              {{ "dashboard.viewMore" | translate }}
            </div>
            }@else {
            <div class="view-less" (click)="viewLessServiceCampaign()">
              <mat-icon svgIcon="ic-view-more"></mat-icon>
              {{ "dashboard.viewLess" | translate }}
            </div>
            } }
          </div>
        </div>
      </div>
    </div>

    <div class="col-6">
      <div
        class="dashboard-content__box"
        [ngStyle]="{ 'padding-right': '0' }"
      >
        <app-loading *ngIf="!isLoaded"></app-loading>

        <h2>Non-Operational Device/Vehicles</h2>

        <div class="table-container">
          <div class="content-table">
            <table *ngIf="rowDataNon.length > 0">
              <tr>
                <th>
                  {{ "vehicleManagement.make" | translate }} <br />
                  {{ "vehicleManagement.plateNumber" | translate }}
                </th>
                <th>
                  {{ "manageSub.table.subscriptionStatus" | translate }}
                </th>
                <th></th>
              </tr>
              <tr *ngFor="let item of rowDataNon">
                <td>
                  <span class="header-mobile">
                    {{ "vehicleManagement.make" | translate }} <br />
                    {{ "vehicleManagement.plateNumber" | translate }}
                  </span>
                  {{ item.vehicleModel }} <br />
                  {{ item.plateNumber }}
                </td>
                <td>
                  <span class="header-mobile">
                    {{ "manageSub.table.subscriptionStatus" | translate }}
                  </span>
                  {{ item.subscriptionStatus }}
                </td>
                <td class="action">
                  <span (click)="onActionClick(item)">
                    <mat-icon svgIcon="ic-eyes"></mat-icon>
                    {{ "common.view" | translate }}
                  </span>
                </td>
              </tr>
            </table>
            <div *ngIf="rowDataNon.length <= 0" class="no-data">
              {{ "deviceManagement.noDeviceFound" | translate }}
            </div>
            @if(pagingInfoNonOperational?.totalPages > 1 && rowDataNon.length > 0) {
            @if(rowDataNon?.length <
            pagingInfoNonOperational?.totalResults) {
            <div class="view-more" (click)="viewMoreNonOperational()">
              <mat-icon svgIcon="ic-view-more"></mat-icon>
              {{ "dashboard.viewMore" | translate }}
            </div>
            }@else {
            <div class="view-less" (click)="viewLessNonOperational()">
              <mat-icon svgIcon="ic-view-more"></mat-icon>
              {{ "dashboard.viewLess" | translate }}
            </div>
            } }
          </div>
        </div>
      </div>
    </div>
  </ng-container>
</div>
