import { Component, HostListener, inject, Input, OnInit } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { PieChartComponent } from '../../../../../core/shared/pie-chart/pie-chart.component';
import { LoadingComponent } from '../../../../../layout/global/loading/loading.component';
import { IconModule } from '../../../../../core/icon/icon.module';
import { DashboardService } from '../../../services';
import { LoadingService, UserService } from '../../../../../core/services';
import { ActivationStatus, ChartType, DeviceManagementTab } from '../../../enums';
import { ItemChart } from '../../../interfaces';
import { ResponsePaging } from '../../../../../core/interfaces';
import { isInViewport } from '../../../../../core/helpers';
import { PERMISSIONS_CODE } from '../../../../../core/constants';

@Component({
  selector: 'app-dashboard-non-device-vehicle',
  standalone: true,
  imports: [
    PieChartComponent,
    TranslateModule,
    CommonModule,
    LoadingComponent,
    IconModule,
    RouterModule,
  ],
  providers: [DashboardService],
  templateUrl: './dashboard-non-device-vehicle.component.html',
  styleUrl: './dashboard-non-device-vehicle.component.scss',
})
export class DashboardNonDeviceVehicleComponent implements OnInit {
  translateService = inject(TranslateService);
  userService = inject(UserService);
  router = inject(Router);
  dashboardService = inject(DashboardService);
  loadingService = inject(LoadingService);

  @Input() index;

  isLoaded: boolean = false;

  @HostListener('window:scroll', ['$event'])
  onScroll(event) {
    this.checkInView();
  }

  device = DeviceManagementTab;
  deviceType = DeviceManagementTab.LDCM;

  dataChart: ItemChart[] = [];
  colors = ['#5c9cd8', '#f589a3'];

  rowData = [];
  pagingInfo: ResponsePaging = {
    currentPage: 0,
    pageSize: 5,
  };
  statusSelected: string;

  ngOnInit(): void {
    if (this.index < 3) {
      this.isLoaded = true;
      this.getChart();
    }
  }

  checkInView() {
    const box = document.querySelector(`.dashboard-non-device`);

    if (box && isInViewport(box) && !this.isLoaded) {
      this.isLoaded = true;
      this.getChart();
    }
  }

  onChangeType(type: any) {
    this.deviceType = type;
  }

  viewDeviceDetail(deviceId: number) {
    if (
      this.userService.isHasPermission([
        PERMISSIONS_CODE.IOT_DEVICE_DETAIL_VIEW,
      ])
    ) {
      this.router.navigate(['/devices', deviceId]);
    }
  }

  getChart(): void {
    this.dashboardService.fetchChartData(
      ChartType.NonOperationalDeviceVehicles,
      (items) => {
        this.dataChart = items;
        this.statusSelected = items?.map((item) => item?.code)?.join(',');
        this.getTableByChart(true);
      }
    );
  }

  getTableByChart(isReset: boolean): void {
    const param = {
      currentPage: this.pagingInfo?.currentPage,
      pageSize: this.pagingInfo?.pageSize,
      deviceType: this.statusSelected,
    };
    this.loadingService.startLoading();
    this.dashboardService.getDeviceByStatus(param).subscribe(
      (response) => {
        this.loadingService.stopLoading();
        this.rowData = isReset
          ? response?.items
          : [...this.rowData, ...response?.items];
        this.pagingInfo = response?.pagination;
      },
      () => {
        this.loadingService.stopLoading();
      }
    );
  }

  viewMore(): void {
    this.pagingInfo.currentPage += 1;
    this.getTableByChart(false);
  }

  viewLess(): void {
    this.pagingInfo.currentPage = 0;
    this.getTableByChart(true);
  }

  viewList(): void {
    this.router.navigate(['/devices'], {
      queryParams: { activationStatus: ActivationStatus.NonOperational },
    });
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  onLegendClick(item): void {
    this.statusSelected =
      item?.length !== this.dataChart?.length
        ? item
            ?.filter((item) => item?.code)
            ?.map((item) => item?.code)
            ?.join(',')
        : item?.map((item) => item?.code)?.join(',');

    if (this.statusSelected === '') {
      this.rowData = [];
    } else {
      this.viewLess();
    }
  }
}
