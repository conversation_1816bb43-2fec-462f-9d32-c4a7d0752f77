import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DashboardDeviceToPairedComponent } from './dashboard-device-to-paired.component';

describe('DashboardDeviceToPairedComponent', () => {
  let component: DashboardDeviceToPairedComponent;
  let fixture: ComponentFixture<DashboardDeviceToPairedComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DashboardDeviceToPairedComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(DashboardDeviceToPairedComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
