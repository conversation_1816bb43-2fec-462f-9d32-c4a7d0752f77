import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DashboardRemoteImmobilizationComponent } from './dashboard-remote-immobilization.component';

describe('DashboardRemoteImmobilizationComponent', () => {
  let component: DashboardRemoteImmobilizationComponent;
  let fixture: ComponentFixture<DashboardRemoteImmobilizationComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DashboardRemoteImmobilizationComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(DashboardRemoteImmobilizationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
