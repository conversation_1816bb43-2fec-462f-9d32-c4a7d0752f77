<div class="dashboard-row">
  <div class="col-6">
    <div class="dashboard-content__box">
      <app-pie-chart
        *ngIf="ticketByStatus?.length > 0"
        [data]="ticketByStatus"
        [title]="'Tickets by Status'"
        [colors]="ticketByStatusColors"
        (onClick)="viewList()"
      ></app-pie-chart>
    </div>
  </div>

  <div class="col-6">
    <div class="dashboard-content__box" [ngStyle]="{ 'padding-right': '0' }">
      <h2>Vehicles with SOS</h2>

      <div class="table-container">
        <div class="content-table">
          @if(rowData?.length > 0) {
          <table>
            <tr>
              <th>
                {{ "manageSub.table.vehicle" | translate }}
              </th>
              <th>
                {{ "tickets.ticketDetail.plate" | translate }}
              </th>
              <th></th>
            </tr>
            <tr *ngFor="let item of rowData">
              <td>
                <span class="header-mobile">
                  {{ "manageSub.table.vehicle" | translate }}:
                </span>
                <a>{{ item.vehicleModel }}</a>
              </td>
              <td>
                <span class="header-mobile">
                  {{ "tickets.ticketDetail.plate" | translate }}:
                </span>
                {{ item.plateNumber }}
              </td>
              <td class="action">
                <span (click)="viewVehicle(item)">
                  <mat-icon svgIcon="ic-eyes"></mat-icon>
                  {{ "common.view" | translate }}
                </span>
              </td>
            </tr>
          </table>

          @if(pagingInfoVehicleSos?.totalPages > 1) { @if(rowData?.length <
          pagingInfoVehicleSos?.totalResults) {
          <div class="view-more" (click)="viewMore()">
            <mat-icon svgIcon="ic-view-more"></mat-icon>
            {{ "dashboard.viewMore" | translate }}
          </div>
          }@else {
          <div class="view-less" (click)="viewLess()">
            <mat-icon svgIcon="ic-view-more"></mat-icon>
            {{ "dashboard.viewLess" | translate }}
          </div>
          } } } @else {
          <div class="no-data">
            {{ "vehicleManagement.noVehicle" | translate }}
          </div>
          }
        </div>
      </div>
    </div>
  </div>
</div>
