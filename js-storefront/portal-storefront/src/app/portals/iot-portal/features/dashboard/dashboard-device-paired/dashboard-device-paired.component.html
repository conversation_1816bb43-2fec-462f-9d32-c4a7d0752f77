<div
  class="dashboard-content__box dashboard-device-paired"
  [ngStyle]="{ 'padding-right': '0' }"
>
  <ng-container *ngIf="isLoaded">
    <h2>
      Devices paired (Current Month) by device type wise
      <!-- <a class="dashboard-content__box__view-list" (click)="viewList()">
        <mat-icon svgIcon="ic-eyes"></mat-icon>
        {{ "common.viewList" | translate }}
      </a> -->
    </h2>

    <!-- <ul class="menu">
            <li 
                [class.active]="deviceType === device.LDCM" 
                (click)="onChangeType(device.LDCM)"
            >
                {{ 'deviceManagement.ldcm' | translate }}
            </li>
            <li 
                [class.active]="deviceType === device.GDCM" 
                (click)="onChangeType(device.GDCM)"
            >
                {{ 'deviceManagement.gdcm' | translate }}
            </li>
        </ul> -->

    <div class="dashboard-row">
      <div class="col-3 flex-block">
        <app-pie-chart
          *ngIf="dataChart?.length > 0"
          [data]="dataChart"
          [colors]="colors"
          [isShowTotla]="false"
          [isShowTitle]="false"
          [isLoading]="!isLoaded"
          (onClick)="viewList()"
          (onLegendClick)="onLegendClick($event)"
        ></app-pie-chart>
      </div>

      <div class="col-9">
        <app-loading *ngIf="!isLoaded"></app-loading>

        <div class="table-container">
          <div class="content-table">
            @if(rowData?.length > 0 && statusSelected) {
            <table>
              <tr>
                <th style="white-space: nowrap">
                  {{ "deviceManagement.deviceId" | translate }}
                </th>
                <th>
                  {{ "deviceManagement.vin" | translate }}
                </th>
                <th>
                  {{ "deviceManagement.deviceStaus" | translate }}
                </th>
                <th>
                  {{ "deviceManagement.activationStatus" | translate }}
                </th>
                <th>
                  {{ "deviceManagement.pairingStatus" | translate }}
                </th>
                <th>
                  {{ "deviceManagement.simStatus" | translate }}
                </th>
              </tr>
              <tr *ngFor="let item of rowData">
                <td>
                  <span class="header-mobile">
                    {{ "deviceManagement.deviceId" | translate }}:
                  </span>
                  <a [routerLink]="['/devices', item.deviceId]">{{
                    item.deviceId
                  }}</a>
                </td>
                <td>
                  <span class="header-mobile">
                    {{ "deviceManagement.vin" | translate }}:
                  </span>
                  {{ item.vin }}
                </td>
                <td>
                  <span class="header-mobile">
                    {{ "deviceManagement.deviceStaus" | translate }}:
                  </span>
                  {{ item.deviceStatus?.name }}
                </td>
                <td>
                  <span class="header-mobile">
                    {{ "deviceManagement.activationStatus" | translate }}:
                  </span>
                  {{ item.activationStatus?.name }}
                </td>
                <td>
                  <span class="header-mobile">
                    {{ "deviceManagement.pairingStatus" | translate }}:
                  </span>
                  {{ item.pairingStatus?.name }}
                </td>
                <td>
                  <span class="header-mobile">
                    {{ "deviceManagement.simStatus" | translate }}:
                  </span>
                  {{ item.simStatus?.name }}
                </td>
              </tr>
            </table>
            @if(pagingInfo?.totalPages > 1) { @if(rowData?.length <
            pagingInfo?.totalResults) {
            <div class="view-more" (click)="viewMore()">
              <mat-icon svgIcon="ic-view-more"></mat-icon>
              {{ "dashboard.viewMore" | translate }}
            </div>
            }@else {
            <div class="view-less" (click)="viewLess()">
              <mat-icon svgIcon="ic-view-more"></mat-icon>
              {{ "dashboard.viewLess" | translate }}
            </div>
            } } } @else {
            <div class="no-data">
              {{ "deviceManagement.noDeviceFound" | translate }}
            </div>
            }
          </div>
        </div>
      </div>
    </div>
  </ng-container>
</div>
