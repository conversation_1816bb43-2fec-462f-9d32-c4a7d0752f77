import { CommonModule } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { Router } from '@angular/router';
import { IconModule } from '../../../../../core/icon/icon.module';
import { PieChartComponent } from '../../../../../core/shared/pie-chart/pie-chart.component';
import { DashboardService } from '../../../services';
import { LoadingService } from '../../../../../core/services';
import { ItemChart, Vehicle } from '../../../interfaces';
import { ResponsePaging } from '../../../../../core/interfaces';
import { ChartType } from '../../../enums';

@Component({
  selector: 'app-dashboard-ticket-vehicle',
  standalone: true,
  imports: [CommonModule, TranslateModule, IconModule, PieChartComponent],
  templateUrl: './dashboard-ticket-vehicle.component.html',
  styleUrl: './dashboard-ticket-vehicle.component.scss',
  providers: [DashboardService],
})
export class DashboardTicketVehicleComponent implements OnInit {
  dashboardService = inject(DashboardService);
  router = inject(Router);
  loadingService = inject(LoadingService);

  ticketByStatus: ItemChart[] = [];

  ticketByStatusColors = [
    '#df6969',
    '#faa627',
    '#5c9cd8',
    '#099ec6',
    '#dc3d97',
  ];

  rowData: Vehicle[] = [];
  pagingInfoVehicleSos: ResponsePaging = {
    currentPage: 0,
    pageSize: 5,
  };

  ngOnInit(): void {
    this.getTicketByStatus();
    this.getVehicleSos(true);
  }

  getVehicleSos(isReset): void {
    const param = {
      currentPage: this.pagingInfoVehicleSos?.currentPage,
      pageSize: this.pagingInfoVehicleSos?.pageSize,
    };
    this.loadingService.startLoading();
    this.dashboardService.getVehicleWithSos(param).subscribe(
      (response) => {
        this.loadingService.stopLoading();
        this.rowData = isReset
          ? response?.items
          : [...this.rowData, ...response?.items];
        this.pagingInfoVehicleSos = response?.pagination;
      },
      () => {
        this.loadingService.stopLoading();
      }
    );
  }

  viewVehicle(data: Vehicle): void {
    this.router.navigate(['/vehicles', data?.vin]);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  viewMore(): void {
    this.pagingInfoVehicleSos.currentPage += 1;
    this.getVehicleSos(false);
  }

  viewLess(): void {
    this.pagingInfoVehicleSos.currentPage = 0;
    this.getVehicleSos(true);
  }

  getTicketByStatus(): void {
    this.dashboardService.fetchChartData(ChartType.TicketByStatus, (items) => {
      this.ticketByStatus = items;
    });
  }

  viewList(): void {
    this.router.navigate(['/tickets']);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }
}
