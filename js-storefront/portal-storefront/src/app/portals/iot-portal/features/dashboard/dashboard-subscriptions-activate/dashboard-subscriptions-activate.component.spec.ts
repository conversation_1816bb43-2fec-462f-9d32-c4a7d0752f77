import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DashboardSubscriptionsActivateComponent } from './dashboard-subscriptions-activate.component';

describe('DashboardSubscriptionsActivateComponent', () => {
  let component: DashboardSubscriptionsActivateComponent;
  let fixture: ComponentFixture<DashboardSubscriptionsActivateComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DashboardSubscriptionsActivateComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(DashboardSubscriptionsActivateComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
