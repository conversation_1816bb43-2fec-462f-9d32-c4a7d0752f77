<div
  class="dashboard-content__box subscription-block"
  [ngStyle]="{ 'padding-right': '0' }"
>
  <ng-container *ngIf="isLoaded">
    <h2>Subscriptions</h2>

    <!-- <ul class="menu">
      <li
        [class.active]="statusSubscription === status.renewalDue"
        (click)="onChangeType(status.renewalDue)"
      >
        {{ "manageSub.subStatus.renewalDue" | translate }}
      </li>
      <li
        [class.active]="statusSubscription === status.expired"
        (click)="onChangeType(status.expired)"
      >
        {{ "manageSub.subStatus.expired" | translate }}
      </li>
    </ul> -->

    <div class="dashboard-row">
      <div class="col-3 flex-block">
        <app-pie-chart
          *ngIf="dataChart?.length > 0"
          [data]="dataChart"
          [colors]="colors"
          [isShowTotla]="false"
          [isShowTitle]="false"
          [isLoading]="!isLoaded"
          (onClick)="viewSubsciptions()"
          (onLegendClick)="onLegendClick($event)"
        ></app-pie-chart>
      </div>

      <div class="col-9">
        <app-loading *ngIf="!isLoaded"></app-loading>

        <div class="table-container">
          <div class="content-table">
            <table *ngIf="rowData.length > 0">
              @if (this.statusSubscription === StatusSubscription.renewalDue) {
              <tr>
                <th>
                  {{ "manageSub.table.vehicle" | translate }}
                </th>
                <th>
                  {{ "manageSub.table.plateNumber" | translate }}
                </th>
                <th>
                  {{ "manageSub.table.subscriptionEndDate" | translate }}
                </th>
                <th>
                  {{ "manageSub.table.package" | translate }}
                </th>
              </tr>
              <tr *ngFor="let item of rowData">
                <td>
                  <span class="header-mobile">
                    {{ "manageSub.table.vehicle" | translate }}:
                  </span>
                  {{ item.vehicleModel }}
                </td>
                <td>
                  <span class="header-mobile">
                    {{ "manageSub.table.plateNumber" | translate }}:
                  </span>
                  {{ item.plateNumber }}
                </td>
                <td>
                  <span class="header-mobile">
                    {{ "manageSub.table.endDate" | translate }}:
                  </span>
                  {{ item.subscriptionEndDate }}
                </td>
                <td>
                  <span class="header-mobile">
                    {{ "manageSub.table.package" | translate }}:
                  </span>
                  {{ item.subscriptionPackage }}
                </td>
              </tr>
              } @else {
              <tr>
                <th>
                  {{ "manageSub.table.vehicle" | translate }}
                </th>
                <th>
                  {{ "manageSub.table.plateNumber" | translate }}
                </th>
                <th>
                  {{ "manageSub.table.endDate" | translate }}
                </th>
              </tr>
              <tr *ngFor="let item of rowData">
                <td>
                  <span class="header-mobile">
                    {{ "manageSub.table.vehicle" | translate }}:
                  </span>
                  {{ item.vehicleModel }}
                </td>
                <td>
                  <span class="header-mobile">
                    {{ "manageSub.table.plateNumber" | translate }}:
                  </span>
                  {{ item.plateNumber }}
                </td>
                <td>
                  <span class="header-mobile">
                    {{ "manageSub.table.endDate" | translate }}:
                  </span>
                  {{ item.subscriptionEndDate }}
                </td>
              </tr>
              }
            </table>
            <div *ngIf="rowData.length == 0" class="no-data">
              {{ "vehicleManagement.noVehicle" | translate }}
            </div>

            @if(pagingInfo?.totalPages > 1 && rowData.length > 0) { @if(rowData?.length <
            pagingInfo?.totalResults) {
            <div class="view-more" (click)="viewMore()">
              <mat-icon svgIcon="ic-view-more"></mat-icon>
              {{ "dashboard.viewMore" | translate }}
            </div>
            }@else {
            <div class="view-less" (click)="viewLess()">
              <mat-icon svgIcon="ic-view-more"></mat-icon>
              {{ "dashboard.viewLess" | translate }}
            </div>
            } }
          </div>
        </div>
      </div>
    </div>
  </ng-container>
</div>
