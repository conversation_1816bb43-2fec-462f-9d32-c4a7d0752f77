import { CommonModule } from '@angular/common';
import { Component, HostListener, inject, Input } from '@angular/core';
import { MatGridListModule } from '@angular/material/grid-list';
import { Router } from '@angular/router';
import { PieChartComponent } from '../../../../../core/shared/pie-chart/pie-chart.component';
import { DashboardService } from '../../../services';
import { LoadingService } from '../../../../../core/services';
import { ItemChart } from '../../../interfaces';
import { isInViewport } from '../../../../../core/helpers';
import { ChartType, StatusTicket } from '../../../enums';

@Component({
  selector: 'app-dashboard-ticket',
  standalone: true,
  imports: [PieChartComponent, CommonModule, MatGridListModule],
  templateUrl: './dashboard-ticket.component.html',
  styleUrl: './dashboard-ticket.component.scss',
  providers: [DashboardService],
})
export class DashboardTicketComponent {
  @Input() index;

  dashboardService = inject(DashboardService);
  router = inject(Router);
  loadingService = inject(LoadingService);

  isLoaded: boolean = false;

  @HostListener('window:scroll', ['$event'])
  onScroll(event) {
    this.checkInView();
  }

  ticketByType: ItemChart[] = [];

  ticketByTypeColors = [
    '#6765d8',
    '#2aba6c',
    '#119900',
    '#d2545f',
    '#fcbf45',
    '#de9e1c',
    '#564c9d',
    '#099ec6',
    '#dc3d97',
  ];

  ticketByStatus: ItemChart[] = [];

  ticketByStatusColors = [
    '#df6969',
    '#faa627',
    '#5c9cd8',
    '#099ec6',
    '#dc3d97',
  ];

  ngOnInit(): void {
    if (this.index < 3) {
      this.isLoaded = true;
      this.getOpenTicketByType();
      this.getTicketByStatus();
    }
  }

  checkInView() {
    const box = document.querySelector(`.dashboard-ticket`);

    if (box && isInViewport(box) && !this.isLoaded) {
      this.isLoaded = true;
      this.getOpenTicketByType();
      this.getTicketByStatus();
    }
  }

  getOpenTicketByType(): void {
    this.dashboardService.fetchChartData(
      ChartType.OpenTicketByType,
      (items) => {
        this.ticketByType = items;
      }
    );
  }

  getTicketByStatus(): void {
    this.dashboardService.fetchChartData(ChartType.TicketByStatus, (items) => {
      this.ticketByStatus = items;
    });
  }

  viewListTicketByStatus(): void {
    this.router.navigate(['/tickets']);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  viewListTicketByType(): void {
    this.router.navigate(['/tickets'], {
      queryParams: { status: StatusTicket.Open},
    });
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }
}
