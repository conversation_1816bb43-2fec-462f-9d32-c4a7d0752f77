import { Component, HostListener, inject, Input, OnInit } from '@angular/core';
import { MatTabsModule } from '@angular/material/tabs';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { PieChartComponent } from '../../../../../core/shared/pie-chart/pie-chart.component';
import { LoadingComponent } from '../../../../../layout/global/loading/loading.component';
import { IconModule } from '../../../../../core/icon/icon.module';
import { DashboardService } from '../../../services';
import { LoadingService } from '../../../../../core/services';
import { StatusSubscription } from '../../../enums/subscription.enum';
import { Item<PERSON>hart } from '../../../interfaces';
import { ResponsePaging } from '../../../../../core/interfaces';
import { isInViewport } from '../../../../../core/helpers';
import { ChartType } from '../../../enums';

@Component({
  selector: 'app-dashboard-subscription',
  standalone: true,
  imports: [
    MatTabsModule,
    TranslateModule,
    PieChartComponent,
    CommonModule,
    IconModule,
    LoadingComponent,
  ],
  templateUrl: './dashboard-subscription.component.html',
  styleUrl: './dashboard-subscription.component.scss',
  providers: [DashboardService],
})
export class DashboardSubscriptionComponent implements OnInit {
  translateService = inject(TranslateService);
  dashboardService = inject(DashboardService);
  router = inject(Router);
  loadingService = inject(LoadingService);

  readonly status = StatusSubscription;
  readonly StatusSubscription = StatusSubscription;
  statusSubscription = StatusSubscription.renewalDue;

  @Input() index;
  
  isLoaded: boolean = false;

  @HostListener('window:scroll', ['$event'])
  onScroll(event) {
    this.checkInView();
	}

  dataChart: ItemChart[] = [];
  colors = ['#2aba6c', '#808080'];

	  rowData = [];
  pagingInfo: ResponsePaging = {
    currentPage: 0,
    pageSize: 5,
  };
  statusSelected: string;

  ngOnInit(): void {
    this.getData();
  }

  // onChangeType(status: any) {
  //   this.statusSubscription = status;
  //   this.rowData = [];

  //   this.getData();
  // }

  getData() {
    if (this.index < 3) {
			this.isLoaded = true;
      this.getChart();
		} 
  }

  checkInView() {
    const box = document.querySelector(`.subscription-block`);

    if (box && isInViewport(box) && !this.isLoaded) {
      this.isLoaded = true;
      this.getChart();
          }
          }

  getChart(): void {
    this.dashboardService.fetchChartData(
      ChartType.SubscriptionDueDate,
      (items) => {
        this.dataChart = items;
        this.statusSelected = items?.map(item => item?.code)?.join(',');
        this.getTableByChart(true);
      }
    );
    }

  getTableByChart(isReset: boolean): void {
    const param = {
      currentPage: this.pagingInfo?.currentPage,
      pageSize: this.pagingInfo?.pageSize,
      status: this.statusSelected,
    };
    this.loadingService.startLoading();
    this.dashboardService.getSubscriptions(param).subscribe(
      (response) => {
        this.loadingService.stopLoading();
        this.rowData = isReset
          ? response?.items
          : [...this.rowData, ...response?.items];
        this.pagingInfo = response?.pagination;
      },
      () => {
        this.loadingService.stopLoading();
  }
    );
  }

  viewSubsciptions(): void {
    this.router.navigate(['/subscription']);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  viewMore(): void {
    this.pagingInfo.currentPage += 1;
    this.getTableByChart(false);
    }

  viewLess(): void {
    this.pagingInfo.currentPage = 0;
    this.getTableByChart(true);
  }

  onLegendClick(item): void {
    this.statusSelected =
      item?.length !== this.dataChart?.length
        ? item
            ?.filter((item) => item?.code)
            ?.map((item) => item?.code)
            ?.join(',')
        : item?.map((item) => item?.code)?.join(',');
    if (this.statusSelected === '') {
      this.rowData = []
    } else {
      this.viewLess();
}
  }
}
