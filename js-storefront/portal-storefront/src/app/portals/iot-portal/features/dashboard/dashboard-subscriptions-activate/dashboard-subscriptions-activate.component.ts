import { Component, HostListener, inject, Input, OnInit } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { IconModule } from '../../../../../core/icon/icon.module';
import { LoadingComponent } from '../../../../../layout/global/loading/loading.component';
import { PieChartComponent } from '../../../../../core/shared/pie-chart/pie-chart.component';
import { DashboardService } from '../../../services';
import { LoadingService } from '../../../../../core/services';
import { ItemChart } from '../../../interfaces';
import { ResponsePaging } from '../../../../../core/interfaces';
import { isInViewport } from '../../../../../core/helpers';
import { ChartType } from '../../../enums';

@Component({
  selector: 'app-dashboard-subscriptions-activate',
  standalone: true,
  imports: [
    IconModule,
    PieChartComponent,
    TranslateModule,
    CommonModule,
    LoadingComponent,
  ],
  templateUrl: './dashboard-subscriptions-activate.component.html',
  styleUrl: './dashboard-subscriptions-activate.component.scss',
  providers: [DashboardService],
})
export class DashboardSubscriptionsActivateComponent implements OnInit {
  @Input() index;

  isLoaded: boolean = false;

  @HostListener('window:scroll', ['$event'])
  onScroll(event) {
    this.checkInView();
  }

  translateService = inject(TranslateService);
  dashboardService = inject(DashboardService);
  router = inject(Router);
  loadingService = inject(LoadingService);

  dataChart: ItemChart[] = [];

  colors = [
    '#dc3d97',
    '#2aba6c',
    '#de9e1c',

    '#808080',
    '#119900',
    '#fcbf45',
    '#564c9d',
    '#099ec6',
  ];

  rowData: any[] = [];
  pagingInfo: ResponsePaging = {
    currentPage: 0,
    pageSize: 5,
  };
  statusSelected: string;

  ngOnInit(): void {
    if (this.index < 3) {
      this.isLoaded = true;
      this.getChart();
    }
  }

  checkInView() {
    const box = document.querySelector(`.dashboard-subscriptions-activate`);

    if (box && isInViewport(box) && !this.isLoaded) {
      this.isLoaded = true;
      this.getChart();
    }
  }

  getChart(): void {
    this.dashboardService.fetchChartData(
      ChartType.SubscriptionToActive,
      (items) => {
        this.dataChart = items;
        this.statusSelected = items?.map(item => item?.code)?.join(',');
        this.getTableByChart(true);
      }
    );
  }

  getTableByChart(isReset: boolean): void {
    const param = {
      currentPage: this.pagingInfo?.currentPage,
      pageSize: this.pagingInfo?.pageSize,
      status: this.statusSelected,
    };
    this.loadingService.startLoading();
    this.dashboardService.getSubscriptionToActive(param).subscribe(
      (response) => {
        this.loadingService.stopLoading();
        this.rowData = isReset
          ? response?.items
          : [...this.rowData, ...response?.items];
        this.pagingInfo = response?.pagination;
      },
      () => {
        this.loadingService.stopLoading();
      }
    );
  }

  viewSubsciptions(): void {
    this.router.navigate(['/subscription']);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  viewMore(): void {
    this.pagingInfo.currentPage += 1;
    this.getTableByChart(false);
  }

  viewLess(): void {
    this.pagingInfo.currentPage = 0;
    this.getTableByChart(true);
  }

  onLegendClick(item): void {
    this.statusSelected =
      item?.length !== this.dataChart?.length
        ? item
          ?.filter((item) => item?.code)
          ?.map((item) => item?.code)
          ?.join(',')
        : item?.map((item) => item?.code)?.join(',');

    if (this.statusSelected === '') {
      this.rowData = []
    } else {
      this.viewLess();
    }
  }
}
