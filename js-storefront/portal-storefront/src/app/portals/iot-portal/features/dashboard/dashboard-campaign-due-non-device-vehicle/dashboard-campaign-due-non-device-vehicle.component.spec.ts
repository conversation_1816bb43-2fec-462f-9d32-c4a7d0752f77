import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DashboardCampaignDueNonDeviceVehicleComponent } from './dashboard-campaign-due-non-device-vehicle.component';

describe('DashboardCampaignDueNonDeviceVehicleComponent', () => {
  let component: DashboardCampaignDueNonDeviceVehicleComponent;
  let fixture: ComponentFixture<DashboardCampaignDueNonDeviceVehicleComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DashboardCampaignDueNonDeviceVehicleComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(DashboardCampaignDueNonDeviceVehicleComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
