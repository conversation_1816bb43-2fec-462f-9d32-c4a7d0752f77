import { Component, HostListener, inject, Input, OnInit } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { IconModule } from '../../../../../core/icon/icon.module';
import { LoadingComponent } from '../../../../../layout/global/loading/loading.component';
import { DeviceService } from '../../../services';
import { LoadingService, NotificationService, UserService } from '../../../../../core/services';
import { ItemDevice, PayloadGetDevice } from '../../../interfaces';
import { ResponsePaging } from '../../../../../core/interfaces';
import { isInViewport } from '../../../../../core/helpers';
import { DeviceManagementTab } from '../../../enums';

@Component({
  selector: 'app-dashboard-sim-enabled',
  standalone: true,
  imports: [
    TranslateModule,
    CommonModule,
    IconModule,
    LoadingComponent,
    RouterModule,
  ],
  providers: [DeviceService, NotificationService],
  templateUrl: './dashboard-sim-enabled.component.html',
  styleUrl: './dashboard-sim-enabled.component.scss',
})
export class DashboardSimEnabledComponent implements OnInit {
  @Input() index;

  translateService = inject(TranslateService);
  deviceService = inject(DeviceService);
  userService = inject(UserService);
  router = inject(Router);
  loadingService = inject(LoadingService);

  @HostListener('window:scroll', ['$event'])
  onScroll(event) {
    this.checkInView();
  }

  isLoaded: boolean = false;
  rowData: ItemDevice[] = [];

  pagingInfo: ResponsePaging = {
    currentPage: 0,
    pageSize: 10,
  };

  onActionClick(device: ItemDevice): void {
    this.deviceService.openEnableSim(device, () => {
      this.getDeviceList(true);
    });
  }

  ngOnInit(): void {
    if (this.index < 3) {
      this.isLoaded = true;
      this.getDeviceList(true);
    }
  }

  checkInView() {
    const box = document.querySelector(`.dashboard-sim-enabled`);

    if (box && isInViewport(box) && !this.isLoaded) {
      this.isLoaded = true;
      this.getDeviceList(true);
    }
  }

  getDeviceList(isReset: boolean): void {
    const params: PayloadGetDevice = {
      currentPage: this.pagingInfo?.currentPage,
      pageSize: this.pagingInfo?.pageSize,
      simStatus: 'PENDING',
      includeHistory: false,
    };

    this.deviceService
      .getDeviceList(params, DeviceManagementTab.LDCM)
      .subscribe(
        (response) => {
          this.loadingService.stopLoading();
          this.rowData = isReset
          ? response?.items
          : [...this.rowData, ...response?.items];
          this.pagingInfo = response?.pagination;
        },
        () => {
          this.loadingService.stopLoading();
        }
      );
  }

  viewMore(): void {
    this.pagingInfo.currentPage += 1;
    this.getDeviceList(false);
  }

  viewLess(): void {
    this.pagingInfo.currentPage = 0;
    this.getDeviceList(true);
  }
}
