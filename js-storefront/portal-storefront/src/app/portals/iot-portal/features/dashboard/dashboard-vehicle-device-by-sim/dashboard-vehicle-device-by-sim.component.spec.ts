import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DashboardVehicleDeviceBySimComponent } from './dashboard-vehicle-device-by-sim.component';

describe('DashboardVehicleDeviceBySimComponent', () => {
  let component: DashboardVehicleDeviceBySimComponent;
  let fixture: ComponentFixture<DashboardVehicleDeviceBySimComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DashboardVehicleDeviceBySimComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(DashboardVehicleDeviceBySimComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
