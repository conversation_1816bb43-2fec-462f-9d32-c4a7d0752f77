<div class="dashboard-row ticket">
    <ng-container *ngIf="isLoaded">
        <div class="col-3">
            <div class="dashboard-content__box">
                <app-pie-chart 
                    *ngIf="ticketByStatus?.length > 0"
                    [data]="ticketByStatus" 
                    [title]="'Approval Tickets by Status'" 
                    [colors]="ticketByStatusColors"
                    [isLoading]="!isLoaded"
                    (onClick)="viewList()"
                ></app-pie-chart>
            </div>
        </div>
    
        <div class="col-9">
            <div class="dashboard-content__box">
                <div class="ticket-by-type">
                    <app-pie-chart 
                        *ngIf="ticketByType?.length > 0"
                        [data]="ticketByType" 
                        [title]="'Approval Tickets by Types'" 
                        [colors]="ticketByTypeColors" 
                        [isShowPercent]="true" 
                        [column]="2"
                        [height]="255"
                        [width]="255"
                        [isLoading]="!isLoaded"
                        (onClick)="viewList()"
                    ></app-pie-chart>
                </div>
            </div>
        </div>
    </ng-container>
</div>
  