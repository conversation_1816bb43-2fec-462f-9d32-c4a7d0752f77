import { Component, inject, OnInit } from '@angular/core';
import { DashboardApprovalTicketComponent } from '../dashboard-approval-ticket/dashboard-approval-ticket.component';
import { DashboardDevicePairedComponent } from '../dashboard-device-paired/dashboard-device-paired.component';
import { DashboardDeviceToPairedComponent } from '../dashboard-device-to-paired/dashboard-device-to-paired.component';
import { DashboardDeviceComponent } from '../dashboard-device/dashboard-device.component';
import { DashboardDueComponent } from '../dashboard-due/dashboard-due.component';
import { DashboardNonDeviceVehicleComponent } from '../dashboard-non-device-vehicle/dashboard-non-device-vehicle.component';
import { DashboardRemoteImmobilizationComponent } from '../dashboard-remote-immobilization/dashboard-remote-immobilization.component';
import { DashboardSimEnabledComponent } from '../dashboard-sim-enabled/dashboard-sim-enabled.component';
import { DashboardSubscriptionComponent } from '../dashboard-subscription/dashboard-subscription.component';
import { DashboardSubscriptionsActivateComponent } from '../dashboard-subscriptions-activate/dashboard-subscriptions-activate.component';
import { DashboardTicketComponent } from '../dashboard-ticket/dashboard-ticket.component';
import { DashboardVehicleDeviceBySimComponent } from '../dashboard-vehicle-device-by-sim/dashboard-vehicle-device-by-sim.component';
import { CommonModule } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { DashboardImmobilizationModeComponent } from '../dashboard-immobilization-mode/dashboard-immobilization-mode.component';
import { DashboardCampaignDueNonDeviceVehicleComponent } from '../dashboard-campaign-due-non-device-vehicle/dashboard-campaign-due-non-device-vehicle.component';

import { DashboardTicketVehicleComponent } from '../dashboard-ticket-vehicle/dashboard-ticket-vehicle.component';
import { PieChartComponent } from '../../../../../core/shared/pie-chart/pie-chart.component';
import { IconModule } from '../../../../../core/icon/icon.module';
import { UserService } from '../../../../../core/services';
import { PERMISSIONS_CODE } from '../../../../../core/constants';

@Component({
  selector: 'app-dashboard-view',
  standalone: true,
  imports: [
    CommonModule,
    DashboardTicketComponent,
    DashboardDeviceComponent,
    DashboardApprovalTicketComponent,
    DashboardSubscriptionsActivateComponent,
    DashboardSimEnabledComponent,
    DashboardSubscriptionComponent,
    DashboardDueComponent,
    DashboardNonDeviceVehicleComponent,
    DashboardDevicePairedComponent,
    DashboardDeviceToPairedComponent,
    DashboardVehicleDeviceBySimComponent,
    DashboardRemoteImmobilizationComponent,
    PieChartComponent,
    DashboardImmobilizationModeComponent,
    DashboardCampaignDueNonDeviceVehicleComponent,
    DashboardTicketVehicleComponent,
    IconModule,
    TranslateModule,
  ],
  templateUrl: './dashboard-view.component.html',
  styleUrl: './dashboard-view.component.scss'
})
export class DashboardViewComponent implements OnInit {
  userService = inject(UserService);
  translateService = inject(TranslateService);

  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;

  ngOnInit(): void {
  }
}
