<div class="dashboard-row vehicle-device-block dashboard-vehicle-device-by-sim">
  <ng-container *ngIf="isLoaded">
    <div
      class="col-6"
      *ngIf="!userService.isHasPermission([PERMISSIONS_CODE.DOSDHELPDESKGROUP])"
    >
      <div
        class="dashboard-content__box"
        [ngStyle]="{ 'padding-right': '0' }"
      >
        <app-loading *ngIf="!isLoaded"></app-loading>

        <h2>Vehicles with SOS</h2>

        <div class="table-container">
          <div class="content-table">
            @if(rowData?.length > 0) {
            <table>
              <tr>
                <th>
                  {{ "manageSub.table.vehicle" | translate }}
                </th>
                <th>
                  {{ "tickets.ticketDetail.plate" | translate }}
                </th>
                <th></th>
              </tr>
              <tr *ngFor="let item of rowData">
                <td>
                  <span class="header-mobile">
                    {{ "manageSub.table.vehicle" | translate }}:
                  </span>
                  <a>{{ item.vehicleModel }}</a>
                </td>
                <td>
                  <span class="header-mobile">
                    {{ "tickets.ticketDetail.plate" | translate }}:
                  </span>
                  {{ item.plateNumber }}
                </td>
                <td class="action">
                  <span (click)="viewVehicle(item)">
                    <mat-icon svgIcon="ic-eyes"></mat-icon>
                    {{ "common.view" | translate }}
                  </span>
                </td>
              </tr>
            </table>

            @if(pagingInfoVehicleSos?.totalPages > 1) { @if(rowData?.length <
            pagingInfoVehicleSos?.totalResults) {
            <div class="view-more" (click)="viewMore()">
              <mat-icon svgIcon="ic-view-more"></mat-icon>
              {{ "dashboard.viewMore" | translate }}
            </div>
            }@else {
            <div class="view-less" (click)="viewLess()">
              <mat-icon svgIcon="ic-view-more"></mat-icon>
              {{ "dashboard.viewLess" | translate }}
            </div>
            } } }@else {
            <div class="no-data">
              {{ "vehicleManagement.noVehicle" | translate }}
            </div>
            }
          </div>
        </div>
      </div>
    </div>

    <div class="col-6">
      <div class="dashboard-content__box">
        <app-pie-chart
          *ngIf="dataChart?.length > 0"
          [data]="dataChart"
          [title]="'LDCM Devices by SIM Status'"
          [colors]="colors"
          [column]="2"
          [isLoading]="!isLoaded"
          [totalLabel]="'common.totalDevices'"
          (onClick)="viewDeviceList()"
        ></app-pie-chart>
      </div>
    </div>
  </ng-container>
</div>
