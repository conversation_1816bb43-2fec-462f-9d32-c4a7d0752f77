import { Component, HostListener, inject, Input, OnInit } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Router, RouterModule } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { ModalPairDeviceComponent } from '../../device/device-list/modal-pair-device/modal-pair-device.component';
import { filter } from 'rxjs';

import { CommonModule } from '@angular/common';
import { AgGridCustomComponent } from '../../../../../core/shared';
import { LoadingComponent } from '../../../../../layout/global/loading/loading.component';
import { IconModule } from '../../../../../core/icon/icon.module';
import { LoadingService, NotificationService, UserService } from '../../../../../core/services';
import { DeviceService } from '../../../services';
import { ItemDevice, PayloadGetDevice } from '../../../interfaces';
import { ResponsePaging } from '../../../../../core/interfaces';
import { isInViewport } from '../../../../../core/helpers';
import { ActionModal } from '../../../../../core/enums';
import { DeviceManagementTab, PairingStatus } from '../../../enums';

@Component({
  selector: 'app-dashboard-device-to-paired',
  standalone: true,
  imports: [
    AgGridCustomComponent,
    TranslateModule,
    LoadingComponent,
    CommonModule,
    IconModule,
    RouterModule,
  ],
  providers: [TranslateService, DeviceService, NotificationService],
  templateUrl: './dashboard-device-to-paired.component.html',
  styleUrl: './dashboard-device-to-paired.component.scss',
})
export class DashboardDeviceToPairedComponent implements OnInit {
  @Input() index;

  translateService = inject(TranslateService);
  userService = inject(UserService);
  router = inject(Router);
  dialog = inject(MatDialog);
  deviceService = inject(DeviceService);
  loadingService = inject(LoadingService);

  isLoaded: boolean = false;

  @HostListener('window:scroll', ['$event'])
  onScroll(event) {
    this.checkInView();
  }

  rowData: ItemDevice[] = [];
  pagingInfo: ResponsePaging = {
    currentPage: 0,
    pageSize: 10,
  };
  

  ngOnInit(): void {
    if (this.index < 3) {
      this.isLoaded = true;
      this.getDeviceList(true);
    }
  }

  checkInView() {
    const box = document.querySelector(`.dashboard-device-to-paired`);

    if (box && isInViewport(box) && !this.isLoaded) {
      this.isLoaded = true;
      this.getDeviceList(true);
    }
  }

  onActionClick(device: any) {
    const dialogRef = this.dialog.open(ModalPairDeviceComponent, {
      width: '530px',
      data: {
        title: this.translateService.instant(
          'deviceManagement.modalPairDevice.title'
        ),
        icon: 'ic-link-pair-red',
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('deviceManagement.pair'),
        deviceId: device.deviceId,
      },
    });
    dialogRef.componentInstance.onPairingComplete.subscribe(
      (success: boolean) => {
        if (success) {
          this.getDeviceList(true);
        }
      }
    );
    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Pair))
      .subscribe((result) => {});
  }

  getDeviceList(isReset: boolean) {
    const params: PayloadGetDevice = {
      currentPage: this.pagingInfo?.currentPage,
      pageSize: this.pagingInfo?.pageSize,
      pairingStatus: PairingStatus.NotPaired,
      includeHistory: false,
    };

    this.loadingService.startLoading();
    this.deviceService
      .getDeviceList(params, DeviceManagementTab.LDCM)
      .subscribe(
        (response) => {
          this.loadingService.stopLoading();
          this.rowData = isReset
          ? response?.items
          : [...this.rowData, ...response?.items];
          this.pagingInfo = response?.pagination;
        },
        () => {
          this.loadingService.stopLoading();
        }
      );
  }

  viewMore(): void {
    this.pagingInfo.currentPage += 1;
    this.getDeviceList(false);
  }

  viewLess(): void {
    this.pagingInfo.currentPage = 0;
    this.getDeviceList(true);
  }
}
