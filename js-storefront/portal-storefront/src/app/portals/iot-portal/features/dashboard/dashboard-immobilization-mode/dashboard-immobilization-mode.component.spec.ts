import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DashboardImmobilizationModeComponent } from './dashboard-immobilization-mode.component';

describe('DashboardImmobilizationModeComponent', () => {
  let component: DashboardImmobilizationModeComponent;
  let fixture: ComponentFixture<DashboardImmobilizationModeComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DashboardImmobilizationModeComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(DashboardImmobilizationModeComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
