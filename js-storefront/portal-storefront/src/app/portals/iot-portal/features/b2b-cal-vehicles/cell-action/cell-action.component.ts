import { CommonModule } from '@angular/common';
import { Component, EventEmitter, inject, Input, OnInit, Output } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IconModule } from '../../../../../core/icon/icon.module';
import { UserService } from '../../../../../core/services';
import { PERMISSIONS_CODE } from '../../../../../core/constants';
import { ActionTable } from '../../../../../core/enums';

@Component({
  selector: 'app-cell-action',
  standalone: true,
  imports: [IconModule, TranslateModule, CommonModule],
  templateUrl: './cell-action.component.html',
  styleUrls: ['./cell-action.component.scss'],
})
export class CellActionComponent {
  @Input() rowIndex!: number;

  @Output() edit = new EventEmitter<void>();
  @Output() remove = new EventEmitter<void>();

  userService = inject(UserService);

  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;

  params!: any;
  isEdit: boolean = false;
  noAction: boolean = false;

  agInit(params: any): void {
    this.params = params;
    this.isEdit =
      params?.data?.status?.code === undefined ||
      params?.data?.status?.code !== 'ACTIVE'
        ? false
        : true;
    this.noAction =
      params?.data?.status?.code !== undefined &&
      params?.data?.status?.code !== 'ACTIVE'
        ? true
        : false;
    this.rowIndex = params.rowIndex;
  }

  refresh(params: any): boolean {
    return true;
  }

  onEdit(): void {
    this.params.onClick(ActionTable.Edit, this.params?.data);
  }

  onAdd(): void {
    this.params.onClick(ActionTable.Add, this.params?.data);
  }
}
