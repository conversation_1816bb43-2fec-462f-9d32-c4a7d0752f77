@if(!noAction && userService.isHasPermission([PERMISSIONS_CODE.IOT_LOAN_MANAGE])) {
  <div class="action-buttons-cell-renderer">
    <button class="edit-button" (click)="onAdd()" *ngIf="!isEdit">
      <mat-icon svgIcon="ic-add-red" class="small-icon"></mat-icon> {{'common.addLoan' | translate}}
    </button>
    <button class="edit-button" (click)="onEdit()" *ngIf="isEdit">
      <mat-icon svgIcon="ic-edit" class="small-icon"></mat-icon> {{'common.editLoan' | translate}}
    </button>
  </div>
}

