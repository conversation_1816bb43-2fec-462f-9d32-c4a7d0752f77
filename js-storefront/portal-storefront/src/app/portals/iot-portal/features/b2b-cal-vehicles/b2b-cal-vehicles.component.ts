import { CommonModule, DatePipe } from '@angular/common';
import { Component, inject } from '@angular/core';
import {
  FormsModule,
  ReactiveFormsModule,
  FormGroup,
  FormControl,
  Validators,
} from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Subscription, filter, take } from 'rxjs';

import { ActivatedRoute, Router } from '@angular/router';
import { CellActionComponent } from './cell-action/cell-action.component';
import { ModalActionLoanComponent } from './modal-action-loan/modal-action-loan.component';
import { AgGridCustomComponent, DropdownFormGroupComponent, FormGroupComponent, WidgetSummaryComponent } from '../../../../core/shared';
import { LoadingComponent } from '../../../../layout/global/loading/loading.component';
import { LoadingService, NotificationService, UserService } from '../../../../core/services';
import { LoanService } from '../../services/loan';
import { ItemWidget, LastImportResult, OptionDropdown, PagingInfo } from '../../../../core/interfaces';
import { LoanSummaryTab, StatusLoan } from '../../enums';
import { ActionModal, ActionTable, DateFormat } from '../../../../core/enums';
import { LOAN_WIDGETS, TICKET_ASSIGNEE } from '../../constants';

@Component({
  selector: 'app-b2b-cal-vehicles',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    MatIconModule,
    WidgetSummaryComponent,
    LoadingComponent,
    AgGridCustomComponent,
    FormGroupComponent,
    DropdownFormGroupComponent,
  ],
  providers: [LoadingService, NotificationService, LoanService, DatePipe],
  templateUrl: './b2b-cal-vehicles.component.html',
  styleUrls: ['./b2b-cal-vehicles.component.scss'],
})
export class B2bCalVehiclesComponent {
  loadingService = inject(LoadingService);
  translateService = inject(TranslateService);
  loanService = inject(LoanService);
  router = inject(Router);
  userService = inject(UserService);
  datePipe = inject(DatePipe);
  dialog = inject(MatDialog);
  activatedRoute = inject(ActivatedRoute);

  subscription = new Subscription();
  selectedSummaryTab: any;
  pagingInfo: PagingInfo = {
    totalItems: 0,
    currentPage: 0,
    numberOfPage: 10,
  };
  importInfo: LastImportResult;

  filterTickets = new FormGroup({
    vin: new FormControl(''),
    loanId: new FormControl(''),
    status: new FormControl(StatusLoan.All),
    filterWithoutLoan: new FormControl(false),
    filterLoanExpiringSoon: new FormControl(false),
  });

  filterList: OptionDropdown[] = [];

  widgets: ItemWidget[] = [
    {
      id: '' as LoanSummaryTab,
      count: 0,
      description: '',
      icon: '',
    },
  ];

  rowData: any[];

  colDefs = [
    {
      headerName: this.translateService.instant('manageLoan.vin'),
      headerValueGetter: () => this.translateService.instant('manageLoan.vin'),
      field: 'chasissNum',
      flex: 1,
      cellClass: 'cell-word-wrap',
      cellRenderer: (params) => {
        return params?.value ? `<span>${params?.value}</span>` : '-';
      },
      wrapText: true,
      autoHeight: true,
    },
    {
      headerName: this.translateService.instant('manageLoan.loanId'),
      headerValueGetter: () =>
        this.translateService.instant('manageLoan.loanId'),
      field: 'id',
      flex: 1,
      autoHeight: true,
      cellClass: 'cell-word-wrap',
      cellRenderer: (params) => {
        return params?.value ? `<span>${params?.value}</span>` : '-';
      },
    },
    {
      headerName: this.translateService.instant('manageLoan.vehicleType'),
      headerValueGetter: () =>
        this.translateService.instant('manageLoan.vehicleType'),
      field: 'vehicleType',
      flex: 1,
      cellRenderer: (params) => {
        return params?.value ? `<span>${params?.value}</span>` : '-';
      },
    },
    {
      headerName: this.translateService.instant('manageLoan.startDate'),
      headerValueGetter: () =>
        this.translateService.instant('manageLoan.startDate'),
      field: 'startDate',
      flex: 1,
      cellRenderer: (params) => {
        return params?.value
          ? `<span>${this.datePipe.transform(
              params?.value,
              DateFormat.ShortDate
            )}</span>`
          : '-';
      },
    },
    {
      headerName: this.translateService.instant('manageLoan.endDate'),
      headerValueGetter: () =>
        this.translateService.instant('manageLoan.endDate'),
      field: 'endDate',
      flex: 1,
      cellRenderer: (params) => {
        return params?.value
          ? `<span>${this.datePipe.transform(
              params?.value,
              DateFormat.ShortDate
            )}</span>`
          : '-';
      },
    },
    {
      headerName: this.translateService.instant('manageLoan.status'),
      headerValueGetter: () =>
        this.translateService.instant('manageLoan.status'),
      field: 'status',
      cellRenderer: (params) => {
        return params?.value?.name
          ? `<span>${params?.value?.name}</span>`
          : '-';
      },
      flex: 1,
      autoHeight: true,
      cellClass: 'cell-word-wrap',
    },
    {
      headerName: this.translateService.instant('manageLoan.actions'),
      headerValueGetter: () =>
        this.translateService.instant('manageLoan.actions'),
      field: 'actions',
      flex: 1,
      cellRenderer: CellActionComponent,
      cellRendererParams: {
        onClick: (type: string, data: any) => this.modalAction(type, data),
      },
      cellClass: 'action-grid action-last-grid',
      sortable: false,
    },
  ];

  defaultColDef = {
    resizable: false,
    valueFormatter: (params) => (params.value ? params.value : '-'),
    sortable: false,
    menuTabs: [],
    wrapHeaderText: true,
    autoHeaderHeight: true,
  };
  suggestionsAssignee: OptionDropdown[] = TICKET_ASSIGNEE;
  currentUser: OptionDropdown;

  loanActionForm: FormGroup = new FormGroup({
    id: new FormControl(''),
    vin: new FormControl({ value: '', disabled: true }, Validators.required),
    loanId: new FormControl('', Validators.required),
    status: new FormControl('ACTIVE', Validators.required),
    vehicleType: new FormControl(''),
    startDate: new FormControl('', Validators.required),
    endDate: new FormControl('', Validators.required),
    closed: new FormControl(''),
  });

  ngOnInit(): void {
    this.getAllFilter();
    this.getWidgets();
    this.activatedRoute.queryParams.pipe(take(1)).subscribe((params) => {
      const { tabId } = params;

      this.filterTickets.patchValue({
        vin: '',
        loanId: '',
        status: StatusLoan.All,
        filterWithoutLoan: tabId === LoanSummaryTab.CAL_VEHICLES_WITHOUT_LOAN_INFO,
        filterLoanExpiringSoon: tabId === LoanSummaryTab.CAL_VEHICLES_WITH_LOANS_EXPIRING_SOON,
      });

      this.router.navigate([], {
        relativeTo: this.activatedRoute,
        queryParams: {
          tabId: null
        },
        queryParamsHandling: 'merge',
        replaceUrl: true,
      });

      this.getLoanList();
    });
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  changeSummaryTab(id: any) {
    this.selectedSummaryTab = id;
    this.pagingInfo.currentPage = 0;

    this.mapWidgetFilter(id);
    this.getLoanList();
  }

  getWidgets(): void {
    this.loadingService.showLoader();
    this.subscription.add(
      this.loanService.getWidgetList().subscribe((response) => {
        this.loadingService.hideLoader();
        const { widgets } = response;

        this.widgets = widgets.map((item) => {
          const { description, icon } =
            LOAN_WIDGETS?.find((widget) => widget.id === item.type) || {};
          return {
            id: item?.type,
            count: item?.count,
            description,
            icon,
          };
        });
      })
    );
  }

  getAllFilter(): void {
    this.loadingService.showLoader();
    this.loanService.getFilter().subscribe((response) => {
      this.loadingService.hideLoader();
      this.filterList = response;
    });
  }

  mapWidgetFilter(id: LoanSummaryTab) {
    switch (id) {
      case LoanSummaryTab.CAL_VEHICLES_WITHOUT_LOAN_INFO: {
        this.filterTickets.patchValue({
          loanId: '',
          vin: '',
          status: StatusLoan.All,
          filterWithoutLoan: true,
          filterLoanExpiringSoon: false,
        });
        break;
      }
      case LoanSummaryTab.CAL_VEHICLES_WITH_LOANS_EXPIRING_SOON: {
        this.filterTickets.patchValue({
          loanId: '',
          vin: '',
          status: StatusLoan.All,
          filterWithoutLoan: false,
          filterLoanExpiringSoon: true,
        });
        break;
      }
    }
  }

  getLoanList(): void {
    const params: any = {
      currentPage: this.pagingInfo?.currentPage,
      pageSize: this.pagingInfo?.numberOfPage,
      vin: this.filterTickets.value.vin,
      loanID: this.filterTickets.value.loanId,
      status: this.filterTickets.value.status,
      filterWithoutLoan: this.filterTickets.value.filterWithoutLoan,
      filterLoanExpiringSoon: this.filterTickets.value.filterLoanExpiringSoon,
    };

    this.loadingService.showLoader();
    this.loanService.getListLoan(params).subscribe({
      next: (response: any) => {
        this.rowData =
          (response?.items &&
            response?.items.length &&
            response?.items.map((item) => {
              return {
                ...item,
                ...item?.loan,
              };
            })) ||
          [];

        this.pagingInfo.totalItems = response?.pagination?.totalResults;

        this.loadingService.hideLoader();
      },
      error: () => {
        this.loadingService.hideLoader();
      },
    });
  }

  onPageChange(value: number): void {
    this.pagingInfo.currentPage = value;
    this.getLoanList();
  }

  changeItemPerPage(item) {
    this.pagingInfo.numberOfPage = item;
    this.pagingInfo.currentPage = 0;
    this.getLoanList();
  }

  onSearch(): void {
    this.pagingInfo.currentPage = 0;
    this.filterTickets.patchValue({
      filterWithoutLoan: false,
      filterLoanExpiringSoon: false,
    });
    this.getLoanList();
  }

  modalAction(type: string, data: any): void {
    const isEdit = type === ActionTable.Edit;
    if (!isEdit) {
      this.loanActionForm.patchValue({
        status: 'ACTIVE',
        vin: data?.chasissNum,
      });
    } else {
      this.loanActionForm.patchValue({
        vin: data?.chasissNum,
        status: data?.status?.code,
        loanId: data?.id,
        endDate: new Date(data?.endDate),
        startDate: new Date(data?.startDate),
      });
    }
    const dialogRef = this.dialog.open(ModalActionLoanComponent, {
      width: '680px',
      maxHeight: '90vh',
      disableClose: true,
      autoFocus: false,
      data: {
        isEdit,
        vin: data?.chasissNum,
        mainForm: this.loanActionForm,
      },
    });

    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe(() => {
        this.pagingInfo.currentPage = 0;
        this.getLoanList();
      });
  }
}
