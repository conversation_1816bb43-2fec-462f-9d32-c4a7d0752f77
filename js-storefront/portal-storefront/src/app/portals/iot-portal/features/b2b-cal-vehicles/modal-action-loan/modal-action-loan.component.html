<div class="modal-action-form">
  <h2 class="title-dialog">
    {{
      isEdit
        ? ("manageLoan.editLoan" | translate)
        : ("manageLoan.addLoan" | translate)
    }}
  </h2>
  <mat-dialog-content
    class="container-dialog"
    [class.loading]="loadingService.isLoading"
  >
    @if (loadingService.isLoading) {
    <div class="spinner"></div>
    }
    <form [formGroup]="mainForm">
      @if(!isWidget) {
      <app-form-group
        [label]="'manageLoan.vin' | translate"
        [control]="vinControl"
        controlId="vin"
        [isReadOnly]="true"
        [required]="true"
        [errorMessage]="
          'common.requiredField'
            | translate : { field: ('manageLoan.vin' | translate) }
        "
      ></app-form-group>
      }

      <div class="row">
        <div class="col-6">
          <app-form-group
            [label]="'manageLoan.loanId' | translate"
            [control]="loanId"
            controlId="loanId"
            [required]="true"
            [isReadOnly]="isReadOnly"
            [errorMessage]="
              'common.requiredField'
                | translate : { field: ('manageLoan.loanId' | translate) }
            "
          ></app-form-group>
        </div>

        <div class="col-6 form-group">
          <app-radio-button
            [label]="'manageLoan.loanStatus'"
            [control]="status"
            [option]="statusRadio"
            (changeOption)="onSelectStatusRadio($event)"
          ></app-radio-button>
        </div>
      </div>
      <div class="row">
        <div class="col-6">
          <app-date-form-group
            [label]="'manageLoan.startDate' | translate"
            [control]="startDate"
            controlId="startDate"
            [required]="true"
            [isReadOnly]="isReadOnly"
            [maxDate]="endDate.value"
            [errorMessage]="
              'common.requiredField'
                | translate : { field: ('manageLoan.startDate' | translate) }
            "
          ></app-date-form-group>
        </div>
        <div class="col-6">
          <app-date-form-group
            [label]="'manageLoan.endDate' | translate"
            [control]="endDate"
            controlId="endDate"
            [required]="true"
            [isReadOnly]="isReadOnly"
            [minDate]="startDate.value"
            [errorMessage]="
              'common.requiredField'
                | translate : { field: ('manageLoan.endDate' | translate) }
            "
          ></app-date-form-group>
        </div>
      </div>
      @if(isEdit && this.mainForm.value.status == 'CLOSED') {
      <div class="col-6 form-group">
        <div class="">
          <mat-checkbox formControlName="closed" class="custom-mat-checkbox">
            {{ "manageLoan.messageClosed" | translate }}
          </mat-checkbox>
        </div>
      </div>
      }
    </form>
    <mat-dialog-actions class="action-dialog modal-action-form__actions">
      <button
        type="button"
        class="btn btn--outline cancel-btn"
        (click)="onCancel()"
      >
        {{ "common.cancel" | translate }}
      </button>
      <button
        class="btn btn--primary"
        type="submit"
        (click)="onSubmit()"
        [disabled]="mainForm?.invalid"
      >
        {{
          isEdit
            ? isWidget
              ? ("common.submit" | translate)
              : ("common.confirm" | translate)
            : isWidget
            ? ("common.enable" | translate)
            : ("common.add" | translate)
        }}
      </button>
    </mat-dialog-actions>
  </mat-dialog-content>
</div>
