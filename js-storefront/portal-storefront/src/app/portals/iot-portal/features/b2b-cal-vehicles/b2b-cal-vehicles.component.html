<app-loading *ngIf="loadingService.isLoading"></app-loading>

<div class="header-section">
  <h2 class="title-page">
    {{ "manageLoan.title" | translate }}
  </h2>
</div>

<app-widget-summary
  [widgets]="widgets"
  (changeTab)="changeSummaryTab($event)"
></app-widget-summary>

<form [formGroup]="filterTickets">
  <div class="filter-ticket">
    <div
      class="filter-ticket__properties"
    >
      <app-form-group
        [label]="'manageLoan.vin' | translate"
        [control]="filterTickets?.controls?.vin"
        [placeholder]="'enterHereToSearch' | translate"
        (onEnter)="onSearch()"
      ></app-form-group>

      <app-form-group
        [label]="'manageLoan.loanId' | translate"
        [control]="filterTickets?.controls?.loanId"
        [placeholder]="'enterHereToSearch' | translate"
        (onEnter)="onSearch()"
      ></app-form-group>

      <app-dropdown-form-group
        [label]="'manageLoan.loanStatus' | translate"
        [control]="filterTickets?.controls?.status"
        [options]="filterList"
        (enter)="onSearch()"
      ></app-dropdown-form-group>
    </div>
    <div class="filter-ticket__search">
      <button type="submit" class="btn-tertiary search" (click)="onSearch()">
        {{ "common.search" | translate }}
      </button>
    </div>
  </div>
</form>

<div class="device-list">
  @if (rowData?.length > 0 ) {
  <app-ag-grid-custom
    class="ticket-table"
    [rowData]="rowData"
    [colDefs]="colDefs"
    [defaultColDef]="defaultColDef"
    [isPaging]="true"
    [pagingInfo]="pagingInfo"
    [isShowActionExport]="false"
    (onPageChange)="onPageChange($event)"
    (changeItemPerPage)="changeItemPerPage($event)"
  >
  </app-ag-grid-custom>
  } @else {
  <div class="no-data">{{ "manageLoan.notFound" | translate }}</div>
  }
</div>
