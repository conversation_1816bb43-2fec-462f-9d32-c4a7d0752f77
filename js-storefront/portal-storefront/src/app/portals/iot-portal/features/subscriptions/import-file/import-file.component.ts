import { Component, inject } from "@angular/core"
import { FormsModule, ReactiveFormsModule, FormGroup, FormControl, Validators } from "@angular/forms"
import { MatDialogModule, MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog"
import { MatIconModule } from "@angular/material/icon"
import { TranslateModule, TranslateService } from "@ngx-translate/core"
import { FormGroupComponent } from "../../../../../core/shared"
import { ImportFilesComponent } from "../../../../../core/shared/import-files/import-files.component"
import { NotificationService } from "../../../../../core/services"
import { ActionModal } from "../../../../../core/enums"

@Component({
  selector: 'app-import-file',
  standalone: true,
  imports: [
    MatDialogModule,
    MatIconModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    FormGroupComponent,
    ImportFilesComponent,
  ],
  templateUrl: './import-file.component.html',
  providers: [NotificationService],
})
export class ImportFileComponent {
  data = inject(MAT_DIALOG_DATA);
  dialogRef = inject(MatDialogRef<ImportFileComponent>);
  translateService = inject(TranslateService);
  
  notificationService = inject(NotificationService);

  form = new FormGroup({
    selectFile: new FormControl(null, Validators.required),
  });

  accept = '.csv';
  confirmTemp = false;

  handleFiles(files: FileList | null): void {
    Array.from(files).forEach((file) => {
      this.form.controls.selectFile.setValue(file);
    });
  }

  handleInvalidFile(): void {
    this.notificationService.showError(
      this.translateService.instant('common.invalidFile')
    );
  }

  onImport() {
    this.confirmTemp = true;
  }

  onCancel() {
    this.dialogRef.close();
  }

  onConfirm() {
    this.dialogRef.close({
      action: ActionModal.Submit,
      data: this.form.controls.selectFile.value,
    });
  }
}
