@import "../../../../../../styles/abstracts/mixins";
@import "../../../../../../styles/abstracts/variables";

:host {
  min-height: calc(100vh - 75px);
  display: block;
  padding: 30px 30px 0;

  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .subscription-detail {
    background: $bg-color-10;
    padding: 15px;
    display: flex;
    flex-direction: column;
    gap: 15px;

    .section-header {
      margin-bottom: 25px;
      &--content {
        span {
          font-size: 18px;
        }
      }

      &.subscription-section {
        justify-content: space-between;

        .subscription-section__left-title {
          display: flex;
          gap: 12px;
          align-items: center;
        }

        button {
          color: #eb0a1e;
          font-size: 14px;
          font-weight: 600;
          display: flex;
          align-items: center;
          gap: 5px;
          cursor: pointer;
        }
      }
    }

    &__container {
      display: flex;
      gap: 15px;

      > * {
        flex: 1;
      }
    }

    &__box {
      background-color: $main-bg-color;
      padding: 25px;
    }

    &__content-detail {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 20px;

      &__item {
        display: flex;
        flex-direction: column;

        :last-child {
          font-weight: 600;
          font-size: 16px;
        }

        .loan-action {
          display: flex;
          gap: 40px;
          &.no-value {
            gap: 50px;
          }

          button {
            color: #eb0a1e;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 5px;
            cursor: pointer;
          }
        }
      }
    }

    app-ag-grid-custom {
      ::ng-deep {
        .ag-header-cell:last-child {
          .ag-header-cell-label {
            justify-content: start;
          }
        }

        .ag-cell p {
          margin: 0;
        }

        .value-tag {
          .ag-cell-value {
            @include value-tag;
          }
        }

        .action-last-grid {
          display: flex;
          align-items: center;
        }
      }
    }
  }
}
