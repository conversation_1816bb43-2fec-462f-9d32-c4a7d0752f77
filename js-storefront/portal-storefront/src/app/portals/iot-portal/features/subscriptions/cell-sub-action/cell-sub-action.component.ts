import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  inject,
  Input,
  Output,
} from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IconModule } from '../../../../../core/icon/icon.module';
import { ItemSubscription } from '../../../interfaces/widgets.interface';
import { UserService } from '../../../../../core/services';
import { PERMISSIONS_CODE } from '../../../../../core/constants';

@Component({
  selector: 'app-cell-sub-action',
  standalone: true,
  imports: [IconModule, TranslateModule, CommonModule],
  templateUrl: './cell-sub-action.component.html',
  styleUrls: ['./cell-sub-action.component.scss'],
})
export class CellSubActionComponent {
  @Input() rowIndex!: number;

  @Output() edit = new EventEmitter<void>();
  @Output() remove = new EventEmitter<void>();

  userService = inject(UserService);

  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;

  params!: any;
  isShowConfirmPayment: boolean = false;
  isActivation: boolean = false;

  agInit(params: any): void {
    this.params = params;

    const data = params?.data as ItemSubscription;
    const hasActivationPermission = this.userService.isHasPermission([
      PERMISSIONS_CODE.IOT_SUBSCRIPTION_ACTIVATE,
    ]);

    const hasPaymentConfirmPermission = this.userService.isHasPermission([
      PERMISSIONS_CODE.IOT_SUBSCRIPTION_PAYMENT_CONFIRM,
    ]);

    this.isActivation = data?.canActivate && hasActivationPermission;

    this.isShowConfirmPayment =
      data?.canConfirmPayment && hasPaymentConfirmPermission;
    this.rowIndex = params.rowIndex;
  }

  refresh(params: any): boolean {
    return true;
  }

  confirmPayment(): void {
    this.params.onClick('Confirm payment', this.params?.data);
  }

  activateSubscription(): void {
    this.params.onClick('Activate subscription', this.params?.data);
  }
}
