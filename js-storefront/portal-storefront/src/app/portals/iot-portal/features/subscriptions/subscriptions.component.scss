@import "../../../../../styles/abstracts/mixins";
@import "../../../../../styles/abstracts/variables";

:host {
  min-height: calc(100vh - 60px);
  display: block;
  
  .header-section {
    background-color: $bg-color-9;
    padding: 20px 16px 100px;

    display: flex;
    justify-content: space-between;

    @include md {
      padding: 30px 30px 135px;
    }

    .title-page {
      color: $text-color-white;
      font-size: $fs38;
      margin: 0;
    }
    .device-action {
      @include md {
        display: flex;
        justify-content: space-between;
      }

      &__tabname {
        display: flex;
        gap: 25px;
        padding: 5px 0 25px;

        font-size: $fs22;
        font-weight: $fw600;
        color: $text-color-4;

        span {
          padding: 0 15px 12px;
          cursor: pointer;

          &.active {
            color: $text-color-white;
            border-bottom: 3px solid $border-color-12;
          }
        }
      }

      &__btn-action {
        display: flex;
        flex-direction: column;
        gap: 12px;

        @include md {
          flex-direction: row;
          gap: 15px;
        }

        .btn-secondary {
          border: 1px solid $btn-bg-secondary-color;

          &:hover {
            border: 1px solid $btn-secondary-hover-background;
          }
        }

        .btn-primary {
          border: 1px solid $btn-bg-primary-color;
        }

        .f-transform-up {
            text-transform: uppercase;
        }
      }
    }
  }

  .device-list {
    padding: 0 16px 16px;

    @include md {
      padding: 0 30px;
    }

    .no-data {
      font-size: $fs18;
      font-weight: $fw600;
      text-align: center;
      padding: 20px;
    }
  }

  .ticket-table {
    ::ng-deep {
      .custom-grid {
        .ag-cell {
          line-height: 24px;
          padding: 15px;

          span {
            p {
              margin: 0;
            }
          }
        }

        .value-tag {
          .ag-cell-value {
            @include value-tag;
          }
        }

        .red-tag {
          span {
            color: $text-color-white;
            background-color: $bg-color-11;
            font-weight: $fw600;
          }
        }

        .yellow-tag {
          span {
            background-color: $bg-color-12;
            font-weight: $fw600;
          }
        }
      }
    }
  }

  app-ag-grid-custom {
    ::ng-deep .ag-header-cell:last-child {
      .ag-header-cell-label {
        justify-content: start;
      }
    }
  }
}
