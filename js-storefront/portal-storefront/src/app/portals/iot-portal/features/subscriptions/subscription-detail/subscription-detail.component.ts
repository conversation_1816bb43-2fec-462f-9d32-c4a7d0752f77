import { CommonModule, DatePipe } from '@angular/common';
import { Component, inject, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ActivatedRoute, Router } from '@angular/router';
import { filter, Subscription } from 'rxjs';
import { ColDef } from 'ag-grid-community';
import { CellSubActionComponent } from '../cell-sub-action/cell-sub-action.component';
import { MatDialog } from '@angular/material/dialog';
import { PackageFeaturesComponent } from '../../items-detail/subscription-widget/package-features/package-features.component';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ModalActionLoanComponent } from '../../b2b-cal-vehicles/modal-action-loan/modal-action-loan.component';
import { IconModule } from '../../../../../core/icon/icon.module';
import { AgGridCustomComponent, BreadcrumbWithLabelComponent } from '../../../../../core/shared';
import { LoadingService, NotificationService, UserService } from '../../../../../core/services';
import { SubscriptionService } from '../../../services/subscription';
import { DEFAULT_COL_DEF, PERMISSIONS_CODE } from '../../../../../core/constants';
import { VehicleType } from '../../../enums/subscription.enum';
import { SubscriptionDetail } from '../../../interfaces/subscription.interface';
import { ActionModal } from '../../../../../core/enums';
import { ItemSubscription } from '../../../interfaces/widgets.interface';

@Component({
  selector: 'app-subscription-detail',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    IconModule,
    BreadcrumbWithLabelComponent,
    AgGridCustomComponent,
  ],
  templateUrl: './subscription-detail.component.html',
  styleUrl: './subscription-detail.component.scss',
  providers: [NotificationService, SubscriptionService, DatePipe],
})
export class SubscriptionDetailComponent implements OnInit, OnDestroy {
  route = inject(ActivatedRoute);
  loadingService = inject(LoadingService);
  notificationService = inject(NotificationService);
  translateService = inject(TranslateService);
  router = inject(Router);
  userService = inject(UserService);
  subService = inject(SubscriptionService);

  dialog = inject(MatDialog);

  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;
  readonly vehicleType = VehicleType;
  readonly ACTIVE_LOAN_STATUS = 'ACTIVE';

  subscription = new Subscription();

  vin: string;
  subscriptionDetail: SubscriptionDetail;

  rowData: ItemSubscription[];

  defaultColDef = {
    ...DEFAULT_COL_DEF,
    valueFormatter: (params) => (params.value ? params.value : '-'),
  };

  colDefs: ColDef<any>[] = [
    {
      headerName: this.translateService.instant('subscriptionTab.package'),
      headerValueGetter: () =>
        this.translateService.instant('subscriptionTab.package'),
      flex: 1,
      wrapText: true,
      autoHeight: true,
      cellClass: 'cell-word-wrap can-click',
      cellRenderer: (params) => {
        return `<p>${
          params?.data?.packageType ? params?.data?.packageType : '-'
        }</p><p>${
          params?.data?.isFreeSubscription
            ? this.translateService.instant('vehicle.subWidget.freePackage')
            : ''
        }</p>`;
      },
      onCellClicked: (event) => this.viewSubscriptionDetail(event?.data),
    },
    {
      headerName: this.translateService.instant('subscriptionTab.startDate'),
      headerValueGetter: () =>
        this.translateService.instant('subscriptionTab.startDate'),
      field: 'startDate',
      wrapText: true,
      autoHeight: true,
      cellClass: 'cell-word-wrap',
      flex: 1,
    },
    {
      headerName: this.translateService.instant('subscriptionTab.endDate'),
      headerValueGetter: () =>
        this.translateService.instant('subscriptionTab.endDate'),
      field: 'endDate',
      wrapText: true,
      autoHeight: true,
      cellClass: 'cell-word-wrap',
      flex: 1,
    },
    {
      headerName: this.translateService.instant('vehicle.subWidget.type'),
      headerValueGetter: () =>
        this.translateService.instant('vehicle.subWidget.type'),
      field: 'type',
      cellRenderer: (params) => {
        return params?.value?.name || '-';
      },
      flex: 1,
      wrapText: true,
      autoHeight: true,
    },
    {
      headerName: this.translateService.instant(
        'subscriptionTab.paymentStatus'
      ),
      headerValueGetter: () =>
        this.translateService.instant('subscriptionTab.paymentStatus'),
      field: 'subscriptionPaymentStatus',
      cellRenderer: (params) => {
        return params?.value ? `<span>${params?.value?.name}</span>` : '';
      },
      cellClassRules: {
        'value-tag': (params) => !!params.value,
      },
      flex: 1,
      wrapText: true,
      autoHeight: true,
    },
    {
      headerName: this.translateService.instant('subscriptionTab.paidBy'),
      headerValueGetter: () =>
        this.translateService.instant('subscriptionTab.paidBy'),
      field: 'paidBy',
      cellRenderer: (params) => {
        return params?.value || '-';
      },
      flex: 1,
      wrapText: true,
      autoHeight: true,
    },
    {
      headerName: this.translateService.instant('vehicle.subWidget.status'),
      headerValueGetter: () =>
        this.translateService.instant('vehicle.subWidget.status'),
      field: 'subscriptionStatus',
      cellRenderer: (params) => {
        return params?.value ? `<span>${params?.value?.name}</span>` : '';
      },
      cellClassRules: {
        'value-tag': (params) => !!params.value,
      },
      flex: 1,
      wrapText: true,
      autoHeight: true,
    },
    {
      headerName: '',
      field: 'actions',
      flex: 1,
      cellRenderer: CellSubActionComponent,
      cellRendererParams: {
        onClick: (type: string, data: any) => this.confirmPayment(),
      },
      cellClass: 'action-last-grid',
      sortable: false,
      minWidth: 160,
    },
  ];

  loanActionForm: FormGroup = new FormGroup({
    id: new FormControl(''),
    vin: new FormControl({ value: '', disabled: true }, Validators.required),
    loanId: new FormControl('', Validators.required),
    status: new FormControl(this.ACTIVE_LOAN_STATUS, Validators.required),
    vehicleType: new FormControl(''),
    startDate: new FormControl('', Validators.required),
    endDate: new FormControl('', Validators.required),
    closed: new FormControl(''),
  });

  ngOnInit(): void {
    this.route.paramMap.subscribe((params) => {
      this.vin = params.get('id');
      this.getSubcriptionDetail();
    });
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }

  getSubcriptionDetail(): void {
    this.loadingService.showLoader();
    this.subscription.add(
      this.subService.getSubcriptionDetail(this.vin).subscribe(
        (response) => {
          this.loadingService.hideLoader();

          this.subscriptionDetail = response;
          this.rowData = response?.subscriptions || ([] as any);
        },
        (error) => {
          this.loadingService.hideLoader();
        }
      )
    );
  }

  confirmPayment() {
    this.subService.openConfirmSubscriptionPayment(this.vin, () =>
      this.getSubcriptionDetail()
    );
  }

  viewSubscriptionDetail(data: ItemSubscription): void {
    const dialogRef = this.dialog.open(PackageFeaturesComponent, {
      width: '650px',
      maxHeight: '90vh',
      data: {
        features: data?.features?.filter((item) => item?.enable),
      },
    });

    dialogRef.afterClosed().subscribe();
  }

  loanAction(isEdit: boolean): void {
    const data = this.subscriptionDetail?.loan;
    if (!isEdit) {
      this.loanActionForm.patchValue({
        status: this.ACTIVE_LOAN_STATUS,
        vin: this.vin,
      });
    } else {
      this.loanActionForm.patchValue({
        vin: this.vin,
        status: data?.status?.code,
        loanId: data?.id,
        endDate: new Date(data?.endDate),
        startDate: new Date(data?.startDate),
      });
    }
    const dialogRef = this.dialog.open(ModalActionLoanComponent, {
      width: '680px',
      maxHeight: '90vh',
      disableClose: true,
      autoFocus: false,
      data: {
        isEdit,
        vin: this.vin,
        mainForm: this.loanActionForm,
      },
    });

    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe(() => {
        this.getSubcriptionDetail();
      });
  }

  activateSubscription(): void {
    this.subService.openActivateSubscription(
      this.vin,
      this.subscriptionDetail?.isValidForSubscriptionEnable,
      () => {
        this.getSubcriptionDetail();
      }
    );
  }
}
