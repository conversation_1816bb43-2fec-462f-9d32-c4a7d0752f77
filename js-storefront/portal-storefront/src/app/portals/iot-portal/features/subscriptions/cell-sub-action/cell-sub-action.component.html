<div class="action-buttons-cell-renderer" [class.multiple-actions]="isShowConfirmPayment && isActivation && userService.isHasPermission([
  PERMISSIONS_CODE.CALLCENTERGROUP,
  PERMISSIONS_CODE.CALLCENTERMA<PERSON>GER<PERSON>OUP,
  PERMISSIONS_CODE.CACDGROUP,
  PERMISSIONS_CODE.DOSDHELPDESKGROUP
]) && userService.isHasPermission([
  PERMISSIONS_CODE.CALLCENTERGROUP,
  PERMISSIONS_CODE.CALLCENTERMANAGERGROUP,
  PERMISSIONS_CODE.CACDGROUP,
  PERMISSIONS_CODE.DOSDHEL<PERSON>ESKGROUP,
  PER<PERSON><PERSON><PERSON><PERSON>_CODE.TMSPHGROUP,
  PERMISSIONS_CODE.TMSPHMANAGERGROUP,
])">
  <button
    class="edit-button"
    (click)="confirmPayment()"
    *ngIf="
      isShowConfirmPayment &&
      userService.isHasPermission([
        PERMISSIONS_CODE.CALLCENTER<PERSON>OUP,
        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_CODE.CALLCENTER<PERSON><PERSON><PERSON><PERSON><PERSON>OUP,
        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_CODE.CACD<PERSON>OUP,
        PERMISSIONS_CODE.DOSDHELPDESKGROUP
      ])
    "
  >
    <mat-icon svgIcon="ic-confirm-payment" class="small-icon"></mat-icon>
    {{ "manageSub.confirmPayment" | translate }}
  </button>
  <button
    class="edit-button"
    (click)="activateSubscription()"
    *ngIf="isActivation &&
     userService.isHasPermission([
        PERMISSIONS_CODE.CALLCENTERGROUP,
        PERMISSIONS_CODE.CALLCENTERMANAGERGROUP,
        PERMISSIONS_CODE.CACDGROUP,
        PERMISSIONS_CODE.DOSDHELPDESKGROUP,
        PERMISSIONS_CODE.TMSPHGROUP,
        PERMISSIONS_CODE.TMSPHMANAGERGROUP,
      ])"
  >
    <mat-icon svgIcon="ic-activation" class="small-icon"></mat-icon>
    {{ "manageSub.activateSubscription" | translate }}
  </button>
</div>
