<app-ag-grid-custom
  [rowData]="rowData"
  [colDefs]="colDefs"
  [defaultColDef]="defaultColDef"
  [templates]="customTemplates"
  [isPaging]="true"
  [importInfo]="importInfo"
  [pagingInfo]="pagingInfo"
  [isLoadingLastImport]="isLoadingLastImport"
  (onPageChange)="onPageChange.emit($event)"
  [isShowActionExport]="false"
  (changeItemPerPage)="changeItemPerPage.emit($event)"
  (downloadFile)="downloadFile.emit()"
>
</app-ag-grid-custom>

<ng-template #actionTemplate let-value="value">
  <span class="action-grid" *ngIf="value as action">
    <mat-icon [svgIcon]="action.icon"></mat-icon>
    <span>{{ action.text }}</span>
  </span>
</ng-template>

<app-import-result-summary
  *ngIf="userService.isHasPermission([PERMISSIONS_CODE.IOT_SUBSCRIPTION_PAYMENT_CONFIRM])"
  [importDate]="importInfo?.date"
  [resultMessage]="importInfo?.resultMessage"
  [fileName]="
    importInfo?.failedRecords?.realFileName ||
    importInfo?.importFile?.realFileName
  "
  [downloadUrl]="
    importInfo?.failedRecords?.downloadUrl ||
    importInfo?.importFile?.downloadUrl
  "
  [isLoading]="isLoadingLastImport"
  [title]="'lastImportResult.lastImportSubResult'"
  (downloadFile)="downloadFile.emit()"
></app-import-result-summary>
