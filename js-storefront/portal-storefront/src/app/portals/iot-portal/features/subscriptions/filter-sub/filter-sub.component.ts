import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';

import { MatIconModule } from '@angular/material/icon';

import { TranslateModule } from '@ngx-translate/core';
import { DateFormGroupComponent, DropdownFormGroupComponent, FormGroupComponent } from '../../../../../core/shared';
import { ScreenSizeService } from '../../../../../core/services';
import { OptionDropdown } from '../../../../../core/interfaces';

@Component({
  selector: 'app-filter-sub',
  templateUrl: './filter-sub.component.html',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatIconModule,
    TranslateModule,
    FormGroupComponent,
    DropdownFormGroupComponent,
    DateFormGroupComponent
  ],
  providers: [
    ScreenSizeService
  ],
  standalone: true,
  styleUrl: './filter-sub.component.scss'
})
export class FilterSubComponent {
  @Input() form: FormGroup;
  @Input() vehicleType: OptionDropdown[];
  @Input() paymentStatus: OptionDropdown[];
  @Input() subStatus: OptionDropdown[];
  @Input() filterEndDate: OptionDropdown[];
  @Input() approvals: OptionDropdown[];
 

  @Input() isAdvancedFilter = false;
  @Input() suggestionsAssignee: OptionDropdown[] = [];

  @Output() search = new EventEmitter();
  @Output() changeAdvancedFilter = new EventEmitter();

  isCustomEndDate: boolean = false;

  public get vin(): FormControl {
    return this.form.get('vin') as FormControl;
  }

  public get vehicleTypeForm(): FormControl {
    return this.form.get('vehicleType') as FormControl;
  }

  public get subscriptionStatus(): FormControl {
    return this.form.get('subscriptionStatus') as FormControl;
  }

  public get paymentStatusForm(): FormControl {
    return this.form.get('paymentStatus') as FormControl;
  }

  public get subscriptionEndDate(): FormControl {
    return this.form.get('subscriptionEndDate') as FormControl;
  }

  public get startDate(): FormControl {
    return this.form.get('startDate') as FormControl;
  }

  public get endDate(): FormControl {
    return this.form.get('endDate') as FormControl;
  }

  onChangeSubEndData(e: any) {
    this.isCustomEndDate = e.value === "CUSTOM";
    if (this.isCustomEndDate) {
      this.form?.controls['startDate']?.addValidators(Validators.required);
      this.form?.controls['endDate']?.addValidators(Validators.required);
      this.form?.controls['startDate']?.updateValueAndValidity();
      this.form?.controls['endDate']?.updateValueAndValidity();
    } else {
      this.form?.controls['startDate']?.clearValidators();
      this.form?.controls['endDate']?.clearValidators();
      this.form?.controls['startDate']?.updateValueAndValidity();
      this.form?.controls['endDate']?.updateValueAndValidity();
    }
  }
}
