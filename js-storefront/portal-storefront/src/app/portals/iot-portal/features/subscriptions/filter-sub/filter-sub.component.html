<form [formGroup]="form">
  <div class="filter-sub">
    <div
      class="filter-sub__properties"
      [class.has-advanced-filter]="isAdvancedFilter"
    >
      <app-form-group
        [label]="'manageSub.filter.vin' | translate"
        [control]="vin"
        [placeholder]="'enterHereToSearch' | translate"
      ></app-form-group>

      <app-dropdown-form-group
        [label]="'manageSub.filter.vehicleType' | translate"
        [control]="vehicleTypeForm"
        [options]="vehicleType"
        (enter)="search.emit()"
      ></app-dropdown-form-group>

      <app-dropdown-form-group
      [label]="'manageSub.filter.subscriptionStatus' | translate"
      [control]="subscriptionStatus"
      [options]="subStatus"
      (enter)="search.emit()"
      ></app-dropdown-form-group>

      <app-dropdown-form-group
      [label]="'manageSub.filter.paymentStatus' | translate"
      [control]="paymentStatusForm"
      [options]="paymentStatus"
      (enter)="search.emit()"
      ></app-dropdown-form-group>

      @if (isAdvancedFilter) {
      <app-dropdown-form-group
        [label]="'manageSub.filter.subscriptionEndDate' | translate"
        [control]="subscriptionEndDate"
        [options]="filterEndDate"
        (changeOption)="onChangeSubEndData($event)"
        (enter)="search.emit()"
      ></app-dropdown-form-group>
        @if(isCustomEndDate) {
        <app-date-form-group
          [label]="'manageLoan.startDate' | translate"
          [control]="startDate"
          controlId="startDate"
          [required]="true"
          [maxDate]="endDate?.value"
          [errorMessage]="
            'common.requiredField'
              | translate : { field: ('manageLoan.startDate' | translate) }
          "
        ></app-date-form-group>
        <app-date-form-group
          [label]="'manageLoan.endDate' | translate"
          [control]="endDate"
          controlId="endDate"
          [required]="true"
          [minDate]="startDate?.value"
          [errorMessage]="
            'common.requiredField'
              | translate : { field: ('manageLoan.endDate' | translate) }
          "
        ></app-date-form-group>
        }
      }
    </div>
    <div class="filter-sub__search">
      <button type="submit" class="btn-tertiary search" (click)="search.emit()" [disabled]="form.invalid">
        {{ "common.search" | translate }}
      </button>
    </div>
  </div>

  <div class="advanced-title" (click)="changeAdvancedFilter.emit()">
    <mat-icon svgIcon="ic-adjustment-red" class="small-icon"></mat-icon>
    {{
      (isAdvancedFilter
        ? "manageSub.filter.lessFilter"
        : "manageSub.filter.advancedFilter"
      ) | translate
    }}
  </div>
</form>
