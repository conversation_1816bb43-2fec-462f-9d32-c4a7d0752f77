<div class="title">
  <div class="title__left">
    <app-breadcrumb-with-label
      [breadcrumbs]="[
        'subscriptionsDetail.subscriptions',
        'subscriptionsDetail.vehicleDetails'
      ]"
      [breadcrumbLinks]="['/subscription', '/subscription/' + (vin || '')]"
      [label]="
        'subscriptionsDetail.vinId'
          | translate
            : {
                vin: subscriptionDetail?.vehicle?.vin
              }
      "
    >
    </app-breadcrumb-with-label>
  </div>
</div>

<div class="subscription-detail">
  <div class="subscription-detail__container">
    <div class="subscription-detail__box">
      <div class="section-header">
        <mat-icon
          svgIcon="ic-car-1"
          class="medium-icon section-header--icon"
        ></mat-icon>
        <div class="section-header--content">
          <span>{{ "subscriptionsDetail.vehicleDetails" | translate }}</span>
        </div>
      </div>

      <div class="subscription-detail__content-detail">
        <div class="subscription-detail__content-detail__item">
          <div>{{ "subscriptionsDetail.vin" | translate }}</div>
          <div>{{ subscriptionDetail?.vehicle?.vin || "-" }}</div>
        </div>

        <div class="subscription-detail__content-detail__item">
          <div>{{ "subscriptionsDetail.model" | translate }}</div>
          <div>{{ subscriptionDetail?.vehicle?.vehicleModel || "-" }}</div>
        </div>

        <div class="subscription-detail__content-detail__item">
          <div>{{ "subscriptionsDetail.vehicleType" | translate }}</div>
          <div>{{ subscriptionDetail?.vehicle?.vehicleType || "-" }}</div>
        </div>

        <div class="subscription-detail__content-detail__item">
          <div>{{ "subscriptionsDetail.plate" | translate }}</div>
          <div>{{ subscriptionDetail?.vehicle?.plateNumber || "-" }}</div>
        </div>

        <div class="subscription-detail__content-detail__item">
          <div>{{ "subscriptionsDetail.deviceType" | translate }}</div>
          <div>{{ subscriptionDetail?.vehicle?.deviceType || "-" }}</div>
        </div>
      </div>
    </div>

    @if(!(!subscriptionDetail?.loan?.id &&
    subscriptionDetail?.vehicle?.vehicleType === 'B2C')) {
    <div class="subscription-detail__box">
      <div class="section-header">
        <mat-icon
          svgIcon="ic-car-percent"
          class="medium-icon section-header--icon"
        ></mat-icon>
        <div class="section-header--content">
          <span>{{ "subscriptionsDetail.loan.title" | translate }}</span>
        </div>
      </div>

      <div class="subscription-detail__content-detail">
        <div class="subscription-detail__content-detail__item">
          <div>{{ "subscriptionsDetail.loan.status" | translate }}</div>
          <div class="loan-action" [class.no-value]="subscriptionDetail?.loan?.status?.name">
            {{ subscriptionDetail?.loan?.status?.name || "-" }}
            <div
              *ngIf="
                userService.isHasPermission([
                  PERMISSIONS_CODE.IOT_LOAN_MANAGE
                ]) &&
                (!subscriptionDetail?.loan?.status?.code ||
                  subscriptionDetail?.loan?.status?.code === ACTIVE_LOAN_STATUS)
              "
            >
              @if(subscriptionDetail?.vehicle?.vehicleType ===
              vehicleType.B2BCAL && !subscriptionDetail?.loan?.id) {
              <button class="edit-button" (click)="loanAction(false)">
                <mat-icon svgIcon="ic-add-red" class="small-icon"></mat-icon>
                {{ "common.addLoan" | translate }}
              </button>
              } @else if(subscriptionDetail?.loan?.id) {
              <button class="edit-button" (click)="loanAction(true)">
                <mat-icon svgIcon="ic-edit" class="small-icon"></mat-icon>
                {{ "common.editLoan" | translate }}
              </button>
              }
            </div>
          </div>
        </div>

        <div class="subscription-detail__content-detail__item">
          <div>{{ "subscriptionsDetail.loan.id" | translate }}</div>
          <div>{{ subscriptionDetail?.loan?.id || "-" }}</div>
        </div>

        <div class="subscription-detail__content-detail__item">
          <div>{{ "subscriptionsDetail.loan.startDate" | translate }}</div>
          <div>{{ subscriptionDetail?.loan?.startDate || "-" }}</div>
        </div>

        <div class="subscription-detail__content-detail__item">
          <div>{{ "subscriptionsDetail.loan.endDate" | translate }}</div>
          <div>{{ subscriptionDetail?.loan?.endDate || "-" }}</div>
        </div>
      </div>
    </div>
    }
  </div>

  <div class="subscription-detail__box">
    <div class="section-header subscription-section">
      <div class="subscription-section__left-title">
        <mat-icon
          svgIcon="ic-sub-widget"
          class="medium-icon section-header--icon"
        ></mat-icon>
        <div class="section-header--content">
          <span>{{ "subscriptionsDetail.subscriptions" | translate }}</span>
        </div>
      </div>
      <button
        *ngIf="
          subscriptionDetail?.vehicle?.vehicleType === vehicleType.B2BCAL &&
          rowData?.length <= 0 &&
          subscriptionDetail?.loan?.status?.code === ACTIVE_LOAN_STATUS &&
          userService.isHasPermission([
            PERMISSIONS_CODE.CALLCENTERGROUP,
            PERMISSIONS_CODE.CALLCENTERMANAGERGROUP,
            PERMISSIONS_CODE.CACDGROUP,
            PERMISSIONS_CODE.DOSDHELPDESKGROUP,
            PERMISSIONS_CODE.TMSPHGROUP,
            PERMISSIONS_CODE.TMSPHMANAGERGROUP
          ])
        "
        class="edit-button"
        (click)="activateSubscription()"
      >
        <mat-icon svgIcon="ic-activation" class="small-icon"></mat-icon>
        {{ "manageSub.activateSubscription" | translate }}
      </button>
    </div>

    <app-ag-grid-custom
      *ngIf="rowData?.length > 0"
      class="ticket-table"
      [rowData]="rowData"
      [colDefs]="colDefs"
      [defaultColDef]="defaultColDef"
      [isShowActionExport]="false"
      [onlyTable]="true"
    >
    </app-ag-grid-custom>
  </div>
</div>
