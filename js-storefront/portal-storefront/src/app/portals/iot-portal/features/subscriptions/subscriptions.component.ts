import { CommonModule, DatePipe } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { FilterSubComponent } from './filter-sub/filter-sub.component';
import { MatDialog } from '@angular/material/dialog';
import { Subscription, catchError, filter, finalize, tap, throwError } from 'rxjs';

import { Router } from '@angular/router';
import { CellSubActionComponent } from './cell-sub-action/cell-sub-action.component';
import { ImportFileComponent } from './import-file/import-file.component';
import { SubscriptionsTableomponent } from './subscriptions-table/subscriptions-table.component';
import { AgGridCustomComponent, RouterLinkCellRendererComponent, WidgetSummaryComponent } from '../../../../core/shared';
import { LoadingComponent } from '../../../../layout/global/loading/loading.component';
import { LoadingService, NotificationService, UserService } from '../../../../core/services';
import { SubscriptionService } from '../../services/subscription';
import { PERMISSIONS_CODE } from '../../../../core/constants';
import { ItemWidget, LastImportResult, OptionDropdown, PagingInfo } from '../../../../core/interfaces';
import { StatusPayment, StatusSubscription, SubscriptionEndDate, SubscriptionSummaryTab, SubscriptionType, VehicleType } from '../../enums/subscription.enum';
import { ActionModal, DateFormat } from '../../../../core/enums';
import { SUB_WIDGETS } from '../../constants/subscription.const';
import { ItemSubscription } from '../../interfaces/widgets.interface';

@Component({
  selector: 'app-subscriptions',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    MatIconModule,
    WidgetSummaryComponent,
    LoadingComponent,
    FilterSubComponent,
    AgGridCustomComponent,
    SubscriptionsTableomponent,
  ],
  providers: [
    LoadingService,
    NotificationService,
    SubscriptionService,
    DatePipe,
  ],
  templateUrl: './subscriptions.component.html',
  styleUrls: ['./subscriptions.component.scss'],
})
export class SubscriptionsComponent implements OnInit {
  loadingService = inject(LoadingService);
  translateService = inject(TranslateService);
  subService = inject(SubscriptionService);
  notificationService = inject(NotificationService);
  router = inject(Router);
  userService = inject(UserService);
  datePipe = inject(DatePipe);
  dialog = inject(MatDialog);
  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;
  subscription = new Subscription();

  selectedSummaryTab: any;
  pagingInfo: PagingInfo = {
    totalItems: 0,
    currentPage: 0,
    numberOfPage: 10,
  };
  importInfo: LastImportResult;

  filterSubscriptions = new FormGroup({
    vin: new FormControl(''),
    vehicleType: new FormControl(''),
    subscriptionStatus: new FormControl(''),
    paymentStatus: new FormControl(''),
    subscriptionEndDate: new FormControl(''),
    loanStatus: new FormControl(''),
    startDate: new FormControl(''),
    endDate: new FormControl(''),
  });

  widgets: ItemWidget[] = [
    {
      id: '' as SubscriptionSummaryTab,
      count: 0,
      description: '',
      icon: '',
    },
  ];

  isAdvancedFilter = true;
  isLoadingLastImport = false;

  filterPaymentStatus: OptionDropdown[];
  filterSubStatus: OptionDropdown[];
  filterVehicleType: OptionDropdown[];
  filterEndDate: OptionDropdown[];
  rowData: ItemSubscription[];

  colDefs = [
    {
      headerName: this.translateService.instant('manageSub.table.vin'),
      headerValueGetter: () =>
        this.translateService.instant('manageSub.table.vin'),
      field: 'vin',
      wrapText: true,
      autoHeight: true,
      flex: 1,
      cellClass: 'cell-word-wrap',
      cellRenderer: RouterLinkCellRendererComponent,
      cellRendererParams: {
        linkBuilder: (data: any) => this.userService.isHasPermission([
          PERMISSIONS_CODE.IOT_SUBSCRIPTION_LIST_VIEW,
        ]) ? `subscription/${data?.vin}` : '',
      }
    },
    {
      headerName: this.translateService.instant('manageSub.table.vehicleType'),
      headerValueGetter: () =>
        this.translateService.instant('manageSub.table.vehicleType'),
      field: 'vehicleType',
      flex: 1,
      wrapText: true,
      autoHeight: true,
      cellClass: 'cell-word-wrap',
    },
    {
      headerName: this.translateService.instant(
        'manageSub.table.subscriptionStatus'
      ),
      headerValueGetter: () =>
        this.translateService.instant('manageSub.table.subscriptionStatus'),
      field: 'subscriptionStatus',
      flex: 1,
      cellRenderer: (params) => {
        return params?.value ? `<span>${params?.value?.name}</span>` : '-';
      },
      cellClassRules: {
        'value-tag': (params) => !!params.value,
      },
      wrapText: true,
      autoHeight: true,
    },
    {
      headerName: this.translateService.instant(
        'manageSub.table.subscriptionPaymentStatus'
      ),
      headerValueGetter: () =>
        this.translateService.instant(
          'manageSub.table.subscriptionPaymentStatus'
        ),
      field: 'paymentStatus',
      flex: 1,
      cellRenderer: (params) => {
        return params?.value ? `<span>${params?.value?.name}</span>` : '-';
      },
      cellClassRules: {
        'value-tag': (params) => !!params.value,
      },
      wrapText: true,
      autoHeight: true,
    },
    {
      headerName: this.translateService.instant(
        'manageSub.table.subscriptionEndDate'
      ),
      headerValueGetter: () =>
        this.translateService.instant('manageSub.table.subscriptionEndDate'),
      field: 'endDateFormat',
      flex: 1,
      cellRenderer: (params) => {
        return params?.value
          ? `<span>${this.datePipe.transform(
              params?.value,
              DateFormat.ShortDate
            )}</span>`
          : '-';
      },
    },
    {
      headerName: this.translateService.instant('manageLoan.actions'),
      headerValueGetter: () =>
        this.translateService.instant('manageLoan.actions'),
      field: 'actions',
      flex: 1,
      cellRenderer: CellSubActionComponent,
      cellRendererParams: {
        onClick: (type: string, data: any) => this.modalAction(type, data),
      },
      cellClass: 'action-last-grid',
      sortable: false,
    },
  ];

  defaultColDef = {
    resizable: false,
    valueFormatter: (params) => (params.value ? params.value : '-'),
    sortable: false,
    menuTabs: [],
    wrapHeaderText: true,
    autoHeaderHeight: true,
  };
  currentUser: OptionDropdown;
  subscriptionType = SubscriptionType;

  ngOnInit(): void {
    this.filterSubscriptions.patchValue({
      vin: '',
      vehicleType: VehicleType.All,
      subscriptionStatus: StatusSubscription.All,
      paymentStatus: StatusPayment.All,
      subscriptionEndDate: SubscriptionEndDate.All,
    });
    this.getAllFilter();
    this.getWidgets();
    this.getSubList();

    if (
      this.userService.isHasPermission([
        PERMISSIONS_CODE.IOT_SUBSCRIPTION_PAYMENT_CONFIRM,
      ])
    ) {
      this.getLastImportResult();
    }
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  getUserInfo(): void {
    this.userService.userRoles$.subscribe((value) => {
      this.currentUser = {
        code: value?.username,
        name: value?.fullName,
      };
    });
  }

  changeSummaryTab(id: any) {
    this.selectedSummaryTab = id;
    this.pagingInfo.currentPage = 0;
    this.isAdvancedFilter = true;

    this.mapWidgetFilter(id);
    this.getSubList(
      id === SubscriptionSummaryTab.requiringSubscriptionActivation
    );
  }

  getWidgets(): void {
    this.loadingService.showLoader();
    this.subscription.add(
      this.subService.getWidgetList().subscribe((response) => {
        this.loadingService.hideLoader();
        const { widgets } = response;

        this.widgets = widgets.map((item) => {
          const { description, icon } =
            SUB_WIDGETS?.find((widget) => widget.id === item.type) || {};
          return {
            id: item?.type,
            count: item?.count,
            description,
            icon,
          };
        });
      })
    );
  }

  changeAdvancedFilter(): void {
    this.isAdvancedFilter = !this.isAdvancedFilter;
  }

  getAllFilter(): void {
    setTimeout(() => {
      this.filterEndDate = this.subService.getEndDateOpt();
    }, 500);

    this.loadingService.showLoader();
    this.subService.getMetadataFilter().subscribe((response) => {
      this.loadingService.hideLoader();
      this.filterSubStatus = response?.subStatus;
      this.filterPaymentStatus = response?.paymentStatus;
      this.filterVehicleType = response?.vehicleType;
    });
  }

  mapWidgetFilter(id: SubscriptionSummaryTab) {
    switch (id) {
      case SubscriptionSummaryTab.requiringSubscriptionActivation: {
        // Status = Open, Priority = High
        this.filterSubscriptions.patchValue({
          vin: '',
          vehicleType: VehicleType.B2BCAL,
          loanStatus: 'ACTIVE',
          subscriptionStatus: StatusSubscription.All,
          paymentStatus: StatusPayment.All,
          subscriptionEndDate: SubscriptionEndDate.All,
        });
        break;
      }
      case SubscriptionSummaryTab.subscriptionExpiringSoon: {
        // Assignee = Unassigned
        this.filterSubscriptions.patchValue({
          vin: '',
          vehicleType: VehicleType.All,
          subscriptionStatus: StatusSubscription.All,
          paymentStatus: StatusPayment.All,
          subscriptionEndDate: SubscriptionEndDate.thisMonth,
        });
        break;
      }
      case SubscriptionSummaryTab.pendingSubscriptionPaymentConfirmation: {
        //Assignee = Current logged in user, Status = Open
        this.filterSubscriptions.patchValue({
          vin: '',
          vehicleType: VehicleType.All,
          subscriptionStatus: StatusSubscription.All,
          paymentStatus: StatusPayment.pendingConfirmation,
          subscriptionEndDate: SubscriptionEndDate.All,
        });
        break;
      }
    }
  }

  getSubList(isNoSubscription: boolean = false): void {
    const params: any = {
      currentPage: this.pagingInfo?.currentPage,
      pageSize: this.pagingInfo?.numberOfPage,
      vin: this.filterSubscriptions.value.vin,
      vehicleType: this.filterSubscriptions.value.vehicleType,
      subStatus: this.filterSubscriptions.value.subscriptionStatus,
      paymentStatus: this.filterSubscriptions.value.paymentStatus,
      endDateOpt: this.filterSubscriptions.value.subscriptionEndDate,
      loanStatus: this.filterSubscriptions.value.loanStatus,
      isNoSubscription,
    };
    if (this.filterSubscriptions.value.subscriptionEndDate === 'CUSTOM') {
      params.fromEndDate = this.datePipe.transform(
        this.filterSubscriptions.value.startDate,
        DateFormat.ShortDate
      );
      params.toEndDate = this.datePipe.transform(
        this.filterSubscriptions.value.endDate,
        DateFormat.ShortDate
      );
    }

    this.loadingService.showLoader();
    this.subService.getListSub(params).subscribe({
      next: (response: any) => {
        this.rowData = response?.items;

        this.pagingInfo.totalItems = response?.pagination?.totalResults;

        this.loadingService.hideLoader();
      },
      error: () => {
        this.loadingService.hideLoader();
      },
    });
    this.filterSubscriptions.patchValue({ loanStatus: '' });
  }

  onPageChange(value: number): void {
    this.pagingInfo.currentPage = value;
    this.getSubList();
  }

  changeItemPerPage(item) {
    this.pagingInfo.numberOfPage = item;
    this.pagingInfo.currentPage = 0;
    this.getSubList();
  }

  searchSub(): void {
    if (this.filterSubscriptions.valid) {
      this.pagingInfo.currentPage = 0;
      this.getSubList();
    }
  }

  modalAction(type: string, data?: any): void {
    switch (type) {
      case SubscriptionType.ACTIVATE_SUBSCRIPTION:
        this.subService.openActivateSubscription(data.vin, data?.isValidForSubscriptionEnable, () =>
          this.refreshData()
        );
        break;
      case SubscriptionType.CONFIRM_PAYMENT:
        this.subService.openConfirmSubscriptionPayment(data.vin, () =>
          this.refreshData()
        );
        break;
      case SubscriptionType.BULK_PAYMENT:
        this.openConfirmPayment(() => this.refreshDataImport);
        break;
      default:
        console.warn(`Unhandled action type: ${type}`);
    }
  }

  openConfirmPayment(onSuccess: () => void): void {
    const dialogRef = this.dialog.open(ImportFileComponent, {
      width: '530px',
      data: {
        title: this.translateService.instant(
          'manageSub.subscriptionAction.paymentConfirm'
        ),
        confirmMsg: this.translateService.instant(
          'manageSub.subscriptionAction.confirmPaymentMsg'
        ),
      },
    });

    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((result) => {
        this.loadingService.showLoader();
        this.subService
          .importConfirmPayment(result?.data)
          .pipe(
            tap(() => {
              this.loadingService.hideLoader();
              onSuccess();
            })
          )
          .subscribe({
            next: () => {
              this.notificationService.showSuccess(
                this.translateService.instant(
                  'manageSub.subscriptionAction.importInProgress'
                )
              );
            },
            error: (error) => {
              this.loadingService.hideLoader();
              this.notificationService.showError(error);
            },
          });
      });
  }

  downloadImportResult(): void {
    const code =
      this.importInfo?.failedRecords?.code || this.importInfo?.importFile?.code;

    this.loadingService.showLoader();

    this.subService
      .downloadImportResult(code)
      .pipe(
        tap((response: Blob) => {
          this.downloadFile(
            response,
            this.importInfo?.failedRecords?.realFileNameWithoutExt ||
              this.importInfo?.importFile?.realFileNameWithoutExt
          );
        }),
        catchError((error) => {
          this.notificationService.showError('Download failed.');
          return throwError(() => error);
        }),
        finalize(() => {
          this.loadingService.hideLoader();
        })
      )
      .subscribe();
  }

  getLastImportResult(): void {
    this.subService.getLastImportResult().subscribe((response) => {
      this.isLoadingLastImport = false;
      this.importInfo = response;
    });
  }

  private downloadFile(blob: Blob, filename: string): void {
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = filename;
    link.click();
    URL.revokeObjectURL(link.href);
  }

  private refreshData(): void {
    this.getSubList();
  }

  private refreshDataImport(): void {
    this.getSubList();
    this.getLastImportResult();
  }

  viewDetail(vin: string): void {
    if (
      this.userService.isHasPermission([
        PERMISSIONS_CODE.IOT_SUBSCRIPTION_LIST_VIEW,
      ])
    ) {
      this.router.navigate(['subscription/' + vin]);
    }
  }
}
