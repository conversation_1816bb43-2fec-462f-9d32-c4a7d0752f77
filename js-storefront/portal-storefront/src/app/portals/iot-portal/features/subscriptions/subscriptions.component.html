<app-loading *ngIf="loadingService.isLoading"></app-loading>

<div class="header-section">
  <h2 class="title-page">
    {{ "manageSub.title" | translate }}
  </h2>
    <div class="device-action">
        <div class="device-action__btn-action">
            <button class="btn-primary f-transform-up" (click)="modalAction(subscriptionType.BULK_PAYMENT)">
                {{ "manageSub.subscriptionAction.bulkConfirmPayment" | translate }}
            </button>
        </div>
    </div>
</div>

<app-widget-summary
  [widgets]="widgets"
  (changeTab)="changeSummaryTab($event)"
></app-widget-summary>

<app-filter-sub
  [form]="filterSubscriptions"
  [isAdvancedFilter]="isAdvancedFilter"
  [vehicleType]="filterVehicleType"
  [paymentStatus]="filterPaymentStatus"
  [subStatus]="filterSubStatus"
  [filterEndDate]="filterEndDate"
  [approvals]="[]"
  (changeAdvancedFilter)="changeAdvancedFilter()"
  (search)="searchSub()"
></app-filter-sub>

<div class="device-list">
  @if (rowData?.length > 0 ) {
    <app-subscriptions-table
    class="ticket-table"
    [rowData]="rowData"
    [colDefs]="colDefs"
    [importInfo]="importInfo"
    [pagingInfo]="pagingInfo"
    [isLoadingLastImport]="isLoadingLastImport"
    (onPageChange)="onPageChange($event)"
    (changeItemPerPage)="changeItemPerPage($event)"
    (downloadFile)="downloadImportResult()"
  ></app-subscriptions-table>
  } @else {
  <div class="no-data">{{ "manageSub.noSubscription" | translate }}</div>
  }
</div>