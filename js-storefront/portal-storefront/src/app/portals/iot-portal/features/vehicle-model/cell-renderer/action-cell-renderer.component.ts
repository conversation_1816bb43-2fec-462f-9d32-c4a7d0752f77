import { Component, EventEmitter, inject, Input, Output } from '@angular/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';

import { TranslateModule } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { IconModule } from '../../../../../core/icon/icon.module';
import { UserService } from '../../../../../core/services/user';
import { PERMISSIONS_CODE } from '../../../../../core/constants';
import { ActionTable } from '../../../../../core/enums';


@Component({
  selector: 'app-action-cell-renderer',
  template: `
    <div class="action-buttons-cell-renderer">
      <button *ngIf="userService.isHasPermission([PERMISSIONS_CODE.IOT_MODEL_SALE_CODE_EDIT])" class="edit-button" (click)="onEdit()">
        <mat-icon svgIcon="ic-edit" class="small-icon"></mat-icon> {{'common.edit' | translate}}
      </button>
      <button *ngIf="!onlyEdit && userService.isHasPermission([PERMISSIONS_CODE.IOT_MODEL_SALE_CODE_DELETE])" class="remove-button" (click)="onRemove()">
        <mat-icon svgIcon="ic-delete" class="small-icon"></mat-icon> {{'common.remove' | translate}}
      </button>
    </div>
  `,
  standalone: true,
  imports: [
    IconModule,
    TranslateModule,
    CommonModule
  ],
})
export class ActionCellRendererComponent implements ICellRendererAngularComp {
  @Input() rowIndex!: number;

  @Output() edit = new EventEmitter<void>();
  @Output() remove = new EventEmitter<void>();

  userService = inject(UserService);

  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;

  params!: any;
  onlyEdit: boolean = false;

  agInit(params: any): void {
    this.params = params;
    this.onlyEdit = params.onlyEdit === undefined ? false : params.onlyEdit;
    this.rowIndex = params.rowIndex;
  }

  refresh(params: any): boolean {
    return true;
  }

  onEdit(): void {
    this.params.onClick(ActionTable.Edit, this.params.data);
  }

  onRemove(): void {
    this.params.onClick(ActionTable.Remove, this.params.data);
  }
}
