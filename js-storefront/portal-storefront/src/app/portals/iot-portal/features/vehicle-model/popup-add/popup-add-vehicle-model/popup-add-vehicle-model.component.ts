import { Component, inject, OnInit } from '@angular/core';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { ReactiveFormsModule } from '@angular/forms';
import { FormGroupComponent } from '../../../../../../core/shared';
import { ActionModal } from '../../../../../../core/enums';

@Component({
  selector: 'app-popup-add-vehicle-model',
  standalone: true,
  imports: [
    MatDialogModule,
    MatIconModule,
    TranslateModule,
    ReactiveFormsModule,
    FormGroupComponent
  ],
  templateUrl: './popup-add-vehicle-model.component.html',
  styleUrls: ['./popup-add-vehicle-model.component.scss'],
})
export class PopupAddVehicleModelComponent {
  data = inject(MAT_DIALOG_DATA);
  dialogRef = inject(MatDialogRef<PopupAddVehicleModelComponent>);

  onCancel() {
    this.dialogRef.close();
  }

  onConfirm() {
    this.dialogRef.close({ action: ActionModal.Submit });
  }
}
