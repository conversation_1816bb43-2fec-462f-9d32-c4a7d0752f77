<mat-icon [svgIcon]="data?.icon" class="icon-title"></mat-icon>
<h2 class="title-dialog">{{ data?.title }}</h2>

<div class="container-dialog">
  <form
    [formGroup]="data.vehicleModelForm"
  >
    <div class="add-vehicle-model-section-form-group">
      <app-form-group
        [label]="'vehicleModel.modelSalesCode' | translate"
        [control]="data.modelSalesCodeControl"
        [placeholder]="'vehicleModel.placeHolderModelSalesCode' | translate"
        controlId="modelSalesCode"
        class="vehicle-model-section__input"
      ></app-form-group>

      <app-form-group
        [label]="'vehicleModel.modelName' | translate"
        [control]="data.modelNameControl"
        [placeholder]="'vehicleModel.placeHolderModelName' | translate"
        controlId="modelName"
        class="vehicle-model-section__input"
      ></app-form-group>
    </div>
  </form>
</div>

<div class="action-dialog">
  <button class="btn-quaternary" (click)="onCancel()">
    {{ data?.cancelBtn }}
  </button>

  <button class="btn-primary btn-confirm" (click)="data?.vehicleModelForm?.invalid ? false : onConfirm()" [disabled]="data?.vehicleModelForm?.invalid">
    {{ data?.submitBtn }}
  </button>
</div>
