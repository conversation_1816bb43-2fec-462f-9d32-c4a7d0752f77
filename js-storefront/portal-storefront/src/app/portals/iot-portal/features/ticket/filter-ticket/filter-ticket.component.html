<form [formGroup]="form">
  <div class="filter-ticket">
    <div
      class="filter-ticket__properties"
      [class.has-advanced-filter]="isAdvancedFilter"
    >
      <app-form-group
        [label]="'tickets.filter.ticketIdOrTitle' | translate"
        [control]="form.controls.ticketId"
        [placeholder]="'enterHereToSearch' | translate"
        (onEnter)="search.emit()"
      ></app-form-group>

      <app-dropdown-form-group
        [label]="'tickets.filter.type' | translate"
        [control]="form.controls.type"
        [options]="typeTickets"
        (enter)="search.emit()"
      ></app-dropdown-form-group>

      <app-form-group
        *ngIf="userService.isHasPermission([PERMISSIONS_CODE.ECARE_OWNER_VIEW])"
        [label]="'tickets.filter.customerName' | translate"
        [control]="form.controls.customerName"
        [placeholder]="'enterHereToSearch' | translate"
      ></app-form-group>

      <app-autocomplete-input
        [label]="'tickets.filter.assignee' | translate"
        [placeholder]="'enterHereToSearch' | translate"
        [suggestions]="suggestionsAssignee"
        [control]="form.controls.assignee"
      >
      </app-autocomplete-input>

      @if (isAdvancedFilter) {
      <app-dropdown-form-group
        [label]="'tickets.filter.priority' | translate"
        [control]="form.controls.priority"
        [options]="priorities"
        (enter)="search.emit()"
      ></app-dropdown-form-group>

      <app-dropdown-form-group
        [label]="'tickets.filter.status' | translate"
        [control]="form.controls.status"
        [options]="status"
        (enter)="search.emit()"
      ></app-dropdown-form-group>

      <app-dropdown-form-group
        [label]="'tickets.filter.approval' | translate"
        [control]="form.controls.approval"
        [options]="approvals"
        (enter)="search.emit()"
      ></app-dropdown-form-group>
      }
    </div>
    <div class="filter-ticket__search">
      <button type="submit" class="btn-tertiary search" (click)="search.emit()">
        {{ "common.search" | translate }}
      </button>
    </div>
  </div>

  <div class="advanced-title" (click)="changeAdvancedFilter.emit()">
    <mat-icon svgIcon="ic-adjustment-red" class="small-icon"></mat-icon>
    {{
      (isAdvancedFilter
        ? "tickets.filter.lessFilter"
        : "tickets.filter.advancedFilter"
      ) | translate
    }}
  </div>
</form>
