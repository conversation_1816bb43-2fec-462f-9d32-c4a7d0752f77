@import "../../../../../../../styles/abstracts/mixins";
@import "../../../../../../../styles/abstracts/variables";
@import "../../../../../../../styles/components/ag-grid";
$height-header-table: 32px;

.call-log-table {
  .custom-table {
    margin-top: 30px;
    width: 100%;
    border-collapse: collapse;
    text-align: left;
  }

  .custom-table th,
  .custom-table td {
    padding: 12px;
  }

  .custom-table th {
    font-size: 16px;
    font-weight: 600;
  }

  .no-data-message {
    text-align: center;
    color: #101010;
    background-color: #F5F5F5;
    font-size: 14px;
  }

  .no-data-wrapper {
    width: 100%;
  }
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &__title {
    margin-bottom: 0 !important;
    font-size: 22px !important;
    font-weight: 600 !important;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  &__icon {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 44px;

    &-svg {
      height: 32px;
      width: 32px;
    }
  }

  &__content {
    display: flex;
    align-items: center;
    justify-content: space-between;

    &__title {
      font-size: 18px;
      font-weight: bold;
      margin: 0 0 4px;
      color: #000;
    }
  }

  &__add-new {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #eb0a1e;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
  }

  .custom-grid {
    width: 100%;
    padding: 15px 0;

    font-family: $text-font-stack;

    .ag-root-wrapper {
      border: none;
    }

    .ag-row:nth-child(odd) {
      background-color: $bg-color-10;

      &.ag-row-hover:not(.ag-full-width-row)::before,
      &.ag-row-hover.ag-full-width-row.ag-row-group::before {
        background-color: $bg-color-10;
      }
    }

    .ag-row:nth-child(even) {
      background-color: $white-bg;

      &.ag-row-hover:not(.ag-full-width-row)::before,
      &.ag-row-hover.ag-full-width-row.ag-row-group::before {
        background-color: $white-bg;
      }
    }

    .ag-header-container {
      background-color: $white-bg;
      font-weight: $fw600;
      font-size: $fs16;
    }

    .ag-header {
      border: none;
      height: $height-header-table !important;
      min-height: $height-header-table !important;
    }

    .ag-header-row {
      height: $height-header-table !important;
      font-weight: 600;
    }

    .ag-header-cell {
      height: $height-header-table !important;

      &:last-child {
        .ag-header-cell-label {
          justify-content: end;
        }
      }
    }

    .ag-row {
      border: none;
      line-height: 52px;

      &:hover {
        background-color: unset !important;
      }
    }

    .can-click {
      &:hover {
        cursor: pointer;
        text-decoration: underline;
      }
    }

    .action-grid {
      app-icon-cell-renderer {
        @include action-grid;
      }
    }

    .ag-cell {
      line-height: 52px;
    }

    .value-tag {
      > span {
        padding: 4px 8px;
        background-color: $bg-color-2;
        border: 1px solid rgba(16, 16, 16, 0.05);
      }
    }

    .ag-cell-focus:not(.ag-cell-range-selected):focus-within {
      border: none !important;
    }

    .cell-word-wrap {
      word-wrap: break-word;
      white-space: normal;
    }
  }
}

.action-buttons-cell-renderer {
    position: absolute;
    right: 0;
}