import { Component, inject, OnInit } from '@angular/core';
import { TicketInfoComponent } from './ticket-info/ticket-info.component';
import { CommonModule } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import {
  ServiceBookingComponent,
  StolenVehicleTrackingComponent,
} from '../../items-detail';
import { WarningCardComponent } from './warning-card/warning-card.component';
import { CurrentLocationComponent } from '../../items-detail/current-location/current-location.component';
import { VerificationComponent } from '../../items-detail/verification/verification.component';
import { WarningComponent } from '../../items-detail/warning/warning.component';
import { InsuranceCoverageComponent } from '../../items-detail/insurance-coverage/insurance-coverage.component';
import { TowingServiceComponent } from '../../items-detail/towing-service/towing-service.component';
import { CallLogTableComponent } from './call-log-table/call-log-table.component';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { filter, finalize, forkJoin, Subscription } from 'rxjs';
import { AuthorizationComponent } from '../../items-detail/authorization/authorization.component';
import { RemoteImmobilizerComponent } from '../../items-detail/remote-immobilizer/remote-immobilizer.component';
import { SnapShotRecordComponent } from '../../items-detail/snap-shot-record/snap-shot-record.component';
import { DiagnosticTroubleCodeComponent } from '../../items-detail/diagnostic-trouble-code/diagnostic-trouble-code.component';
import { ManagerApprovalComponent } from '../../items-detail/manager-approval/manager-approval.component';
import { EmergencyComponent } from '../../items-detail/emergency/emergency.component';
import { DtcWidgetComponent } from '../../items-detail/dtc-widget/dtc-widget.component';
import { BreadcrumbWithLabelComponent } from '../../../../../core/shared';
import { IconModule } from '../../../../../core/icon/icon.module';
import { VehicleService } from '../../../services';
import { LoadingService, NotificationService, UserService } from '../../../../../core/services';
import { ApprovalStatus, CallLog, DTCWidget, InsuranceDetails, Ticket, TicketDetail, TowingServiceDetails, Vehicle } from '../../../interfaces';
import { DateFormat } from '../../../../../core/enums';
import { PERMISSIONS_CODE } from '../../../../../core/constants';
import { DeviceManagementTab, TypeTicket } from '../../../enums';
import { TicketsDetailService } from '../../../services/tickets/ticket-detail.service';
import { handleErrors } from '../../../../../core/helpers';
import { environment } from '../../../../../../environments/environment';
import { Emergency, Warning } from '../../../../../core/interfaces/vehicle.interface';

@Component({
  selector: 'app-ticket-detail',
  standalone: true,
  imports: [
    TicketInfoComponent,
    BreadcrumbWithLabelComponent,
    IconModule,
    CommonModule,
    TranslateModule,
    ServiceBookingComponent,
    CurrentLocationComponent,
    VerificationComponent,
    WarningComponent,
    InsuranceCoverageComponent,
    TowingServiceComponent,
    CallLogTableComponent,
    AuthorizationComponent,
    StolenVehicleTrackingComponent,
    RemoteImmobilizerComponent,
    SnapShotRecordComponent,
    DiagnosticTroubleCodeComponent,
    ManagerApprovalComponent,
    EmergencyComponent,
    DtcWidgetComponent
  ],
  providers: [TicketsDetailService, NotificationService, VehicleService],
  templateUrl: './ticket-detail.component.html',
  styleUrls: ['./ticket-detail.component.scss'],
})
export class TicketDetailComponent implements OnInit {
  ticket?: Ticket;
  vehicle?: Vehicle;
  towingService: TowingServiceDetails;
  callLogData: CallLog[] = [];
  ticketId: string = '';
  approvalStatus: ApprovalStatus | null = null;
  towingServiceData: any = null;
  insuranceData: InsuranceDetails;
  isNotExpire: boolean = false;
  isVerified: boolean = false;
  mainSubscription = new Subscription();
  warnings: Warning[] = [];
  sscData = null;
  dtcData: DTCWidget[] = [];
  countAPI: number = 0;
  emergencyData: Emergency[] = [];
  approvalStatusInterval: ReturnType<typeof setInterval> | null = null;
  routerSubscription: Subscription = new Subscription();
  dateFormat = DateFormat;

  ticketsService = inject(TicketsDetailService);
  vehicleService = inject(VehicleService);
  route = inject(ActivatedRoute);
  loadingService = inject(LoadingService);
  notificationService = inject(NotificationService);
  translateService = inject(TranslateService);
  router = inject(Router);
  userService = inject(UserService);
    
  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;
  readonly TypeTicket = TypeTicket;
  readonly DeviceManagementTab = DeviceManagementTab;

  ngOnInit(): void {
    this.route.paramMap.subscribe((params) => {
      this.ticketId = params.get('id');
      this.getTicketDetail(this.ticketId);

      if (this.userService.isHasPermission([PERMISSIONS_CODE.ECARE_CUSTOMER_VERIFICATION_VIEW])) {
        this.fetchApprovalStatus();
        this.startApprovalStatusUpdate();
      }
    });
    this.routerSubscription = this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe(() => {
        const currentRoute = this.router.url;
        if (!this.isTicketDetailRoute(currentRoute)) {
          this.stopApprovalStatusUpdate();
        }
      });
  }

  ngOnDestroy() {
    this.routerSubscription.unsubscribe();
    this.stopApprovalStatusUpdate();
    this.mainSubscription && this.mainSubscription.unsubscribe();
    this.loadingService.hideLoader();
  }

  private isTicketDetailRoute(route: string): boolean {
    return route.startsWith('/tickets/');
  }

  startApprovalStatusUpdate(): void {
    this.stopApprovalStatusUpdate();
    this.approvalStatusInterval = setInterval(() => {
      this.fetchApprovalStatus();
    }, 5000);
  }

  stopApprovalStatusUpdate(): void {
    if (this.approvalStatusInterval) {
      clearInterval(this.approvalStatusInterval);
      this.approvalStatusInterval = null;
    }
  }

  onVerificationStatusChanged(isVerified: boolean) {
    this.isVerified = isVerified;
  }

  getTicketDetail(ticketId: string) {
    this.loadingService.showLoader();
    this.mainSubscription.add(
      this.ticketsService.getTicketDetail(ticketId).subscribe({
        next: (data: TicketDetail) => {
          this.ticket = data.ticket;
          this.ticket.vehicleModel = data.vehicle?.vehicleModel;
          this.ticket.vehicleSubType = data.vehicle?.vehicleSubType;
          this.vehicle = data.vehicle;
          // this.countAPI = 5;
          this.countAPI = [
            PERMISSIONS_CODE.ECARE_EMERGENCY_VIEW,
            // ECARE_WARNING_VIEW,
            PERMISSIONS_CODE.ECARE_TOWING_VIEW,
            PERMISSIONS_CODE.ECARE_CALL_LOG_VIEW,
            PERMISSIONS_CODE.ECARE_VEHICLE_IOT_VIEW,
            PERMISSIONS_CODE.ECARE_DTC_VIEW
          ].filter(item => this.userService.allPermission?.includes(item))?.length;

          this.loadingService.showLoader();

          if (this.userService.isHasPermission([PERMISSIONS_CODE.ECARE_EMERGENCY_VIEW, PERMISSIONS_CODE.ECARE_WARNING_VIEW])) {
            this.getWarningByTicket();
          }
          
          if (this.userService.isHasPermission([PERMISSIONS_CODE.ECARE_TOWING_VIEW])) {
            this.getTowingServiceDetails();
          }
          
          if (this.userService.isHasPermission([PERMISSIONS_CODE.ECARE_CALL_LOG_VIEW])) {
            this.getCallLogList();
          }
          
          if (this.userService.isHasPermission([PERMISSIONS_CODE.ECARE_VEHICLE_IOT_VIEW])) {
            this.getInsuranceDetails();
          }
          
          if (this.userService.isHasPermission([PERMISSIONS_CODE.ECARE_DTC_VIEW])) { 
            this.getDTCWidget();
          }
          
        },
        error: (error) => {
          this.loadingService.hideLoader();
          console.error('Failed to load ticket details', error);
        },
      })
    );
  }

  handleCountAPI() {
    this.countAPI -= 1;
    if (this.countAPI === 0) {
      this.loadingService.hideLoader();
    }
  }


  getWarningByTicket(isRefresh: boolean = false) {
    isRefresh && this.loadingService.showLoader();
    let pageSize = 1;
    if (this.ticket?.type?.code === TypeTicket.ALARM || this.ticket?.type?.code === TypeTicket.SOS) {
      pageSize = 10
    }
    this.ticketsService
      .getWarningListForTicket(this.ticketId, pageSize)
      .pipe(finalize(() => this.handleCountAPI()))
      .subscribe((res) => {
        if (res) {
          if (res?.warning && Object.keys(res?.warning).length > 0) {
            this.warnings = [{...res?.warning}];
          }

          if (res?.emergencies && Object.keys(res?.emergencies).length > 0 && res?.emergencies?.items && res?.emergencies?.items.length) {
            this.emergencyData = res.emergencies.items;
          }
        }
        isRefresh && this.loadingService.hideLoader();
      }, error => {
        isRefresh && this.loadingService.hideLoader();
      });
  }

  getDTCWidget() {
    this.mainSubscription.add(
      this.ticketsService
        .getDTCWidget(this.ticketId)
        .pipe(finalize(() => this.handleCountAPI()))
        .subscribe(
          (res: DTCWidget[]) => {
            if (res) {
              this.dtcData = res;
            }
          },
          (err) => {
            handleErrors(err, this.notificationService);
          }
        )
    );
  }

  getInsuranceDetails() {
    this.mainSubscription.add(
      this.ticketsService
        .getInsuranceDetails(this.ticket?.vin)
        .pipe(finalize(() => this.handleCountAPI()))
        .subscribe(
          (res) => {
            if (res) {
              this.insuranceData = res;
              const today = new Date();
              const endDate = new Date(this.insuranceData.expiredDate);
              this.isNotExpire = endDate.getTime() >= today.getTime();
            }
          },
          (error) => {
            handleErrors(error, this.notificationService);
          }
        )
    );
  }

  getTowingServiceDetails(isRefresh: boolean = false) {
    isRefresh && this.loadingService.showLoader();
    this.mainSubscription.add(
      this.ticketsService
        .getTowingServiceDetails(this.ticket?.vin)
        .pipe(finalize(() => !isRefresh && this.handleCountAPI()))
        .subscribe(
          (res) => {
            if (res && Object.keys(res).length > 0) {
              this.towingServiceData = res;
            }
            isRefresh && this.loadingService.hideLoader();
          },
          (error) => {
            handleErrors(error, this.notificationService);
            isRefresh && this.loadingService.hideLoader();
          }
        )
    );
  }

  getCallLogList() {
    this.mainSubscription.add(
      this.ticketsService
        .getCallLogList(this.ticket?.ticketID)
        .pipe(finalize(() => this.handleCountAPI()))
        .subscribe({
          next: (res) => {
            if (res?.items?.length > 0) {
              this.callLogData = res?.items;
            }
          },
          error: (err) => {
            this.callLogData = [];
            handleErrors(err, this.notificationService);
          },
        })
    );
  }

  onRefresh() {
    this.loadingService.showLoader();
    this.ticketsService.refreshCurrentLocation(this.ticketId).subscribe(
      (res: Vehicle) => {
        if (res) {
          this.vehicle = {
            ...this.vehicle,
            ...res,
          };
        }
        this.loadingService.hideLoader();
      },
      (error) => {
        this.notificationService.showError(this.translateService.instant('tickets.ticketDetail.cannotRefreshLatestVehicleInfo'))
        this.loadingService.hideLoader();
      }
    );
  }

  fetchApprovalStatus() {
    if (!this.ticketId) {
      console.error('Ticket ID is missing. Cannot fetch approval status.');
      return;
    }

    this.ticketsService.getApprovalStatus(this.ticketId).subscribe({
      next: (status) => {
        this.approvalStatus = status;
      },
      error: (error) => {
        console.error('Failed to fetch approval status:', error);
        handleErrors(error, this.notificationService);
      },
    });
  }

  get fuelLevelClass(): string {
    const fuelRemaining = this.vehicle?.fuelRemaining || '0.0';
    const fuelValue = parseFloat(fuelRemaining);
    return fuelValue > 20 ? 'grey' : 'red';
  }

  formatOdo(value: number | null | undefined): string {
    if (!value || value === null || value === 0) return '0';
    return new Intl.NumberFormat('en-US').format(value);
  }

  getFuelLevel(fuelRemaining: string | null | undefined): string {
    if (!fuelRemaining || fuelRemaining === 'null') return '-';
    const fuel = parseFloat(fuelRemaining);
    if (isNaN(fuel)) return '-';
    return `${fuel.toFixed(1).replace('.', ',')}%`;
  }

  getFullImageUrl(imagePath: string): string {
    if (!imagePath) return 'assets/images/missing_product_EN.png';
    return `${environment.OCC_BASE_URL}${imagePath}`;
  }

  refreshTicketDetails(): void {
    if (this.ticket?.ticketID) {
      this.getTicketDetail(this.ticket.ticketID);
    }
  }

  get deviceConnectionStatus(): string {
    const activeSimStates = ['ACTIVE_FOR_SUBSCRIPTION', 'ACTIVE_FOR_TESTING'];
    if (activeSimStates.includes(this.vehicle?.sim?.code)) {
      return this.translateService.instant('vehicle.deviceConnected');
    }
    return this.translateService.instant('vehicle.deviceDisconnected');
  }

  showAuthorization(): boolean {
    return (
      !!this.approvalStatus &&
      (this.ticket?.type?.code === TypeTicket.SVT ||
        this.ticket?.type?.code === TypeTicket.IMMOBILISER)
    );
  }

  openWarningDetail(warningMasterId: string): void {
    this.router.navigate(['/warning-master', warningMasterId])
  }
}
