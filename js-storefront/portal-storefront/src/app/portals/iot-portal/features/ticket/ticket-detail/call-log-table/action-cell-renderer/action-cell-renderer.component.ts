import { Component, inject } from '@angular/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { CommonModule } from '@angular/common';
import { UserService } from '../../../../../../../core/services';
import { IconModule } from '../../../../../../../core/icon/icon.module';
import { PERMISSIONS_CODE } from '../../../../../../../core/constants';

@Component({
  selector: 'app-action-cell-renderer',
  template: `
    <div *ngIf="userService.isHasPermission([PERMISSIONS_CODE.ECARE_CALL_LOG_MANAGE])" class="action-buttons-cell-renderer">
      <button class="edit-button" (click)="onEdit()">
        <mat-icon svgIcon="ic-edit" class="small-icon"></mat-icon>
      </button>
      <button class="remove-button" (click)="onRemove()">
        <mat-icon svgIcon="ic-delete" class="small-icon"></mat-icon>
      </button>
    </div>
  `,
  standalone: true,
  imports: [CommonModule, IconModule],
})
export class ActionCellRendererComponent implements ICellRendererAngularComp {
  userService = inject(UserService);
        
  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;
  params: any;

  agInit(params: any): void {
    this.params = params;
  }

  refresh(): boolean {
    return false;
  }

  onEdit(): void {
    if (this.params.onClick) {
      this.params.onClick('Edit', this.params.data);
    }
  }

  onRemove(): void {
    if (this.params.onClick) {
      this.params.onClick('Remove', this.params.data);
    }
  }
}
