<div class="call-log-modal">
    <h2 class="modal-title">{{ data.title | translate }}</h2>

    <form [formGroup]="callLogForm" (ngSubmit)="onSubmit()">
        <div class="form-group">
            <app-date-time-picker-form-group [label]="'callLog.callTimestamp' | translate" controlId="callTime"
                [control]="callLogForm.controls.callTime" [required]="true" [maxDate]="maxDate"
                [errorMessage]="'common.requiredField' | translate: { field: ('callLog.callTimestamp' | translate) }">
            </app-date-time-picker-form-group>

            <div class="form-row">
                <app-dropdown-form-group [label]="'callLog.type' | translate" controlId="type" displayField="name" valueField="code"
                    [control]="callLogForm.controls.type" [options]="typeOptions"
                    [required]="true"></app-dropdown-form-group>

                <app-dropdown-form-group [label]="'callLog.result' | translate" controlId="result" displayField="name" valueField="code"
                    [control]="callLogForm.controls.result" [options]="resultOptions"
                    [required]="true"></app-dropdown-form-group>
            </div>

            <app-form-group [label]="'callLog.comment' | translate" controlId="comment"
                [control]="callLogForm.controls.comment" [noLitmitCharacter]="true"
                [errorMessage]="'common.requiredField' | translate: { field: ('callLog.comment' | translate) }"></app-form-group>

            <div class="form-actions">
                <button type="button" class="btn btn--outline cancel-btn" (click)="onCancel()">
                    {{ data.cancelBtn | translate }}
                </button>
                <button type="submit" class="btn btn--primary" [disabled]="callLogForm.invalid">
                    {{ data.submitBtn | translate }}
                </button>
            </div>
        </div>
    </form>
</div>