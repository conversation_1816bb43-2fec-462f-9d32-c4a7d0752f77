@import "../../../../../../styles/abstracts/mixins";
@import "../../../../../../styles/abstracts/variables";

:host {
  form {
    padding: 30px 30px 0;

    .filter-ticket {
      display: flex;
      gap: 20px;
      justify-content: space-between;

      &__properties {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 0 20px;
        width: 100%;
      }

      &__search {
        display: flex;
        align-items: end;

        .search {
            margin-top: 0;
            margin-bottom: 22px;
        }
      }
    }

    .advanced-title {
      color: $text-color-5;
      font-weight: $fw600;
      font-size: $fs14;

      width: fit-content;
      display: flex;
      align-items: center;
      gap: 5px;

      cursor: pointer;
    }
  }
}
