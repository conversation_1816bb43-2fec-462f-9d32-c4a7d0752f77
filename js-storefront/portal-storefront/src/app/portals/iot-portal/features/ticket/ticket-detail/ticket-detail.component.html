<div class="ticket-detail">
  <div class="ticket-detail__breadcrumb">
    <app-breadcrumb-with-label
      [breadcrumbs]="['Tickets', ticket?.ticketID || '']"
      [breadcrumbLinks]="['/tickets', '/tickets/' + (ticket?.ticketID || '')]"
      [label]="ticket?.title || ''"
    >
    </app-breadcrumb-with-label>
  </div>
  <div class="ticket-detail__content">
    <div class="ticket-detail__infor">
      <div class="ticket-detail__box">
        <app-ticket-info [ticket]="ticket" (ticketUpdated)="refreshTicketDetails()"></app-ticket-info>
      </div>
      <div class="ticket-detail__box">
        <div class="vehicle-info">
          <div class="vehicle-info__image">
            <img [src]="vehicle?.vehicleImage ? getFullImageUrl(vehicle.vehicleImage) : 'assets/images/missing_product_EN.png'"
                [alt]="vehicle?.vehicleModel || 'Missing Product'" />
          </div>
        <div [ngClass]="{
            'vehicle-info__status-connected': deviceConnectionStatus === translateService.instant('vehicle.deviceConnected'),
            'vehicle-info__status-disconnected': deviceConnectionStatus === translateService.instant('vehicle.deviceDisconnected')
          }" class="vehicle-info__status">
            <span class="vehicle-info__status-text">
             {{ deviceConnectionStatus }}
            <mat-icon *ngIf="deviceConnectionStatus === translateService.instant('vehicle.deviceConnected')" class="normal-icon"
                svgIcon="ic-green-check"></mat-icon>
            <mat-icon *ngIf="deviceConnectionStatus === translateService.instant('vehicle.deviceDisconnected')" class="normal-icon"
                svgIcon="ic-close-grey"></mat-icon>
                    </span>
          </div>
          <div class="vehicle-info__details">
            <h3 class="vehicle-info__title">{{ vehicle?.vehicleModel ? vehicle?.vehicleModel : '-' }}</h3>
            <p class="vehicle-info__sub-details">
                {{ ticket?.vin ? ticket.vin : '-' }}
                <span *ngIf="vehicle?.vehicleColor"> · {{ vehicle.vehicleColor }}</span>
                <span *ngIf="vehicle?.plateNumber"> · {{ vehicle.plateNumber }}</span>
            </p>
          </div>
          <div class="vehicle-info__stats">
            <div class="vehicle-info__stat">
              <div class="vehicle-info__stat-icon">
                <mat-icon class="large-icon" svgIcon="ic-automatic"></mat-icon>
              </div>
              <div class="vehicle-info__stat-text">
                <p>{{ vehicle?.odo ? formatOdo(vehicle?.odo) : "0"  }} {{'common.km' | translate}}</p>
                <small>{{'tickets.traveled' | translate}}</small>
              </div>
            </div>
            <div class="vehicle-info__stat">
              <div class="vehicle-info__stat-icon">
                <mat-icon class="large-icon" svgIcon="ic-engine"></mat-icon>
              </div>
              <div class="vehicle-info__stat-text">
                <p
                  class="{{ vehicle?.engineStatus === 'ON' ? 'green' : 'red' }}"
                >
                  {{ vehicle?.engineStatus === "ON" ? "On" : "Off" }}
                </p>
                <small>{{'tickets.engine' | translate}}</small>
              </div>
            </div>
            <div class="vehicle-info__stat">
              <div class="vehicle-info__stat-icon">
                <mat-icon class="large-icon" svgIcon="ic-gas"></mat-icon>
              </div>
              <div class="vehicle-info__stat-text">
                <p [class]="fuelLevelClass">{{ getFuelLevel(vehicle?.fuelRemaining) }}</p>
                <small> {{'tickets.fuelLeft' | translate}}</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    @if (ticket?.warningOccurredTimes?.length > 0) {
      <div class="ticket-detail__box">
        <div class="warning-occurred">
          <h4 class="warning-occurred__title">
            <mat-icon class="medium-icon" svgIcon="ic-time"></mat-icon>
            {{ "tickets.ticketDetail.warningTitle" | translate }}
          </h4>
          <ul class="warning-occurred__list">
            <li
              *ngFor="let time of ticket?.warningOccurredTimes; let last = last"
              class="warning-occurred__item"
            >
              <div class="time-line">
                <div class="dot"></div>
                <div *ngIf="!last" class="line"></div>
              </div>
              <div class="time-info">
                <span class="text">{{
                  time
                }}</span>
              </div>
            </li>
          </ul>
        </div>
      </div>
    }
    </div>

    <div class="ticket-detail__overview-container">
      <div class="ticket-detail__overview-container-content">
        <div *ngIf="userService.isHasPermission([PERMISSIONS_CODE.ECARE_CUSTOMER_VERIFICATION_VIEW])" [ngClass]="{ 'ticket-detail--verification-verified': (ticket?.isVerified || isVerified)}" class="ticket-detail__box ticket-detail--verification">
          <app-verification [ticket]="ticket" (verificationSuccess)="onVerificationStatusChanged($event)"></app-verification>
        </div>
        

        @if ( userService.isHasPermission([PERMISSIONS_CODE.ECARE_MANAGER_APPROVAL_VIEW]) 
          && (ticket?.type?.code === TypeTicket.SVT || ticket?.type?.code === TypeTicket.IMMOBILISER)) {
          <div class="ticket-detail__box ticket-detail--manager-approval">
            <app-manager-approval
            [ticket]="ticket"
            [approvalStatus]="approvalStatus"
            [verifiedDate]="
              ticket?.verifiedDate ||
              approvalStatus.validTill ||
              approvalStatus.triggerDate
            "
            (refreshApprovalStatus)="fetchApprovalStatus()"
          >
          </app-manager-approval>
          </div>
        }

        @if (userService.isHasPermission([PERMISSIONS_CODE.ECARE_SVT_ENABLE_DISABLE]) 
         && ticket?.type?.code === TypeTicket.SVT) {
          <div class="ticket-detail__box ticket-detail--svt">
            <app-stolen-vehicle-tracking [ticket]="ticket" [checkApprovalStatus]="approvalStatus"></app-stolen-vehicle-tracking>
          </div>
        }

        @if (userService.isHasPermission([PERMISSIONS_CODE.ECARE_RI_ENABLE_DISABLE]) 
          && ticket?.type?.code === TypeTicket.IMMOBILISER) {
          <div class="ticket-detail__box ticket-detail--svt">
            <app-remote-immobilizer [ticket]="ticket" [approvalStatus]="approvalStatus"></app-remote-immobilizer>
          </div>
        }

        <div class="ticket-detail__box">
          <app-current-location [vehicle]="vehicle" (refresh)="onRefresh()"></app-current-location>
        </div>

        @if (userService.isHasPermission([PERMISSIONS_CODE.ECARE_EMERGENCY_VIEW])
         && (ticket?.type?.code === TypeTicket.SOS || ticket?.type?.code === TypeTicket.ALARM)) {
          <div class="ticket-detail__box ticket-detail--warning">
            <app-emergency [vin]="ticket?.vin" [data]="emergencyData" [isTicket]="true" (refresh)="getWarningByTicket(true)"></app-emergency>
          </div>
        }

        @if(userService.isHasPermission([PERMISSIONS_CODE.ECARE_WARNING_VIEW])
         && ticket?.type?.code === TypeTicket.WARNING) {
          <div class="ticket-detail__box ticket-detail--warning">
            <app-warning [data]="warnings" [onlyOneItem]="true" (warningId)="openWarningDetail($event)"></app-warning>
          </div>
        }

        @if (userService.isHasPermission([PERMISSIONS_CODE.ECARE_VEHICLE_IOT_VIEW])
         && (ticket?.type?.code === TypeTicket.WARNING 
            || ticket?.type?.code === TypeTicket.SOS 
            || ticket?.type?.code === TypeTicket.ALARM)) {
          <div class="ticket-detail__box">
            <app-insurance-coverage [insuranceData]="insuranceData" [haveData]="insuranceData?.isActive && isNotExpire"></app-insurance-coverage>
          </div>
        }
        <div class="ticket-detail__box-haft">
          <div *ngIf="(ticket?.type?.code === TypeTicket.WARNING 
            || ticket?.type?.code === TypeTicket.SOS 
            || ticket?.type?.code === TypeTicket.ALARM) 
            && (towingServiceData? towingServiceData?.ticketId === this.ticketId : true)  
            && userService.isHasPermission([PERMISSIONS_CODE.ECARE_TOWING_VIEW])"
            class="ticket-detail__box">
            
            <app-towing-service
              [towingServiceData]="towingServiceData"
              [vin]="ticket?.vin"
              [ticketId]="ticketId"
              [hasActiveCustomerRelation]="vehicle?.hasActiveCustomerRelation"
              (refreshTowingDetails)="getTowingServiceDetails(true)"
            ></app-towing-service>
          </div>

          <div
            *ngIf="
              (ticket?.type?.code === TypeTicket.WARNING ||
                ticket?.type?.code === TypeTicket.SOS ||
                ticket?.type?.code === TypeTicket.ALARM) &&
              userService.isHasPermission([
                PERMISSIONS_CODE.ECARE_SERVICE_BOOKING_VIEW
              ])
            "
            class="ticket-detail__box ticket-detail--service-booking"
          >
            <app-service-booking
              [vin]="ticket?.vin"
              [vehicleMileage]="vehicle?.odo"
              [hasActiveCustomerRelation]="vehicle?.hasActiveCustomerRelation"
            ></app-service-booking>
          </div>
        </div>
        
        <!-- @if(ticket?.type?.code === TypeTicket.WARNING) {
          <div class="ticket-detail__box-haft">
            <div class="ticket-detail__box">
              <app-snap-shot-record [data]="sscData"></app-snap-shot-record>
            </div>
            <div *ngIf="userService.isHasPermission([PERMISSIONS_CODE.ECARE_DTC_VIEW])" class="ticket-detail__box">
              <app-diagnostic-trouble-code [data]="dtcData"></app-diagnostic-trouble-code>
            </div>
          </div>
        } -->

        @if(ticket?.type?.code === TypeTicket.WARNING && vehicle?.deviceType === DeviceManagementTab.GDCM && userService.isHasPermission([PERMISSIONS_CODE.ECARE_DTC_VIEW]) ) {
          <div class="ticket-detail__box">
            <app-dtc-widget [vin]="vehicle?.vin" [fromTicket]="true"></app-dtc-widget>
          </div>
        }

        <div *ngIf="userService.isHasPermission([PERMISSIONS_CODE.ECARE_CALL_LOG_VIEW])" class="ticket-detail__box">
            <app-call-log-table [ticket]="ticket" [rowData]="callLogData"></app-call-log-table>
        </div>
      </div>
    </div>
  </div>
</div>
