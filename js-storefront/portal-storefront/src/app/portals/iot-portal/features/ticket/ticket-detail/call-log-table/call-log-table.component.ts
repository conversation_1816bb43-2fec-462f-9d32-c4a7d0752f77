import {ChangeDetectorRef, Component, inject, Input, OnInit, ViewEncapsulation} from '@angular/core';
import { ColDef, DomLayoutType, GridOptions } from 'ag-grid-community';
import { CommonModule } from '@angular/common';
import { AgGridAngular } from 'ag-grid-angular';
import { ActionCellRendererComponent } from './action-cell-renderer/action-cell-renderer.component';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatDialog } from '@angular/material/dialog';
import { CallLogModalComponent } from './call-log-modal/call-log-modal.component';
import {catchError, filter, of, switchMap, tap} from "rxjs";
import { IconModule } from '../../../../../../core/icon/icon.module';
import { LoadingService, NotificationService, UserService } from '../../../../../../core/services';
import { CallLog, Ticket } from '../../../../interfaces';
import { PERMISSIONS_CODE } from '../../../../../../core/constants';
import { ActionModal } from '../../../../../../core/enums';
import { DialogConfirmComponent } from '../../../../../../core/shared';
import { TicketsDetailService } from '../../../../services/tickets/ticket-detail.service';
@Component({
  selector: 'app-call-log-table',
  templateUrl: './call-log-table.component.html',
  styleUrls: ['./call-log-table.component.scss'],
  standalone: true,
  imports: [AgGridAngular, IconModule, CommonModule, TranslateModule],
  providers: [LoadingService, TicketsDetailService, NotificationService],
  encapsulation: ViewEncapsulation.None,
})
export class CallLogTableComponent {
  @Input() rowData: CallLog[] = [];
  @Input() ticket!: Ticket;
  private translateService = inject(TranslateService);
  private dialog = inject(MatDialog);
  private cdr = inject(ChangeDetectorRef);
  private loadingService = inject(LoadingService);
  private ticketsService = inject(TicketsDetailService);
  private notificationService = inject(NotificationService);

  userService = inject(UserService);
      
  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;

  colDefs: ColDef<CallLog>[] = [
    {
      headerName: this.translateService.instant('callLog.dateTime'),
      headerValueGetter: () => this.translateService.instant('callLog.dateTime'),
      field: 'callTime',
      flex: 1.5,
      cellRenderer: (params) => {
        return params?.value
          ? `<div class="date-time-cell">
                <span class="date">${params.value}</span>
              </div>`
          : '-';
      },
      cellClass: 'date-time-column',
      wrapText: true,
      autoHeight: true,
    },
    {
      headerName: this.translateService.instant('callLog.type'),
      headerValueGetter: () => this.translateService.instant('callLog.type'),
      field: 'type',
      flex: 1.5,
      valueFormatter: (params) => params.value?.name || '-',
      wrapText: true,
      autoHeight: true,
    },
    {
      headerName: this.translateService.instant('callLog.result'),
      headerValueGetter: () => this.translateService.instant('callLog.result'),
      field: 'result',
      flex: 3,
      valueFormatter: (params) => params.value?.name || '-',
      wrapText: true,
      autoHeight: true,
    },
    {
      headerName: this.translateService.instant('callLog.comment'),
      headerValueGetter: () => this.translateService.instant('callLog.comment'),
      field: 'comment',
      flex: 3,
      wrapText: true,
      autoHeight: true,
    },
    {
      headerName: '',
      flex: 1,
      cellRenderer: ActionCellRendererComponent,
      cellRendererParams: {
        onClick: (type: string, data: CallLog) =>
          this.handleActionTable(type, data),
      },
      cellClass: 'action-grid',
      wrapText: true,
      autoHeight: true,
    },
  ];

  defaultColDef: ColDef<CallLog> = {
    resizable: false,
    valueFormatter: (params) => (params.value ? params.value : '-'),
    sortable: false,
    menuTabs: [],
    lockPosition: true,
  };

  gridOptions: GridOptions<CallLog> = {
    domLayout: 'autoHeight' as DomLayoutType,
    rowHeight: 50,
  };

  rowHeight = 50;

  handleActionTable(action: string, data: CallLog): void {
    if (action === 'Edit') {
      this.openEditModal(data);
    } else if (action === 'Remove') {
      this.onRemove(data);
    }
  }

  openEditModal(data: CallLog): void {
    const dialogData = {
      ...data,
      callTime: data.callTime,
      title: this.translateService.instant('callLog.modal.updateCallLog'),
      cancelBtn: this.translateService.instant('common.cancel'),
      submitBtn: this.translateService.instant('common.update'),
      isEdit: true,
    };

    if (this.ticket) {
      dialogData['ticket'] = this.ticket;
    }

    const dialogRef = this.dialog.open(CallLogModalComponent, {
      width: '695px',
      data: dialogData,
    });

    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((res) => {
        this.getAllCallLog();
      });
  }

  onRemove(data: CallLog): void {
    const dialogRef = this.dialog.open(DialogConfirmComponent, {
      width: '530px',
      data: {
        title: this.translateService.instant(
          'callLog.deleteConfirmation.title'
        ),
        icon: 'ic-warning',
        confirmMsg: this.translateService.instant(
          'callLog.deleteConfirmation.message'
        ),
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.remove'),
      },
    });

    dialogRef
      .afterClosed()
      .pipe(
        filter((result) => result?.action === ActionModal.Submit),
        tap(() => this.loadingService.showLoader()),
        switchMap(() =>
          this.ticketsService
            .deleteCallLog(this.ticket?.ticketID, data.id)
            .pipe(
              tap((res) => {
                if (res?.code === 'SUCCESS') {
                  this.notificationService.showSuccess(res?.message);
                  this.rowData = this.rowData.filter(
                    (log) => log.id !== data.id
                  );
                  this.cdr.detectChanges();
                } else {
                  this.notificationService.showError(res?.message);
                }
              }),
              catchError((error) => {
                const err = error?.error;
                this.notificationService.showError(
                  err?.errors?.length > 0
                    ? err.errors[0].message
                    : 'Something went wrong.'
                );
                return of(null);
              }),
              tap(() => this.loadingService.hideLoader())
            )
        )
      )
      .subscribe({
        next: () => this.cdr.detectChanges(),
        error: () => this.cdr.detectChanges(),
        complete: () => this.cdr.detectChanges(),
      });
  }

  addNewCallLog() {
    const dialogRef = this.dialog.open(CallLogModalComponent, {
      width: '695px',
      data: {
        title: this.translateService.instant('callLog.title'),
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.create'),
        ticket: this.ticket,
        isEdit: false,
      },
    });

    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((res) => {
        this.getAllCallLog();
      });
  }

  getAllCallLog(): void {
    this.ticketsService.getCallLogList(this.ticket?.ticketID).subscribe({
      next: (res) => {
        this.rowData = res?.items || [];
        this.cdr.detectChanges();
      },
      error: () => {
        this.rowData = [];
        this.cdr.detectChanges();
      },
    });
  }
}
