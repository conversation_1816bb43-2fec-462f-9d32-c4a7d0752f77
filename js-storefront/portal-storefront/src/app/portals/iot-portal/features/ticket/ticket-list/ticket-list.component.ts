import { Component, inject, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';

import {
  debounceTime,
  distinctUntilChanged,
  filter,
  map,
  of,
  Subscription,
  switchMap,
  take,
} from 'rxjs';
import { FilterTicketComponent } from '../filter-ticket/filter-ticket.component';
import { CustomHeaderTicketComponent } from '../custom-header-ticket/custom-header-ticket.component';
import { ActivatedRoute, Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { CreateTicketComponent } from '../../items-detail';
import { LoadingComponent } from '../../../../../layout/global/loading/loading.component';
import { AgGridCustomComponent, RouterLinkCellRendererComponent, WidgetSummaryComponent } from '../../../../../core/shared';
import { LoadingService, NotificationService, UserService } from '../../../../../core/services';
import { TicketsService } from '../../../services';
import { TypeTicketPipe } from '../../../pipes';
import { PERMISSIONS_CODE } from '../../../../../core/constants';
import { ItemWidget, LastImportResult, OptionDropdown, PagingInfo } from '../../../../../core/interfaces';
import { ApprovalTicket, AssigneeTicket, PriorityTicket, StatusTicket, TicketSummaryTab, TypeTicket } from '../../../enums';
import { FilterTickets, ItemTicket, PayloadGetTicketList } from '../../../interfaces';
import { ActionModal, DateFormat } from '../../../../../core/enums';
import { TICKET_ASSIGNEE, TICKET_WIDGETS } from '../../../constants';

@Component({
  selector: 'app-ticket-list',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    MatIconModule,
    WidgetSummaryComponent,
    LoadingComponent,
    FilterTicketComponent,
    AgGridCustomComponent,
  ],
  providers: [LoadingService, NotificationService, TicketsService, DatePipe, TypeTicketPipe],
  templateUrl: './ticket-list.component.html',
  styleUrl: './ticket-list.component.scss',
})
export class TicketListComponent implements OnInit, OnDestroy {
  loadingService = inject(LoadingService);
  translateService = inject(TranslateService);
  ticketsService = inject(TicketsService);
  router = inject(Router);
  userService = inject(UserService);
  datePipe = inject(DatePipe);
  dialog = inject(MatDialog);
  activatedRoute = inject(ActivatedRoute);
  typeTicketPipe = inject(TypeTicketPipe);

  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;
  subscription = new Subscription();

  selectedSummaryTab: any;
  pagingInfo: PagingInfo = {
    totalItems: 0,
    currentPage: 0,
    numberOfPage: 10,
  };
  importInfo: LastImportResult;

  filterTickets = new FormGroup({
    ticketId: new FormControl(),
    type: new FormControl(),
    customerName: new FormControl(),
    assignee: new FormControl(),
    priority: new FormControl(),
    status: new FormControl(),
    approval: new FormControl(),
  });

  widgets: ItemWidget[] = [
    {
      id: '' as TicketSummaryTab,
      count: 0,
      description: '',
      icon: '',
    },
  ];

  isAdvancedFilter = true;
  filterList: FilterTickets;

  rowData: ItemTicket[];

  colDefs = [
    {
      headerName: this.translateService.instant('tickets.ticketList.ticketId'),
      headerValueGetter: () =>
        this.translateService.instant('tickets.ticketList.ticketId'),
      wrapText: true,
      flex: 1,
      headerComponent: CustomHeaderTicketComponent,
      headerComponentParams: {
        secondHeader: this.translateService.instant(
          'tickets.ticketList.ticketType'
        ),
      },
      autoHeight: true,
      cellClass: 'cell-word-wrap',
      cellRenderer: RouterLinkCellRendererComponent,
      cellRendererParams: {
        linkBuilder: (data: any) => this.userService.isHasPermission([
          PERMISSIONS_CODE.ECARE_TICKET_DETAIL_VIEW,
        ]) ? `tickets/${data?.ticketID}` : '',
        displayValue: (params) => {
          return `<p>${params?.ticketID}</p><p>${this.typeTicketPipe.transform(params?.type)}</p>`;
        },
      }
    },
    {
      headerName: this.translateService.instant('tickets.ticketList.priority'),
      headerValueGetter: () =>
        this.translateService.instant('tickets.ticketList.priority'),
      field: 'priority',
      flex: 1,
      cellRenderer: (params) => {
        return params?.value ? `<span>${params?.value?.name}</span>` : '';
      },
      cellClassRules: {
        'value-tag': (params) => !!params.value,
        'red-tag': (params) => params.value?.code === PriorityTicket.High,
        'yellow-tag': (params) => params.value?.code === PriorityTicket.Mid,
      },
    },
    {
      headerName: this.translateService.instant('tickets.ticketList.status'),
      headerValueGetter: () =>
        this.translateService.instant('tickets.ticketList.status'),
      field: 'status',
      flex: 1,
      cellRenderer: (params) => {
        return params?.value ? `<span>${params?.value?.name}</span>` : '';
      },
      cellClassRules: {
        'value-tag': (params) => !!params.value,
        'yellow-tag': (params) =>
          params.value?.code === StatusTicket.InProgress,
      },
    },
    {
      headerName: this.translateService.instant('tickets.ticketList.customer'),
      headerValueGetter: () =>
        this.translateService.instant('tickets.ticketList.customer'),
      flex: 1,
      headerComponent: CustomHeaderTicketComponent,
      headerComponentParams: {
        secondHeader: this.translateService.instant(
          'tickets.ticketList.mobile'
        ),
      },
      wrapText: true,
      autoHeight: true,
      cellClass: 'cell-word-wrap',
      cellRenderer: (params) => {
        if (params.data.customerName && params.data.mobileNumber) {
          return `<p>${params.data.customerName}</p><p>${params.data.mobileNumber}</p>`;
        }
        if (params.data.customerName && !params.data.mobileNumber) {
          return `<p>${params.data.customerName}</p>`;
        }
        return `<p>-</p>`;
      },
      hide: !this.userService.isHasPermission([
        PERMISSIONS_CODE.ECARE_OWNER_VIEW,
      ]),
    },
    {
      headerName: this.translateService.instant('tickets.ticketList.plate'),
      headerValueGetter: () =>
        this.translateService.instant('tickets.ticketList.plate'),
      flex: 1,
      wrapText: true,
      autoHeight: true,
      headerComponent: CustomHeaderTicketComponent,
      headerComponentParams: {
        secondHeader: this.translateService.instant(
          'tickets.ticketList.drivable'
        ),
      },
      cellRenderer: (params) => {
        const drivable = params.data.isDrivable
          ? this.translateService.instant('common.yes')
          : this.translateService.instant('common.no');
        return `<p>${params.data.plateNumber || ''}</p><p>${
          drivable || ''
        }</p>`;
      },
    },
    {
      headerName: this.translateService.instant('tickets.ticketList.title'),
      headerValueGetter: () =>
        this.translateService.instant('tickets.ticketList.title'),
      flex: 3,
      autoHeight: true,
      cellClass: 'cell-word-wrap',
      headerComponent: CustomHeaderTicketComponent,
      headerComponentParams: {
        secondHeader: this.translateService.instant('tickets.ticketList.vin'),
      },
      cellRenderer: (params) => {
        return `<p class="data-word-wrap">${params?.data?.title}</p><p>${params?.data?.vin}</p>`;
      },
    },

    {
      headerName: this.translateService.instant(
        'tickets.ticketList.createDate'
      ),
      headerValueGetter: () =>
        this.translateService.instant('tickets.ticketList.createDate'),
      field: 'createdDate',
      flex: 1,
      cellRenderer: (params) => {
        return params?.value
          ? `<span>${this.datePipe.transform(
              params?.value,
              DateFormat.ShortDate
            )}</span>`
          : '';
      },
      tooltipValueGetter: (params) => {
        return this.datePipe.transform(
          params?.data?.createdDate,
          DateFormat.ShortDate
        );
      },
    },
  ];

  defaultColDef = {
    resizable: false,
    valueFormatter: (params) => (params.value ? params.value : '-'),
    sortable: false,
    menuTabs: [],
    wrapHeaderText: true,
    autoHeaderHeight: true,
  };
  suggestionsAssignee: OptionDropdown[] = TICKET_ASSIGNEE;
  currentUser: OptionDropdown;

  ngOnInit(): void {
    this.getAllFilter();
    this.getWidgets();

    this.activatedRoute.queryParams.pipe(take(1)).subscribe((params) => {
      const { status, assignee, priority, type } = params;

      this.filterTickets.patchValue({
        ticketId: '',
        type: type || TypeTicket.All,
        customerName: '',
        assignee: assignee
          ? TICKET_ASSIGNEE?.find((item) => item?.code === assignee) || {
              code: assignee,
              name: this.userService.userInfo?.fullName,
            }
          : TICKET_ASSIGNEE[0],

        priority: priority || PriorityTicket.All,
        status: status || StatusTicket.All,
        approval: ApprovalTicket.All,
      });

      this.router.navigate([], {
        relativeTo: this.activatedRoute,
        queryParams: {
          status: null,
          assignee: null,
          priority: null,
          type: null,
        },
        queryParamsHandling: 'merge',
        replaceUrl: true,
      });

      this.getTicketList();
    });

    this.filterTickets.controls.assignee.valueChanges
      .pipe(
        debounceTime(300),
        distinctUntilChanged(),
        filter((value) => typeof value === 'string'),
        switchMap((value: string) => {
          if (value?.length >= 3) {
            return this.ticketsService.getAssignee(value, null).pipe(
              map((response) => ({
                assignees: response?.assignees,
                value,
              }))
            );
          }
          return of({ assignees: [], value });
        })
      )
      .subscribe(
        (response: any) => {
          const optionDefault = [...TICKET_ASSIGNEE, this.currentUser];
          let filterOption = optionDefault;
          if (response?.value?.length >= 3) {
            filterOption = optionDefault.filter((option) =>
              option?.name
                ?.toLowerCase()
                ?.includes(response?.value.toLowerCase())
            );
          }

          this.suggestionsAssignee = [
            ...filterOption,
            ...response?.assignees
              ?.map((item) => ({
                code: item?.username,
                name: item?.name,
              }))
              ?.filter((item) => item.code !== this.currentUser?.code),
          ];
        },
        (err) => {
          this.suggestionsAssignee = [];
        }
      );
    this.getUserInfo();
  }

  ngAfterViewInit(): void {}

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  getUserInfo(): void {
    this.userService.userRoles$.subscribe((value) => {
      this.currentUser = {
        code: value?.username,
        name: value?.fullName,
      };
      this.suggestionsAssignee = [...TICKET_ASSIGNEE, this.currentUser];
    });
  }

  changeSummaryTab(id: any) {
    this.selectedSummaryTab = id;
    this.pagingInfo.currentPage = 0;
    this.isAdvancedFilter = true;

    this.mapWidgetFilter(id);
    this.getTicketList();
  }

  getWidgets(): void {
    this.loadingService.showLoader();
    this.subscription.add(
      this.ticketsService.getWidgetTicket().subscribe((response) => {
        this.loadingService.hideLoader();
        const { widgets } = response;

        this.widgets = widgets.map((item) => {
          const { description, icon } =
            TICKET_WIDGETS?.find((widget) => widget.id === item.type) || {};
          return {
            id: item?.type,
            count: item?.count,
            description,
            icon,
          };
        });
      })
    );
  }

  changeAdvancedFilter(): void {
    this.isAdvancedFilter = !this.isAdvancedFilter;
  }

  getAllFilter(): void {
    this.loadingService.showLoader();
    this.ticketsService.getFilter().subscribe((response) => {
      this.loadingService.hideLoader();
      this.filterList = response;
    });
  }

  mapWidgetFilter(id: TicketSummaryTab) {
    switch (id) {
      case TicketSummaryTab.HighPriorityTickets: {
        // Status = Open, Priority = High
        this.filterTickets.patchValue({
          ticketId: '',
          type: TypeTicket.All,
          customerName: '',
          assignee: TICKET_ASSIGNEE[0],
          priority: PriorityTicket.High,
          status: StatusTicket.Open,
          approval: ApprovalTicket.All,
        });
        break;
      }
      case TicketSummaryTab.UnassignedTickets: {
        // Assignee = Unassigned
        this.filterTickets.patchValue({
          ticketId: '',
          type: TypeTicket.All,
          customerName: '',
          assignee: TICKET_ASSIGNEE[1],
          priority: PriorityTicket.All,
          status: StatusTicket.All,
          approval: ApprovalTicket.All,
        });
        break;
      }
      case TicketSummaryTab.MyOpenTickets: {
        //Assignee = Current logged in user, Status = Open
        this.filterTickets.patchValue({
          ticketId: '',
          type: TypeTicket.All,
          customerName: '',
          assignee: this.currentUser,
          priority: PriorityTicket.All,
          status: StatusTicket.Open,
          approval: ApprovalTicket.All,
        });
        break;
      }
    }
  }

  getTicketList(): void {
    const isUnassigned =
      this.filterTickets.value.assignee.code === AssigneeTicket.Unassigned;
    const params: PayloadGetTicketList = {
      currentPage: this.pagingInfo?.currentPage,
      pageSize: this.pagingInfo?.numberOfPage,
      ticketIdOrTitle: this.filterTickets.value.ticketId,
      type: this.filterTickets.value.type,
      customerName: this.filterTickets.value.customerName,
      priority: this.filterTickets.value.priority,
      status: this.filterTickets.value.status,
      assigneeUserName: isUnassigned
        ? null
        : this.filterTickets.value.assignee.code,
      isUnassigned,
    };

    this.loadingService.showLoader();
    this.ticketsService.getTicketList(params).subscribe({
      next: (response: any) => {
        this.rowData = response?.items;

        this.pagingInfo.totalItems = response?.pagination?.totalResults;

        this.loadingService.hideLoader();
      },
      error: () => {
        this.loadingService.hideLoader();
      },
    });
  }

  onPageChange(value: number): void {
    this.pagingInfo.currentPage = value;
    this.getTicketList();
  }

  changeItemPerPage(item) {
    this.pagingInfo.numberOfPage = item;
    this.pagingInfo.currentPage = 0;
    this.getTicketList();
  }

  viewTicketDetail(id) {
    if (
      this.userService.isHasPermission([
        PERMISSIONS_CODE.ECARE_TICKET_DETAIL_VIEW,
      ])
    ) {
      this.router.navigate(['tickets/' + id]);
    }
  }

  searchTicket(): void {
    this.pagingInfo.currentPage = 0;
    this.getTicketList();
  }

  createTicket(): void {
    const dialogRef = this.dialog.open(CreateTicketComponent, {
      width: '850px',
      maxHeight: '90vh',
      disableClose: true,
      autoFocus: false,
      data: {},
    });

    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe(() => {
        this.pagingInfo.currentPage = 0;
        this.getTicketList();
      });
  }
}
