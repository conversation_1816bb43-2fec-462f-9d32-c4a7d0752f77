import { Component } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IHeaderParams } from 'ag-grid-community';

@Component({
  selector: 'app-custom-header-ticket',
  standalone: true,
  imports: [
    TranslateModule
  ],
  templateUrl: './custom-header-ticket.component.html',
  styleUrl: './custom-header-ticket.component.scss'
})
export class CustomHeaderTicketComponent {
  params!: IHeaderParams & { secondHeader: string };

  agInit(params: IHeaderParams & { secondHeader: string }): void {
    this.params = params;
  }
}
