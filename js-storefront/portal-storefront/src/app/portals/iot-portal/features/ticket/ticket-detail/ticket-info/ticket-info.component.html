<div class="ticket-info">
    <div class="ticket-info__header">
        <h3 class="ticket-info__title display-flex-center">
            <span>
                <mat-icon svgIcon="ic-ticket" class="medium-icon"></mat-icon>
            </span>
            {{ 'tickets.ticketDetail.titleInfo' | translate }}
        </h3>
        @if(userService.isHasPermission([PERMISSIONS_CODE.ECARE_TICKET_UPDATE])) {
            <button
              mat-button
              class="ticket-info__edit display-flex-center"
              (click)="openEditModal()"
            >
              <span>
                <mat-icon svgIcon="ic-edit" class="small-icon"></mat-icon>
              </span>
              {{ "common.edit" | translate }}
            </button>
        }
    </div>
    <div class="ticket-info__details">
        <p>{{ 'tickets.ticketDetail.ticketId' | translate }}: <strong>{{ ticket?.ticketID }}</strong></p>
        <p>{{ 'tickets.ticketDetail.ticketType' | translate }}: 
            <strong>{{ (ticket?.type?.code === TypeTicket.SVT 
                || ticket?.type?.code === TypeTicket.IMMOBILISER) ?  
                ("tickets.ticketDetail.securityIssues" | translate) : ticket?.type?.name }}
            </strong>
        </p>
        @if(ticket?.type?.code === TypeTicket.SVT 
            || ticket?.type?.code === TypeTicket.IMMOBILISER) {
            <p>{{ 'tickets.ticketDetail.subType' | translate }}: <strong>{{ ticket?.type?.name }}</strong></p>
        }
        
        <p>
            {{ 'tickets.ticketDetail.priority' | translate }}:
            <strong>
                <span class="ticket-info__priority"
             [ngClass]="{
            'ticket-info__priority--high': ticket?.priority?.code === PriorityTicket?.High,
            'ticket-info__priority--medium': ticket?.priority?.code === PriorityTicket?.Mid,
            'ticket-info__priority--low': ticket?.priority?.code === PriorityTicket?.Low
          }">
                    {{ ticket?.priority?.name }}
                </span>
            </strong>
        </p>
        <p>
            {{ 'tickets.ticketDetail.status' | translate }}:
            <strong><span class="ticket-info__grey-box">{{ ticket?.status?.name }}</span></strong>
        </p>
        <p>
            {{ 'tickets.ticketDetail.vehicleType' | translate }}:
            <strong>
                {{ ticket?.vehicleType }}
                <span *ngIf="ticket?.vehicleSubType"> - {{ ticket?.vehicleSubType }}</span>
            </strong>
        </p>
        <p>{{ 'tickets.ticketDetail.assignee' | translate }}: <strong>{{ ticket?.assignee ? ticket?.assignee.name : '-' }}</strong></p>
        
        @if(userService.isHasPermission([PERMISSIONS_CODE.ECARE_OWNER_VIEW])) {
            <p>{{ 'tickets.ticketDetail.customer' | translate }}: <strong>{{ ticket?.customerName }}</strong></p>
            <p>{{ 'tickets.ticketDetail.mobile' | translate }}: <strong>{{ ticket?.mobileNumber ? ticket?.mobileNumber : "-" }}</strong></p>
        }
       
        <p>{{ 'tickets.ticketDetail.plate' | translate }}: <strong>{{ ticket?.plateNumber }}</strong></p>
        <p>
            {{ 'tickets.ticketDetail.drivable' | translate }}:
            <strong>
                <span class="ticket-info__grey-box ticket-info__drivable">
                    {{ ticket?.isDrivable ? ('common.yes' | translate) : ('common.no' | translate) }}
                </span>
            </strong>
        </p>
        <p>{{ 'common.description' | translate }}: <strong>{{ ticket?.description || '-' }}</strong></p>
    </div>
</div>
