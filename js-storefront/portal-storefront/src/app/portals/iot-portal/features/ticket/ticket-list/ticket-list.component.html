<app-loading *ngIf="loadingService.isLoading"></app-loading>

<div class="header-section">
  <h2 class="title-page">
    {{ "tickets.tickets" | translate }}
  </h2>
  <button
    *ngIf="userService.isHasPermission([PERMISSIONS_CODE.ECARE_TICKET_CREATE])"
    class="btn-primary"
    (click)="createTicket()"
  >
    {{ "tickets.createTicket" | translate }}
  </button>
</div>

<app-widget-summary
  [widgets]="widgets"
  (changeTab)="changeSummaryTab($event)"
></app-widget-summary>

<app-filter-ticket
  [form]="filterTickets"
  [isAdvancedFilter]="isAdvancedFilter"
  [typeTickets]="filterList?.searchTypeList"
  [priorities]="filterList?.priorityList"
  [status]="filterList?.statusList"
  [approvals]="[]"
  [suggestionsAssignee]="suggestionsAssignee"
  (changeAdvancedFilter)="changeAdvancedFilter()"
  (search)="searchTicket()"
></app-filter-ticket>

<div class="device-list">
  @if (rowData?.length > 0 ) {
  <app-ag-grid-custom
    class="ticket-table"
    [rowData]="rowData"
    [colDefs]="colDefs"
    [defaultColDef]="defaultColDef"
    [isPaging]="true"
    [pagingInfo]="pagingInfo"
    [isShowActionExport]="false"
    (onPageChange)="onPageChange($event)"
    (changeItemPerPage)="changeItemPerPage($event)"
  >
  </app-ag-grid-custom>
  } @else {
  <div class="no-data">{{ "tickets.noTicket" | translate }}</div>
  }
</div>
