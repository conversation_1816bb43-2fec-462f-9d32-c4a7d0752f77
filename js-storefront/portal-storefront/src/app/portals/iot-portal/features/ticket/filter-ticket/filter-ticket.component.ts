import { CommonModule } from '@angular/common';
import { Component, EventEmitter, inject, Input, Output } from '@angular/core';
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';

import { MatIconModule } from '@angular/material/icon';

import { TranslateModule } from '@ngx-translate/core';
import { AutocompleteInputComponent, DropdownFormGroupComponent, FormGroupComponent } from '../../../../../core/shared';
import { ScreenSizeService, UserService } from '../../../../../core/services';
import { OptionDropdown } from '../../../../../core/interfaces';
import { PERMISSIONS_CODE } from '../../../../../core/constants';

@Component({
  selector: 'app-filter-ticket',
  templateUrl: './filter-ticket.component.html',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatIconModule,
    TranslateModule,
    FormGroupComponent,
    DropdownFormGroupComponent,
    AutocompleteInputComponent
  ],
  providers: [
    ScreenSizeService
  ],
  standalone: true,
  styleUrl: './filter-ticket.component.scss'
})
export class FilterTicketComponent {
  @Input() form: any;
  @Input() typeTickets: OptionDropdown[];
  @Input() priorities: OptionDropdown[];
  @Input() status: OptionDropdown[];
  @Input() approvals: OptionDropdown[];
 

  @Input() isAdvancedFilter = false;
  @Input() suggestionsAssignee: OptionDropdown[] = [];

  @Output() search = new EventEmitter();
  @Output() changeAdvancedFilter = new EventEmitter();

  userService = inject(UserService);
    
  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;
}
