import { Component, Output, EventEmitter, inject, OnInit } from '@angular/core';
import {
  FormGroup,
  FormControl,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatRadioModule } from '@angular/material/radio';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import {
  debounceTime,
  distinctUntilChanged,
  filter,
  switchMap,
  of,
  map,
  catchError,
} from 'rxjs';
import { IconModule } from '../../../../../../../core/icon/icon.module';
import { AutocompleteInputComponent, DropdownFormGroupComponent, FormGroupComponent } from '../../../../../../../core/shared';
import { LoadingService, NotificationService } from '../../../../../../../core/services';
import { TicketsService } from '../../../../../services';
import { FilterTickets, UpdateTicketPayload } from '../../../../../interfaces';
import { OptionDropdown } from '../../../../../../../core/interfaces';
import { PriorityTicket, RolesForAssignee, TypeTicket } from '../../../../../enums';


@Component({
  selector: 'app-edit-ticket-info-modal',
  templateUrl: './edit-ticket-info-modal.component.html',
  styleUrls: ['./edit-ticket-info-modal.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    IconModule,
    DropdownFormGroupComponent,
    MatRadioModule,
    FormGroupComponent,
    TranslateModule,
    ReactiveFormsModule,
    AutocompleteInputComponent,
  ],
  providers: [LoadingService, TicketsService, NotificationService],
})
export class EditTicketInfoFormComponent implements OnInit {
  data = inject(MAT_DIALOG_DATA);
  dialogRef = inject(MatDialogRef<EditTicketInfoFormComponent>);
  filterList: FilterTickets;
  currentUser: OptionDropdown;

  @Output() formSubmit = new EventEmitter<Record<string, string | boolean>>();
  @Output() formCancel = new EventEmitter<void>();

  loadingService = inject(LoadingService);
  translateService = inject(TranslateService);
  ticketsService = inject(TicketsService);
  notificationService = inject(NotificationService);

  readonly TypeTicket = TypeTicket;

  ticketForm = new FormGroup({
    ticketId: new FormControl({ value: '', disabled: true }),
    type: new FormControl({ value: '', disabled: true }, Validators.required),
    subType: new FormControl({ value: '', disabled: true }, Validators.required),
    status: new FormControl('', Validators.required),
    title: new FormControl('', Validators.required),
    vehiclePlate: new FormControl({ value: '', disabled: true }),
    vehicleInfo: new FormControl({ value: '', disabled: true }),
    priority: new FormControl('', Validators.required),
    assignee: new FormControl(''),
    drivable: new FormControl(true, Validators.required),
    description: new FormControl(''),
  });

  typeOptions: OptionDropdown[] = [];
  statusOptions: OptionDropdown[] = [];
  priorityOptions: OptionDropdown[] = [];
  assigneeOptions: OptionDropdown[] = [];
  subTypeOption: OptionDropdown[] = [];

  isSecurityIssues: boolean;

  ngOnInit() {
    if (this.data) {
      this.getAllFilter();
      this.isSecurityIssues = this.data.type?.code === TypeTicket.SVT 
      || this.data.type?.code === TypeTicket.IMMOBILISER;
      this.ticketForm.patchValue({
        ticketId: this.data.ticketID,
        type: this.isSecurityIssues 
          ? TypeTicket.SECURITY_ISSUES
          : this.data.type?.code,
        subType: this.isSecurityIssues ? this.data.type?.code : false,
        status: this.data.status?.code,
        title: this.data.title,
        vehiclePlate: this.data.plateNumber,
        vehicleInfo: `${this.data.vehicleType} (${this.data.vin})`,
        priority: this.data.priority?.code,
        assignee: this.data?.assignee ?? '',
        drivable: this.data.isDrivable ?? false,
        description: this.data.description ?? '',
      });
    }

    this.setupAssigneeSearch();
  }

  getAllFilter(): void {
    this.loadingService.showLoader();
    this.ticketsService.getFilter().subscribe((response) => {
      this.loadingService.hideLoader();
      this.filterList = response;
      this.typeOptions = this.filterList.typeList
        .filter((item) => item.code !== '')
        .map((item) => ({
          code: item.code,
          name: item.name,
        }));

      this.statusOptions = this.filterList.statusList
        .filter((item) => item.code !== '')
        .map((item) => ({
          code: item.code,
          name: item.name,
        }));

      this.priorityOptions = this.filterList.priorityList
        .filter((item) => item.code !== '')
        .map((item) => ({
          code: item.code,
          name: item.name,
        }));

      this.subTypeOption = this.filterList.subTypeList?.filter((item) => item.code !== '');
    });
  }

  setupAssigneeSearch(): void {
    const verifiedDisplayName =
      this.data?.authorization?.verifiedInfo?.displayName || 'Unknown User';
    this.currentUser = { code: 'current', name: verifiedDisplayName };

    const optionDefault = [this.currentUser];

    this.ticketForm.controls.assignee.valueChanges
      .pipe(
        debounceTime(300),
        distinctUntilChanged(),
        filter(
          (value: string) => typeof value === 'string' && value.length >= 3
        ),
        switchMap((searchTerm: string) =>
          this.ticketsService.getAssignee(searchTerm, RolesForAssignee.CreateTicket).pipe(
            map((response) =>
              response.assignees.map((assignee) => ({
                code: assignee.username,
                name: assignee.name,
              }))
            ),
            map((apiOptions: OptionDropdown[]) => {
              const filteredDefault = optionDefault.filter((option) =>
                option.name.toLowerCase().includes(searchTerm.toLowerCase())
              );

              return [
                ...filteredDefault,
                ...apiOptions.filter(
                  (apiOption) =>
                    !filteredDefault.some(
                      (defaultOption) => defaultOption.code === apiOption.code
                    )
                ),
              ];
            }),
            catchError((error) => {
              console.error('Failed to fetch assignees:', error);
              return of(optionDefault);
            })
          )
        )
      )
      .subscribe((options: OptionDropdown[]) => {
        this.assigneeOptions = options;
      });
  }

  onSubmit(): void {
    if (this.ticketForm.valid) {
      let assignee = {};
      if (this.ticketForm.value?.assignee === this.data.assignee?.name) {
        assignee = {
          username: this.data.assignee.username,
          name: this.data.assignee.name,
        };
      } else {
        const selectedAssignee = this.ticketForm.value
          .assignee as unknown as OptionDropdown;
        if (selectedAssignee?.code && selectedAssignee?.name) {
            assignee = {
            username: selectedAssignee.code,
            name: selectedAssignee.name,
            };
        } else {
            assignee = null;
        }
      }

      const payload: UpdateTicketPayload = {
        title: this.ticketForm.value.title as string,
        status: this.statusOptions.find(
          (status) => status.code === this.ticketForm.value.status
        )!,
        priority: this.priorityOptions.find(
          (priority) => priority.code === this.ticketForm.value.priority
        )!,
        description: this.ticketForm.value.description as string,
        isDrivable: this.ticketForm.value.drivable as boolean,
        assignee: assignee as string,
      };

      this.loadingService.showLoader();

      this.ticketsService
        .updateTicketInfo(this.data.ticketID as string, payload)
        .pipe(
          map((response) => {
            const successMessage = this.translateService.instant(
              'tickets.ticketDetail.editModalInfo.successMessage'
            );
            this.notificationService.showSuccess(successMessage);
            this.notificationService.showSuccess('Ticket updated successfully');
            this.loadingService.hideLoader();
            this.dialogRef.close(true);
          }),
          catchError((error) => {
            console.error('Update failed:', error);
            this.notificationService.showError(error?.message);
            this.loadingService.hideLoader();
            return of(null);
          })
        )
        .subscribe();
    } else {
      console.error('Form is invalid');
      this.ticketForm.markAllAsTouched();
    }
  }

  onCancel() {
    this.dialogRef.close();
  }

  changePriority(event): void {
      const { value } = event;
      if (value === PriorityTicket.High) {
        this.ticketForm.patchValue({
          drivable: false,
        });
      }
    }
}
