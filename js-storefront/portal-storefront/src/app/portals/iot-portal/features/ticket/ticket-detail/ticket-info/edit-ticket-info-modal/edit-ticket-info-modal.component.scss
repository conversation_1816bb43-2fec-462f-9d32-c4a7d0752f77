@import "../../../../../../../../styles/abstracts/mixins";
@import "../../../../../../../../styles/abstracts/variables";

:host {
    .edit-ticket-form {
        display: flex;
        flex-direction: column;
        gap: 20px;

        .ticket-data {
            display: flex;
            flex-direction: column;
            margin-bottom: 16px;
            font-size: 16px;
            color: #101010;

            &__label {
                font-weight: 600;
                margin-bottom: 12px;
            }

            &__value {
                font-weight: 400;
            }
        }

        .mb--2 {
            margin-bottom: -21px;
        }

        &__icon {
            display: flex;
            flex-direction: column;
            gap: 10px;

            p {
                display: flex;
                align-items: center;
                gap: 10px;
                font-size: 14px;
                font-weight: 400;
                margin: 0;
            }
        }

        &__row {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
        }

        &__field {
            width: 100%;
        }

        &__vehicle-info {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 14px;
            color: #757575;
        }

        &__drivable {
            display: flex;
            flex-direction: column;
            gap: 8px;

            span {
                font-weight: 600;
                font-size: 16px;
                color: $text-color;
            }

            mat-radio-group {
                display: flex;
                align-items: center;
                gap: 24px;
                margin-left: -8px;

                .custom-radio-button {
                    font-size: 16px;
                    font-weight: 400;
                    color: $text-color;

                    .mat-mdc-radio .mdc-radio__background .mdc-radio__outer-circle {
                        border-color: $text-color !important;
                    }

                    &.mat-mdc-radio-checked .mdc-radio__background .mdc-radio__outer-circle {
                        border-color: $text-color !important;
                    }

                    .mat-radio-inner-circle {
                        background-color: transparent;

                        &.mat-mdc-radio-inner-circle {
                            background-color: #ff1744 !important;
                        }
                    }
                }
            }
        }

        &__actions {
            display: flex;
            justify-content: center;
            gap: 20px;

            .btn {
                border-radius: 0;
                min-width: 215px;
                height: 50px;
            }
        }

        .form-group {
            margin-bottom: 20px;

            &__label {
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 8px;
                display: block;
            }

            &__textarea {
                width: 100%;
                min-height: 120px;
                padding: 10px;
                border: 1px solid #d9d9d9;
                border-radius: 4px;
                font-size: 14px;
                color: #333;
                font-family: inherit;
                resize: vertical;

                &:focus {
                    border-color: #3f51b5;
                    outline: none;
                    box-shadow: 0 0 5px rgba(63, 81, 181, 0.5);
                }
            }
        }
    }
}

:host ::ng-deep .custom-radio-button.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle {
    border: 2px solid $text-color !important;
}