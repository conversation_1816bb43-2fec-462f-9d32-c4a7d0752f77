import { Component, Output, EventEmitter, inject, OnInit } from '@angular/core';
import {
  FormGroup,
  FormControl,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CommonModule, DatePipe } from '@angular/common';
import { IconModule } from '../../../../../../../core/icon/icon.module';
import { DateTimePickerFormGroupComponent, DropdownFormGroupComponent, FormGroupComponent } from '../../../../../../../core/shared';
import { LoadingService, NotificationService } from '../../../../../../../core/services';
import { TicketsDetailService } from '../../../../../services/tickets/ticket-detail.service';
import { RequestCallLog, Ticket } from '../../../../../interfaces';
import { ActionModal, DateFormat } from '../../../../../../../core/enums';
import { OptionDropdown } from '../../../../../../../core/interfaces';


@Component({
  selector: 'app-call-log-modal',
  templateUrl: './call-log-modal.component.html',
  styleUrls: ['./call-log-modal.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    IconModule,
    TranslateModule,
    ReactiveFormsModule,
    DropdownFormGroupComponent,
    FormGroupComponent,
    TranslateModule,
    DateTimePickerFormGroupComponent
  ],
  providers: [LoadingService, NotificationService, TicketsDetailService, DatePipe],
})
export class CallLogModalComponent implements OnInit {
  data = inject(MAT_DIALOG_DATA);
  ticket: Ticket;
  dateFormat = DateFormat;
  dialogRef = inject(MatDialogRef<CallLogModalComponent>);
  datePipe = inject(DatePipe);

  @Output() formSubmit = new EventEmitter<any>();
  @Output() formCancel = new EventEmitter<void>();

  loadingService = inject(LoadingService);
  translateService = inject(TranslateService);
  ticketsService = inject(TicketsDetailService);
  notificationService = inject(NotificationService);

  callLogForm = new FormGroup({
    type: new FormControl('', Validators.required),
    callTime: new FormControl<Date | null>(null, Validators.required),
    result: new FormControl('', Validators.required),
    comment: new FormControl(''),
  });
  
  maxDate: Date = new Date();
  typeOptions: OptionDropdown[] = [];
  resultOptions: OptionDropdown[] = [];

  ngOnInit(): void {
    this.ticket = this.data.ticket;
    this.initOptions();

    if (this.data.isEdit) {
      this.patchFormValues();
    } else {
      this.initForm();
    }
  }

  initForm(): void {
    this.callLogForm = new FormGroup({
      type: new FormControl('', Validators.required),
      callTime: new FormControl(null, Validators.required),
      result: new FormControl('', Validators.required),
      comment: new FormControl(''),
    });
  }

  initOptions(): void {
    this.ticketsService
      .getCallLogMetadata(this.ticket?.ticketID)
      .subscribe((res) => {
        if (res) {
          this.typeOptions = res.types;
          this.resultOptions = res.results;
          this.resultOptions = res.results.map((result) => ({
            code: result.code,
            name: this.translateService.instant(result.name),
          }));
          if (this.data.isEdit) {
            this.patchFormValues();
          }
        }
      });
  }

  patchFormValues(): void {
    if (!this.data?.callTime) {
      console.error('Invalid date or time in data:', this.data?.callTime);
      return;
    }
    this.callLogForm.patchValue({
      type: this.data.type?.code || '',
      callTime: new Date(this.data?.callTime),
      result: this.data.result?.code || '',
      comment: this.data.comment,
    });
  }



  onSubmit(): void {
    if (this.callLogForm.valid) {
      const parsedDate = this.datePipe.transform(this.callLogForm.value.callTime, this.dateFormat.FullDate);
      const dataSend: RequestCallLog = {
        type: this.callLogForm.value.type,
        result: this.callLogForm.value.result,
        callTime: parsedDate,
        comment: this.callLogForm.value.comment,
      };
      if (this.data.isEdit) {
        this.ticketsService
          .updateCallLog(this.ticket?.ticketID, this.data.id, dataSend)
          .subscribe((res) => {
            this.loadingService.showLoader();

            this.formSubmit.emit(res);
            this.dialogRef.close({ action: ActionModal.Submit, data: res });
            this.loadingService.hideLoader();

            const toastMessage = this.translateService.instant(
              'callLog.successMessages.updateSuccess'
            );
            this.notificationService.showSuccess(toastMessage);
          });
      } else {
        this.ticketsService
          .createCallLog(this.ticket?.ticketID, dataSend)
          .subscribe((res) => {
            this.loadingService.showLoader();

            this.formSubmit.emit(res);
            this.dialogRef.close({ action: ActionModal.Submit, data: res });
            this.loadingService.hideLoader();

            const toastMessage = this.translateService.instant(
              'callLog.successMessages.createSuccess'
            );
            this.notificationService.showSuccess(toastMessage);
          });
      }
    } else {
      this.callLogForm.markAllAsTouched();
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  formatDate(dateString: string, timeString: string): string {
    const date = new Date(dateString);
    const [hours, minutes] = timeString.split(':');

    // Update hours and minutes in the date object
    date.setHours(parseInt(hours, 10));
    date.setMinutes(parseInt(minutes, 10));

    // Format the date as MM/dd/yyyy hh:mm
    const formattedDate = [
      String(date.getMonth() + 1).padStart(2, '0'), // Month is 0-indexed
      String(date.getDate()).padStart(2, '0'),
      date.getFullYear(),
    ].join('/');

    const formattedTime = [
      String(date.getHours()).padStart(2, '0'),
      String(date.getMinutes()).padStart(2, '0'),
    ].join(':');

    return `${formattedDate} ${formattedTime}`;
  }
}
