import { Component, Input } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { CommonModule } from '@angular/common';
import { IconModule } from '../../../../../../core/icon/icon.module';

@Component({
  selector: 'app-warning-card',
  standalone: true,
  imports: [IconModule, MatButtonModule, CommonModule],
  templateUrl: './warning-card.component.html',
  styleUrls: ['warning-card.component.scss'],
})
export class WarningCardComponent {
  @Input() warningTitle: string = 'Warning';
  @Input() warningDescription: string = 'Airbag warning';
  @Input() warningDate: string = '11/12/2024';
  @Input() warningTime: string = '10:28';
  @Input() priority: string = 'High';
  @Input() status: string = 'Active';
}
