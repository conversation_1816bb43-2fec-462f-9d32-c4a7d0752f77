<h2 class="title-dialog">{{ 'tickets.ticketDetail.editModalInfo.title' | translate }}</h2>
<div class="container-dialog">
    <form class="edit-ticket-form" [formGroup]="ticketForm" (ngSubmit)="onSubmit()">
        <div class="edit-ticket-form__row">
            <div class="ticket-data">
                <label class="ticket-data__label">{{ 'tickets.ticketDetail.editModalInfo.ticketId' | translate }}</label>
                <p class="ticket-data__value">{{data.ticketID}}</p>
            </div>

            <app-dropdown-form-group [label]="'tickets.filter.type' | translate" [control]="ticketForm.controls.type"
                [options]="typeOptions" [required]="true"></app-dropdown-form-group>

                @if(this.isSecurityIssues) {
                    <app-dropdown-form-group
                        [label]="'tickets.ticketDetail.subType' | translate"
                        [control]="ticketForm.controls.subType"
                        [options]="subTypeOption"
                        [required]="true"
                    ></app-dropdown-form-group>
                } @else {
                    <app-dropdown-form-group [label]="'tickets.filter.status' | translate"
                        [control]="ticketForm.controls.status" [options]="statusOptions"
                        [required]="true">
                    </app-dropdown-form-group>
                }
            
        </div>

        @if(this.isSecurityIssues) {
            <app-dropdown-form-group [label]="'tickets.filter.status' | translate"
                [control]="ticketForm.controls.status" [options]="statusOptions"
                [required]="true">
            </app-dropdown-form-group>
        }
        
        <app-form-group [label]="'tickets.ticketDetail.title' | translate" [control]="ticketForm.controls.title"
            controlId="title" [required]="true"
            [errorMessage]="'common.requiredField' | translate:{ field: ('tickets.filter.ticketIdOrTitle' | translate) }"></app-form-group>

        <app-form-group [label]="'tickets.ticketDetail.editModalInfo.vehiclePlate' | translate" class="mb--2"
            [control]="ticketForm.controls.vehiclePlate" controlId="vehiclePlate" [required]="false"
            [errorMessage]="'common.requiredField' | translate:{ field: ('tickets.ticketList.plate' | translate) }"></app-form-group>

        <div class="edit-ticket-form__icon">
            <p>
                <mat-icon matSuffix svgIcon="ic-vin"></mat-icon>
                {{data.vin}}
            </p>
            <p>
                <mat-icon matSuffix svgIcon="ic-car"></mat-icon>
                {{data.vehicleModel}}
            </p>
            <p>
                <mat-icon matSuffix svgIcon="ic-bag"></mat-icon>
                {{data.vehicleType}}
            </p>
        </div>

        <div class="edit-ticket-form__row">
            <app-dropdown-form-group [label]="'tickets.filter.priority' | translate"
                [control]="ticketForm.controls.priority" [options]="priorityOptions"
                [required]="true" 
                (changeOption)="changePriority($event)"></app-dropdown-form-group>

            <app-autocomplete-input [label]="'tickets.filter.assignee' | translate" [controlId]="'assignee'"
                [placeholder]="'enterHereToSearch' | translate" [suggestions]="assigneeOptions"
                [control]="ticketForm.controls.assignee">
            </app-autocomplete-input>

            <div class="edit-ticket-form__drivable">
                <span>{{ 'tickets.ticketList.drivable' | translate }}</span>
                <mat-radio-group formControlName="drivable">
                    <mat-radio-button class="custom-radio-button" [value]="true">
                        {{ 'common.yes' | translate }}
                    </mat-radio-button>
                    <mat-radio-button class="custom-radio-button" [value]="false">
                        {{ 'common.no' | translate }}
                    </mat-radio-button>
                </mat-radio-group>
            </div>
        </div>

        <div class="form-group">
            <label for="description" class="form-group__label">
                {{ 'tickets.filter.description' | translate }}
            </label>
            <textarea id="description" class="form-group__textarea" formControlName="description"
                placeholder="{{ 'tickets.ticketList.titleEdit' | translate }}"></textarea>
        </div>

        <div class="action-dialog edit-ticket-form__actions">
            <button type="button" class="btn btn--outline cancel-btn" (click)="onCancel()">
                {{ 'common.cancel' | translate }}
            </button>
            <button class="btn btn--primary" type="submit" [disabled]="ticketForm.invalid">
                {{ 'common.edit' | translate }}
            </button>
        </div>
    </form>
</div>
