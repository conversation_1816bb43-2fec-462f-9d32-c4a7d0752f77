import { ResponsePaging } from "../../../core/interfaces";


export interface VehicleModelListRequest {
  currentPage: number;
  pageSize: number;
  modelSaleCode: string;
  modelName: string;
  show: string;
}

export interface VehicleModelInfo {
  modelSaleCode: string;
  modelName: string;
  createdDate: string;
  modifiedDate: string;
}

export interface VehicleModelList {
  type: string;
  isShowAllAllowed: boolean;
  isShowPageAllowed: boolean;
  pagination: ResponsePaging;
  items: VehicleModelInfo[];
}

export interface AddVehicleModel {
  modelSaleCode: string;
  modelName: string;
}
