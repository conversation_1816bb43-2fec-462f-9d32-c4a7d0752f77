import { ResponsePaging } from "../../../core/interfaces";

export interface WarningItem {
  name: string;
  relatedTicketId: string;
  relatedTicketTitle: string;
  priority: { code: string; name: string };
  status: { code: string; name: string };
  timestamp: string;
}

export interface WarningList {
  pagination: ResponsePaging;
  items: WarningItem[];
}

export interface WarningListRequest {
  warningName: string;
  vin: string;
  currentPage?: number;
  pageSize?: number;
  ticketId?: string;
  type?: string;
}

export interface WarningDetail {
  id: string;
  name: string;
  type: string;
  priority: { code: string; name: string };
  status: { code: string; name: string };
  relatedTicketId: string;
  relatedTicketTitle: string;
  timestamp?: string;
  customerAction?: string;
  details?: string;
  mid?: string;
  notificationPromptCustAction?: string;
  notificationPromptDesc?: string;
  notificationPromptTitle?: string;
  responseLeadTime?: string;
  code: string;
  safeCondition: string;
  vehicleCondition: boolean;
  notifyCustomer: boolean;
  requiredDealerVisit?: boolean;
  callCustomer: boolean;
  warningType?: string;
}

export interface WarningMasterListRequest {
  warningName: string;
  warningCode: string;
  currentPage?: number;
  pageSize?: number;
}

export interface WarningMasterItem {
  name: string;
  code: string;
  details: string;
  priority: { code: string; name: string };
  type: { code: string; name: string };
}

export interface WarningMasterList {
  pagination: ResponsePaging;
  items: WarningMasterItem[];
}