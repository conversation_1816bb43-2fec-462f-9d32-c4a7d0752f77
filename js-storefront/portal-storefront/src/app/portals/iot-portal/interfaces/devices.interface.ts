import { OptionDropdown, ResponseCommon, ResponsePaging } from "../../../core/interfaces";
import { CanActiveForSim } from "../enums";


export interface ResponseDeviceList {
  isShowAllAllowed: boolean;
  isShowPageAllowed: boolean;
  items: ItemDevice[];
  numberOfNotification: number;
  pagination: ResponsePaging;
  type: string;
}

export interface ItemDevice {
  deviceId?: string;
  deviceStatus?: ItemStatus;
  gdcmDeviceStatus?: ItemStatus;
  gdcmDeviceStatusText?: string;
  iccid?: string;
  imei?: string;
  importedDate?: string;
  imsi?: string;
  pairedBy?: string;
  pairedOn?: string;
  pairingStatus?: ItemStatus;
  simStatus?: ItemStatus;
  type?: ItemStatus;
  vin?: string;
  signalTestingStatus?: string;
  statusEnum?: ItemStatus;
  activationStatus?: ItemStatus;
  chasissNum?: string; // vin
  canActiveFor?: CanActiveForSim;
  importFile?: {
    code?: string;
    realFileName?: string;
    realFileNameWithoutExt?: string;
    suffix?: string;
    downloadUrl?: string;
    mime?: string;
    url?: string;
  }
}

export interface ItemDeviceWidget {
  deviceId?: string;
  deviceStatus?: ItemStatus;
  simStatus?: ItemStatus;
  activationStatus?: ItemStatus
}



export interface ItemStatus {
  code?: string;
  name?: string;
}

export interface PayloadGetDevice {
  currentPage?: number;
  pageSize?: number;
  pairingStatus?: string;
  simStatus?: string;
  activationStatus?: string;
  deviceStatus?: string;
  idOrVin?: string;
  includeHistory?: boolean;
}

export interface FilterDevice {
  activationStatusList: OptionDropdown[];
  deviceStatusList: OptionDropdown[];
  pairingStatusList: OptionDropdown[];
  simStatusList: OptionDropdown[];
}

export interface ResponseEnableSim extends ResponseCommon {
  simId: string;
  errors?: {
    message?: string;
    type?: string;
  }[];
  result?: string;
}
