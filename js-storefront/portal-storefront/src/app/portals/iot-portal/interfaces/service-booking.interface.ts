import { OptionDropdown, ResponsePaging } from '../../../core/interfaces';
import { DealerItem } from './ticket-detail.interface';

export interface ItemServiceBooking {
  bookingNo?: string;
  status?: OptionDropdown;
  vin?: string;
  bookingDate?: Date | string;
  bookingTime?: string;
  waitPickupAtDealer?: boolean;
  pickupDate?: Date | string;
  pickupTime?: string;
  bookingReasons?: string;
  odo?: string;
  remark?: string;
  dealerText?: string;
  dealer?: {
    name?: string;
    displayName?: string;
    phoneNumber?: string;
    defaultEmail?: string;
    addressDisplay?: string;
    distanceToTargetPoint?: number;
  };
  dbmBookingNo?: string;
  bookingReason?: OptionDropdown;
}

export interface ResponseServiceBookingList {
  items?: ItemServiceBooking[];
  pagination: ResponsePaging;
  isShowAllAllowed: boolean;
  isShowPageAllowed: boolean;
  type?: string;
}

export interface AvailableDates {
  availableBookingDays: string;
  unavailableBookingDays: string;
}

export interface MetadataServiceBooking {
  dealers: DealerItem[];
  bookingReasons: OptionDropdown[];
  cancelReasons: OptionDropdown[];
  preferDealer?: DealerItem;
}

export interface ServiceBookingDetail {
  bookingNo: string;
  status: OptionDropdown;
  vin: string;
  bookingDate: string;
  bookingTime: string;
  waitPickupAtDealer: true;
  pickupDate: string;
  pickupTime: string;
  bookingReasons: string;
  odo: string;
  remark?: string;
  dealerText?: string;
  dealer: DealerItem;
  cancelReason?: OptionDropdown;
  cancelReasonDescription?: string;
  bookingReason?: OptionDropdown;
  dbmBookingNo?: string;
}
