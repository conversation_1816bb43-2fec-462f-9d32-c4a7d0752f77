
import { OptionDropdown, ResponsePaging } from '../../../core/interfaces';
import { ManagerApprovalAction, ManagerApprovalStatus } from '../enums';
import { Ticket } from './ticket-detail.interface';

export interface ResponseTicketList {
  type: string;
  items: ItemTicket[];
  pagination: ResponsePaging;
  isShowAllAllowed: boolean;
  isShowPageAllowed: boolean;
}

export interface FilterTickets {
  priorityList: OptionDropdown[];
  statusList: OptionDropdown[];
  typeList: OptionDropdown[];
  searchTypeList: OptionDropdown[];
  subTypeList: OptionDropdown[];
}

export interface PayloadGetTicketList {}

export interface ItemTicket {
  ticketID: string;
  status: OptionDropdown;
  priority: OptionDropdown;
  assignedAgent: string;
  isDrivable: boolean;
  description: string;
  type: OptionDropdown;
  title: string;
  plateNumber: string;
  vin: string;
  vehicleType: string;
  customerName: string;
  mobileNumber: string;
}

export interface Assignee {
  name: string;
  username: string;
}

export interface UpdateTicketPayload {
  title: string;
  status: { code: string; name: string };
  priority: { code: string; name: string };
  description: string;
  isDrivable: boolean;
  assignee: string;
}

export interface TicketWidget {
  type: string;
  tickets: Ticket[];
  vin: string;
}

export interface VehiclePlateInfo {
  vehicleImage?: string;
  vehicleModel?: string;
  vehicleColor?: string;
  plateNumber?: string;
  odo?: string;
  engineStatus?: string;
  fuelRemaining?: string;
  map?: string;
  currentAddress?: string;
  dateAndTime?: string;
  longitude?: string;
  latitude?: string;
  sim?: string;
  vin?: string;
  vehicleMake?: string;
  vehicleType?: string;
  vehicleSubType?: string;
}

export interface ResponseList<T> {
  type: string;
  items: T[];
  pagination: ResponsePaging;
  isShowAllAllowed: boolean;
  isShowPageAllowed: boolean;
}

export interface ResponseRequestInfo {
  action: ManagerApprovalAction;
  comment: string;
  requestBy: string;
  requestDate: string;
  status: ManagerApprovalStatus;
  approvedDate?: string;
  approvedBy?: string;
}
