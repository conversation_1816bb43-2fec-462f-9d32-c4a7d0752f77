export interface DashBoardItemSubscription {
  vehicle?: string;
  plateNumber?: string;
  vehicleType?: string;
  package?: string;
  endDate?: string;
}

export interface DashBoardListItemSubscription {
  items: DashBoardItemSubscription;
}

export interface ItemChart {
  value: number;
  name: string;
  code?: string;
}

export interface SpecialServiceCampaignDueItem {
  make?: string;
  plateNo?: string;
  ssc?: string;
  validDate?: string;
  status?: string;
}

export interface NonOperationalDevice {
  make?: string;
  plateNo?: string;
  status?: string;
}

export interface ServiceBookingDashboard {
  serviceBookingDealer?: string;
  serviceBookingOn?: string;
  serviceBookingReason?: string;
  serviceBookingStatus?: string;
  vehicleModel?: string;
  vin?: string;
  hasActiveCustomerRelation?: string;
  serviceBookingNo?: string;
}

export interface PMSDashboardItem {
  plateNumber?: string;
  vehicleModel?: string;
  vin?: string;
}
