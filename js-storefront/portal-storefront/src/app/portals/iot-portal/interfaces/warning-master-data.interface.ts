import { OptionDropdown } from "../../../core/interfaces";

export interface WarningMasterDataDetail {
  actionCallCenter?: string;
  actionCustomer?: string;
  addInfo?: string;
  code?: string;
  details?: string;
  flagDist?: boolean;
  notificationNecessity?: boolean;
  isCallCustomer?: boolean;
  isNotifyCustomer?: boolean;
  name?: string;
  priority?: OptionDropdown | any;
  requiredDealerVisit?: boolean;
  responseLeadTime?: string;
  safeCon?: string;
  type?: OptionDropdown | any;
  vehicleCon?: boolean;
  notificationPromptTitle?: string;
  notificationPromptDesc?: string;
  notificationPromptAction?: string;
  icon?: string;
}
