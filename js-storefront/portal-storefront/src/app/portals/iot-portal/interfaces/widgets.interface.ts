import { ResponsePaging } from '../../../core/interfaces';
import { ItemStatus } from './devices.interface';

export interface ItemSubscription {
  endDate?: string;
  id?: string;
  isFreeSubscription?: boolean;
  packageType?: string;
  paidBy?: string;
  simStatus?: ItemStatus;
  startDate?: string;
  subscriptionPaymentStatus?: ItemStatus;
  subscriptionStatus?: ItemStatus;
  type?: ItemStatus;
  features?: FeatureSubscription[];
  canConfirmPayment?: boolean;
  canActivate?: boolean;
}

export interface FeatureSubscription {
  code: string;
  enable: boolean;
  name: string;
}

export interface ResponseSubscriptionList {
  items: ItemSubscription[];
  pagination: ResponsePaging;
}

export interface DiagnosticsTrigger {
  triggerType?: number;
  counterValue?: number;
  diagnosticsAcquisitionTime?: number;
  warningTriggerOccurrenceTime?: number;
}

export interface VehicleInformation {
  lastLocation?: string;
  obd2InstalledFlag?: boolean;
  odoInformationKm?: number;
  odoInformationMile?: string;
}

export interface DTCInfo {
  dtcCode?: string;
  dtcValue?: string;
  friendlyName?: string;
  status?: string;
}

export interface SIDInfo {
  sid?: string;
  sf?: string;
  dtc?: DTCInfo[];
}

export interface DiagnosticsDetails {
  ecuAddress?: string;
  nta?: string;
  diagnosticsPhase?: number;
  ecuPhaseId?: string;
  communicationProtocol?: number;
  communicationType?: number;
  nodeFriendlyName?: string;
  odxVersion?: string;
  sid?: SIDInfo[];
}

export interface DTCWidget {
  diagnosticsTrigger?: DiagnosticsTrigger;
  vehicleInformation?: VehicleInformation;
  diagnosticsDetails?: DiagnosticsDetails[];
}

export interface OwnershipVerificationWidget {
  requestedDate?: string,
  verified?: boolean,
  verifyRemainingDays?: number;
}

export interface OwnerWidget {
  type?: string;
  address?: string;
  mobileNumber?: string;
  emailAddress?: string;
  isEmailVerified?: boolean;
  name?: string;
}
