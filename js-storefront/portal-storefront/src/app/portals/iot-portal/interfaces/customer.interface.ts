import { ResponsePaging } from "../../../core/interfaces";

export interface CustomerItem {
  vin: string;
  plateNo?: string;
  customerName: string;
  vehicleModel?: string;
  mobile?: string;
  vehicleMake?: string;
  make?: string;
  model?: string;
  emergencyCreateTime?: string | null;
  emergencyName?: string | null;
  numOfEmergency: number;
  warningCreateTime?: string | null;
  warningName?: string | null;
  numOfWarning: number;
  numOfSSC: number;
  sscCategory?: string;
  sscExpiryDate?: string | null;
  warningOccurredTime?: string;
  warningPriority?: string;
  nextMileage: number;
  warningIcon?: string;
}

export interface CustomerResponse {
  items: CustomerItem[];
  pagination: ResponsePaging;
}
