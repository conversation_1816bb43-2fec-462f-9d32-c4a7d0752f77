import { DeviceSummaryTab } from '../enums';

export const LDCM_WIDGETS = [
  {
    id: DeviceSummaryTab.WaitingForPairing,
    description: 'deviceManagement.deviceWaitingForPairing',
    icon: 'ic-repair',
  },
  {
    id: DeviceSummaryTab.WaitingForEnable,
    description: 'deviceManagement.simWaitingForEnable',
    icon: 'ic-sim-1',
  },
  {
    id: DeviceSummaryTab.NonOperational,
    description: 'deviceManagement.nonOperationalDevices',
    icon: 'ic-non-operational',
  },
];

export const GDCM_WIDGETS = [
    {
      id: DeviceSummaryTab.WaitingForTerminated,
      description: 'deviceManagement.deviceWaitingForTerminated',
      icon: 'ic-link-pair',
    },
    {
      id: DeviceSummaryTab.NonOperationalGDCM,
      description: 'deviceManagement.nonOperationalDevices',
      icon: 'ic-sim',
    },
    {
      id: DeviceSummaryTab.OperationalDevices,
      description: 'deviceManagement.operationalDevices',
      icon: 'ic-non-operational',
    },
  ];
