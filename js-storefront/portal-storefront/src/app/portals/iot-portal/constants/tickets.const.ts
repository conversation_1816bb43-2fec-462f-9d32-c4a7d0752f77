import { MatDateFormats } from "@angular/material/core";
import { AssigneeTicket, CallResult, CallType, TicketSummaryTab } from "../enums";

export const TICKET_WIDGETS = [
  {
    id: TicketSummaryTab.HighPriorityTickets,
    description: 'tickets.highPriorityTickets',
    icon: 'ic-warning',
  },
  {
    id: TicketSummaryTab.UnassignedTickets,
    description: 'tickets.unassignedTickets',
    icon: 'ic-document',
  },
  {
    id: TicketSummaryTab.MyOpenTickets,
    description: 'tickets.myOpenTickets',
    icon: 'ic-car-1',
  },
];

export const TICKET_ASSIGNEE = [
  {
    code: AssigneeTicket.All,
    name: 'All'
  },
  {
    code: AssigneeTicket.Unassigned,
    name: 'Unassigned'
  },
]

export const CallTypeOptions = Object.entries({
  IN_CAL: 'Incoming Call',
  OUT_CAL: 'Outgoing Call',
}).map(([code, name]) => ({ code, name }));


export const CallResultOptions = Object.entries({
  UNABLE_REACH: 'Unable to reach customer',
  CUSTOMER_REQUEST_CALL_BACK: 'Customer Request Call Back',
  INFORMATION_PROVIDED: 'Information Provided',
  OTHER: 'Other',
}).map(([code, name]) => ({ code, name }));

export const DATE_FORMATS: MatDateFormats = {
  parse: {
    dateInput: 'MM/DD/YYYY',
  },
  display: {
    dateInput: 'MM/DD/YYYY',
    monthYearLabel: 'MMM YYYY',
    dateA11yLabel: 'MM/DD/YYYY',
    monthYearA11yLabel: 'MMMM YYYY',
  },
};