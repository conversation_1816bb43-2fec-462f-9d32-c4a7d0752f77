import { DashboardWidget } from "../enums";

export const CALLCENTERGROUP_WIDGETS = [
	{
		id: 'unassignedTicket',
		count: 12,
		description: 'tickets.unassignedTickets',
		icon: 'ic-warning',
	},
	{
		id: 'highPriorityTickets',
		count: 3,
		description: 'tickets.highPriorityTickets',
		icon: 'ic-priority',
	},
	{
		id: 'assignedWarningTickets',
		count: 5,
		description: 'tickets.assignedWarningTickets',
		icon: 'ic-document',
	},
	{
		id: 'vehicleWithSos',
		count: 3,
		description: 'vehicleManagement.vehiclesEmergency',
		icon: 'ic-repair',
	},
];

export const IOTSERVICEADVISORGROUP_WIDGETS = [
	{
		id: 'assignedTickets',
		count: 12,
		description: 'tickets.assignedTickets',
		icon: 'ic-document',
	},
	{
		id: 'highPriorityTickets',
		count: 2,
		description: 'tickets.highPriorityTickets',
		icon: 'ic-priority',
	},
	{
		id: 'attendedTickets',
		count: 5,
		description: 'tickets.attendedTickets',
		icon: 'ic-customer',
	},
	{
		id: 'vehiclesSOS',
		count: 6,
		description: 'vehicleManagement.vehiclesEmergency',
		icon: 'ic-repair',
	},
]

export const TMSPHMANAGERGROUP_WIDGETS = [
	{
		id: 'calPendingPaymentConfirmation',
		count: 12,
		description: 'dashboard.widgets.pendingPayment',
		icon: 'ic-clock',
	},
	{
		id: 'calVehicleDueForRenewal',
		count: 6,
		description: 'dashboard.widgets.vehicleDue',
		icon: 'ic-warning-1',
	},
	{
		id: 'calLoansAboutToClose',
		count: 4,
		description: 'dashboard.widgets.loansToClose',
		icon: 'ic-plane',
	},
	{
		id: 'calWithoutLoanInfo',
		count: 10,
		description: 'dashboard.widgets.loanInfo',
		icon: 'ic-info',
	},
]

export const DOSDHELPDESKGROUP_WIDGETS = [
	{
		id: 'ldcmSimPendingForActivation',
		count: 12,
		description: 'dashboard.widgets.simPending',
		icon: 'ic-sim',
	},
	{
		id: 'ldcmDevicePendingForActivation',
		count: 4,
		description: 'dashboard.widgets.devicePendingActivation',
		icon: 'ic-clock',
	},
	{
		id: 'ldcmDevicePendingForCancellation',
		count: 7,
		description: 'dashboard.widgets.devicePendingCancelation',
		icon: 'ic-close-circle',
	},
]

export const TMTGROUP_WIDGETS = [
	{
		id: 'waitingForPairing',
		count: 12,
		description: 'dashboard.widgets.devicesPairing',
		icon: 'ic-link-pair',
	},
	{
		id: 'waitingForEnable',
		count: 3,
		description: 'dashboard.widgets.SIMEnable',
		icon: 'ic-sim',
	},
	{
		id: 'nonOperationalDevices',
		count: 6,
		description: 'dashboard.widgets.nonOperationalDevices',
		icon: 'ic-stop',
	},
]


export const DASHBOARD_WIDGET = [
	// CALLCENTERGROUP_WIDGETS
	{
		id:  DashboardWidget.UnassignedTicket,
		description: 'tickets.unassignedTicketsDashboard',
		icon: 'ic-warning',
	},
	{
		id:  DashboardWidget.HighPriorityTickets,
		description: 'tickets.highPriorityTicketsDashboard',
		icon: 'ic-priority',
	},
	{
		id:  DashboardWidget.AssignedWarningTickets,
		description: 'tickets.assignedWarningTickets',
		icon: 'ic-document',
	},
	{
		id:  DashboardWidget.VehicleWithSos,
		description: 'vehicleManagement.vehiclesEmergency',
		icon: 'ic-repair',
	},
	// IOTSERVICEADVISORGROUP_WIDGETS
	{
		id:  DashboardWidget.AssignedTickets,
		description: 'tickets.assignedTickets',
		icon: 'ic-document',
	},
	{
		id:  DashboardWidget.HighPriorityTickets,
		description: 'tickets.highPriorityTicketsDashboard',
		icon: 'ic-priority',
	},
	{
		id:  DashboardWidget.AttendedTickets,
		description: 'tickets.attendedTickets',
		icon: 'ic-customer',
	},
	{
		id:  DashboardWidget.VehiclesSOS,
		description: 'vehicleManagement.vehiclesEmergency',
		icon: 'ic-repair',
	},

	// TMSPHMANAGERGROUP_WIDGETS
	{
		id:  DashboardWidget.CalPendingPaymentConfirmation,
		description: 'dashboard.widgets.pendingPayment',
		icon: 'ic-clock',
	},
	{
		id:  DashboardWidget.CalVehicleDueForRenewal,
		description: 'dashboard.widgets.vehicleDue',
		icon: 'ic-warning-1',
	},
	{
		id:  DashboardWidget.CalLoansAboutToClose,
		description: 'dashboard.widgets.loansToClose',
		icon: 'ic-plane',
	},
	{
		id:  DashboardWidget.CalWithoutLoanInfo,
		description: 'dashboard.widgets.loanInfo',
		icon: 'ic-info',
	},

	// DOSDHELPDESKGROUP_WIDGETS
	{
		id:  DashboardWidget.LdcmSimPendingForActivation,
		description: 'dashboard.widgets.simPending',
		icon: 'ic-sim',
	},
	{
		id:  DashboardWidget.LdcmDevicePendingForActivation,
		description: 'dashboard.widgets.devicePendingActivation',
		icon: 'ic-clock',
	},
	{
		id:  DashboardWidget.LdcmDevicePendingForCancellation,
		description: 'dashboard.widgets.devicePendingCancelation',
		icon: 'ic-close-circle',
	},

	// TMTGROUP_WIDGETS
	{
		id:  DashboardWidget.WaitingForPairing,
		description: 'dashboard.widgets.devicesPairing',
		icon: 'ic-link-pair',
	},
	{
		id:  DashboardWidget.WaitingForEnable,
		description: 'dashboard.widgets.SIMEnable',
		icon: 'ic-sim',
	},
	{
		id: DashboardWidget.NonOperationalDevices,
		description: 'dashboard.widgets.nonOperationalDevices',
		icon: 'ic-stop',
	},
]
