import { inject, Pipe, PipeTransform } from '@angular/core';
import { ItemDevice } from '../interfaces';
import { TranslateService } from '@ngx-translate/core';
import { getDeviceAction } from '../helpers';
import { UserService } from '../../../core/services/user';
@Pipe({
  name: 'deviceAction',
  standalone: true,
})
export class DeviceActionPipe implements PipeTransform {
  translate = inject(TranslateService);
  userService = inject(UserService);
  transform(
    itemDevice: ItemDevice,
    isLDCM: boolean
  ): { text: string; icon: string } {
    const action = getDeviceAction(itemDevice, isLDCM, this.userService);

    return {
      text: this.translate.instant(action?.text),
      icon: action?.icon,
    };
  }
}
