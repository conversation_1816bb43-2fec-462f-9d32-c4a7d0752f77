import { inject, Pipe, PipeTransform } from '@angular/core';
import { TypeTicket } from '../enums';
import { TranslateService } from '@ngx-translate/core';
import { OptionDropdown } from '../../../core/interfaces';

@Pipe({
  name: 'getTypeTicket',
  standalone: true,
})
export class TypeTicketPipe implements PipeTransform {
  translateService = inject(TranslateService);
  transform(value: OptionDropdown): string {
    return value?.code === TypeTicket.SVT ||
      value?.code === TypeTicket.IMMOBILISER
      ? this.translateService.instant('tickets.ticketDetail.securityIssues')
      : value?.name;
  }
}
