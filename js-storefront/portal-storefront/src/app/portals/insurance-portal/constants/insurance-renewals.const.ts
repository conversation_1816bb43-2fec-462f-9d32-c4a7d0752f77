export const ID_WIDGET_PENDING_POLICIES = 'pendingPolicies';
export const ID_WIDGET_FAILED_TO_ASSIGN_POLICIES = 'failedToAssignPolicies';
export const ID_WIDGET_EXPIRING_SOON_POLICIES = 'expiringSoonPolicies';
export const ID_WIDGET_TO_BE_APPROVED = 'toBeApprovedPolicies';
export const ID_WIDGET_COMPLETED = 'completedPolicies';
export const MONTH_NUMBER = 24;
export const SORT_BY_CODE_INSURANCE_NO = 'insuranceNo';
export const SORT_BY_CODE_STATUS = 'status';

export const COORDINATOR_INSURANCE_RENEWALS_WIDGETS = [
  {
    id: ID_WIDGET_PENDING_POLICIES,
    description: 'newVehicleInsurance.pendingPolicies',
    icon: 'ic-pending-policies',
    count: 0,
  },
  {
    id: ID_WIDGET_FAILED_TO_ASSIGN_POLICIES,
    description: 'newVehicleInsurance.failedToAssignPolicies',
    icon: 'ic-failed-assign-policies',
    count: 0,
  },
  {
    id: ID_WIDGET_EXPIRING_SOON_POLICIES,
    description: 'newVehicleInsurance.expiringSoonPolicies',
    icon: 'ic-expiring-soon-policies',
    count: 0,
  },
];

export const PARTNER_INSURANCE_RENEWALS_WIDGETS = [
  {
    id: ID_WIDGET_TO_BE_APPROVED,
    description: 'newVehicleInsurance.toBeApprovedPolicies',
    icon: 'ic-pending-policies',
    count: 0,
  },
  {
    id: ID_WIDGET_COMPLETED,
    description: 'newVehicleInsurance.completedPolicies',
    icon: 'ic-failed-assign-policies',
    count: 0,
  },
  {
    id: ID_WIDGET_EXPIRING_SOON_POLICIES,
    description: 'newVehicleInsurance.expiringSoonPolicies',
    icon: 'ic-expiring-soon-policies',
    count: 0,
  },
];

export const OWNER_TYPE = {
  OWNER: 'OWNER',
  DRIVER: 'DRIVER',
  ASSIGNEE: 'ASSIGNEE',
};
