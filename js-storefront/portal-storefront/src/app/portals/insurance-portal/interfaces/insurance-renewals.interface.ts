import { OptionDropdown, ResponsePaging } from "../../../core/interfaces";
import { InsuranceQuotation, ResponseCustomerInformation, ResponseVehicleInformation } from "./new-vehicle-insurance-detail.interface";

export interface RequestInsuranceRenewalsList {
  vin?: string;
  dealer?: string;
  status?: string;
  month?: string;
  customerName?: string;
  currentPage?: number;
  pageSize?: number;
  ticketId?: string;
}

export interface InsuranceRenewalsItem {
  approvalDate : string;
  creationSource : string;
  customerName : string;
  insurancePartnerName : string;
  requestedDate : string;
  status : { code : string };
  vehicle : string;
}

export interface ResponseInsuranceRenewalsList {
  type: string;
  pagination: ResponsePaging;
  items: InsuranceRenewalsItem[];
}

export interface FilterInsuranceRenewals {
  statusList: OptionDropdown[];
}

export interface CreateInsuranceData {
  insuranceInfo : {
    customerInfo: ResponseCustomerInformation;
    vehicleInfo: ResponseVehicleInformation;
  }
  quotationData: InsuranceQuotation;
}

export interface ResponseAddInsurance {
  code: string;
  message: string;
  canAddInsurance: boolean;
  hasExisting: boolean;
  existingInsurance?: {
    policyCode: string;
    expireDate: string;
    insuranceCompany?: string;
  };
  insuranceData?: CreateInsuranceData;
}

export interface RequestAddOffMarketInsurance {
  ePolicy: File,
  insuranceConsent: File,
  vin: string,
  policyCode: string,
  insuranceCompanyOM: string,
  insuranceCompanyCodeOM: string,
  startDate: string,
  expiredDate: string,
  telematicInsuranceOptionOM: string,
  consentDate: string
}

export interface InsuranceCompany {
  code: string,
  type: {
      code: string,
      name: string
  },
  name: string,
  supportTelematic: boolean,
  supportIntegration: boolean
}
