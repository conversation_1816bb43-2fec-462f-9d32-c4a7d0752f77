import { OptionDropdown } from "../../../core/interfaces";

export interface Documents {
  code?: string;
  creationTime?: number | any; // This is milliseconds
  realFileName?: string;
  downloadUrl?: string;
  url?: string;
  suffix?: string;
  realFileNameWithoutExt?: string;
  containerId?: string;
}

export interface InsuranceQuotation {
  installmentPayment?: boolean;
  installmentMonths?: number;
  authenticationCost: number;
  coverageAonIssuing: number;
  coverageAutoPersonalAccident: number;
  coverageExcessBodilyInjury: number;
  coverageLossOfUse: number;
  coverageOdIssuing: number;
  coveragePersonalAccident: number;
  coveragePropertyDamage: number;
  coverageRiotStrikeCivilCommotion: number;
  coverageRoadsideAssitance: number;
  coverageThirdPartyLiability: number;
  coverageVehicleAccessories: number;
  docsAmount: number;
  docsRate: number;
  grossPremium: {
    currencyIso: string;
    value: number;
  };
  grossPremiumEachVehicle: {
    currencyIso: string;
    value: number;
  };
  installmentEachMonth: {
    currencyIso: string;
    value: number;
  };
  localGovernmentStamp: number;
  localGovernmentStampRate: number;
  netPremium: number;
  noOfVehicles: number;
  premiumOdIssuingRate: number;
  premiumAonIssuing: number;
  premiumAonIssuingRate: number;
  premiumAutoPersonalAccident: number;
  premiumExcessBodilyInjury: number;
  premiumLossOfUse: number;
  premiumOdIssuing: number;
  premiumPersonalAccident: number;
  premiumPropertyDamage: number;
  premiumRiotStrikeCivilCommotion: number;
  premiumRiotStrikeCivilCommotionRate: number;
  premiumRoadsideAssitance: number;
  premiumThirdPartyLiability: number;
  premiumVehicleAccessories: number;
  totalCoverage: {
    currencyIso: string;
    value: number;
  };
  totalPremium: {
    currencyIso: string;
    value: number;
  };
  vatAmount: number;
  vatRate: number;
  policyCode: string;
  policyDate: string;
  policyExpiryDate: string;
}

export interface ResponseNewVehicleInsuranceDetail {
  code: string;
  consentDate: string;
  customerUid: string;
  cancellationReason?: string;
  documents: Documents[];
  EPolicyDocs: Documents[];
  alreadyAssigned?: boolean;
  alreadyReleased?: boolean;
  expireDate: string; // This is in milliseconds
  insuranceCoverage: string;
  insurancePartnerCode: string;
  insuranceQuotations: InsuranceQuotation;
  startDate: number; // This is in milliseconds
  status: string;
  transactionType: string;
  typeOfInsurance: string;
  vehicleCode: string;
  vinPaired: boolean;
  scheme: string;
  dateOfPolicy?: string;
  policyCode?: string;
  insuranceCost?: {
    currencyIso: string;
    value: string;
  };
  editable?: boolean;
  uploadInsuranceForm?: Documents[];
  insuranceCombinedQuotation?: InsuranceQuotationCTPL;
  needToNotifyCustomer?: boolean;
}

export interface ResponseCustomerInformation {
  addressDisplay: string;
  customerBirthDate: string;
  customerEmail: string;
  customerGroup: {
    code: string;
    name: string;
  };
  customerName: string;
  customerSex: {
    code: string;
    name: string;
  };
  phone: string;
  uid: string;
  ownership?: {
    code: string;
    name: string;
  };
  relationName?: string;
  insuredData?: {
    firstName: string;
    lastName: string;
    ownerType?: string;
    relationName?: string;
    relationCode?: string;
  }
}

export interface ResponseInsurerPartner {
  company?: {
    code?: string;
    name?: string;
  };
  type?: string;
  mortgage?: {
    code?: string;
    name?: string;
  };
  startDate?: string;
  mortgageAddress?: string;
  mortgageAddr?: string;
  promoAvailment?: boolean;
  mvFileNumber?: string;
  supportInsIntegration?: boolean;
  consentForm?: Documents;
  consentDate?: any;
  drivingScore?: string | number;
  telematicDiscount?: {
    currencyIso: string;
    value: string;
  };
  vinPaired?: boolean;
}

export interface ResponseVehicleInformation {
  chasissNum: string;
  code: string;
  color: string;
  insuranceCost?: {
    currencyIso: string;
    value: string;
  };
  makeBy: string;
  modelYear: string;
  paidPrice: {
    currencyIso: string;
    value: string;
  };
  variant?: string;
  bodyType?: string;
  powerTransmission?: string;
  fuelType?: string;
  seats?: string;
  maximumWeight?: string;
  productClassification?: string;
  unloadedWeight?: string;
  vehicleModel: string;
  vsiDate: string;
  orderNo: string;
  orderStatus: {
    code: string;
    name: string;
  };
  plateNumber: string;
  engineNumber: string;
  vehicleName?: string;
  bodyTypeName?: string;
}

export interface ResponseInsuranceInformation {
  scheme: string;
  transactionType?: string;
  typeOfInsurance?: string;
  insuranceCoverage: string;
  insuranceType?: string;
  insuranceCompany?: string;
  insuranceCost?: {
    currencyIso: string;
    value: string;
  };
  consentDate?: string;
}

export interface InsuranceQuotationCTPL {
  compData: InsuranceQuotation;
  ctplData: InsuranceQuotation;
  installmentEachMonth?: {
    currencyIso: string;
    value: number;
  };
  installmentPayment?: boolean;
  installmentMonths?: number;
  noOfVehicles?: number;
}

export interface RequestUpdateCustomerDetail {
  firstName: string;
  middleName: string;
  lastName: string;
  phoneNumber: string;
  email: string;
  streetNumber: string;
  appartment: string;
  streetName: string;
  village: string;
  regionIso: string;
  provinceIso: string;
  cityIso: string;
  barangayIso: string;
  zipCode: string;
  title: string;
  gender: string;
  birthday: string;
}

export interface VehicleOptionsResponse {
  enums?: {
    bodyType: OptionDropdown[];
    fuelTypes: OptionDropdown[];
    vehicleCategoryType: OptionDropdown[];
  };
}

export interface VehicleAccessory {
  id: string;
  item: string;
  srp: { value: string; currencyIso: string };
  quantity: number;
  coverage: { value: string; currencyIso: string };
  status?: string;
}
export interface VehicleAccessoryRespon {
  id: string,
  name: string;
  srp: { value: string; currencyIso: string };
  quantity: number;
  coverage: { value: string; currencyIso: string };
  status?: string;
}

export interface VehicleAccessoriesRequest {
  name: string;
  srp: number;
  quantity: number;
  coverage?: number;
  approved?: boolean;
}

export interface Invoice {
  id: string;
  name: string;
  url: string;
}

export interface VehicleAccessoriesResponse {
  accessories: VehicleAccessoryRespon[];
  invoices: Invoice[];
  remark: string;
}

export interface VehicleAccessoryDetailResponse {
  id: string;
  name: string;
  quantity: number;
  srp: number;
  remark: string;
}