<h2 class="title-dialog">
    {{ "insuranceRenewals.addOffMarketInsurance" | translate }}
  </h2>
  
  <mat-dialog-content
    class="container-dialog customer-information-update"
    [class.loading]="loadingService.isLoading"
  >
  <form [formGroup]="form">
    <div class="row">
      <div class="col-6">
        <app-form-group
          [label]="'newVehicleInsurance.detail.vehicleInformation.vin' | translate"
          [control]="form.controls.vin"
          [required]="true"
          [maxLength]="255"
          [placeholder]="'insuranceRenewals.vinPlaceholder' | translate"
        ></app-form-group>
      </div>
      <div class="col-6">
        <app-form-group
          [label]="'newVehicleInsurance.detail.insuranceInformation.insurancePolicyCode' | translate"
          [control]="form.controls.policyCode"
          [required]="true"
          [maxLength]="255"
          [placeholder]="'insuranceRenewals.policyCodePlaceholder' | translate"
        ></app-form-group>
      </div>
    </div>
    <div class="row">
      <div class="col-6">
        <app-date-form-group
          [label]="'newVehicleInsurance.detail.insurerInformation.startDate' | translate"
          [control]="form.controls.startDate"
          [placeholder]="'mm/dd/yyyy'"
          [required]="true"
          [errorMessage]="'validation.date' | translate"
        ></app-date-form-group>
      </div>
      <div class="col-6">
        <app-date-form-group
          [label]="'insuranceRenewals.expiryDate' | translate"
          [control]="form.controls.expiredDate"
          [placeholder]="'mm/dd/yyyy'"
          [required]="true"
          [errorMessage]="'validation.date' | translate"
          [minDate]="form?.controls?.startDate?.value > today?.getTime()
            ? form?.controls?.startDate?.value
            : today"
        ></app-date-form-group>
      </div>
    </div>
    <div class="row">
      <div class="col-12">
        <app-dropdown-form-group
          [label]="'newVehicleInsurance.detail.insuranceInformation.insuranceCompany' | translate"
          [control]="form.controls.insuranceCompanyCodeOM"
          [options]="insuranceCompanyOption"
          [placeholder]="'insuranceRenewals.insuranceCompanyPlaceholder' | translate"
          [required]="true"
        ></app-dropdown-form-group>
      </div>
    </div>
    <div class="row">
      <div class="col-12">
        <app-radio-button
          [label]="'insuranceRenewals.telematicsInsurance'"
          [control]="form.controls.telematicInsuranceOM"
          [isHorizontal]="true"
          [gap]="100"
          [option]="telematicsInsuranceList"
        ></app-radio-button>
      </div>
    </div>
    <div class="row">
      <div class="col-6">
        <app-import-files
          [label]="'newVehicleInsurance.detail.ePolicy' | translate"
          [placeholder]="'deviceManagement.noFileSelected' | translate"
          [control]="form.controls.ePolicy"
          [accept]="E_POLICY_EXTENSION"
          [required]="true"
          (filesSelected)="handleEPolicyFiles($event)"
          (handleInvalidFile)="handleInvalidFile()"
        ></app-import-files>
      </div>
      <div class="col-6">
        <app-import-files
          [label]="'insuranceRenewals.insuranceConsent' | translate"
          [placeholder]="'deviceManagement.noFileSelected' | translate"
          [control]="form.controls.insuranceConsent"
          [accept]="INSURANCE_CONSENT_EXTENSION"
          [required]="true"
          (filesSelected)="handleInsuranceConsentFiles($event)"
          (handleInvalidFile)="handleInvalidFile()"
        ></app-import-files>
      </div>
    </div>
    <div class="row">
      <div class="col-12">
        <app-date-form-group
          [label]="'newVehicleInsurance.detail.insuranceInformation.consentDate' | translate"
          [control]="form.controls.consentDate"
          [placeholder]="'mm/dd/yyyy'"
          [required]="true"
          [errorMessage]="'validation.date' | translate"
        ></app-date-form-group>
      </div>
    </div>
  </form>
  </mat-dialog-content>
  <mat-dialog-actions class="action-dialog">
    <button class="btn-quaternary" (click)="onCancel()">
      {{ "common.cancel" | translate }}
    </button>
    <button
      class="btn-primary"
      [disabled]="form.invalid"
      (click)="onSubmit()"
    >
      {{ "common.submit" | translate }}
    </button>
  </mat-dialog-actions>