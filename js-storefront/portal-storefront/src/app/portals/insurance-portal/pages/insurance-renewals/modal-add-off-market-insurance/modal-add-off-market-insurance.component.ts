import { CommonModule, DatePipe } from '@angular/common';
import { Component, inject } from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { DateFormat } from '../../../../../core/enums';
import { DateTimeHelper } from '../../../../../core/helpers/date-time.helper';
import { IconModule } from '../../../../../core/icon/icon.module';
import { OptionDropdown } from '../../../../../core/interfaces';
import { NotificationService, LoadingService } from '../../../../../core/services';
import { UserService } from '../../../../../core/services/user';
import { DropdownFormGroupComponent, FormGroupComponent, DateFormGroupComponent } from '../../../../../core/shared';
import { RequestAddOffMarketInsurance } from '../../../interfaces';
import { RadioButtonComponent } from "../../../../../core/shared/radio-button/radio-button.component";
import { ImportFilesComponent } from "../../../../../core/shared/import-files/import-files.component";
import { E_POLICY_EXTENSION, INSURANCE_CONSENT_EXTENSION } from '../../../constants/insurance.const';
import { InsuranceRenewalsService } from '../../../services/insurance-renewals/insurance-renewals.service';
import { Router } from '@angular/router';
import { AddressService } from '../../../../iot-portal/services/address/address.service';


@Component({
  selector: 'app-modal-add-off-market-insurance',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    IconModule,
    ReactiveFormsModule,
    DropdownFormGroupComponent,
    FormGroupComponent,
    MatDialogModule,
    DateFormGroupComponent,
    RadioButtonComponent,
    ImportFilesComponent
],
  templateUrl: './modal-add-off-market-insurance.component.html',
  styleUrl: './modal-add-off-market-insurance.component.scss',
  providers: [
    NotificationService,
    DatePipe,
    DateTimeHelper
  ],
})
export class ModalAddOffMarketInsuranceComponent {
  dialogRef = inject(MatDialogRef<ModalAddOffMarketInsuranceComponent>);
  data = inject(MAT_DIALOG_DATA);
  notificationService = inject(NotificationService);
  translateService = inject(TranslateService);
  loadingService = inject(LoadingService);
  userService = inject(UserService);
  insuranceRenewalsService = inject(InsuranceRenewalsService);
  addressService = inject(AddressService);
  datePipe = inject(DatePipe);
  translate = inject(TranslateService);
  router = inject(Router);
  private dateTimeHelper = inject(DateTimeHelper);
  E_POLICY_EXTENSION = E_POLICY_EXTENSION;
  INSURANCE_CONSENT_EXTENSION = INSURANCE_CONSENT_EXTENSION;
  today = new Date();
  duration = 8000;

  form = new FormGroup({
    vin: new FormControl('', Validators.required),
    policyCode: new FormControl('', Validators.required),
    startDate: new FormControl(null, Validators.required),
    expiredDate: new FormControl(null, Validators.required),
    insuranceCompanyCodeOM: new FormControl('', Validators.required),
    telematicInsuranceOM: new FormControl(false),
    ePolicy: new FormControl(null, Validators.required),
    insuranceConsent: new FormControl(null, Validators.required),
    consentDate: new FormControl(null, Validators.required),
  });

  insuranceCompanyOption: OptionDropdown[];
  telematicsInsuranceList: { code: boolean; name: string }[] = [
    {
      code: false,
      name: this.translateService.instant('common.no'),
    },
    {
      code: true,
      name: this.translateService.instant('common.yes'),
    },
  ];

  ngOnInit(): void {
    this.getfilter();
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  getfilter(): void {
    this.insuranceRenewalsService.getInsuranceCompanies().subscribe(
      (response) => {
        this.insuranceCompanyOption = response;
      }
    );
  }

  onSubmit(): void {
    const formValue = this.form.value;

    const payload = {
      ...formValue,
      startDate: this.dateTimeHelper.convertUTCtoDisplayDate(formValue?.startDate, DateFormat.ShortDate),
      expiredDate: this.dateTimeHelper.convertUTCtoDisplayDate(formValue?.expiredDate, DateFormat.ShortDate),
      consentDate: this.dateTimeHelper.convertUTCtoDisplayDate(formValue?.consentDate, DateFormat.ShortDate),
      telematicInsuranceOptionOM: formValue.telematicInsuranceOM ? 'ELEMATICS' : 'OTHER',
    } as RequestAddOffMarketInsurance;

    this.loadingService.showLoader();
    this.insuranceRenewalsService.addOffMarketInsurance(payload).subscribe(
      (response) => {
        this.loadingService.hideLoader();
        if (response.code === '200') {
          this.notificationService.showSuccess(response.message, this.duration);
          this.dialogRef.close(true);
        } else {
          this.notificationService.showError(response.message, this.duration);
        }
      },
      (error) => {
        this.loadingService.hideLoader();
        this.notificationService.showError(this.translate.instant('common.generalError'));
      }
    );
  }

  handleEPolicyFiles(files: FileList | null): void {
    if (files && files.length !== 0) {
      this.form.controls.ePolicy.setValue(files[0]);
    }
  }

  handleInsuranceConsentFiles(files: FileList | null): void {
    if (files && files.length !== 0) {
      this.form.controls.insuranceConsent.setValue(files[0]);
    }
  }

  handleInvalidFile(): void {
    this.notificationService.showError(
      'Invalid file'
    );
  }
}
