<form [formGroup]="filterForm">
    <div class="filter-form">
      <div
        class="filter-form-properties"
        [class.has-advanced-filter]="isAdvancedFilter"
      >
        <app-dropdown-form-group
          [label]="'newVehicleInsurance.filter.dealer' | translate"
          [control]="filterForm.controls.dealer"
          [options]="dealers"
        ></app-dropdown-form-group>
        <app-dropdown-form-group
          [label]="'newVehicleInsurance.filter.status' | translate"
          [control]="filterForm.controls.status"
          [options]="status"
        ></app-dropdown-form-group>
        <app-dropdown-form-group
          [label]="'newVehicleInsurance.filter.month' | translate"
          [control]="filterForm.controls.months"
          [options]="months"
        ></app-dropdown-form-group>
  
        @if (isAdvancedFilter) {
          <app-form-group
            [label]="'newVehicleInsurance.filter.vinNo' | translate"
            [control]="filterForm.controls.vinNo"
            [placeholder]="'newVehicleInsurance.filter.vinNoPlaceholder' | translate"
          ></app-form-group>
          <app-form-group
            [label]="'newVehicleInsurance.filter.customerName' | translate"
            [control]="filterForm.controls.customerName"
            [placeholder]="'newVehicleInsurance.filter.customerNamePlaceholder' | translate"
          ></app-form-group>
          <app-dropdown-form-group
            [label]="'newVehicleInsurance.filter.sortBy' | translate"
            [control]="filterForm.controls.sortBy"
            [options]="sortBy"
          ></app-dropdown-form-group>
        }
      </div>
      <div class="filter-form-search">
        <button type="submit" class="btn-tertiary search" (click)="search.emit()">
          {{ "common.search" | translate }}
        </button>
      </div>
    </div>
  
    <div class="advanced-title" (click)="changeAdvancedFilter()">
      <mat-icon svgIcon="ic-adjustment-red" class="small-icon"></mat-icon>
      {{
        (isAdvancedFilter
          ? "newVehicleInsurance.filter.lessFilter"
          : "newVehicleInsurance.filter.advancedFilter"
        ) | translate
      }}
    </div>
  </form>
  