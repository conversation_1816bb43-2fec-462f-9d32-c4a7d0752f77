import { Component, inject, ViewChild } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ItemWidget, PagingInfo } from '../../../../core/interfaces';
import { CommonModule, DatePipe } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DateFormat } from '../../../../core/enums';
import { COORDINATOR_INSURANCE_RENEWALS_WIDGETS, ID_WIDGET_COMPLETED, ID_WIDGET_EXPIRING_SOON_POLICIES, ID_WIDGET_FAILED_TO_ASSIGN_POLICIES, ID_WIDGET_PENDING_POLICIES, ID_WIDGET_TO_BE_APPROVED, PARTNER_INSURANCE_RENEWALS_WIDGETS, SORT_BY_CODE_INSURANCE_NO } from '../../constants/insurance-renewals.const';
import { LoadingComponent } from '../../../../layout/global/loading/loading.component';
import { LoadingService } from '../../../../core/services';
import { AgGridCustomComponent, WidgetSummaryComponent } from '../../../../core/shared';
import { FilterInsuranceRenewalsComponent } from './filter-insurance-renewals/filter-insurance-renewals.component';
import { InsuranceRenewalsService } from '../../services/insurance-renewals/insurance-renewals.service';
import { InsuranceRenewalsItem, ResponseInsuranceRenewalsList } from '../../interfaces';
import { ROLES } from '../../../../core/constants/roles.const';
import { Router } from '@angular/router';
import { ModalAddInsuranceComponent } from './modal-add-insurance/modal-add-insurance.component';
import { MatDialog } from '@angular/material/dialog';
import { ModalAddOffMarketInsuranceComponent } from './modal-add-off-market-insurance/modal-add-off-market-insurance.component';
import { filter, finalize, forkJoin, tap } from 'rxjs';
import { UserService } from '../../../../core/services/user';
import { RouterLinkCellRendererComponent } from '../../../../core/shared/router-link-cell-renderer/router-link-cell-renderer.component';

@Component({
  selector: 'app-insurance-renewals',
  standalone: true,
  imports: [
    CommonModule,
    LoadingComponent,
    TranslateModule,
    WidgetSummaryComponent,
    FormsModule,
    ReactiveFormsModule,
    FilterInsuranceRenewalsComponent,
    AgGridCustomComponent,
  ],
  providers: [
    DatePipe,
  ],
  templateUrl: './insurance-renewals.component.html',
  styleUrl: './insurance-renewals.component.scss'
})
export class InsuranceRenewalsComponent {
  @ViewChild(FilterInsuranceRenewalsComponent) filter: FilterInsuranceRenewalsComponent;
  private dialog = inject(MatDialog);
  widgets: ItemWidget[] = COORDINATOR_INSURANCE_RENEWALS_WIDGETS;
  selectedSummaryTab = ID_WIDGET_PENDING_POLICIES;

  rowData: InsuranceRenewalsItem[];
  colDefs = [];
  defaultColDef: any = {};
  pagingInfo: PagingInfo = {
    totalItems: 0,
    currentPage: 0,
    numberOfPage: 10,
  };

  dateFormat = DateFormat;
  isExpiringSoonPolicies = false;
  roles = [];
  isCoordinator = false;
  isAdministrator = false;
  currentRole: string;

  constructor(
    public loadingService: LoadingService,
    public translateService: TranslateService,
    private insuranceRenewalsService: InsuranceRenewalsService,
    private datePipe: DatePipe,
    private router: Router,
    private userService: UserService,
  ) {
    this.roles = JSON.parse(localStorage.getItem('roles'))?.roles || [];
    this.isCoordinator = this.roles.includes(ROLES.COORDINATORGROUP);
    this.isAdministrator = this.roles.includes(ROLES.INSURANCEADMINGROUP);
  }

  changeSummaryTab(id: string): void {
    this.selectedSummaryTab = id;
    this.pagingInfo.currentPage = 0;

    this.mapWidgetFilter(id);
    this.getInsuranceRenewalsList(this.isExpiringSoonPolicies).subscribe();
  }

  mapWidgetFilter(id: string) {
    switch (id) {
      case ID_WIDGET_PENDING_POLICIES: {
        // Status = Pending
        this.filter.filterForm.patchValue({
          status: 'PENDING',
          months: '',
          vinNo: '',
          customerName: '',
          sortBy: SORT_BY_CODE_INSURANCE_NO,
        });
        this.isExpiringSoonPolicies = false;
        break;
      }
      case ID_WIDGET_FAILED_TO_ASSIGN_POLICIES: {
        // Status = Failed to assign 
        this.filter.filterForm.patchValue({
          status: 'FAILED',
          months: '',
          vinNo: '',
          customerName: '',
          sortBy: SORT_BY_CODE_INSURANCE_NO,
        });
        this.isExpiringSoonPolicies = false;
        break;
      }
      case ID_WIDGET_TO_BE_APPROVED: {
        // Status = In Process
        this.filter.filterForm.patchValue({
          status: 'IN_PROCESS',
          months: '',
          vinNo: '',
          customerName: '',
          sortBy: SORT_BY_CODE_INSURANCE_NO,
        });
        this.isExpiringSoonPolicies = false;
        break;
      }
      case ID_WIDGET_COMPLETED: {
        // Status = Completed
        this.filter.filterForm.patchValue({
          status: 'COMPLETED',
          months: '',
          vinNo: '',
          customerName: '',
          sortBy: SORT_BY_CODE_INSURANCE_NO,
        });
        this.isExpiringSoonPolicies = false;
        break;
      }
      case ID_WIDGET_EXPIRING_SOON_POLICIES: {
        // ‘Insurance expiry date’ is in 30 days
        this.isExpiringSoonPolicies = true;
        this.filter.filterForm.patchValue({
          status: '',
          months: '',
          vinNo: '',
          customerName: '',
          sortBy: SORT_BY_CODE_INSURANCE_NO,
        });
        break;
      }
    }
  }

  ngOnInit(): void {
    this.initializeTable();
    this.currentRole = this.userService.getGroupRole(this.roles);
    this.setWidgetsName();
  }

  setWidgetsName() {
    // If insurance partner, change the widget name
    if (this.currentRole === ROLES.INSURANCEPARTNERGROUP) {
      this.widgets = PARTNER_INSURANCE_RENEWALS_WIDGETS;
    }
  }

  getWidgets() {
    this.loadingService.showLoader();
    const dealerId = this.filter?.filterForm?.value?.dealer || '';
    return this.insuranceRenewalsService.getWidget(dealerId).pipe(
      tap((res) => {
        if (res) {
          res?.widgets.forEach((widget, index: number) => {
            if (widget.type === this.widgets[index].id) {
              this.widgets[index].count = widget.count;
            }
          });
        }
      },
      finalize(() => this.loadingService.hideLoader())
    ));
  }

  getInsuranceRenewalsList(expiringSoonPolicies: boolean = false) {
    if (this.filter) {
      const form = this.filter.filterForm.value;
      const dataSend: any = {
        dealer: form.dealer || '',
        status: form.status,
        month: form.months,
        vin: form.vinNo,
        expiringSoonPolicies,
        customerName: form.customerName,
        sort: form.sortBy === 'insuranceNo' ? `${form.sortBy}:desc` : form.sortBy,
        currentPage: this.pagingInfo.currentPage,
        pageSize: this.pagingInfo.numberOfPage,
      };
      this.loadingService.showLoader();
      return this.insuranceRenewalsService.getInsuranceRenewalsList(dataSend).pipe(
        tap((res: ResponseInsuranceRenewalsList) => {
          if (res) {
            if (res.items) {
              this.rowData = res.items.map((item) => {
                item.requestedDate = this.datePipe.transform(
                  item.requestedDate,
                  this.dateFormat.FullDatePeriod
                );
                item.approvalDate = this.datePipe.transform(
                  item.approvalDate,
                  this.dateFormat.FullDatePeriod
                );
                return item;
              });
            }
  
            if (res.pagination) {
              this.pagingInfo = {
                totalItems: res.pagination.totalResults,
                currentPage: res.pagination.currentPage,
                numberOfPage: res.pagination.pageSize,
              };
            }
          }
        }),
        finalize(() => this.loadingService.hideLoader())
      );
    }
    return null;
  }

  searchInsuranceRenewals(): void {
    this.pagingInfo.currentPage = 0;
    forkJoin([this.getWidgets(), this.getInsuranceRenewalsList(this.isExpiringSoonPolicies)]).pipe(
      tap(() => this.loadingService.showLoader()),
      finalize(() => this.loadingService.hideLoader())
    ).subscribe();
  }

  initializeTable(): void {
    this.colDefs = [
      {
        headerValueGetter: () => this.translateService.instant('newVehicleInsurance.table.insuranceNo'),
        field: 'insuranceNo',
        flex: 1,
        sortable: false,
        cellClass: 'cell-word-wrap',
        cellRenderer: RouterLinkCellRendererComponent,
        cellRendererParams: {
          linkBuilder: (data: any) => `/insurance/insurance-renewals/${data?.insuranceNo}`,
        }
      }, {
        headerValueGetter: () => this.translateService.instant('newVehicleInsurance.table.status'),
        field: 'status',
        flex: 1,
        sortable: false,
        cellRenderer: (params: any) => {
          const code = params.data.status ? params.data.status.code : '';
          return code
          ? `<span class="value-tag grey-tag">${this.translateService.instant(`newVehicleInsurance.status.${code}`)}</span>`
          : '-';
        }
      }, {
        headerValueGetter: () => this.translateService.instant('newVehicleInsurance.table.customerName'),
        field: 'customerName',
        flex: 1,
        sortable: false,
        cellRenderer: (params) => {
          return params?.value ? params?.value : '-';
        },
      }, {
        headerValueGetter: () => this.translateService.instant('newVehicleInsurance.table.model'),
        field: 'model',
        flex: 1,
        sortable: false,
        cellRenderer: (params) => {
          return params?.value ? params?.value : '-';
        },
      }, {
        headerValueGetter: () => this.translateService.instant('newVehicleInsurance.table.insurancePartnerName'),
        field: 'insurancePartnerName',
        flex: 1,
        sortable: false,
        cellRenderer: (params) => {
          return params?.value ? params?.value : '-';
        },
      }, {
        headerValueGetter: () => this.translateService.instant('newVehicleInsurance.table.vin'),
        field: 'vin',
        flex: 1,
        sortable: false,
        cellRenderer: (params) => {
          return params?.value ? params?.value : '-';
        },
      }, {
        headerValueGetter: () => this.translateService.instant('newVehicleInsurance.table.plateNo'),
        field: 'plateNo',
        flex: 1,
        sortable: false,
        cellRenderer: (params) => {
          return params?.value ? params?.value : '-';
        },
      }, {
        headerValueGetter: () => this.translateService.instant('newVehicleInsurance.table.requestedDate'),
        field: 'requestedDate',
        flex: 1,
        sortable: false,
        cellRenderer: (params) => {
          return params?.value ? params?.value : '-';
        },
      }, {
        headerValueGetter: () => this.translateService.instant('newVehicleInsurance.table.approvalDate'),
        field: 'approvalDate',
        flex: 1,
        sortable: false,
        cellRenderer: (params) => {
          return params?.value ? params?.value : '-';
        },
      }
    ];
    // If user is not in Insurance Partner Group, show Creation Source column
    if (!this.roles.includes(ROLES.INSURANCEPARTNERGROUP)) {
      this.colDefs.push({
        headerValueGetter: () => this.translateService.instant('newVehicleInsurance.table.creationSource'),
        field: 'creationSource',
        flex: 1,
        sortable: false,
        cellRenderer: (params) => {
          return params?.value ? params?.value : '-';
        },
      });
    }
    this.defaultColDef = {
      resizable: false,
    };
  }

  onPageChange(newPage: number): void {
    this.pagingInfo.currentPage = newPage;
    this.getInsuranceRenewalsList(this.isExpiringSoonPolicies).subscribe();
  }

  onResultsPerPageChange(event: number): void {
    this.pagingInfo = {
      ...this.pagingInfo,
      currentPage: 0,
      numberOfPage: event,
    };
    this.getInsuranceRenewalsList(this.isExpiringSoonPolicies).subscribe();
  }

  onAddInsurance(): void {
    const dialogRef = this.dialog.open(ModalAddInsuranceComponent, {
      width: '530px',
      data: {
        icon: 'ic-add-red',
        cancelBtn: this.translateService.instant('common.cancel'),
      },
      autoFocus: true,
    });
    dialogRef.componentInstance.toggleloading.subscribe((flag) => {
      flag ? this.loadingService.showLoader() : this.loadingService.hideLoader();
    });
  }

  onAddOffMarketInsurance(): void {
    // Add off market insurance
    const dialogRef = this.dialog.open(ModalAddOffMarketInsuranceComponent, {
      width: '850px',
      disableClose: true,
      autoFocus: false,
      panelClass: 'dialog-insurance-custom',
      data: {
        
      },
    });
    dialogRef.afterClosed()
      .pipe(filter((result) => result))
      .subscribe((() => {
        // Reload the page
        this.searchInsuranceRenewals();
      }));
  }
}
