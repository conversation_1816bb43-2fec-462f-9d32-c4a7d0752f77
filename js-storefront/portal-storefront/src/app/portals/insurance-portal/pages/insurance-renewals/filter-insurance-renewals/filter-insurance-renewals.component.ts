import { CommonModule, DatePipe } from '@angular/common';
import { Component, EventEmitter, Output } from '@angular/core';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import * as _ from 'lodash-es';
import { LoadingService, ScreenSizeService } from '../../../../../core/services';
import { FormGroupComponent } from '../../../../../core/shared';
import { DropdownFormGroupComponent } from '../../../../../core/shared/dropdown-form-group/dropdown-form-group.component';
import { DateFormat } from '../../../../../core/enums';
import { SORT_BY_CODE_INSURANCE_NO, SORT_BY_CODE_STATUS, MONTH_NUMBER } from '../../../constants/insurance-renewals.const';
import { OptionDropdown } from '../../../../../core/interfaces';
import { FilterInsuranceRenewals } from '../../../interfaces';
import { InsuranceRenewalsService } from '../../../services/insurance-renewals/insurance-renewals.service';
import { UserService } from '../../../../../core/services/user';
import { ROLES } from '../../../../../core/constants/roles.const';
import { Subject, takeUntil } from 'rxjs';


@Component({
  selector: 'app-filter-insurance-renewals',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatIconModule,
    TranslateModule,
    FormGroupComponent,
    DropdownFormGroupComponent,
  ],
  providers: [
    ScreenSizeService,
    DatePipe
  ],
  standalone: true,
  templateUrl: './filter-insurance-renewals.component.html',
  styleUrl: './filter-insurance-renewals.component.scss'
})
export class FilterInsuranceRenewalsComponent {
  @Output() search = new EventEmitter();

  filterForm = new FormGroup({
    dealer: new FormControl(''),
    status: new FormControl(''),
    months: new FormControl(''),
    vinNo: new FormControl(''),
    customerName: new FormControl(''),
    sortBy: new FormControl(SORT_BY_CODE_INSURANCE_NO),
  });

  dealers: OptionDropdown[];
  status: OptionDropdown[];
  months: OptionDropdown[];
  isAdvancedFilter = false;
  sortBy = [{
    code: SORT_BY_CODE_INSURANCE_NO,
    name: this.translateService.instant('newVehicleInsurance.table.insuranceNo'),
  }, {
    code: SORT_BY_CODE_STATUS,
    name: this.translateService.instant('newVehicleInsurance.table.status'),
  }];
  filterResponse: FilterInsuranceRenewals;
  dateFormat = DateFormat;
  currentRoles: string[] = [];
  currentRole: string;
  private destroy$ = new Subject<void>();

  constructor(
    private datePipe: DatePipe,
    private insuranceRenewalsService: InsuranceRenewalsService,
    private userService: UserService,
    public loadingService: LoadingService,
    public translateService: TranslateService,
  ) {}

  ngOnInit(): void {
    this.getAllFilter();
    this.setMonths(MONTH_NUMBER);
    this.userService.getUserRoles$()
    .pipe(takeUntil(this.destroy$))
    .subscribe((value) => {
      if (value) {
        this.currentRoles = value.roles || [];
        this.currentRole = this.userService.getGroupRole(this.currentRoles);
        this.getDealer();
      }
    });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  getAllFilter(): void {
    this.loadingService.showLoader();
    this.insuranceRenewalsService.getFilter().subscribe(
      (res: FilterInsuranceRenewals) => {
        if (res) {
          this.status = res.statusList || [];
        }
        this.loadingService.hideLoader();
      },
      (err) => {
        this.loadingService.hideLoader();
      }
    );
  }

  getDealer(): void {
    this.loadingService.showLoader();
    this.insuranceRenewalsService.getInsuranceData().subscribe(
      (res) => {
        if (res) {
          this.dealers = _.orderBy(res?.dealers, ['displayName'], ['asc'])
            .map((dealer) => ({
              code: dealer.name,
              name: dealer.displayName,
            }));
          // If user is coordinator, disable the dealer dropdown
          if (this.currentRole === ROLES.COORDINATORGROUP) {
            this.filterForm.get('dealer')?.disable();
          } else {
            this.dealers.unshift({ code: '', name: this.translateService.instant('common.all') });
          }
          if (this.dealers && this.dealers.length) {
            this.filterForm.patchValue({
              dealer: this.dealers[0].code,
            });
          }
          this.search.emit();
        }
        this.loadingService.hideLoader();
      },
      (err) => {
        this.loadingService.hideLoader();
      }
    );
  }

  changeAdvancedFilter(): void {
    this.isAdvancedFilter = !this.isAdvancedFilter;
  }

  setMonths(monthNo: number): void {
    const today = new Date();
    const monthsList = Array.from({ length: monthNo }, (_, i) => {
      const date = new Date(today.getFullYear(), today.getMonth() - i, 1);
      const month = this.datePipe.transform(date, this.dateFormat.MonthYear) || '';
      return {
        name: month,
        code: month
      };
    });

    this.months = [{ code: '', name: this.translateService.instant('common.all')}, ...monthsList]
  }
}
