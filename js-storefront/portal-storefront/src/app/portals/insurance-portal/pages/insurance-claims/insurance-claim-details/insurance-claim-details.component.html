<div class="new-vehicle-insurance-detail container-details">
  <div class="container-details__header p-b-0">
    <div>
      <app-breadcrumb-with-label
        [breadcrumbs]="[
          'insuranceClaimsDetails.pageName' | translate,
          'insuranceClaimsDetails.pageDetails' | translate
        ]"
        [breadcrumbLinks]="['/insurance/insurance-claims']"
        [label]="claimId"
        [className]="'breadcrumb-with-data'"
      >
      </app-breadcrumb-with-label>
      <div class="container-details__data">
        {{ "newVehicleInsurance.statusText" | translate }}:
        <span class="status">{{
          claimData?.claimInformationData?.status?.name ?? "-"
        }}</span>
      </div>
    </div>
    <app-action-button-insurance-claim
      [showConfirm]="showConfirmBtnByStatus(claimData?.canConfirm, claimData?.claimInformationData?.status?.code)"
      [showApprove]="
        showApproveBtnByStatus(claimData?.claimInformationData?.status?.code)
      "
      [showApproveEstimation]="showApproveEstimationBtnByStatus(claimData?.canApproveEstimation)"
      [currentRole]="currentRole"
      [status]="claimData?.claimInformationData?.status?.code"
      (handleReviseInsurance)="onReviseInsurance()"
      (handleConfirmInsurance)="onConfirmInsurance()"
      (handleApproveInsurance)="onApproveInsurance(claimData?.claimInformationData?.status?.code)"
    ></app-action-button-insurance-claim>
  </div>
  <div class="container-details__content">
    <div class="container-details-group">
      <app-dealer
        [data]="dealerData"
        icon="ic-dealer"
        title="insuranceClaimsDetails.dealer.title"
      ></app-dealer>

      <div class="customer-information">
        <app-dealer
          [data]="customerInforData"
          icon="ic-owner-details"
          title="insuranceClaimsDetails.customerInfor.title"
        ></app-dealer>
        @if (isCustomerEditable) {
          <button class="btn-link" (click)="onEditCustomerInformation()">
            <mat-icon svgIcon="ic-edit" class="small-icon" aria-hidden="true"></mat-icon>
            {{ "common.edit" | translate }}
          </button>
        }
      </div>
    </div>
    <app-bp-estimation-information
      [currentRole]="currentRole"
      [data]="claimData?.claimInformationData"
      [repairOrderData]="claimData?.repairOrderData"
      [status]="claimData?.claimInformationData?.status?.code"
      (handleDownload)="onDownloadFile($event)"
      (handleSelectedFiles)="onFileSelected($event)"
      (handleDelete)="onRemoveFile($event)"
    ></app-bp-estimation-information>
    <app-vehicle-information-claims
      [data]="claimData?.vehicleInfoData"
    ></app-vehicle-information-claims>
    <app-parts-and-services
      [rowDataParts]="claimData?.partCostDataList && claimData?.partCostDataList.length ? claimData?.partCostDataList : []"
      [rowDataServices]="laborCostDataList"
      [rowDataMaterials]="claimData?.otherMaterialCostDataList || []"
      [dataGrand]="dataGrand"
      [remarks]="claimData?.remarks"
      [currentRole]="currentRole"
      [status]="claimData?.claimInformationData?.status?.code"
      (selectedParts)="handleGetRowSelectedParts($event)"
      (selectedServices)="handleGetRowSelectedServices($event)"
      (selectedOtherMaterial)="handleGetRowSelectedOtherMaterial($event)"
    ></app-parts-and-services>
  </div>
  <div class="container-details__footer">
    <app-action-button-insurance-claim
      [showConfirm]="showConfirmBtnByStatus(claimData?.canConfirm, claimData?.claimInformationData?.status?.code)"
      [showApprove]="
        showApproveBtnByStatus(claimData?.claimInformationData?.status?.code)
      "
      [showApproveEstimation]="showApproveEstimationBtnByStatus(claimData?.canApproveEstimation)"
      [currentRole]="currentRole"
      [status]="claimData?.claimInformationData?.status?.code"
      (handleReviseInsurance)="onReviseInsurance()"
      (handleConfirmInsurance)="onConfirmInsurance()"
      (handleApproveInsurance)="onApproveInsurance(claimData?.claimInformationData?.status?.code)"
    ></app-action-button-insurance-claim>
  </div>
</div>
