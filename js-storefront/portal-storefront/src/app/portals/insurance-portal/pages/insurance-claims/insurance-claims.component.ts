import { Component, inject, ViewChild } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { CommonModule, DatePipe } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { FilterInsuranceClaimsComponent } from './filter-insurance-claims/filter-insurance-claims.component';
import { map, catchError, finalize, of, Observable, forkJoin, tap } from 'rxjs';
import { LoadingComponent } from '../../../../layout/global/loading/loading.component';
import { WidgetSummaryComponent, AgGridCustomComponent } from '../../../../core/shared';
import { ItemWidget, PagingInfo } from '../../../../core/interfaces';
import { InsuranceClaimsItem, RequesteInsuranceClaimsList, ResponseInsuranceClaimsList } from '../../interfaces';
import { InsuranceClaimsService } from '../../services/insurance-claims/insurance-claims.service';
import { ID_WIDGET_PENDING_CLAIMS, SORT_BY_CODE_REPAIR_ESTIMATION_NO, ID_WIDGET_CONFIRMED_CLAIMS, ID_WIDGET_APPROVED_CLAIMS, COORDINATOR_INSURANCE_CLAIMS_WIDGETS, PARTNER_INSURANCE_CLAIMS_WIDGETS } from '../../constants/insurance-claims.const';
import { DateFormat } from '../../../../core/enums';
import { LoadingService } from '../../../../core/services';
import { DateTimeHelper } from '../../../../core/helpers/date-time.helper';
import { Router } from '@angular/router';
import { fork } from 'child_process';
import { UserService } from '../../../../core/services/user';
import { ROLES } from '../../../../core/constants/roles.const';
import { RouterLinkCellRendererComponent } from '../../../../core/shared/router-link-cell-renderer/router-link-cell-renderer.component';

@Component({
  selector: 'app-insurance-claims',
  standalone: true,
  imports: [
    CommonModule,
    LoadingComponent,
    TranslateModule,
    WidgetSummaryComponent,
    FormsModule,
    ReactiveFormsModule,
    FilterInsuranceClaimsComponent,
    AgGridCustomComponent,
  ],
  providers: [DatePipe, DateTimeHelper],
  templateUrl: './insurance-claims.component.html',
  styleUrl: './insurance-claims.component.scss',
})
export class InsuranceClaimsComponent {
  @ViewChild(FilterInsuranceClaimsComponent)
  filter: FilterInsuranceClaimsComponent;
  widgets: ItemWidget[] = COORDINATOR_INSURANCE_CLAIMS_WIDGETS;
  selectedSummaryTab = ID_WIDGET_PENDING_CLAIMS;

  private insuranceClaimsService = inject(InsuranceClaimsService);
  private datePipe = inject(DatePipe);
  public loadingService = inject(LoadingService);
  private translateService = inject(TranslateService);
  private dateTimeHelper = inject(DateTimeHelper);
  private router = inject(Router)
  private userService = inject(UserService);

  rowData: InsuranceClaimsItem[];
  colDefs = [];
  defaultColDef: any = {};
  pagingInfo: PagingInfo = {
    totalItems: 0,
    currentPage: 0,
    numberOfPage: 10,
  };

  dateFormat = DateFormat;
  roles: string[];
  currentRole: string;

  changeSummaryTab(id: string): void {
    this.selectedSummaryTab = id;
    this.pagingInfo.currentPage = 0;
    this.mapWidgetFilter(id);
    this.getInsuranceClaimsList().subscribe();
  }

  mapWidgetFilter(id: string) {
    switch (id) {
      case ID_WIDGET_PENDING_CLAIMS: {
        // Status = PENDING for other roles and Status = FOR_REVISION for partner
        if (this.currentRole === ROLES.INSURANCEPARTNERGROUP) {
          this.filter.filterForm.patchValue({
            status: 'FOR_REVISION',
            months: '',
            vin: '',
            repairEstimationNo: '',
            sortBy: SORT_BY_CODE_REPAIR_ESTIMATION_NO,
          });
        } else {
          this.filter.filterForm.patchValue({
            status: 'PENDING',
            months: '',
            vin: '',
            repairEstimationNo: '',
            sortBy: SORT_BY_CODE_REPAIR_ESTIMATION_NO,
          });
        }
        break;
      }
      case ID_WIDGET_CONFIRMED_CLAIMS: {
        // Status = CONFIRMED
        this.filter.filterForm.patchValue({
          status: 'CONFIRMED',
          months: '',
          vin: '',
          repairEstimationNo: '',
          sortBy: SORT_BY_CODE_REPAIR_ESTIMATION_NO,
        });
        break;
      }
      case ID_WIDGET_APPROVED_CLAIMS: {
        // Status = APPROVED
        this.filter.filterForm.patchValue({
          status: 'APPROVED',
          months: '',
          vin: '',
          repairEstimationNo: '',
          sortBy: SORT_BY_CODE_REPAIR_ESTIMATION_NO,
        });
        break;
      }
    }
  }

  ngOnInit(): void {
    this.roles = JSON.parse(localStorage.getItem('roles'))?.roles || [];
    this.currentRole = this.userService.getGroupRole(this.roles);
    this.setWidgetsName();
    this.initializeTable();
  }

  setWidgetsName(): void {
    // If insurance partner, change the widget name
    if (this.currentRole === ROLES.INSURANCEPARTNERGROUP) {
      this.widgets = PARTNER_INSURANCE_CLAIMS_WIDGETS;
    }
  }

  getWidgets() {
    this.loadingService.showLoader();

    const dealerCode = this.filter?.filterForm?.controls['dealer'].value || '';

    return this.insuranceClaimsService
      .getWidget(dealerCode)
      .pipe(
        map((res) => {
          if (res?.widgets) {
            this.widgets.forEach((widget, index) => {
              const matchingWidget = res.widgets.find(
                (w) => w.type === widget.id
              );
              if (matchingWidget) {
                this.widgets[index].count = matchingWidget.count;
              }
            });
          }
        }),
        catchError((error) => {
          console.error('Failed to fetch widgets:', error);
          return of(null);
        }),
        finalize(() => this.loadingService.hideLoader())
      )
  }

  getInsuranceClaimsList() {
    if (!this.filter || !this.filter.dealers?.length) {
      console.warn(
        'Dealer data not available yet. Waiting for filter to initialize.'
      );
      return null;
    }

    const dataSend: RequesteInsuranceClaimsList = {
      dealer: this.filter.filterForm.controls['dealer'].value || '',
      status: this.filter.filterForm.value.status,
      month: this.filter.filterForm.value.months,
      vin: this.filter.filterForm.value.vin,
      sort: this.filter.filterForm.value.sortBy,
      repairEstimation: this.filter.filterForm.value.repairEstimationNo,
      currentPage: this.pagingInfo.currentPage,
      pageSize: this.pagingInfo.numberOfPage,
    };

    this.loadingService.showLoader();

    return this.insuranceClaimsService
      .getInsuranceClaimsList(dataSend)
      .pipe(
        map((res: ResponseInsuranceClaimsList) => {
          if (res?.items) {
            this.rowData = res.items.map((item) => ({
              ...item,
              submitedDate: this.dateTimeHelper.convertUTCtoDisplayDate(
                item.submitedDate,
                this.dateFormat.FullDatePeriod
              ),
            }));
          }
          if (res.pagination) {
            this.pagingInfo = {
              totalItems: res.pagination.totalResults,
              currentPage: res.pagination.currentPage,
              numberOfPage: res.pagination.pageSize,
            };
          }
        }),
        catchError((error) => {
          console.error('Error fetching insurance claims:', error);
          return [];
        }),
        finalize(() => this.loadingService.hideLoader())
      )
  }

  searchInsuranceClaimsList(): void {
    this.pagingInfo.currentPage = 0;
    forkJoin([this.getWidgets(), this.getInsuranceClaimsList()]).pipe(
      tap(() => this.loadingService.hideLoader()),
      finalize(() => this.loadingService.hideLoader())
    ).subscribe();
  }

  initializeTable(): void {
    this.colDefs = [
      {
        headerValueGetter: () =>
          this.translateService.instant(
            'insuranceClaims.table.repairEstimationNo'
          ),
        field: 'claimNumber',
        flex: 1,
        sortable: true,
        cellClass: 'cell-word-wrap',
        cellRenderer: RouterLinkCellRendererComponent,
        cellRendererParams: {
          linkBuilder: (data: any) => `/insurance/insurance-claims/${data?.claimNumber}`,
        }
      },
      {
        headerValueGetter: () =>
          this.translateService.instant('insuranceClaims.table.model'),
        field: 'model',
        flex: 1,
        sortable: false,
        cellRenderer: (params) => {
          return params?.value ? `<span>${params?.value}</span>` : '-';
        },
      },
      {
        headerValueGetter: () => this.translateService.instant('newVehicleInsurance.table.insurancePartnerName'),
        field: 'insurancePartnerName',
        flex: 1,
        sortable: false,
        cellRenderer: (params) => {
          return params?.value ? `<span>${params?.value}</span>` : '-';
        },
      },
      {
        headerValueGetter: () =>
          this.translateService.instant('insuranceClaims.table.vin'),
        field: 'vin',
        flex: 1,
        sortable: false,
        cellRenderer: (params) => {
          return params?.value ? `<span>${params?.value}</span>` : '-';
        },
      },
      {
        headerValueGetter: () =>
          this.translateService.instant('insuranceClaims.table.plateNo'),
        field: 'plateNumber',
        flex: 1,
        sortable: false,
        cellRenderer: (params) => {
          return params?.value ? `<span>${params?.value}</span>` : '-';
        },
      },
      {
        headerValueGetter: () =>
          this.translateService.instant('insuranceClaims.table.dealer'),
        field: 'dealerName',
        flex: 1,
        sortable: false,
        cellRenderer: (params) => {
          return params?.value ? `<span>${params?.value}</span>` : '-';
        },
      },
      {
        headerValueGetter: () =>
          this.translateService.instant('insuranceClaims.table.status'),
        field: 'status',
        flex: 1,
        sortable: false,
        cellRenderer: (params: any) => {
          const code = params.data.status ? params.data.status.code : '';
          return code
            ? `<span class="value-tag grey-tag">${this.translateService.instant(
                `common.status.${code}`
              )}</span>`
            : '-';
        },
      },
      {
        headerValueGetter: () =>
          this.translateService.instant('insuranceClaims.table.roStatus'),
        field: 'roStatus',
        flex: 1,
        sortable: false,
        cellRenderer: (params) => {
          const code = params.data.roStatus ? params.data.roStatus.code : '';
          return code
            ? `<span class="value-tag grey-tag">${this.translateService.instant(
                `common.status.${code}`
              )}</span>`
            : '-';
        },
      },
      {
        headerValueGetter: () =>
          this.translateService.instant('insuranceClaims.table.submitted'),
        field: 'submitedDate',
        flex: 1,
        sortable: false,
        cellRenderer: (params) => {
          if (!params?.value) return '-';

          const formattedDate = this.datePipe.transform(
            params.value,
            DateFormat.FullDate
          );
          if (!formattedDate) return '-';

          const [date, time] = formattedDate.split(' ');

          return `<span>${date}<br>${time}</span>`;
        },
      },
    ];
    this.defaultColDef = {
      resizable: false,
    };
  }

  onPageChange(newPage: number): void {
    this.pagingInfo.currentPage = newPage;
    this.getInsuranceClaimsList().subscribe();
  }

  onResultsPerPageChange(event: number): void {
    this.pagingInfo = {
      ...this.pagingInfo,
      currentPage: 0,
      numberOfPage: event,
    };
    this.getInsuranceClaimsList().subscribe();
  }

  viewInsuranceDetail(id: string): void {
    // Redirect to New Vehicle Insurance Detail page
    this.router.navigate(['/insurance/insurance-claims', id]);
  }
}
