<app-loading *ngIf="loadingService.isLoading"></app-loading>

<div class="header-section">
    <h2 class="title-page">
        {{ "insuranceClaims.pageName" | translate }}
    </h2>
</div>

<app-widget-summary [widgets]="widgets" (changeTab)="changeSummaryTab($event)"></app-widget-summary>

<app-filter-insurance-claims (search)="searchInsuranceClaimsList()"></app-filter-insurance-claims>

<div class="device-list">
    @if (rowData?.length > 0 ) {
    <app-ag-grid-custom class="ticket-table" [rowData]="rowData" [colDefs]="colDefs" [defaultColDef]="defaultColDef"
        [isPaging]="true" [pagingInfo]="pagingInfo" [isShowActionExport]="false" [isTextWrap]="true"
        (onPageChange)="onPageChange($event)" (changeItemPerPage)="onResultsPerPageChange($event)">
    </app-ag-grid-custom>
    } @else {
    <div class="no-data">{{ "insuranceClaims.noData" | translate }}</div>
    }
</div>