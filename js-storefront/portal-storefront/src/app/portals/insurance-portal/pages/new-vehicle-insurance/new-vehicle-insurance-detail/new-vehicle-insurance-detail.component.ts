import { Component, HostListener, inject } from '@angular/core';
import { BreadcrumbWithLabelComponent } from '../../../../../core/shared/breadcrumb-with-label/breadcrumb-with-label.component';
import { ActivatedRoute, NavigationEnd, NavigationError, NavigationStart, Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { NewVehicleInsuranceDetailService } from '../../../services/new-vehicle-insurance/new-vehicle-insurance-detail.service';
import {
  LoadingService,
  NotificationService,
  VehicleAccessoriesService,
} from '../../../../../core/services';
import { DialogConfirmComponent } from '../../../../../core/shared';
import { MatDialog } from '@angular/material/dialog';
import { filter, finalize, Observable, Subject, Subscription } from 'rxjs';
import { ActionModal, DateFormat, InsuranceType } from '../../../../../core/enums';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { downloadFile } from '../../../../../core/helpers/function-common.helper';
import {
  ResponseCustomerInformation,
  Documents,
  ResponseInsuranceInformation,
  ResponseInsurerPartner,
  ResponseNewVehicleInsuranceDetail,
  ResponseVehicleInformation,
  InsuranceQuotation,
  VehicleAccessory,
  VehicleAccessoryRespon,
  InsuranceQuotationCTPL,
} from '../../../interfaces';
import { ROLES } from '../../../../../core/constants/roles.const';
import { OptionDropdown, ResponseCommon } from '../../../../../core/interfaces';
import { CurrencyPipe, DatePipe } from '@angular/common';
import { handleErrors } from '../../../../../core/helpers';
import { INSURANCE_STATUS } from '../../../constants/new-vehicle-insurance-detail.const';
import { ActionButtonsComponent } from '../../../features/insurance-details/action-buttons/action-buttons.component';
import { ModalCancelInsuranceComponent } from '../../../features/insurance-details/action-buttons/modal-cancel-insurance/modal-cancel-insurance.component';
import { AttachmentsComponent } from '../../../features/insurance-details/attachments/attachments.component';
import { CustomerInformationComponent } from '../../../features/insurance-details/customer-information/customer-information.component';
import { DocumentsComponent } from '../../../features/insurance-details/documents/documents.component';
import { InsuranceCalculationComponent } from '../../../features/insurance-details/insurance-calculation/insurance-calculation.component';
import { InsuranceInformationComponent } from '../../../features/insurance-details/insurance-information/insurance-information.component';
import { InsurerInformationComponent } from '../../../features/insurance-details/insurer-information/insurer-information.component';
import { ModalInsurerInfomationEditComponent } from '../../../features/insurance-details/insurer-information/modal-insurer-infomation-edit/modal-insurer-infomation-edit.component';
import { VehicleInformationComponent } from '../../../features/insurance-details/vehicle-information/vehicle-information.component';
import { DateTimeHelper } from '../../../../../core/helpers/date-time.helper';
import { UserService } from '../../../../../core/services/user';
import moment from 'moment';
import { ModalUpdateVehicleInfomationComponent } from '../../../features/insurance-details/vehicle-information/modal-update-vehicle-infomation/modal-update-vehicle-infomation.component';
import { VehicleAccessoriesComponent } from "../../../features/insurance-details/vehicle-accessories/vehicle-accessories.component";
import { InsuranceCoverageList } from '../../../enum';
import { InsuranceCalculationCompCtplComponent } from '../../../features/insurance-details/insurance-calculation-comp-ctpl/insurance-calculation-comp-ctpl.component';
import { DataStoreService } from '../../../../../core/services/data-store.service';
@Component({
  selector: 'app-new-vehicle-insurance-detail',
  standalone: true,
  imports: [
    BreadcrumbWithLabelComponent,
    TranslateModule,
    MatIconModule,
    CustomerInformationComponent,
    VehicleInformationComponent,
    InsuranceCalculationComponent,
    InsuranceInformationComponent,
    DocumentsComponent,
    InsurerInformationComponent,
    ActionButtonsComponent,
    AttachmentsComponent,
    VehicleAccessoriesComponent,
    InsuranceCalculationCompCtplComponent
  ],
  providers: [NotificationService, DatePipe, DateTimeHelper, CurrencyPipe],
  templateUrl: './new-vehicle-insurance-detail.component.html',
  styleUrl: './new-vehicle-insurance-detail.component.scss',
})
export class NewVehicleInsuranceDetailComponent {
  insuranceId: string;
  customerInformation: ResponseCustomerInformation;
  insuranceDetail: ResponseNewVehicleInsuranceDetail;
  vehicleInformation: ResponseVehicleInformation;
  insuranceCalculation: InsuranceQuotation | InsuranceQuotationCTPL;
  insuranceInformation: ResponseInsuranceInformation;
  documents: Documents[];
  epolicy: Documents[];
  docsVehicleAccessories: Documents[];
  insurerPartner: ResponseInsurerPartner;
  route = inject(ActivatedRoute);
  router = inject(Router);
  insuranceDetailService = inject(NewVehicleInsuranceDetailService);
  insuranceVehicleAccessoriesSevice = inject(VehicleAccessoriesService);
  loadingService = inject(LoadingService);
  notificationService = inject(NotificationService);
  dialog = inject(MatDialog);
  translateService = inject(TranslateService);
  datePipe = inject(DatePipe);
  dataStoreSerive = inject(DataStoreService);
  private dateTimeHelper = inject(DateTimeHelper);
  private userService = inject(UserService);
  private currencyPipe = inject(CurrencyPipe);
  isEditable: boolean = false;
  insurerInforForm: FormGroup = new FormGroup({
    type: new FormControl('', Validators.required),
    name: new FormControl('', Validators.required),
    companyName: new FormControl(''),
    mortgageName: new FormControl(''),
    supportIntegration: new FormControl(''),
    promoAvailment: new FormControl(false),
    startDate: new FormControl(''),
    mortgage: new FormControl(''),
    mortgageAddress: new FormControl(''),
    consentDate: new FormControl(''),
    consentForm: new FormControl(''),
    consentFormReal: new FormControl(''),
  });

  vehicleInformationForm: FormGroup = new FormGroup({
    chasissNum: new FormControl({ value: '', disabled: true }),
    makeBy: new FormControl('', Validators.required),
    vehicleModel: new FormControl('', Validators.required),
    modelYear: new FormControl('', Validators.required),
    color: new FormControl('', Validators.required),
    engineNumber: new FormControl('', Validators.required),
    plateNumberCSNumber: new FormControl('', Validators.required),
    orderNumber: new FormControl(''),
    orderStatus: new FormControl(''),
    paidPrice: new FormControl('', Validators.required),
    vsiDate: new FormControl('', Validators.required),
    code: new FormControl('', Validators.required),
    variant: new FormControl('', Validators.required),
    bodyType: new FormControl('', Validators.required),
    powerTransmission: new FormControl('', Validators.required),
    fuelType: new FormControl('', Validators.required),
    seats: new FormControl('', Validators.required),
    unloadedWeight: new FormControl('', Validators.required),
    maximumWeight: new FormControl('', Validators.required),
    productClassification: new FormControl('', Validators.required),
  });

  cancelInsuranceReasonForm: FormGroup = new FormGroup({
    reason: new FormControl('', Validators.required),
  });
  subscription = new Subscription();
  currentRoles: string[] = [];
  rowDataVehicleAccessory: VehicleAccessory[] = [];
  isInvoiceRequired: boolean = false;
  isRemarkRequired: boolean = false;
  currentRole: string = '';
  remarks: string = '';
  ROLES = ROLES;
  insuranceStatus = '';
  isHideBtnRelease: boolean = false;
  isLeaving: Subject<boolean> = new Subject<boolean>();
  isChanged: boolean = false;
  isCancel: boolean = false;
  isEditInsurerInfor: boolean = false;
  newRoute: string = '';
  insurancePartnerInformation = {};
  INSURANCE_STATUS = INSURANCE_STATUS;
  isAlreadyAssigned: boolean;
  InsuranceCoverageList = InsuranceCoverageList;
  bodyType: OptionDropdown[];
  ngOnInit(): void {
    this.subscription.add(
      this.router.events.subscribe((event) => {
        if (event instanceof NavigationStart) {
          // Show loading indicator
          this.newRoute = event.url;
        }
        if (event instanceof NavigationEnd) {
          // Hide loading indicator
        }
        if (event instanceof NavigationError) {
          // Hide loading indicator
          // Present error to user
          console.log(event.error);
        }
      })
    );
    this.subscription.add(
      this.route.params.subscribe((params) => {
        this.insuranceId = params['id'];
        this.getInsuranceDetail(this.insuranceId);
        this.getAllVehicleAccessories(this.insuranceId);
        this.subscription.add(
          this.userService.getUserRoles$().subscribe((value) => {
            this.currentRoles = value?.roles || [];
            this.currentRole = this.userService.getGroupRole(this.currentRoles);
          })
        );
      })
    );
  }

  ngOnDestroy() {
    this.subscription && this.subscription.unsubscribe();
    this.dataStoreSerive.setIsEditNotify(false);
  }

  getInsuranceTypeOption(): void {
    this.insuranceDetailService.getInsuranceTypeOption().subscribe(
      (res: any) => {
        if (res) {
          this.bodyType = res?.enums?.bodyType || [];
          if (this.vehicleInformation?.bodyType && this.bodyType && this.bodyType.length) {
            this.vehicleInformation.bodyTypeName = this.bodyType.find(item => item?.code === this.vehicleInformation?.bodyType)?.name || '';
          }
        }
      },
      (err) => {
      }
    );
  }

  checkDocumentEmpty(isEmpty: boolean) {
    this.isInvoiceRequired = isEmpty;
  }

  handleRemarkChange(value: string) {
    this.remarks = value;
    const isEmpty = value.trim().length === 0;
    if (Array.isArray(this.rowDataVehicleAccessory) &&
    this.rowDataVehicleAccessory.length > 0) {
        this.isRemarkRequired = isEmpty;
    }
  }

  reloadInsuranceData() {
    this.getInsuranceDetail(this.insuranceId);
    this.getAllVehicleAccessories(this.insuranceId);
  }

  getInsuranceDetail(insuranceCode: string, ) {
    this.loadingService.showLoader();
    this.subscription.add(
      this.insuranceDetailService.getInsuranceDetail(insuranceCode).subscribe(
        (data) => {
          this.insuranceDetail = data;
          this.getInsuranceInformation(this.insuranceDetail);
          this.getDocuments(this.insuranceDetail);
          this.getInsuranceCalculation(this.insuranceDetail);
          if (this.insuranceDetail.customerUid) {
            this.getCustomerInformation(this.insuranceDetail.customerUid);
          }
          if (this.insuranceDetail.code) {
            this.getVehicleInformation(this.insuranceDetail.code);
          }
          if (this.insuranceId) {
            this.getInsurerPartner(this.insuranceId);
          }
          this.insuranceDetailService.updateInsuranceStatus(
            this.insuranceDetail.status
          );
          this.insuranceStatus = this.insuranceDetail.status;
          this.isAlreadyAssigned = this.insuranceDetail?.alreadyAssigned;
          this.loadingService.hideLoader();
        },
        (err) => {
          this.loadingService.hideLoader();
        }
      )
    );
  }

  getCustomerInformation(uid: string) {
    this.loadingService.showLoader();
    this.insuranceDetailService.getCustomerInformation(uid).subscribe(
      (data) => {
        this.customerInformation = {
          ...data,
          customerBirthDate: data?.customerBirthDate
            ? this.dateTimeHelper.convertUTCtoDisplayDate(
                data?.customerBirthDate,
                DateFormat.ShortDate
              )
            : '',
          ownership: {
            code: data?.insuredData?.ownerType,
            name: this.insuranceDetailService.getOwnerShipName(
              data?.insuredData?.ownerType,
              data?.relationName
            ),
          },
        };
        this.loadingService.hideLoader();
      },
      (err) => {
        this.loadingService.hideLoader();
      }
    );
  }

  getInsurerPartner(insurancePartnerCode: string) {
    this.loadingService.showLoader();
    this.subscription.add(
      this.insuranceDetailService
        .getInsurerPartner(insurancePartnerCode)
        .subscribe(
          (data) => {
            if (!this.isEditInsurerInfor ||
            (this.insuranceDetail?.insuranceCoverage == InsuranceCoverageList.COMP_CTPL &&
            (data?.type === InsuranceType.TELEMATICS || this.insurerInforForm?.value?.type === InsuranceType.TELEMATICS))
            ) {
              this.insurerPartner = null;
              this.insurerPartner = {
                ...data,
                mortgageAddress: data?.mortgageAddr || '',
              };
              this.insurerInforForm.reset();
              this.insurerInforForm.patchValue({
                ...data,
                name: data?.company?.code || '',
                companyName: data?.company?.name || '',
                mortgageAddress: data?.mortgageAddr || '',
              });
            }

            this.insurancePartnerInformation = {
              ...this.insurancePartnerInformation,
              ...this.insurerPartner,
              insuranceType: data?.type,
              insuranceCompany: data?.company?.name,
            };
            this.loadingService.hideLoader();
          },
          (err) => {
            this.loadingService.hideLoader();
          }
        )
    );
  }

  getAllVehicleAccessories(vehicleCode: string) {
    this.loadingService.showLoader();
    this.subscription.add(
      this.insuranceVehicleAccessoriesSevice
        .getAllVehicleAccessories(vehicleCode)
        .pipe(finalize(() => this.loadingService.hideLoader()))
        .subscribe((data) => {
          this.rowDataVehicleAccessory = (data.accessories || []).map(
            (accessory: VehicleAccessoryRespon) => ({
              id: accessory.id,
              item: accessory.name,
              srp: accessory.srp,
              quantity: accessory.quantity,
              coverage: accessory.coverage,
              approved: accessory.status,
            })
          );
          this.remarks = data.remark;
          this.docsVehicleAccessories = data.invoices;
        })
    );
  }

  getVehicleInformation(insuranceCode: string): void {
    this.loadingService.showLoader();
    this.subscription.add(
      this.insuranceDetailService
        .getVehicleInformation(insuranceCode)
        .subscribe({
          next: (data) => {
            this.vehicleInformation = {
              ...data,
              insuranceCost: this.insuranceDetail?.insuranceCost,
            };
            this.loadingService.hideLoader();
            this.getInsuranceTypeOption();
          },
          error: (err) => {
            this.loadingService.hideLoader();
          },
        })
    );
  }

  getInsuranceInformation(insuranceDetail: ResponseNewVehicleInsuranceDetail) {
    const {
      scheme,
      insuranceCoverage,
      transactionType,
      typeOfInsurance,
      dateOfPolicy,
      expireDate,
      policyCode,
      insuranceCost,
      consentDate,
      uploadInsuranceForm,
    } = insuranceDetail;

    this.insuranceInformation = {
      scheme,
      insuranceCoverage,
      transactionType,
      typeOfInsurance,
    };
    this.insurancePartnerInformation = {
      ...this.insurancePartnerInformation,
      dateOfPolicy,
      expireDate,
      policyCode,
      insuranceCost,
      consentDate: this.dateTimeHelper.convertUTCtoDisplayDate(
        consentDate,
        DateFormat.ShortDate
      ),
      uploadInsuranceForm,
    };
  }

  getDocuments(insuranceDetail: ResponseNewVehicleInsuranceDetail) {
    const { documents, EPolicyDocs } = insuranceDetail;
    this.documents = documents;
    this.epolicy = EPolicyDocs;
  }

  getInsuranceCalculation(insuranceDetail: ResponseNewVehicleInsuranceDetail) {
    const { insuranceQuotations, insuranceCombinedQuotation } = insuranceDetail;
    this.insuranceCalculation = insuranceQuotations || insuranceCombinedQuotation;
    
  }

  /**
   * Status is Pending/Failed To assign/In Progress/completed/Partially paid/payment confirmed/payment Processing
   * SHOW Cancel button
   * @param status
   * @returns
   */
  showCancelBtnByStatus(status: string): boolean {
    if (
      (this.currentRole === this.ROLES.COORDINATORGROUP &&
        [
          INSURANCE_STATUS.PENDING,
          INSURANCE_STATUS.FAILED,
          INSURANCE_STATUS.IN_PROCESS,
          INSURANCE_STATUS.COMPLETED,
          INSURANCE_STATUS.PARTPAID,
          INSURANCE_STATUS.PAYMENT_CONFIRM,
          INSURANCE_STATUS.PAYMENT_PROCESSING,
        ].includes(status)) ||
      (this.currentRole === this.ROLES.INSURANCEADMINGROUP &&
        [
          INSURANCE_STATUS.PENDING,
          INSURANCE_STATUS.FAILED,
          INSURANCE_STATUS.IN_PROCESS,
          INSURANCE_STATUS.COMPLETED,
        ].includes(status))
    ) {
      return true;
    }
    return false;
  }

  /**
   * Status is Pending/Failed To assign/Cancelled
   * SHOW Assign button
   * @param status
   * @returns
   */
  showAssignBtnByStatus(status: string): boolean {
    if (
      (this.currentRole === this.ROLES.COORDINATORGROUP ||
        this.currentRole === this.ROLES.INSURANCEADMINGROUP) &&
      [
        INSURANCE_STATUS.PENDING,
        INSURANCE_STATUS.FAILED,
        INSURANCE_STATUS.CANCELLED,
      ].includes(status)
    ) {
      return true;
    }
    return false;
  }

  showApproveBtnByStatus(status: string): boolean {
    if (
      (this.currentRole === this.ROLES.INSURANCEPARTNERGROUP ||
        this.currentRole === this.ROLES.INSURANCEADMINGROUP) &&
      status === INSURANCE_STATUS.IN_PROCESS
    ) {
      return true;
    }
    return false;
  }

  showReleaseBtnByStatus(status: string): boolean {
    return (
      ((this.currentRole === this.ROLES.INSURANCEPARTNERGROUP &&
        status !== INSURANCE_STATUS.CANCELLED) ||
        this.currentRole === this.ROLES.INSURANCEADMINGROUP) &&
      !this.insuranceDetail?.alreadyReleased &&
      this.insuranceDetail?.editable &&
      this.epolicy &&
      this.epolicy.length > 0
    );
  }

  onAssignInsurance(status: string) {
    if (this.rowDataVehicleAccessory && this.rowDataVehicleAccessory.length > 0 && 
      (!this.docsVehicleAccessories || (this.docsVehicleAccessories && this.docsVehicleAccessories.length === 0))) {
      this.notificationService.showError(
        'insurance.error.pleaseUpLoadInvoiceVehicle'
      );
      return;
    }

    if (this.insurerInforForm.valid) {
      const { scheme, transactionType, typeOfInsurance } = this.insuranceDetail;
      if (scheme && transactionType && typeOfInsurance) {
        this.onConfirmAssign();
      } else {
        this.notificationService.showError(
          'insurance.error.pleaseInputInsuranceInformation'
        );
      }
    } else {
      this.notificationService.showError(
        'insurance.error.pleaseInputInsuranceCompany'
      );
    }
  }

  onReleaseInsurance() {
    this.loadingService.showLoader();
    this.subscription.add(
      this.insuranceDetailService
        .releaseEpolicy(this.insuranceId)
        .pipe(finalize(() => this.loadingService.hideLoader()))
        .subscribe(
          (res) => {
            if (res.code === 'SUCCESS') {
              this.notificationService.showSuccess(res?.message);
            } else {
              this.notificationService.showError(res?.message);
            }
            this.getInsuranceDetail(this.insuranceId);
          },
          (err) => {
            handleErrors(err, this.notificationService);
          }
        )
    );
  }

  onCancelInsurance() {
    const dialogRef = this.dialog.open(ModalCancelInsuranceComponent, {
      width: '530px',
      data: {
        form: this.cancelInsuranceReasonForm,
        title: this.translateService.instant(
          'insurance.modal.cancelInsurance',
          { insuranceNo: this.insuranceId }
        ),
        icon: 'ic-warning',
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.confirm'),
      },
    });

    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((res) => {
        if (res) {
          this.loadingService.showLoader();
          this.subscription.add(
            this.insuranceDetailService
              .cancelInsurance(
                this.cancelInsuranceReasonForm.value.reason,
                this.insuranceId
              )
              .pipe(finalize(() => this.loadingService.hideLoader()))
              .subscribe(
                (res: ResponseCommon) => {
                  if (res?.code === '200') {
                    this.notificationService.showSuccess(
                      this.translateService.instant(
                        'insurance.cancelInsuranceSuccess',
                        { insuranceNo: this.insuranceId }
                      )
                    );
                  } else {
                    this.notificationService.showError(res?.message);
                  }
                  this.getInsuranceDetail(this.insuranceId);
                },
                (err) => {
                  handleErrors(err, this.notificationService);
                  this.getInsuranceDetail(this.insuranceId);
                }
              )
          );
        }
      });
  }

  onApproveInsurance() {
    if (this.isRemarkRequired) {
        this.notificationService.showError(
        'vehicleAccessories.pleaseEnterRemarks'
        );
        return;
    }

    let canApprove = this.insuranceDetail?.policyCode && this.insuranceDetail?.expireDate && this.insuranceDetail?.dateOfPolicy;
    if (this.insuranceDetail?.insuranceCoverage == 'COMP_CTPL') {
      canApprove = this.insuranceDetail?.insuranceCombinedQuotation?.compData?.policyCode &&
      this.insuranceDetail?.insuranceCombinedQuotation?.compData?.policyDate &&
      this.insuranceDetail?.insuranceCombinedQuotation?.compData?.policyExpiryDate &&
      this.insuranceDetail?.insuranceCombinedQuotation?.ctplData?.policyCode &&
      this.insuranceDetail?.insuranceCombinedQuotation?.ctplData?.policyDate &&
      this.insuranceDetail?.insuranceCombinedQuotation?.ctplData?.policyExpiryDate; 
    }

    if (
      canApprove
    ) {
      const dialogRef = this.dialog.open(DialogConfirmComponent, {
        width: '530px',
        data: {
          title: this.translateService.instant(
            'insurance.modal.approveInsurance'
          ),
          icon: 'ic-checked-red',
          confirmMsg: this.translateService.instant(
            'insurance.modal.approveInsuranceMsg',
            {
              insuranceNo: this.insuranceId,
            }
          ),
          cancelBtn: this.translateService.instant('common.cancel'),
          submitBtn: this.translateService.instant('common.approve'),
        },
      });

      dialogRef
        .afterClosed()
        .pipe(filter((result) => result?.action === ActionModal.Submit))
        .subscribe((res) => {
          if (res) {
            this.loadingService.showLoader();
            this.subscription.add(
              this.insuranceDetailService
                .approveInsurance(this.insuranceId)
                .pipe(finalize(() => this.loadingService.hideLoader()))
                .subscribe(
                  (data: any) => {
                    if (data.code === '200') {
                      this.notificationService.showSuccess(
                        this.translateService.instant(
                          'insurance.updateInsuranceSuccess',
                          { insuranceNo: this.insuranceId }
                        )
                      );
                    } else {
                      this.notificationService.showError(data?.message);
                    }
                    this.getInsuranceDetail(this.insuranceId);
                  },
                  (err) => {
                    handleErrors(err, this.notificationService);
                  }
                )
            );
          }
        });
    } else {
      this.notificationService.showError('insurance.error.errorWhenApprove');
    }
  }

  onConfirmAssign() {
    const dialogRef = this.dialog.open(DialogConfirmComponent, {
      width: '530px',
      data: {
        title: this.translateService.instant(
          'insurance.modal.confirmAssignment'
        ),
        icon: 'ic-checked-red',
        confirmMsg: this.translateService.instant(
          'insurance.modal.confirmAssignmentMsg',
          {
            insuranceNo: this.insuranceId,
            insuranceCompanyName: this.insurerPartner?.company?.name,
          }
        ),
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.confirm'),
      },
    });

    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((res) => {
        if (res) this.assignNewInsurances();
      });
  }

  readFileAsync(file) {
    return new Promise((resolve, reject) => {
      const reader: any = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result.split(',')[1]);
    });
  }

  async onFileSelected(e: any) {
    this.loadingService.showLoader();
    try {
      const { files, isEPolicy } = e;
      let params: any;
      let tempFiles: string[] = [];
      let tempFileName: string[] = [];
      for (const file of files) {
        const content: any = await this.readFileAsync(file);
        tempFiles.push(content);
        tempFileName.push(file.name);
      }
      if (isEPolicy) {
        params = {
          EPolicyDocs: tempFiles,
          EPolicyDocsName: tempFileName,
        };
      } else {
        params = {
          docs: tempFiles,
          docsName: tempFileName,
        };
      }
      this.subscription.add(
        this.insuranceDetailService
          .uploadFile(params, this.insuranceId)
          .subscribe(
            (data) => {
              if (data.code === 'SUCCESS') {
                this.getInsuranceDetail(this.insuranceId);
              } else {
                handleErrors(data, this.notificationService);
              }
            },
            (err) => {
              this.loadingService.hideLoader();
            }
          )
      );
    } catch (error) {
      this.loadingService.hideLoader();
    }
  }

  onRemoveFile(e: any) {
    const { file, isEPolicy } = e;
    const dialogRef = this.dialog.open(DialogConfirmComponent, {
      width: '530px',
      data: {
        title: this.translateService.instant(
          'insurance.modal.removeAttachment'
        ),
        icon: 'ic-delete',
        class: 'ic-color-red',
        confirmMsg: this.translateService.instant(
          'insurance.modal.removeAttachmentConfirm',
          { fileName: file?.realFileName }
        ),
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.remove'),
      },
    });

    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe(() => {
        this.loadingService.showLoader();
        this.subscription.add(
          this.insuranceDetailService
            .deleteFile(file.code)
            .pipe(finalize(() => this.loadingService.hideLoader()))
            .subscribe(
              (data) => {
                this.notificationService.showSuccess(
                  'insurance.attachmentRemoved'
                );
                this.getInsuranceDetail(this.insuranceId);
              },
              (err) => {
                handleErrors(err, this.notificationService);
              }
            )
        );
      });
  }

  onEditInsurerInformation(data: any) {
    const tempInsurerPartner = {
      ...data,
      name: this.insurerInforForm.value.name || data?.company?.code || '',
      mortgage:
        this.insurerInforForm.value.mortgage?.code ||
        data?.mortgage?.code ||
        '',
    };
    this.insurerInforForm.patchValue(tempInsurerPartner);
    const dialogRef = this.dialog.open(ModalInsurerInfomationEditComponent, {
      width: '570px',
      data: {
        mainForm: this.insurerInforForm,
        insuranceId: this.insuranceId,
        status: this.insuranceDetail?.status,
        vinPaired: this.insuranceDetail?.vinPaired,
        insuranceCoverage: this.insuranceDetail?.insuranceCoverage,
      },
    });

    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((res) => {
        if (res) {
          this.notificationService.showSuccess(
            'insurance.updateInsurerSuccess'
          );
          this.insurerPartner = {
            ...this.insurerInforForm?.value,
            company: {
              name: this.insurerInforForm?.value?.companyName || this.insurerPartner?.company?.name || '',
              code: this.insurerInforForm?.value?.name || this.insurerPartner?.company?.code || '',
            },
            mortgage: {
              name: this.insurerInforForm?.value?.mortgageName || this.insurerPartner?.mortgage?.name || '',
              code: this.insurerInforForm?.value?.mortgage || this.insurerPartner?.mortgage?.code || '' ,
            },
            supportInsIntegration:
              this.insurerInforForm?.value?.supportIntegration || '',
          };
          if (
            this.insurerInforForm?.value.type === InsuranceType.TELEMATICS &&
            this.insurerInforForm.value.consentFormReal
          ) {
            const file = this.insurerInforForm.value.consentFormReal[0];
            this.insurerPartner.consentForm = {
              creationTime: new Date(),
              realFileName: file?.realFileName || file?.name,
              downloadUrl: URL.createObjectURL(file),
            };
          }
          this.isEditInsurerInfor = true;
          this.isChanged = true;
        }
      });
  }

  private parseCurrencyString(value: string | null | undefined): string | null {
    if (value === null || value === undefined || typeof value !== 'string') {
      return null;
    }
    const cleanedValue = value.replace(/[₱$€£¥,]/g, '').trim();
    if (/^-?\d+(\.\d+)?$/.test(cleanedValue)) {
      return cleanedValue;
    }
    return null;
  }

  private formatDateToCustomUTC(date: Date): string {
    const year = date.getUTCFullYear().toString().padStart(4, '0');
    const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
    const day = date.getUTCDate().toString().padStart(2, '0');
    const hours = date.getUTCHours().toString().padStart(2, '0');
    const minutes = date.getUTCMinutes().toString().padStart(2, '0');
    const seconds = date.getUTCSeconds().toString().padStart(2, '0');
    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}+0000`;
  }

  onUpdateVehicleInformation(data: any) {
    if (!this.vehicleInformation || !this.vehicleInformationForm) {
      console.error(
        'Cannot update: Vehicle information or form not initialized.'
      );
      return;
    }
    const priceData = this.vehicleInformation?.paidPrice;
    const originalCurrencyIso = priceData?.currencyIso || 'PHP';
    let formattedPaidPrice = '';

    if (priceData?.value && priceData?.currencyIso) {
      try {
        const numericValue = parseFloat(priceData.value);
        if (!isNaN(numericValue)) {
          formattedPaidPrice =
            this.currencyPipe.transform(
              numericValue,
              priceData.currencyIso,
              'symbol',
              '1.2-2',
              'en-PH'
            ) || '';
        } else {
          console.warn(
            'Paid price value is not a valid number:',
            priceData.value
          );
        }
      } catch (error) {
        console.error('Error formatting currency:', error);
      }
    }
    const tempVehicle = {
      ...this.vehicleInformation,
      plateNumberCSNumber: this.vehicleInformation?.plateNumber,
      orderStatus: this.vehicleInformation?.orderStatus?.name ? 
      this.vehicleInformation?.orderStatus?.name :this.vehicleInformation?.orderStatus,
      orderNumber: this.vehicleInformation?.orderNo,
      paidPrice: formattedPaidPrice,
      code: this.vehicleInformation?.code,
      maximumWeight: this.vehicleInformation?.maximumWeight,
      unloadedWeight: this.vehicleInformation?.unloadedWeight,
    };
    this.vehicleInformationForm.patchValue(
      Object.fromEntries(
        Object.entries(tempVehicle).map(([key, value]) => [key, value || ''])
      )
    );
    const dialogRef = this.dialog.open(ModalUpdateVehicleInfomationComponent, {
      width: '850px',
      data: {
        mainForm: this.vehicleInformationForm,
      },
    });

    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((res) => {
        if (res) {
          const finalFormValues = this.vehicleInformationForm.value;
          const editedPriceStringValue = this.parseCurrencyString(
            finalFormValues.paidPrice
          );
          const apiPayload = {
            bodyType: finalFormValues.bodyType,
            chasissNum: this.vehicleInformation.chasissNum,
            code: finalFormValues.code,
            color: finalFormValues.color,
            engineNumber: finalFormValues.engineNumber,
            fuelType: finalFormValues.fuelType,
            makeBy: finalFormValues.makeBy,
            maximumWeight: finalFormValues.maximumWeight,
            modelYear: finalFormValues.modelYear,
            orderNo: finalFormValues.orderNumber === "" ? null : finalFormValues.orderNumber,
            orderStatus: finalFormValues.orderStatus === "" ? null : finalFormValues.orderStatus,
            paidPrice: editedPriceStringValue
              ? {
                  currencyIso: originalCurrencyIso,
                  value: editedPriceStringValue,
                }
              : null,
            plateNumber: finalFormValues.plateNumberCSNumber,
            powerTransmission: finalFormValues.powerTransmission,
            productClassification: finalFormValues.productClassification,
            seats: finalFormValues.seats,
            unloadedWeight: finalFormValues.unloadedWeight,
            variant: finalFormValues.variant,
            vehicleModel: finalFormValues.vehicleModel,
            vsiDate: finalFormValues.vsiDate
              ? this.formatDateToCustomUTC(finalFormValues.vsiDate)
              : null,
          };
          this.loadingService.showLoader();
          this.insuranceDetailService
            .updateVehicleInfo(this.insuranceId, apiPayload)
            .pipe(finalize(() => this.loadingService.hideLoader()))
            .subscribe({
              next: (updateResponse) => {
                this.notificationService.showSuccess(
                  'newVehicleInsurance.detail.vehicleInformation.updateVehicleSuccess'
                );
                this.getVehicleInformation(this.insuranceDetail.code);
              },
              error: (err) => {
                console.error('Error updating vehicle info:', err);
                this.notificationService.showError(
                  'newVehicleInsurance.detail.vehicleInformation.updateVehicleFailed'
                );
              },
            });
        }
      });
  }

  async assignNewInsurances() {
    const dataForm = this.insurerInforForm.value;
    const body: any = {
      insuranceCode: this.insuranceId,
      insuranceType: dataForm?.type || '',
      insuranceCompany: dataForm?.name || '',
      startDate: dataForm.startDate ? moment(dataForm.startDate).format() : '',
      mortgage: dataForm?.mortgage || '',
      mortgageAddress: dataForm?.mortgageAddress || '',
      insurancePromoAvailment: dataForm?.promoAvailment,
    };
    if (dataForm?.type === InsuranceType.TELEMATICS) {
      body.consentDate = dataForm?.consentDate
        ? moment(dataForm?.consentDate).format()
        : '';
      if (dataForm?.consentFormReal && dataForm?.consentFormReal[0]) {
        const file = await this.readFileAsync(dataForm?.consentFormReal[0]);
        body.consentForm = {
          fileName: dataForm?.consentFormReal[0]?.name || '',
          data: file,
        };
      }
    }

    this.loadingService.showLoader();
    this.subscription.add(
      this.insuranceDetailService
        .assignNewInsurances(body, this.insuranceId)
        .pipe(finalize(() => this.loadingService.hideLoader()))
        .subscribe(
          (res: ResponseCommon) => {
            if (res.code == '200') {
              this.notificationService.showSuccess(res?.message);
            } else {
              this.notificationService.showError(res?.message);
            }
            this.isEditInsurerInfor = false;
            this.isChanged = false;
            if (this.insuranceId) {
              this.getInsuranceDetail(this.insuranceId);
            }
          },
          (err) => {
            handleErrors(err, this.notificationService);
          }
        )
    );
  }

  onDownloadFile(file: Documents) {
    downloadFile(file.realFileName, file.downloadUrl);
  }

  openLeavingModal() {
    const dialogRef = this.dialog.open(DialogConfirmComponent, {
      width: '530px',
      data: {
        title: this.translateService.instant('leave.title'),
        icon: 'ic-warning',
        confirmMsg: this.translateService.instant('leave.message'),
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.leave'),
      },
    });

    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((res) => {
        if (res) {
          this.onLeave(true);
        } else {
          this.onStay();
        }
      });
  }

  onLeave(isCancel: boolean) {
    this.isLeaving.next(true);

    this.isCancel = isCancel;

    if (this.newRoute !== '') {
      this.router.navigate([this.newRoute]);
    } else {
      this.router.navigate(['insurance/new-vehicle-insurance']);
    }
  }

  onStay() {
    this.isLeaving.next(false);
  }

  // @HostListener allows us to also guard against browser refresh, close, etc.
  @HostListener('window:beforeunload', ['$event'])
  unloadHandler(event: Event) {
    if (this.isChanged) {
      return false;
    }
    return true;
  }
  canDeactivate(): boolean | Observable<boolean> | Promise<boolean> {
    if (this.isChanged) {
      if (!this.dialog.openDialogs || !this.dialog.openDialogs.length) {
        this.openLeavingModal();
      }
      return this.isCancel ? this.isCancel : this.isLeaving;
    } else {
      return true;
    }

    // insert logic to check if there are pending changes here;
    // returning true will navigate without confirmation
    // returning false will show a confirm dialog before navigating away
  }
}
