import { Component, EventEmitter, inject, Input, OnInit, Output } from '@angular/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { PERMISSIONS_CODE } from '../../../../../../core/constants';
import { UserService } from '../../../../../../core/services/user';
import { CurrencyItem } from '../../../../../../core/interfaces';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-cell-currency-custom',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './cell-currency-custom.component.html',
  styleUrls: ['./cell-currency-custom.component.scss']
})
export class CellCurrencyCustomComponent implements ICellRendererAngularComp {
  @Input() rowIndex!: number;

  @Output() close = new EventEmitter<void>();
  userService = inject(UserService);
    
  readonly PERMISSIONS_CODE = PERMISSIONS_CODE;
    
  params!: any;
  data: CurrencyItem;

  agInit(params: any): void {
    this.params = params;
    this.data = params?.value === undefined ?  null : params?.value;
  }

  refresh(params: any): boolean {
    return true;
  }
}
