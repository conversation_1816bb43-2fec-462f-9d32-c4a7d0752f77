import { Component, EventEmitter, Input, Output } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-action-buttons',
  standalone: true,
  imports: [
    TranslateModule,
  ],
  templateUrl: './action-buttons.component.html',
  styleUrl: './action-buttons.component.scss'
})
export class ActionButtonsComponent {
  @Input() showCancel: boolean = false;
  @Input() showAssign: boolean = false;
  @Input() showApprove: boolean = false;
  @Input() showRelease: boolean = false;

  @Output() handleAssignInsurance = new EventEmitter();
  @Output() handleReleaseInsurance = new EventEmitter();
  @Output() handleCancelInsurance = new EventEmitter();
  @Output() handleApproveInsurance = new EventEmitter();
  
  onAssignInsurance() {
    this.handleAssignInsurance.emit();
  }

  onReleaseInsurance() {
    this.handleReleaseInsurance.emit();
  }

  onCancelInsurance() {
    this.handleCancelInsurance.emit();
  }

  onApproveInsurance() {
    this.handleApproveInsurance.emit();
  }
}
