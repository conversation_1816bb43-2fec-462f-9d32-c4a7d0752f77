import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, inject, OnInit, Output } from '@angular/core';
import { ReactiveFormsModule, FormsModule, FormBuilder, FormGroup, FormControl, Validators } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDialogModule, MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { toNumber, sum, sumBy } from 'lodash-es';
import { Subject, debounceTime, finalize } from 'rxjs';
import { IconModule } from '../../../../../../core/icon/icon.module';
import { LoadingService, NotificationService } from '../../../../../../core/services';
import { FormGroupComponent } from '../../../../../../core/shared';
import { LoadingComponent } from '../../../../../../layout/global/loading/loading.component';
import { InsuranceCoverageList, InsuranceInput } from '../../../../enum';
import { InsuranceQuotation, UpdateInsuranceCostRequest } from '../../../../interfaces';
import { InsuranceCalculationService } from '../../../../services/insurance-calculation/insurance-calculation.service';

@Component({
  selector: 'app-modal-calculate-compulsory-tpl',
  imports: [
    CommonModule,
    TranslateModule,
    IconModule,
    ReactiveFormsModule,
    FormGroupComponent,
    MatCheckboxModule,
    MatDialogModule,
    FormsModule,
    MatDividerModule,
    LoadingComponent,
  ],
  providers: [LoadingService, NotificationService, InsuranceCalculationService],
  standalone: true,
  templateUrl: './modal-calculate-compulsory-tpl.component.html',
  styleUrls: ['./modal-calculate-compulsory-tpl.component.scss'],
})
export class ModalCalculateCompulsoryTplComponent implements OnInit {
  @Output() insuranceUpdated = new EventEmitter();

  data = inject(MAT_DIALOG_DATA);

  notificationService = inject(NotificationService);
  translateService = inject(TranslateService);
  loadingService = inject(LoadingService);
  private insuranceService = inject(InsuranceCalculationService);

  private fb = inject(FormBuilder);
  private cdr = inject(ChangeDetectorRef);
  private calculateSubject = new Subject<string>();

  dialogRef = inject(MatDialogRef<ModalCalculateCompulsoryTplComponent>);
  installmentPayment: boolean = false;
  netPremiumChanged: boolean = false;
  netCoverageChanged: boolean = false;
  totalPremiumChanged: boolean = false;
  curNetPremiumBeforeTaxPremium: number = 0;
  vatRatePercent: number = 0;
  curTotal: number = 0;
  docsRatePercent: number = 0;
  form: FormGroup;
  typeInput: string = '';
  controlNameRate: string = '';
  nameInput: string = '';
  INSURANCE_INPUT = InsuranceInput;
  isRenewal: boolean = false;
  insuranceItems = [
    {
      label: this.translateService.instant('insuranceCalculation.ctpl'),
      controlName: 'ctpl',
      coverage: { value: 0, disable: false },
      premium: { value: 0, disable: false },
    },
    {
      controlName: '',
      mode: 'noted',
    },
    {
      label: this.translateService.instant(
        'insuranceCalculation.netPremiumBeforeTax'
      ),
      controlName: 'netPremiumBeforeTax',
      premium: { value: 0, disable: true },
      coverage: { value: 0, disable: true },
      bold: true,
    },
    {
      label: this.translateService.instant(
        'insuranceCalculation.documentaryStamp'
      ),
      controlName: 'documentaryStamp',
      premium: { value: 0, disable: false },
    },
    {
      label: this.translateService.instant('insuranceCalculation.vat'),
      controlName: 'vat',
      premium: { value: 0, disable: true },
    },
    {
      label: this.translateService.instant(
        'insuranceCalculation.localGovernmentTax'
      ),
      controlName: 'localGovernmentTax',
      premium: { value: 0, disable: false },
    },
    {
      label: this.translateService.instant('insuranceCalculation.grossPremium'),
      controlName: 'grossPremium',
      premium: { value: 0, disable: true },
      bold: true,
    },
    {
      label: this.translateService.instant(
        'insuranceCalculation.authenticationFee'
      ),
      controlName: 'authenticationFee',
      premium: { value: 0, disable: false },
    },
    {
      label: this.translateService.instant('insuranceCalculation.totalPremium'),
      controlName: 'total',
      premium: { value: 0, disable: true },
      bold: true,
    },
  ];

  excludedPremiumFields = [
    'documentaryStamp',
    'vat',
    'localGovernmentTax',
    'grossPremium',
    'authenticationFee',
    'total',
    'netPremiumBeforeTax',
  ];

  excludedPremiumFieldsInput = [
    'documentaryStampPremium',
    'vatPremium',
    'localGovernmentTaxPremium',
    'grossPremiumPremium',
    'authenticationFeePremium',
    'totalPremium',
    'netPremiumBeforeTaxPremium',
  ];

  onlyPremiumFormHaveRateFields = [];

  ngOnInit(): void {
    this.isRenewal = this.data?.isRenewal;
    this.createInsuranceForm();
    this.prefillFormWithData(this.data.insuranceCalculation);
    this.calculateSubject.pipe(debounceTime(800)).subscribe((changedControlName) => {
      if (this.typeInput === 'Premium' || this.typeInput === 'Rate')
        this.netPremiumChanged = true;
      if (this.typeInput === 'Coverage' || (this.typeInput === 'Premium' && this.onlyPremiumFormHaveRateFields.includes(this.nameInput))) this.netCoverageChanged = true;
      this.calculatePremiums(changedControlName);
    });
  }

  private createInsuranceForm(): void {
    this.form = this.fb.group({
      noVAT: new FormControl(false),
    });

    this.form.addControl(
      'netPremiumBeforeTaxPremium',
      new FormControl({ value: 0, disabled: true })
    );
    this.form.addControl(
      'netPremiumBeforeTaxCoverage',
      new FormControl({ value: 0, disabled: true })
    );

    this.insuranceItems.forEach((item) => {

      if (item.coverage) {
        const coverageControl = new FormControl(item.coverage.value ?? 0, [
          Validators.required,
          Validators.pattern(/^\d+(\.\d{1,2})?$/),
        ]);
        if (item.coverage.disable) {
          coverageControl.disable();
        }
        this.form.addControl(`${item.controlName}Coverage`, coverageControl);
      }

      if (item.premium) {
        const premiumControl = new FormControl(item.premium.value ?? 0);
        const premiumAfterNetControl = new FormControl<number | null>(
          item.premium.value ?? 0,
          [Validators.pattern(/^\d+(\.\d{1,2})?$/)]
        );

        if (item.premium.disable) {
          premiumControl.disable();
        }
        if (!this.excludedPremiumFields.includes(`${item.controlName}`)) {
          this.form.addControl(
            `${item.controlName}Premium`,
            premiumAfterNetControl
          );
        }
        this.form.addControl(`${item.controlName}Premium`, premiumControl);
      }
    });
    this.form.get('noVAT')?.valueChanges.subscribe((checked) => {
      this.calculatePremiums();
    });
  }


  private prefillFormWithData(data: InsuranceQuotation): void {
    if (!data) return;
    const premiums: number[] = [];
    const coverages: number[] = [];

    let netPremiumBeforeTaxPremium = 0;
    let netPremiumBeforeTaxCoverage = 0;

    // Populate form controls with values from `insuranceCalculation`
    this.form.patchValue({
      installmentPayment: data.installmentPayment,
      terms: data.installmentMonths,
      monthlyPayment: data.installmentEachMonth,
    });

    this.insuranceItems.forEach((item) => {
      const controlName = item.controlName;

      switch (controlName) {
        case 'ctpl':
          this.form.get(controlName)?.setValue(data.premiumAonIssuingRate ?? 0);
          this.form
            .get(`${controlName}Coverage`)
            ?.setValue(data.coverageThirdPartyLiability ?? 0);
          this.form
            .get(`${controlName}Premium`)
            ?.setValue(data.premiumThirdPartyLiability ?? 0);
          break;
        case 'documentaryStamp':
          this.form
            .get(`${controlName}Premium`)
            ?.setValue(data.docsAmount ?? 0);
          this.docsRatePercent = data.docsRate;
          break;

        case 'vat':
          this.form.get(`${controlName}Premium`)?.setValue(data.vatAmount ?? 0);
          this.vatRatePercent = data.vatRate;
          if (data.vatAmount === 0) {
            this.form.get('noVAT')?.setValue(true);
          }
          break;

        case 'localGovernmentTax':
          this.form
            .get(`${controlName}Premium`)
            ?.setValue(data.localGovernmentStamp ?? 0);
          break;

        case 'grossPremium':
          this.form
            .get(`${controlName}Premium`)
            ?.setValue(data.grossPremium?.value ?? 0);
          break;

        case 'authenticationFee':
          this.form
            .get(`${controlName}Premium`)
            ?.setValue(data.authenticationCost ?? 0);
          break;

        case 'total':
          this.form
            .get(`${controlName}Premium`)
            ?.setValue(data.totalPremium?.value ?? 0);
          break;
      }

      if (!this.excludedPremiumFields.includes(`${controlName}`)) {
        const premium =
          toNumber(this.form.get(`${controlName}Premium`)?.value) || 0;
        const coverage =
          toNumber(this.form.get(`${controlName}Coverage`)?.value) || 0;

        if (premium > 0) {
          premiums.push(premium);
        }
        if (coverage > 0) {
          coverages.push(coverage);
        }
      }
    });

    netPremiumBeforeTaxPremium = sum(premiums);
    netPremiumBeforeTaxCoverage = sum(coverages);
    this.curNetPremiumBeforeTaxPremium = +netPremiumBeforeTaxPremium.toFixed(2);
    this.curTotal = data.totalPremium?.value ?? 0;
    this.form.patchValue({
      netPremiumBeforeTaxCoverage: netPremiumBeforeTaxCoverage.toFixed(2),
      netPremiumBeforeTaxPremium: netPremiumBeforeTaxPremium.toFixed(2),
      documentaryStampPremium: data.docsAmount ?? 0,
      vatPremium: data.vatAmount ?? 0,
      localGovernmentTaxPremium: data.localGovernmentStamp ?? 0,
      grossPremiumPremium: data.grossPremium?.value ?? 0,
      totalPremium: data.totalPremium?.value ?? 0,
    });
    this.cdr.detectChanges();
  }

  calculatePremiums(changedControlName?: string): void {
    let newNetPremium = 0;
    let newNetCoverage = 0;
    const premiums: number[] = [];
    const coverages: number[] = [];
  
    let baseControlName = changedControlName;
    let changeType = '';
    if (changedControlName?.endsWith('Premium')) {
      baseControlName = changedControlName.replace('Premium', '');
      changeType = 'Premium';
    } else if (changedControlName?.endsWith('Coverage')) {
      baseControlName = changedControlName.replace('Coverage', '');
      changeType = 'Coverage';
    } else {
      changeType = 'Rate';
    }
  
    if (this.onlyPremiumFormHaveRateFields.includes(baseControlName)) {
      // Special case for vehicleAccessories: use odTheft's rate
      let rate = 0;
      if (baseControlName === 'vehicleAccessories') {
        rate = toNumber(this.form.get('odTheft')?.value) || 0;
      } else {
        rate = toNumber(this.form.get(baseControlName)?.value) || 0;
      }
      const coverage = toNumber(this.form.get(`${baseControlName}Coverage`)?.value) || 0;
      const premium = toNumber(this.form.get(`${baseControlName}Premium`)?.value) || 0;
  
      if (changeType === 'Rate' || changeType === 'Coverage') {
        // Rate or Coverage changed: recalculate Premium only
        const newPremium = rate > 0 ? (coverage * rate) / 100 : 0;
        this.form.get(`${baseControlName}Premium`)?.setValue(newPremium.toFixed(2), { emitEvent: false });
      } else if (changeType === 'Premium') {
        // Premium changed: recalculate Coverage only
        const newCoverage = rate > 0 ? (premium * 100) / rate : 0;
        this.form.get(`${baseControlName}Coverage`)?.setValue(newCoverage.toFixed(2), { emitEvent: false });
      }
    }
  
    this.insuranceItems.forEach((item) => {
      const controlName = item.controlName;
      if (!this.excludedPremiumFields.includes(controlName)) {
        const premium = toNumber(this.form.get(`${controlName}Premium`)?.value) || 0;
        const coverage = toNumber(this.form.get(`${controlName}Coverage`)?.value) || 0;
        if (premium > 0) premiums.push(premium);
        if (coverage > 0) coverages.push(coverage);
      }
    });
  
    newNetPremium = sumBy(premiums);
    newNetCoverage = sumBy(coverages);
  
    if (+this.curNetPremiumBeforeTaxPremium !== +newNetPremium) {
      this.netPremiumChanged = true;
      this.curNetPremiumBeforeTaxPremium = newNetPremium;
    }
    if (+this.form.get('netPremiumBeforeTaxCoverage')?.value !== +newNetCoverage) {
      this.netCoverageChanged = true;
    }
    let documentaryStamp;
    let localGovernmentTax;
    let vat;
    let authenticationFeePremium;
    // Calculate derived fields
    if (
      this.excludedPremiumFields.includes(this.nameInput)
    ) {
      documentaryStamp =
        toNumber(this.form.get(`documentaryStampPremium`)?.value) || 0;
      localGovernmentTax =
        toNumber(this.form.get(`localGovernmentTaxPremium`)?.value) || 0;
      authenticationFeePremium =
        toNumber(this.form.get(`authenticationFeePremium`)?.value) || 0;
    } else {
      documentaryStamp = newNetPremium * (this.docsRatePercent / 100);
      localGovernmentTax =
      toNumber(this.form.get(`localGovernmentTaxPremium`)?.value) || 0;
      authenticationFeePremium =
        toNumber(this.form.get(`authenticationFeePremium`)?.value) || 0;
    }

    vat = this.form.get('noVAT')?.value
    ? 0
    : newNetPremium * this.vatRatePercent;

    const grossPremium =
      newNetPremium + documentaryStamp + localGovernmentTax + vat;
    const total = parseFloat(
      (grossPremium + authenticationFeePremium).toFixed(2)
    );
    // Update form values without emitting events
    this.form.patchValue(
      {
        netPremiumBeforeTaxPremium: parseFloat(newNetPremium.toFixed(2)),
        netPremiumBeforeTaxCoverage: parseFloat(newNetCoverage.toFixed(2)),
        documentaryStampPremium: parseFloat(documentaryStamp.toFixed(2)),
        localGovernmentTaxPremium: parseFloat(localGovernmentTax.toFixed(2)),
        vatPremium: parseFloat(vat.toFixed(2)),
        grossPremiumPremium: parseFloat(grossPremium.toFixed(2)),
        totalPremium: parseFloat(total.toFixed(2)),
      },
      { emitEvent: false }
    );
    if (this.data?.insuranceCalculation?.vatAmount != this.form.get(`vatPremium`)?.value) {
      this.form.get(`vatPremium`)?.markAsDirty();
    } else {
      this.form.get(`vatPremium`)?.markAsPristine();
    }
    this.totalPremiumChanged =
    this.form.get(`documentaryStampPremium`).dirty ||
    this.form.get(`localGovernmentTaxPremium`).dirty ||
    this.form.get(`vatPremium`).dirty ||
    this.form.get(`authenticationFeePremium`).dirty ||
    this.netPremiumChanged;
    
    this.cdr.detectChanges();
  }

  updateInsuranceCalculation(): void {
    if (this.form.valid) {
      try {
        const updatedData = this.form;
        const insuranceCode = this.data.insuranceId;
        const vehiclePaidPrice = this.data.paidPriceVehicle;

        const netPremium =
          parseFloat(this.form.get('netPremiumBeforeTaxPremium')?.value) || 0;
        const netCoverage =
          parseFloat(this.form.get('netPremiumBeforeTaxCoverage')?.value) || 0;

        const formTotalPremium =
          parseFloat(this.form.get('totalPremium')?.value) || 0;

        const dataSend = this.responDataInsuranceCalculation(
          vehiclePaidPrice,
          updatedData,
          formTotalPremium,
          netPremium,
          netCoverage
        );
        // If there is create insurance, update the calculation only, no need to save data yet
        if (this.data.insuranceInput === this.INSURANCE_INPUT.CREATE) {
          this.insuranceUpdated.emit(dataSend);
          this.dialogRef.close();
        } else {
          // If there is new/renewal insurance, save the data
          if (!insuranceCode) {
            this.notificationService.showError(
              this.translateService.instant(
                'newVehicleInsurance.detail.insuranceCalculation.codeMissing'
              )
            );
            return;
          }
          this.loadingService.showLoader();
          this.insuranceService
            .updateSubInsuranceCost(
              insuranceCode,
              dataSend
            )
            .pipe(
              finalize(() => {
                this.loadingService.hideLoader();
              })
            )
            .subscribe(
              (res) => {
                if (res.code === '200') {
                  this.notificationService.showSuccess(
                    this.translateService.instant(
                      'newVehicleInsurance.detail.insuranceCalculation.updateCTPLSuccess'
                    )
                  );
                  this.insuranceUpdated.emit();
                  this.dialogRef.close(updatedData.value);
                }
              },
              (err) => {
                this.notificationService.showError(
                  this.translateService.instant(
                    'newVehicleInsurance.detail.insuranceCalculation.updateCTPLFailed'
                  )
                );
              }
            );
        }
      } catch (err) {
        console.log(err);
        this.loadingService.hideLoader();
      }
    }
  }

  responDataInsuranceCalculation(
    vehiclePaidPrice: number | string,
    data: any,
    total: number,
    netPremium: number,
    netCoverage: number
  ): UpdateInsuranceCostRequest | any {
    const toNumber = (value: any): number => parseFloat(value ?? 0.0);
    return {
      coverageType: InsuranceCoverageList.CTPL,
      vehiclePaidPrice: toNumber(vehiclePaidPrice),
      premiumOdIssuingRate: toNumber(this.form.controls['odTheft']?.value ?? 0),
      premiumOdIssuing: toNumber(this.form.controls['odTheftPremium']?.value  ?? 0),
      premiumAonIssuingRate: toNumber(this.form.controls['aon']?.value ?? 0),
      premiumAonIssuing: toNumber(this.form.controls['aonPremium']?.value ?? 0),
      premiumRiotStrikeCivilCommotionRate: toNumber(this.form.controls['rscc']?.value ?? 0),
      premiumRiotStrikeCivilCommotion: toNumber(this.form.controls['rsccPremium']?.value ?? 0),
      premiumExcessBodilyInjury: toNumber(this.form.controls['excessiveBodyInjuryPremium']?.value ?? 0),
      premiumPropertyDamage: toNumber(this.form.controls['propertyDamagePremium']?.value ?? 0),
      premiumPersonalAccident: toNumber(this.form.controls['personalAccidentPremium']?.value ?? 0),
      premiumAutoPersonalAccident: toNumber(this.form.controls['autoPassengerPremium']?.value ?? 0),
      premiumLossOfUse: toNumber(this.form.controls['lossOfUsePremium']?.value ?? 0),
      premiumRoadsideAssitance: toNumber(this.form.controls['roadsideAssistancePremium']?.value ?? 0),
      localGovernmentStampRate: toNumber(this.form.controls['localGovernmentStampRate']?.value ?? 0),
      netPremium: netPremium,
      docsAmount: toNumber(this.form.controls['documentaryStampPremium']?.value ?? 0),
      vatAmount: toNumber(this.form.controls['vatPremium']?.value ?? 0),
      localGovernmentStamp: toNumber(this.form.controls['localGovernmentTaxPremium']?.value ?? 0),
      totalPremium: total,
      coverageOdIssuing: toNumber(this.form.controls['odTheftCoverage']?.value ?? 0),
      coverageAonIssuing: toNumber(this.form.controls['aonCoverage']?.value ?? 0),
      coverageExcessBodilyInjury: toNumber(this.form.controls['excessiveBodyInjuryCoverage']?.value ?? 0),
      coverageThirdPartyLiability: toNumber(this.form.controls['ctplCoverage']?.value ?? 0),
      premiumThirdPartyLiability: toNumber(this.form.controls['ctplPremium']?.value ?? 0),
      coveragePropertyDamage: toNumber(this.form.controls['propertyDamageCoverage']?.value ?? 0),
      coveragePersonalAccident: toNumber(this.form.controls['personalAccidentCoverage']?.value ?? 0),
      coverageAutoPersonalAccident: toNumber(this.form.controls['autoPassengerCoverage']?.value ?? 0),
      coverageRiotStrikeCivilCommotion: toNumber(this.form.controls['rsccCoverage']?.value ?? 0),
      coverageLossOfUse: toNumber(this.form.controls['lossOfUseCoverage']?.value ?? 0),
      coverageRoadsideAssitance: toNumber(this.form.controls['roadsideAssistanceCoverage']?.value ?? 0),
      totalCoverage: netCoverage ?? 0,
      authenticationCost: toNumber(this.form.controls['authenticationFeePremium']?.value ?? 0)
    }
  }

  updateOnInput(event: Event, controlName: string, type, name: string): void {
    const input = event.target as HTMLInputElement;
    const newValue = parseFloat(input.value);
    if (!this.excludedPremiumFieldsInput.includes(controlName)) {
      this.typeInput = type;
    }
    if (this.onlyPremiumFormHaveRateFields.includes(name)) {
      this.netPremiumChanged = true;
    }
    this.controlNameRate = controlName;
    this.nameInput = name;
    if (!isNaN(newValue)) {
      this.calculateSubject.next(controlName);
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }
}
