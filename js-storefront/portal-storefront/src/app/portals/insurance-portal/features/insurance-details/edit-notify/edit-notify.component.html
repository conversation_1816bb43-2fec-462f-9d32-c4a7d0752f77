<div class="edit-notify">
  <div class="notify-popup" [class.enabled]="isEnabledPopup">
    <div class="notify-popup-header">
      <span>{{'editNotify.changesDetected' | translate}}</span>
      <button class="close-btn" (click)="isEnabledPopup = false">
        <mat-icon svgIcon="ic-close-white"></mat-icon>
      </button>
    </div>
    <div class="notify-popup-body">
      {{'editNotify.pleaseClickRecentUpdates' | translate}}
    </div>
  </div>

  <div class="btn-link-box">
    <button class="btn-link" [class.enabled]="isEnabledButton" (click)="isEnabledButton ? onClickNotify() : false">
      <mat-icon
        svgIcon="ic-edit-notify"
        class="medium-icon"
        aria-hidden="true"
      ></mat-icon>
      {{ "common.notify" | translate }}
    </button>
  </div>
</div>