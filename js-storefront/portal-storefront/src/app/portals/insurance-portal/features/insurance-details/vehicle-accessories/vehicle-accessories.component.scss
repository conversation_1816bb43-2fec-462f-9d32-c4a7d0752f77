.detail-box {
    display: flex;
    flex-direction: column;
    gap: 40px;
}

.vehicle-accessories-bottom {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 100px;
    margin-top: 5px;
}

.remarks-text {
    &.no-data {
        opacity: 0.5;
        text-align: left;
        padding-left: 0;
        font-size: 14px;
    }
}

.remarks-section {
    label {
        color: #3A3A3A;
        font-size: 14px;
        font-weight: 400;
    }

    .remarks-text {
        font-size: 16px;
        font-weight: 600;
    }
}

.remarks-section {
    label {
        font-size: 16px;
        font-weight: 600;
        display: block;
        padding-bottom: 7px;
    }

    .btn-small {
        float: right;
        margin-top: 12px;
        font-size: 12px;
        padding: 10px !important;
    }
}

.remarks-section .required-star {
    color: #d32f2f;
    margin-left: 2px;
    font-size: 16px;
    vertical-align: top;
}

.remarks-textarea {
    width: 100%;
    max-height: 65px;
    font-size: 16px;
    padding: 10px 15px;
    border: 1px solid rgba(16, 16, 16, 0.2);
    border-radius: 1px;
    box-sizing: border-box;
}