import { CommonModule } from '@angular/common';
import { Component, EventEmitter, inject, OnInit, Output } from '@angular/core';
import { ReactiveFormsModule, FormGroup, FormControl, Validators } from '@angular/forms';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { of, switchMap, tap, catchError, finalize } from 'rxjs';
import { IconModule } from '../../../../../../core/icon/icon.module';
import { OptionDropdown } from '../../../../../../core/interfaces';
import { NotificationService, LoadingService } from '../../../../../../core/services';
import { DropdownFormGroupComponent, FormGroupComponent, AutocompleteInputComponent, DateFormGroupComponent } from '../../../../../../core/shared';
import { InsuranceInformationService } from '../../../../services/Insurance-information/Insurance-information.service';

@Component({
  selector: 'app-customer-information-update',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    IconModule,
    ReactiveFormsModule,
    DropdownFormGroupComponent,
    MatDialogModule
  ],
  templateUrl: './modal-update-insurance-information.component.html',
  styleUrl: './modal-update-insurance-information.component.scss',
  providers: [NotificationService, InsuranceInformationService],
})
export class ModalUpdateInsuranceInformationComponent implements OnInit {
  @Output() insuranceUpdated = new EventEmitter<void>();

  dialogRef = inject(MatDialogRef<ModalUpdateInsuranceInformationComponent>);
  data = inject(MAT_DIALOG_DATA);
  notificationService = inject(NotificationService);
  translateService = inject(TranslateService);
  loadingService = inject(LoadingService);
  insuranceInformationService = inject(InsuranceInformationService);

  form = new FormGroup({
    scheme: new FormControl('', Validators.required),
    typeTransaction: new FormControl('', Validators.required),
    typeInsurance: new FormControl('', Validators.required),
    insuranceCoverage: new FormControl(''),
  });

  insuranceCoverageList: OptionDropdown[] = [];
  insuranceSchemeList: OptionDropdown[] = [];
  transactionTypeList: OptionDropdown[] = [];
  typeOfInsuranceList: OptionDropdown[] = [];

  ngOnInit(): void {
    this.getInsuranceFilters();
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  getInsuranceFilters(): void {
    this.loadingService.showLoader();
    this.insuranceInformationService.getInsuranceFilters().subscribe(
      (response) => {
        this.insuranceCoverageList =
          response?.insuranceCoverageList
            ?.filter((item) => item.code && item.name)
            ?.map((item) => ({ code: item.code, name: item.name })) || [];

        this.insuranceSchemeList =
          response?.insuranceSchemeList
            ?.filter((item) => item.code && item.name)
            ?.map((item) => ({ code: item.code, name: item.name })) || [];

        this.transactionTypeList =
          response?.transactionTypeList
            ?.filter((item) => item.code && item.name)
            ?.map((item) => ({ code: item.code, name: item.name })) || [];

        this.typeOfInsuranceList =
          response?.typeOfInsuranceList
            ?.filter((item) => item.code && item.name)
            ?.map((item) => ({ code: item.code, name: item.name })) || [];

        if (
          !this.form.get('scheme')?.value &&
          this.insuranceSchemeList.length > 0
        ) {
          this.form.get('scheme')?.setValue(this.insuranceSchemeList[0].code);
        }

        if (
          !this.form.get('typeTransaction')?.value &&
          this.transactionTypeList.length > 0
        ) {
          this.form
            .get('typeTransaction')
            ?.setValue(this.transactionTypeList[0].code);
        }
        const parentData = this.data?.insuranceInformation;
        this.form.patchValue({
          scheme:
            parentData?.scheme ||'',
          typeTransaction:
            parentData?.transactionType || '',
          typeInsurance:
            parentData?.typeOfInsurance || '',
          insuranceCoverage:
            parentData?.insuranceCoverage ||'',
        });

        this.loadingService.hideLoader();
      },
      (err) => {
        this.notificationService.showError(
          this.translateService.instant('common.generalError')
        );
        this.loadingService.hideLoader();
      }
    );
  }

  updateCustomerInformation(): void {
    if (this.form.valid) {
      const requestBody = {
        scheme: this.form.get('scheme')?.value,
        transactionType: this.form.get('typeTransaction')?.value,
        typeOfInsurance: this.form.get('typeInsurance')?.value,
        insuranceCoverage: this.form.get('insuranceCoverage')?.value,
      };

      const insuranceCode = this.data.insuranceId;

      if (!insuranceCode) {
        this.notificationService.showError(
          this.translateService.instant('insurance.missingInsuranceCode')
        );
        return;
      }

      this.loadingService.showLoader();

      of(requestBody)
        .pipe(
          switchMap((payload) =>
            this.insuranceInformationService.updateInsuranceInfo(
              insuranceCode,
              payload,
              this.data?.isRenewal
            )
          ),
          tap((response) => {
            if (response?.code === '200') {
              this.notificationService.showSuccess(
                this.translateService.instant(
                  'newVehicleInsurance.detail.insuranceInformation.updateSuccess'
                )
              );
              this.insuranceUpdated.emit();
              this.dialogRef.close(true);
            } else {
              this.notificationService.showError(
                response?.message || 'Update failed.'
              );
            }
          }),
          catchError((error) => {
            console.error('Error updating insurance info:', error);
            this.notificationService.showError(
              this.translateService.instant(
                'newVehicleInsurance.detail.insuranceCalculation.updateFailed'
              )
            );
            return of(null);
          }),
          finalize(() => this.loadingService.hideLoader())
        )
        .subscribe();
    } else {
      this.notificationService.showError(
        this.translateService.instant('insurance.formInvalid')
      );
    }
  }
}
