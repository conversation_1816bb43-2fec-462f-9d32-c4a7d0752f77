import { CommonModule } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { catchError, finalize, of, Subscription } from 'rxjs';
import { ActionModal, InsuranceType } from '../../../../../../core/enums';
import { OptionDropdown } from '../../../../../../core/interfaces';
import { LoadingService, NotificationService } from '../../../../../../core/services';
import { DropdownFormGroupComponent, RadioButtonComponent, DateFormGroupComponent, FormGroupComponent } from '../../../../../../core/shared';
import { NewVehicleInsuranceDetailService } from '../../../../services/new-vehicle-insurance/new-vehicle-insurance-detail.service';
import { ImportFilesComponent } from '../../../../../../core/shared/import-files/import-files.component';
import { CONSENT_FORM } from '../../../../constants/insurance.const';
import { VehicleOptionsResponse } from '../../../../interfaces';
import { DateTimePickerFormGroupComponent } from "../../../../../../core/shared/date-time-picker-form-group/date-time-picker-form-group.component";

@Component({
  selector: 'app-modal-insurer-infomation-edit',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    CommonModule,
    MatDialogModule,
    TranslateModule,
    DropdownFormGroupComponent,
    RadioButtonComponent,
    DateFormGroupComponent,
    FormGroupComponent,
    ImportFilesComponent,
    DateTimePickerFormGroupComponent
],
  templateUrl: './modal-update-vehicle-infomation.component.html',
  styleUrls: ['./modal-update-vehicle-infomation.component.scss'],
  providers: [NotificationService],
})
export class ModalUpdateVehicleInfomationComponent implements OnInit {
  mainForm: FormGroup = new FormGroup({});
  subscription = new Subscription();
  insuranceId: string = '';
  isRenewal: boolean = false;
  insuranceType = InsuranceType;
  CONSENT_FORM = CONSENT_FORM;
  subs = new Subscription();
  bodyTypeOptions: OptionDropdown[] = [];
  fuelTypeOptions: OptionDropdown[] = [];
  productClassificationOptions: OptionDropdown[] = [];

  dialogRef = inject(MatDialogRef<ModalUpdateVehicleInfomationComponent>);
  data = inject(MAT_DIALOG_DATA);
  insuranceDetailService = inject(NewVehicleInsuranceDetailService);
  loadingService = inject(LoadingService);
  translate = inject(TranslateService);
  notificationService = inject(NotificationService);

  get chasissNum(): FormControl {
    return this.mainForm.get('chasissNum') as FormControl;
  }

  get makeBy(): FormControl {
    return this.mainForm.get('makeBy') as FormControl;
  }

  get vehicleModel(): FormControl {
    return this.mainForm.get('vehicleModel') as FormControl;
  }

  get modelYear(): FormControl {
    return this.mainForm.get('modelYear') as FormControl;
  }

  get color(): FormControl {
    return this.mainForm.get('color') as FormControl;
  }

  get engineNumber(): FormControl {
    return this.mainForm.get('engineNumber') as FormControl;
  }

  get plateNumberCSNumber(): FormControl {
    return this.mainForm.get('plateNumberCSNumber') as FormControl;
  }

  get orderNumber(): FormControl {
    return this.mainForm.get('orderNumber') as FormControl;
  }

  get orderStatus(): FormControl {
    return this.mainForm.get('orderStatus') as FormControl;
  }

  get paidPrice(): FormControl {
    return this.mainForm.get('paidPrice') as FormControl;
  }

  get vsiDate(): FormControl {
    return this.mainForm.get('vsiDate') as FormControl;
  }

  get code(): FormControl {
    return this.mainForm.get('code') as FormControl;
  }

  get variant(): FormControl {
    return this.mainForm.get('variant') as FormControl;
  }

  get bodyType(): FormControl {
    return this.mainForm.get('bodyType') as FormControl;
  }

  get powerTransmission(): FormControl {
    return this.mainForm.get('powerTransmission') as FormControl;
  }

  get fuelType(): FormControl {
    return this.mainForm.get('fuelType') as FormControl;
  }

  get seats(): FormControl {
    return this.mainForm.get('seats') as FormControl;
  }

  get unloadedWeight(): FormControl {
    return this.mainForm.get('unloadedWeight') as FormControl;
  }

  get maximumWeight(): FormControl {
    return this.mainForm.get('maximumWeight') as FormControl;
  }

  get productClassification(): FormControl {
    return this.mainForm.get('productClassification') as FormControl;
  }
  ngOnInit() {
    this.mainForm = this.data?.mainForm as FormGroup;
    this.isRenewal = this.data?.isRenewal;
    this.loadingService.showLoader();
    this.loadVehicleOptions();
     if (
       this.mainForm?.controls['vsiDate']?.value &&
       typeof this.mainForm.controls['vsiDate'].value === 'string'
     ) {
       this.mainForm.patchValue({
         vsiDate: new Date(this.mainForm.controls['vsiDate'].value),
       });
     }
  }

  ngOnDestroy(): void {
    this.subs.unsubscribe();
  }

  loadVehicleOptions() {
    const sub = this.insuranceDetailService
      .getInsuranceTypeOption()
      .pipe(
        finalize(() => this.loadingService.hideLoader()),
        catchError((error) => {
          console.error('Error loading vehicle options:', error);
          return of(null);
        })
      )
      .subscribe((response: VehicleOptionsResponse | null) => {
        if (response) {
          this.bodyTypeOptions = response.enums.bodyType || [];
          this.fuelTypeOptions = response.enums.fuelTypes || [];
          this.productClassificationOptions =
            response.enums.vehicleCategoryType || [];
        } else {
          this.bodyTypeOptions = [];
          this.fuelTypeOptions = [];
          this.productClassificationOptions = [];
        }
      });
    this.subs.add(sub);
  }

  onCancel() {
    this.dialogRef.close();
  }

  onSubmit() {
    this.dialogRef.close({
      action: ActionModal.Submit,
    });
  }
}
