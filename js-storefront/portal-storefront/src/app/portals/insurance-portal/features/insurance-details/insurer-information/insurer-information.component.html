<div class="detail-box">
  <div class="detail-box-header">
    <div class="detail-box-title">
      <mat-icon svgIcon="ic-insurer-information" class="medium-icon section-header--icon" ></mat-icon>
      {{ "newVehicleInsurance.detail.insurerInformation.sectionName" | translate }}
    </div>
    @if (isEditable) {
      <button class="btn-link" (click)="onEditInsurerInformation(insurerInformation)">
        <mat-icon svgIcon="ic-edit" class="small-icon" aria-hidden="true"></mat-icon>
        {{ "common.edit" | translate }}
      </button>
    }
  </div>
  <div class="detail-content">
    @if (currentRole !== ROLES.INSURANCEADMINGROUP && currentRole !== ROLES.INSURANCEDEPARTMENTGROUP) {
      <!-- stt completed and isAlreadyAssigned is false, remove class two-items-row -->
      <div class="detail-row" [ngClass]="{ 'two-items-row': !(insuranceStatus === INSURANCE_STATUS.COMPLETED && !isAlreadyAssigned) }">
        <div class="detail-element">
          <div class="detail-label">{{ "newVehicleInsurance.detail.insurerInformation.insuranceType" | translate }}</div>
          <div class="detail-value">{{
            !insurerInformation?.type
            ? '-'
            : "insurance.insuranceType." + insurerInformation?.type | translate
          }}</div>
        </div>
        <div class="detail-element">
          <div class="detail-label">{{ "newVehicleInsurance.detail.insurerInformation.insuranceCompany" | translate }}</div>
          <div class="detail-value">{{ insurerInformation?.company?.name || '-' }}</div>
        </div>
        <div class="detail-element">
          <div class="detail-label">{{ "newVehicleInsurance.detail.insurerInformation.startDate" | translate }}</div>
          <div class="detail-value">{{ convertUTCtoDisplayDate(insurerInformation?.startDate) }}</div>
        </div>
        @if (insuranceInput === this.INSURANCE_INPUT.RENEWAL && insurerInformation?.supportInsIntegration) {
          <div class="detail-element">
            <div class="detail-label">{{ "newVehicleInsurance.detail.insurerInformation.mvFileNumber" | translate }}</div>
            <div class="detail-value">{{ insurerInformation?.mvFileNumber || '-' }}</div>
          </div>
        }
        <div class="detail-element">
          <div class="detail-label">{{ "newVehicleInsurance.detail.insurerInformation.mortgage" | translate }}</div>
          <div class="detail-value">{{ insurerInformation?.mortgage?.name || '-' }}</div>
        </div>
        <div class="detail-element">
          <div class="detail-label">{{ "newVehicleInsurance.detail.insurerInformation.mortgageAddress" | translate }}</div>
          <div class="detail-value">{{ insurerInformation?.mortgageAddress || '-' }}</div>
        </div>
        @if (insurerInformation?.type === this.insuranceType.TOYOTA
          && insurerInformation?.vinPaired && insuranceCoverage !== InsuranceCoverageList.COMP_CTPL
          && insuranceInput === this.INSURANCE_INPUT.RENEWAL) {
          <div class="detail-element">
            <div class="detail-label">{{ "insurance.modal.drivingScore" | translate }}</div>
            <div class="detail-value">{{ (insurerInformation?.drivingScore| number: '1.1-1') || '-' }}</div>
          </div>
          <div class="detail-element">
            <div class="detail-label">{{ "insurance.modal.telematicsDiscount" | translate }}</div>
            <div class="detail-value">{{ (insurerInformation?.telematicDiscount?.value| number: '1.1-1') || '-' }}</div>
          </div>
        }
        @if (insurerInformation?.type === this.insuranceType.TELEMATICS) {
          <div class="detail-element">
            <div class="detail-label">{{ "insurance.modal.consentDate" | translate }}</div>
            <div class="detail-value">{{ convertUTCtoDisplayDate(insurerInformation?.consentDate) }}</div>
          </div>
          @if (insuranceInput === this.INSURANCE_INPUT.RENEWAL) {
            <div class="detail-element">
              <div class="detail-label">{{ "insurance.modal.drivingScore" | translate }}</div>
              <div class="detail-value">{{ (insurerInformation?.drivingScore | number: '1.1-1') || '-' }}</div>
            </div>
            <div class="detail-element">
              <div class="detail-label">{{ "insurance.modal.telematicsDiscount" | translate }}</div>
              <div class="detail-value">{{ (insurerInformation?.telematicDiscount?.value | number: '1.1-1') || '-' }}</div>
            </div>
          }
          <div class="detail-element insurance-consent-form">
            <div class="detail-label">{{ "insurance.modal.insuranceConsentForm" | translate }}</div>
            @if (insurerInformation?.consentForm) {
              <app-file-list
                [fileList]="[insurerInformation?.consentForm]"
                [isHiddenRemove]="true"
                (handleDownload)="onDownloadFile($event)"
              ></app-file-list>
            } @else {
              <div class="detail-value">-</div>
            }
          </div>
        }
        @if (insuranceInput === this.INSURANCE_INPUT.NEW) {
          <div class="detail-element">
            <div class="detail-label">{{ "newVehicleInsurance.detail.insurerInformation.insurancePromoAvailment" | translate }}</div>
            <div class="detail-value">
              @if (insurerInformation?.promoAvailment) {
                {{ "common.yes" | translate }}
              } @else {
                {{ "common.no" | translate }}
              }
            </div>
          </div>
        }
      </div>
    } @else {
      <div class="detail-row">
        <div class="detail-element">
          <div class="detail-label">{{ "newVehicleInsurance.detail.insurerInformation.insuranceType" | translate }}</div>
          <div class="detail-value">{{
            !insurerInformation?.type
            ? '-'
            : "insurance.insuranceType." + insurerInformation?.type | translate
          }}</div>
        </div>
        <div class="detail-element">
          <div class="detail-label">{{ "newVehicleInsurance.detail.insurerInformation.insuranceCompany" | translate }}</div>
          <div class="detail-value">{{ insurerInformation?.company?.name || '-' }}</div>
        </div>
        <div class="detail-element">
          <div class="detail-label">{{ "newVehicleInsurance.detail.insurerInformation.startDate" | translate }}</div>
          <div class="detail-value">{{ convertUTCtoDisplayDate(insurerInformation?.startDate) }}</div>
        </div>
        @if (insuranceInput === this.INSURANCE_INPUT.RENEWAL) {
          <div class="detail-element">
            <div class="detail-label">{{ "newVehicleInsurance.detail.insurerInformation.mvFileNumber" | translate }}</div>
            <div class="detail-value">{{ insurerInformation?.mvFileNumber || '-' }}</div>
          </div>
        }
        <div class="detail-element">
          <div class="detail-label">{{ "newVehicleInsurance.detail.insurerInformation.mortgage" | translate }}</div>
          <div class="detail-value">{{ insurerInformation?.mortgage?.name || '-' }}</div>
        </div>
      </div>
      <div class="detail-row">
        @if (insurerInformation?.type === this.insuranceType.TELEMATICS) {
          <div class="detail-element">
            <div class="detail-label">{{ "insurance.modal.consentDate" | translate }}</div>
            <div class="detail-value">{{ convertUTCtoDisplayDate(insurerInformation?.consentDate) }}</div>
          </div>
          @if (insuranceInput === this.INSURANCE_INPUT.RENEWAL) {
            <div class="detail-element">
              <div class="detail-label">{{ "insurance.modal.drivingScore" | translate }}</div>
              <div class="detail-value">{{ (insurerInformation?.drivingScore | number: '1.1-1') || '-' }}</div>
            </div>
            <div class="detail-element">
              <div class="detail-label">{{ "insurance.modal.telematicsDiscount" | translate }}</div>
              <div class="detail-value">{{ (insurerInformation?.telematicDiscount?.value | number: '1.1-1') || '-' }}</div>
            </div>
          }
        }
        <div class="detail-element">
          <div class="detail-label">{{ "newVehicleInsurance.detail.insurerInformation.mortgageAddress" | translate }}</div>
          <div class="detail-value">{{ insurerInformation?.mortgageAddress || '-' }}</div>
        </div>
        @if (insuranceInput === this.INSURANCE_INPUT.NEW) {
        <div class="detail-element">
          <div class="detail-label">{{ "newVehicleInsurance.detail.insurerInformation.insurancePromoAvailment" | translate }}</div>
          <div class="detail-value">
            @if (insurerInformation?.promoAvailment) {
              {{ "common.yes" | translate }}
            } @else {
              {{ "common.no" | translate }}
            }
          </div>
        </div>
        }
        @if ((insurerInformation?.type === this.insuranceType.TELEMATICS && insuranceInput === this.INSURANCE_INPUT.RENEWAL)
        || (insurerInformation?.type === this.insuranceType.TELEMATICS && insuranceInput === this.INSURANCE_INPUT.NEW)) { 
          <div class="detail-element"></div>
        }
      </div>
      @if (insurerInformation?.type === this.insuranceType.TELEMATICS) {
        <div class="detail-element insurance-consent-form">
          <div class="detail-label">{{ "insurance.modal.insuranceConsentForm" | translate }}</div>
          @if (insurerInformation?.consentForm) {
            <app-file-list
              [fileList]="[insurerInformation?.consentForm]"
              [isHiddenRemove]="true"
              (handleDownload)="onDownloadFile($event)"
            ></app-file-list>
          } @else {
            <div class="detail-value">-</div>
          }
        </div>
      }
    }
  </div>
</div>