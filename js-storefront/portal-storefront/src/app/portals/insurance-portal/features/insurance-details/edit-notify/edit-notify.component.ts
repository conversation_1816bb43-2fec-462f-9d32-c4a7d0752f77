import { Component, inject, Input, OnInit } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { DataStoreService } from '../../../../../core/services/data-store.service';
import { finalize, Subscription } from 'rxjs';
import {
  LoadingService,
  NotificationService,
} from '../../../../../core/services';
import { NewVehicleInsuranceDetailService } from '../../../services/new-vehicle-insurance/new-vehicle-insurance-detail.service';
import { handleErrors } from '../../../../../core/helpers';

@Component({
  selector: 'app-edit-notify',
  standalone: true,
  imports: [MatIconModule, TranslateModule],
  providers: [NotificationService],
  templateUrl: './edit-notify.component.html',
  styleUrls: ['./edit-notify.component.scss'],
})
export class EditNotifyComponent {
  @Input() insuranceCode: string = '';
  isEnabledButton: boolean = false;
  isEnabledPopup: boolean = false;
  subscription = new Subscription();

  dataStoreService = inject(DataStoreService);
  loadingService = inject(LoadingService);
  notificationService = inject(NotificationService);
  newVehicleInsuranceDetailService = inject(NewVehicleInsuranceDetailService);
  ngOnInit() {
    this.subscription.add(
      this.dataStoreService.isEditNotify.subscribe((res) => {
        this.isEnabledButton = this.isEnabledPopup = res;
      })
    );
  }

  ngOnDestroy() {
    this.subscription?.unsubscribe();
  }

  onClickNotify() {
    if (!this.isEnabledButton) return;
    this.loadingService.showLoader();
    this.subscription.add(
      this.newVehicleInsuranceDetailService
        .notifyInsuranceCost(this.insuranceCode)
        .pipe(finalize(() => this.loadingService.hideLoader())).subscribe(res => {
          if (res?.code === '200') {
            this.notificationService.showSuccess(res?.message)
          } else {
            this.notificationService.showError(res?.message)
          }
          this.dataStoreService.setIsEditNotify(false);
        }, err => {
          handleErrors(err, this.notificationService);
        })
    );
  }
}
