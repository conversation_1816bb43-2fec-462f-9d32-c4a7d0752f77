<div class="detail-box">
  <div class="detail-box-header">
    <div class="detail-box-title">
      <mat-icon svgIcon="ic-ticket" class="medium-icon section-header--icon" ></mat-icon>
      {{ "newVehicleInsurance.detail.attachments" | translate }}
    </div>
  </div>
  <div class="detail-content" [ngClass]="{
    'single-column': isComprehensiveCompulsoryTPL, 'full-column' : !isComprehensiveCompulsoryTPL}">
    <div class="detail-row">
      <div class="detail-element">
        @if (isDocumentUploadReadOnly) {
          <label class="form-group__label">{{ 'newVehicleInsurance.detail.documents' | translate }}</label>
        } @else {
          <app-upload-file
            [label]="'newVehicleInsurance.detail.documents' | translate"
            [placeholder]="'uploadFile.selectFile' | translate"
            [multiple]="false"
            [isLabelHidden]="false"
            (filesSelected)="onFileSelected($event)"
          ></app-upload-file>
        }
        @if (isDocumentUploadReadOnly && documents?.length === 0) {
          <div class="no-attachment">{{ 'uploadFile.noAttachedDocuments' | translate }}</div>
        } @else {
          <app-file-list
            [fileList]="documents"
            [isHiddenRemove]="isDocumentUploadReadOnly"
            (handleDelete)="onRemoveFile($event)"
            (handleDownload)="onDownloadFile($event)"
          ></app-file-list>
        }
      </div>
      @if (!isComprehensiveCompulsoryTPL) {
      <div class="detail-element">
          @if (isEPolicyUploadReadOnly) {
            <label class="form-group__label">{{ 'newVehicleInsurance.detail.ePolicy' | translate }}</label>
          } @else {
            <app-upload-file
              [label]="'newVehicleInsurance.detail.ePolicy' | translate"
              [placeholder]="'uploadFile.selectMax' | translate: { number: 3 }"
              [multiple]="true"
              [isLabelHidden]="false"
              (filesSelected)="onFileSelected($event, true)"
            ></app-upload-file>
          }
          @if (isEPolicyUploadReadOnly && epolicy?.length === 0) {
            <div class="no-attachment">{{ 'uploadFile.noAttachedDocuments' | translate }}</div>
          } @else {
            <app-file-list
              [fileList]="epolicy"
              [isHiddenRemove]="isEPolicyUploadReadOnly"
              (handleDelete)="onRemoveFile($event, true)"
              (handleDownload)="onDownloadFile($event)"
            ></app-file-list>
        }
      </div>
        }
    </div>
  </div>
</div>
