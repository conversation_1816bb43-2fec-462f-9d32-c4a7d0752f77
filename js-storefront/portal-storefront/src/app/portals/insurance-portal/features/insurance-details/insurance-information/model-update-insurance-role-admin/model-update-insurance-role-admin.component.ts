import { CommonModule, DatePipe } from '@angular/common';
import { Component, EventEmitter, inject, OnInit, Output } from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { IconModule } from '../../../../../../core/icon/icon.module';
import {
  LoadingService,
  NotificationService,
} from '../../../../../../core/services';
import {
  DateFormGroupComponent,
  DropdownFormGroupComponent,
  FormGroupComponent,
} from '../../../../../../core/shared';
import { InsuranceInformationService } from '../../../../services/Insurance-information/Insurance-information.service';
import moment from 'moment';
import { DateFormat } from '../../../../../../core/enums';
import { InsuranceCoverageList, InsuranceInput } from '../../../../enum';
import { OptionDropdown } from '../../../../../../core/interfaces';

@Component({
  selector: 'app-model-update-insurance-role-admin',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    IconModule,
    ReactiveFormsModule,
    MatDialogModule,
    DateFormGroupComponent,
    FormGroupComponent,
    DropdownFormGroupComponent,
  ],
  providers: [NotificationService, InsuranceInformationService, DatePipe],
  templateUrl: './model-update-insurance-role-admin.component.html',
  styleUrls: ['./model-update-insurance-role-admin.component.scss'],
})
export class ModelUpdateInsuranceRoleAdminComponent implements OnInit {
  @Output() insuranceUpdated = new EventEmitter<void>();
  dateFormat = DateFormat;

  dialogRef = inject(MatDialogRef<ModelUpdateInsuranceRoleAdminComponent>);
  data = inject(MAT_DIALOG_DATA);
  notificationService = inject(NotificationService);
  translateService = inject(TranslateService);
  loadingService = inject(LoadingService);
  insuranceInformationService = inject(InsuranceInformationService);
  datePipe = inject(DatePipe);
  currentDate = new Date();
  dateOfPolicyWarning = '';
  dateOfPolicyCTPLWarning: string = '';
  form = new FormGroup({
    scheme: new FormControl('', Validators.required),
    typeTransaction: new FormControl('', Validators.required),
    typeInsurance: new FormControl('', Validators.required),
    insuranceCoverage: new FormControl(''),
    policyCode: new FormControl(''),
    dateOfPolicy: new FormControl(null),
    expireDate: new FormControl(null),
    compDateOfPolicy: new FormControl(null),
    compExpireDate: new FormControl(null),
    compPolicyCode: new FormControl(''),
    ctplDateOfPolicy: new FormControl(null),
    ctplExpireDate: new FormControl(null),
    ctplPolicyCode: new FormControl('')
  });

  insuranceCoverageList: OptionDropdown[] = [];
  insuranceSchemeList: OptionDropdown[] = [];
  transactionTypeList: OptionDropdown[] = [];
  typeOfInsuranceList: OptionDropdown[] = [];
  InsuranceCoverageList = InsuranceCoverageList;
  ngOnInit(): void {
    this.getInsuranceFilters();
    this.form.patchValue({
      policyCode: this.data?.insuranceInformation?.policyCode,
      dateOfPolicy: this.data?.insuranceInformation?.dateOfPolicy ? new Date(this.data?.insuranceInformation?.dateOfPolicy) : null,
      expireDate: this.data?.insuranceInformation?.expireDate ? new Date(this.data?.insuranceInformation?.expireDate) : null,
      compDateOfPolicy: this.data?.insuranceCalculation?.compData?.policyDate || null,
      compExpireDate: this.data?.insuranceCalculation?.compData?.policyExpiryDate || null,
      compPolicyCode: this.data?.insuranceCalculation?.compData?.policyCode || '',
      ctplDateOfPolicy: this.data?.insuranceCalculation?.ctplData?.policyDate || null,
      ctplExpireDate: this.data?.insuranceCalculation?.ctplData?.policyExpiryDate || null,
      ctplPolicyCode: this.data?.insuranceCalculation?.ctplData?.policyCode || ''
    });

    if (this.data.insuranceInput === InsuranceInput.NEW) {
      this.form.get('ctplExpireDate').setValidators(this.ctplExpireDateValidator.bind(this));
      this.form.get('ctplExpireDate').updateValueAndValidity();
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  getInsuranceFilters(): void {
    this.loadingService.showLoader();
    this.insuranceInformationService.getInsuranceFilters().subscribe(
      (response) => {
        this.insuranceCoverageList =
          response?.insuranceCoverageList
            ?.filter((item) => item.code && item.name)
            ?.map((item) => ({ code: item.code, name: item.name })) || [];

        this.insuranceSchemeList =
          response?.insuranceSchemeList
            ?.filter((item) => item.code && item.name)
            ?.map((item) => ({ code: item.code, name: item.name })) || [];

        this.transactionTypeList =
          response?.transactionTypeList
            ?.filter((item) => item.code && item.name)
            ?.map((item) => ({ code: item.code, name: item.name })) || [];

        this.typeOfInsuranceList =
          response?.typeOfInsuranceList
            ?.filter((item) => item.code && item.name)
            ?.map((item) => ({ code: item.code, name: item.name })) || [];

        if (
          !this.form.get('scheme')?.value &&
          this.insuranceSchemeList.length > 0
        ) {
          this.form.get('scheme')?.setValue(this.insuranceSchemeList[0].code);
        }

        if (
          !this.form.get('typeTransaction')?.value &&
          this.transactionTypeList.length > 0
        ) {
          this.form
            .get('typeTransaction')
            ?.setValue(this.transactionTypeList[0].code);
        }
        const parentData = this.data?.insuranceInformation;
        this.form.patchValue({
          scheme: parentData?.scheme || '',
          typeTransaction: parentData?.transactionType || '',
          typeInsurance: parentData?.typeOfInsurance || '',
          insuranceCoverage: parentData?.insuranceCoverage || '',
        });

        this.loadingService.hideLoader();
      },
      (err) => {
        this.notificationService.showError(
          this.translateService.instant('common.generalError')
        );
        this.loadingService.hideLoader();
      }
    );
  }

  updateCustomerInformation(): void {
    this.loadingService.showLoader();
    const data = this.form?.value;
    let payload: any = {
      scheme: this.form.get('scheme')?.value,
      transactionType: this.form.get('typeTransaction')?.value,
      typeOfInsurance: this.form.get('typeInsurance')?.value,
      insuranceCoverage: this.form.get('insuranceCoverage')?.value,
    };
    if (this.form?.controls?.insuranceCoverage?.value === InsuranceCoverageList.COMP_CTPL) {
      payload = {
        ...payload,
        compDateOfPolicy: data?.compDateOfPolicy ? this.datePipe.transform(data?.compDateOfPolicy, DateFormat.ShortDate) : "",
        compExpireDate: data?.compExpireDate ? this.datePipe.transform(data?.compExpireDate, DateFormat.ShortDate) : "",
        compPolicyCode: data?.compPolicyCode,
        ctplDateOfPolicy: data?.ctplDateOfPolicy ? this.datePipe.transform(data?.ctplDateOfPolicy, DateFormat.ShortDate) : "",
        ctplExpireDate: data?.ctplExpireDate ? this.datePipe.transform(data?.ctplExpireDate, DateFormat.ShortDate) : "",
        ctplPolicyCode: data?.ctplPolicyCode
      }
    } else {
      payload = {
        ...payload,
        policyCode: this.form.get('policyCode').value,
        dateOfPolicy: this.datePipe.transform(
          this.form.get('dateOfPolicy').value,
          DateFormat.ShortDate
        ),
        expireDate: this.datePipe.transform(
          this.form.get('expireDate').value,
          DateFormat.ShortDate
        ),
      }
    }
    this.insuranceInformationService
      .updateInsuranceRoleAdmin(
        this.data.insuranceId,
        payload
      )
      .subscribe(
        () => {
          this.insuranceUpdated.emit();
          this.notificationService.showSuccess(
            this.translateService.instant(
              'newVehicleInsurance.detail.insuranceInformation.updateSuccess'
            )
          );
          this.dialogRef.close(true);
          this.loadingService.hideLoader();
        },
        () => {
          this.notificationService.showError(
            this.translateService.instant('common.generalError')
          );
          this.loadingService.hideLoader();
        }
      );
  }
  onExpireDateChange(event: any, name: string): void {
    // Expire date should be greater than current date
    if (event.toDate() < this.currentDate) {
      this.form.get(name).setErrors({ invalid: true });
    }
  }

  onDateOfPolicyChange(event: any, isCTPL: boolean = false): void {
    // if date of policy is greater than current date 30 days, system will display a warning message under this field:
    // "You’ve entered <x> days beyond current date." which x = selected day - current date
    // if date of policy is less than current date 30 days, system will display a warning message under this field:
    // "You’ve entered <x> days advance from current date." which x = current date - selected day
    const dateOfPolicy = moment(event.toDate());
    const today = moment(this.currentDate);
    const diffDays = Math.abs(dateOfPolicy.diff(today, 'days')) - 30;
    if (diffDays >= 0) {
      if (isCTPL) {
        if (dateOfPolicy.isAfter(today)) {
          this.dateOfPolicyCTPLWarning = this.translateService.instant('newVehicleInsurance.detail.insuranceInformation.dateOfPolicyBeyond', { days: diffDays });
        } else {
          this.dateOfPolicyCTPLWarning = this.translateService.instant('newVehicleInsurance.detail.insuranceInformation.dateOfPolicyAdvance', { days: diffDays });
        }
      } else {
        if (dateOfPolicy.isAfter(today)) {
          this.dateOfPolicyWarning = this.translateService.instant('newVehicleInsurance.detail.insuranceInformation.dateOfPolicyBeyond', { days: diffDays });
        } else {
          this.dateOfPolicyWarning = this.translateService.instant('newVehicleInsurance.detail.insuranceInformation.dateOfPolicyAdvance', { days: diffDays });
        }
      }
    } else {
      if (isCTPL) {
        this.dateOfPolicyCTPLWarning = '';
      } else {
        this.dateOfPolicyWarning = '';
      }
    }
    if (this.data.insuranceInput === InsuranceInput.NEW) {
      this.form.get('ctplExpireDate').updateValueAndValidity();
    }
  }

  ctplExpireDateValidator(control: FormControl) {
      // Show the reminder message for CTPL end date in partner view 
      // if the CTPL expiry date is less than (CTPL date of Policy Insurance + 1094 days) or greater than (CTPL  date of Policy Insurance + 1095 days)
      // 'The CTPL policy expiry date should be three years from the date of policy issuance.'
      
      const ctplDateOfPolicy = this.form.get('ctplDateOfPolicy').value;
      if (!ctplDateOfPolicy || !control.value) {
        return null;
      }
  
      const ctplExpireDate = moment(control.value);
      const maxDate = moment(ctplDateOfPolicy).add(3, 'years');
  
      if (!ctplExpireDate.isSame(maxDate)) {
        return { ctplExpireDateInvalid: true };
      }
  
      return null;
  }
}
