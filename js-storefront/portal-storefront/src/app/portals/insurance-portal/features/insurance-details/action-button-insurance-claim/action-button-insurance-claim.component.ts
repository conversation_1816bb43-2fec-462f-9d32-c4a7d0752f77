import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { ROLES } from '../../../../../core/constants/roles.const';
import { INSURANCE_CLAIM_STATUS } from '../../../constants/insurance-claims.const';

@Component({
  selector: 'app-action-button-insurance-claim',
  standalone: true,
  imports: [TranslateModule, CommonModule],
  templateUrl: './action-button-insurance-claim.component.html',
  styleUrls: ['./action-button-insurance-claim.component.scss'],
})
export class ActionButtonInsuranceClaimComponent {
  @Input() showConfirm: boolean = false;
  @Input() showApprove: boolean = false;
  @Input() showApproveEstimation: boolean = false
  @Input() status: string = '';
  @Input() currentRole: string = '';

  @Output() handleReviseInsurance = new EventEmitter();
  @Output() handleConfirmInsurance = new EventEmitter();
  @Output() handleApproveInsurance = new EventEmitter();

  insuranceClaimStatus = INSURANCE_CLAIM_STATUS
  ROLES = ROLES;

  onReviseInsurance() {
    this.handleReviseInsurance.emit();
  }

  onConfirmInsurance() {
    this.handleConfirmInsurance.emit();
  }

  onApproveInsurance() {
    this.handleApproveInsurance.emit();
  }
}
