::ng-deep {
    :host {
        ::ng-deep {
            input:disabled {
                background-color: #F5F5F5 !important;
            }
        }
    }
  .custom-dialog-create-va {

    .title-dialog {
        padding-top: 35px !important;
    }

    .mdc-dialog__content {
      padding: 35px 55px;
    }
  }

  .modal-add-va-action {
    justify-content: center !important;
    gap: 20px;
  }
}

:host ::ng-deep {
    .status-error {
        margin-top: -20px;
    }

    .custom-mat-checkbox {
        .mdc-form-field {
            position: relative;

            .mdc-checkbox {
                position: absolute;
                top: 0px;
                left: 4px;
                padding: 0;
            }

            .mdc-checkbox__native-control,
            .mat-mdc-checkbox-touch-target {
                height: 18px !important;
                width: 18px !important;
            }

            .mdc-checkbox__background {
                top: 2px;
                left: 8px;
            }
        }

        .mdc-label {
            color: var(--Primary-Black, #101010);
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            padding-left: 33px;
        }
    }
}