import { Component, EventEmitter, inject, Input, Output } from '@angular/core';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { ModalUpdateInsuranceCalculationComponent } from './modal-update-insurance-calculation/modal-update-insurance-calculation';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { ROLES } from '../../../../../core/constants/roles.const';
import { IconModule } from '../../../../../core/icon/icon.module';
import { NotificationService, LoadingService } from '../../../../../core/services';
import { INSURANCE_STATUS } from '../../../constants/new-vehicle-insurance-detail.const';
import { InsuranceQuotation } from '../../../interfaces';
import { NewVehicleInsuranceDetailService } from '../../../services/new-vehicle-insurance/new-vehicle-insurance-detail.service';
import { InsuranceInput } from '../../../enum';

@Component({
  selector: 'app-insurance-calculation',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    IconModule,
    ReactiveFormsModule,
    MatDialogModule,
  ],
  templateUrl: './insurance-calculation.component.html',
  styleUrl: './insurance-calculation.component.scss',
  providers: [NotificationService, LoadingService, MatDialog],
})
export class InsuranceCalculationComponent {
  @Input() insuranceId: string;
  @Input() paidPriceVehicle: string;
  @Input() insuranceCalculation: InsuranceQuotation | any;
  @Input() currentRole: string;
  @Input() insuranceInput: string;
  
  @Output() insuranceUpdated = new EventEmitter<void>();
  
  insuranceStatus: string;
  isEditable: boolean = false;
  form: any;
  ROLES = ROLES;

  dialog = inject(MatDialog);
  notificationService = inject(NotificationService);
  translateService = inject(TranslateService);
  loadingService = inject(LoadingService);

  insuranceService = inject(NewVehicleInsuranceDetailService);
  private destroy$ = new Subject<void>();

  ngOnInit(): void {
    this.insuranceService.insuranceStatus$
    .pipe(takeUntil(this.destroy$))
    .subscribe((status) => {
      this.insuranceStatus = status;
      this.setIsEditable();
    });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  openEditDialog(): void {
    const dialogRef = this.dialog.open(
      ModalUpdateInsuranceCalculationComponent,
      {
        width: '1000px',
        data: {
          title: this.translateService.instant(
            'newVehicleInsurance.detail.insuranceCalculation.sectionName'
          ),
          icon: '',
          confirmMsg: this.translateService.instant(
            'newVehicleInsurance.detail.insuranceCalculation.editMsg'
          ),
          disableClose: true,
          autoFocus: true,
          restoreFocus: true,
          cancelBtn: this.translateService.instant('common.cancel'),
          submitBtn: this.translateService.instant('common.update'),
          formData: this.form?.value,
          insuranceId: this.insuranceId,
          paidPriceVehicle: this.paidPriceVehicle,
          insuranceCalculation: this.insuranceCalculation,
          insuranceInput: this.insuranceInput
        },
      }
    );

    dialogRef.componentInstance.insuranceUpdated.subscribe((data) => {
        this.insuranceUpdated.emit(data);
    });
  }

  setIsEditable(): void {
    // Coordinator + new insurance/renewal insurance with status 'Pending' can edit customer information only
    // create insurance can edit customer information
    if (
      ((this.currentRole === this.ROLES.COORDINATORGROUP)
        && this.insuranceStatus === INSURANCE_STATUS.PENDING)
      || this.insuranceInput === InsuranceInput.CREATE || 
      (this.currentRole === this.ROLES.INSURANCEADMINGROUP && [INSURANCE_STATUS.PENDING, INSURANCE_STATUS.CANCELLED, INSURANCE_STATUS.FAILED].includes(this.insuranceStatus))
    ) {
      this.isEditable = true;
    } else {
      this.isEditable = false;
    }
  }

}
