import {
  Component,
  EventEmitter,
  inject,
  Input,
  Output,
  SimpleChanges,
} from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import {
    Documents,
  VehicleAccessory,
} from '../../../interfaces';
import { CellCurrencyCustomComponent } from './cell-currency-custom/cell-currency-custom.component';
import { CellStatusCustomComponent } from './cell-status-custom/cell-status-custom.component';
import { TableCustomComponent } from './table-custom/table-custom.component';
import { GrandTotalComponent } from './grand-total/grand-total.component';
import { CommonModule, CurrencyPipe, DecimalPipe } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ROLES } from '../../../../../core/constants/roles.const';
import { IconModule } from '../../../../../core/icon/icon.module';
import { downloadFile } from '../../../../../core/helpers/function-common.helper';
import { LoadingService, NotificationService, VehicleAccessoriesService } from '../../../../../core/services';
import { ColDef } from 'ag-grid-community';
import { UploadInvoiceComponent  } from './upload-invoice/upload-invoice.component';
import { CellActionComponent } from './cell-action/cell-action.component';
import { catchError, filter, finalize, forkJoin, of, switchMap, tap } from 'rxjs';
import { ActionModal } from '../../../../../core/enums/modal.enum';
import { DialogConfirmComponent } from '../../../../../core/shared/modal/dialog-confirm/dialog-confirm.component';
import { MatDialog } from '@angular/material/dialog';
import { ModalVehicleAccessoriesComponent } from './modal-vehicle-accessories/modal-vehicle-accessories.component';
import { INSURANCE_STATUS } from '../../../constants/new-vehicle-insurance-detail.const';
import { ActionTable } from '../../../../../core/enums/vehical-model.enum';

@Component({
  selector: 'app-vehicle-accessories',
  standalone: true,
  imports: [
    TranslateModule,
    TableCustomComponent,
    GrandTotalComponent,
    IconModule,
    UploadInvoiceComponent,
    FormsModule,
    CommonModule
  ],
  providers: [DecimalPipe, CurrencyPipe],
  templateUrl: './vehicle-accessories.component.html',
  styleUrls: ['./vehicle-accessories.component.scss'],
})
export class VehicleAccessoriesComponent {
  @Input() insuranceCode: string;
  @Input() docsVehicleAccessories?: Documents[];
  @Input() rowDataVehicleAccessory: VehicleAccessory[];
  @Input() currentRole: string;
  @Input() status: string;
  @Input() remarks: string;
  @Output() selectedVehicleAccessory = new EventEmitter();
  @Output() insuranceUpdated = new EventEmitter<void>();
  @Output() documentsEmpty? = new EventEmitter<boolean>();
  @Output() remarksChanged? = new EventEmitter<string>();
  
  editingRow: VehicleAccessory = null;
  deletingRow: VehicleAccessory = null;
  ROLES = ROLES;
  INSURANCE_STATUS = INSURANCE_STATUS;
  private translateService = inject(TranslateService);
  private notificationService = inject(NotificationService);
  private vehicleAccessoriesService = inject(VehicleAccessoriesService);
  private dialog = inject(MatDialog);
  loadingService = inject(LoadingService);

  defaultColDef: ColDef<any> = {
    resizable: false,
    sortable: false,
    menuTabs: [],
    suppressMovable: true,
  };

  gridOptions: any = {
    rowSelection: 'multiple',
    suppressRowClickSelection: true,
  };

  colDataVehicleAccessory: any;
  ngOnChanges(changes: SimpleChanges): void {
    if (
      (changes['status'] && changes['status'].currentValue) ||
      (changes['currentRole'] && changes['currentRole'].currentValue)
    ) {
      this.colDataVehicleAccessory = this.initDataCol();
    }
    
    if (changes['remarks']) {
    this.remarksChanged.emit(changes['remarks'].currentValue);
    }
}

  onCellAction(type: string, data: VehicleAccessory) {
    if (type === ActionTable.Edit) {
      this.editingRow = data;
      this.deletingRow = null;
      this.onEditItem(data);
    } else if (type === ActionTable.Remove) {
      this.deletingRow = data;
      this.editingRow = null;
      this.onDelete(data);
    }
  }

  initDataCol() {
    let tmpconfigapprove = [
      {
        headerName: this.translateService.instant(
          'insuranceClaimsDetails.parts.approved'
        ),
        headerValueGetter: () =>
          this.translateService.instant(
            'insuranceClaimsDetails.parts.approved'
          ),
        field: 'status',
        headerClass: 'header-center',
        cellClass: 'cell-center',
        cellRenderer: CellStatusCustomComponent,
        flex: 1,
      },
    ];
    let tmpcolumnconfig = [
      {
        headerName: this.translateService.instant('vehicleAccessories.item'),
        headerValueGetter: () =>
          this.translateService.instant('vehicleAccessories.item'),
        field: 'item',
        cellRenderer: (params) => {
          return params?.value ? params?.value : '-';
        },
        flex: 1,
      },
      {
        headerName: this.translateService.instant('vehicleAccessories.srp'),
        headerValueGetter: () =>
          this.translateService.instant('vehicleAccessories.srp'),
        field: 'srp',
        cellRenderer: CellCurrencyCustomComponent,
      },
      {
        headerName: this.translateService.instant(
          'vehicleAccessories.quantity'
        ),
        headerValueGetter: () =>
          this.translateService.instant('vehicleAccessories.quantity'),
        field: 'quantity',
        cellRenderer: (params) => {
          return params?.value ? params?.value : '-';
        },
        flex: 1,
      },
      {
        headerName: this.translateService.instant(
          'vehicleAccessories.coverage'
        ),
        headerValueGetter: () =>
          this.translateService.instant('vehicleAccessories.coverage'),
        field: 'coverage',
        cellRenderer: CellCurrencyCustomComponent,
      },
    ];
    let finalColumnConfig: any[] = [];
    finalColumnConfig = [...tmpcolumnconfig, ...tmpconfigapprove];
    if (!this.isAccessoryReadonly) {
        const actionColumn = {
            headerName: this.translateService.instant('vehicleAccessories.action'),
            headerClass: 'text-right',
            headerValueGetter: () => this.translateService.instant('vehicleAccessories.action'),
            field: 'action',
            flex: 1,
            maxWidth: 150,
            minWidth: 150,
            cellRenderer: CellActionComponent,
            cellRendererParams: (params: any) => ({
            status: this.getActionStatus(this.currentRole, this.status),
            onClick: (type: string, data: any) => this.onCellAction(type, data),
            }),
            cellClass: 'action-last-grid',
            sortable: false,
        };
        finalColumnConfig.push(actionColumn);
    }
    return finalColumnConfig;
  }

  readonly editStatuses = [
    INSURANCE_STATUS.PENDING,
    INSURANCE_STATUS.CANCELLED,
    INSURANCE_STATUS.FAILED
  ];
  
  readonly readonlyStatuses = [
    INSURANCE_STATUS.IN_PROCESS,
    INSURANCE_STATUS.COMPLETED,
    INSURANCE_STATUS.PAYMENT_PROCESSING,
    INSURANCE_STATUS.PAYMENT_CONFIRM,
    INSURANCE_STATUS.PARTPAID,
  ];
    
  get isAccessoryReadonly(): boolean {
    if (this.currentRole === ROLES.INSURANCEADMINGROUP) return false;
    if (this.currentRole === ROLES.INSURANCEDEPARTMENTGROUP) return true;
    
    if (this.currentRole === ROLES.INSURANCEPARTNERGROUP && this.status === INSURANCE_STATUS.IN_PROCESS) {
      return false;
    }
    // Coordinator can edit in Pending/Cancel/Failed
    if (this.currentRole === ROLES.COORDINATORGROUP && this.editStatuses.includes(this.status)) {
      return false;
    }
    // All other cases are readonly
    return true;
  }
  
  get isInvoiceReadonly(): boolean {
    if (this.currentRole === ROLES.INSURANCEADMINGROUP) return false;
    if (this.currentRole === ROLES.INSURANCEDEPARTMENTGROUP) return true;
    // Coordinator can upload in Pending/Cancel/Failed
    if (this.currentRole === ROLES.COORDINATORGROUP && this.editStatuses.includes(this.status)) {
      return false;
    }
    return true;
  }
  
  // Remarks
  get isRemarksReadonly(): boolean {
    if (this.currentRole === ROLES.INSURANCEADMINGROUP) return false;
    if (this.currentRole === ROLES.INSURANCEDEPARTMENTGROUP) return true;
    if (this.currentRole === ROLES.INSURANCEPARTNERGROUP && this.status === INSURANCE_STATUS.IN_PROCESS) {
      return false;
    }
    return true;
  }
    

  get isRemarksRequired(): boolean {
    return (
      !this.isRemarksReadonly && Array.isArray(this.rowDataVehicleAccessory) &&
      this.rowDataVehicleAccessory.length > 0
    );
  }

  getActionStatus(role: string, status: string): 'edit' | 'delete' {
    if (
      role === ROLES.INSURANCEPARTNERGROUP &&
      status === INSURANCE_STATUS.IN_PROCESS
    ) {
      return 'edit';
    }
    return 'delete';
  }

  onDocumentsEmpty(e: any) {
    this.documentsEmpty.emit(this.docsVehicleAccessories?.length === 0);
  }

  onRemarksChanged(value: string) {
    this.remarks = value;
    this.remarksChanged.emit(value);
  }

  onCreateRemark() {
    this.remarksChanged.emit(this.remarks);
    if (!this.remarks || this.remarks.trim().length === 0) {
      return;
    }

    this.loadingService.showLoader();
    this.vehicleAccessoriesService
      .createVehicleAccessoriesRemark(this.insuranceCode, this.remarks)
      .pipe(finalize(() => this.loadingService.hideLoader()))
      .subscribe({
        next: (res) => {
          if (res.code === 'SUCCESS') {
            this.notificationService.showSuccess(
              this.translateService.instant('vehicleAccessories.remarkCreated')
            );
          } else {
            this.notificationService.showError(
              res.message || 'Failed to create remark'
            );
          }
        },
        error: () => {
          this.notificationService.showError(
            this.translateService.instant(
              'vehicleAccessories.remarkCreateError'
            )
          );
        },
      });
  }

  handleGetRowSelectedVehicleAccessory(event: any) {
    this.selectedVehicleAccessory.emit(event);
  }

  onAddItem() {
    const dialogRef = this.dialog.open(ModalVehicleAccessoriesComponent, {
      width: '850px',
      data: {
        title: this.translateService.instant(
          'vehicleAccessories.modal.addItem'
        ),
        insuranceCode: this.insuranceCode,
        status: 'add',
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.save'),
      },
      panelClass: 'custom-dialog-create-va',
    });

    dialogRef
      .afterClosed()
      .pipe(
        tap((result) => {
          if (result?.action === ActionModal.Submit) {
            this.loadingService.showLoader();
          }
        }),
        filter((result) => result?.action === ActionModal.Submit),
        tap((result) => {
          if (result?.data) {
            const newItem: VehicleAccessory = {
              id: result.data.id,
              item: result.data.item,
              srp: result.data.srp,
              quantity: result.data.quantity,
              coverage: result.data.coverageAmount,
              status: result.data.approved ? 'APPROVED' : 'REJECTED',
            };
            this.rowDataVehicleAccessory = [
              ...this.rowDataVehicleAccessory,
              newItem,
            ];
            this.insuranceUpdated.emit();
          }
          this.loadingService.hideLoader();
        })
      )
      .subscribe(() => {
        this.loadingService.hideLoader();
      });
  }

  onEditItem(data: VehicleAccessory) {
    const dialogRef = this.dialog.open(ModalVehicleAccessoriesComponent, {
      width: '850px',
      data: {
        title: this.translateService.instant(
          'vehicleAccessories.modal.editItem'
        ),
        status: 'edit',
        selectedVehicleAccessory: data,
        insuranceCode: this.insuranceCode,
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.save'),
      },
      panelClass: 'custom-dialog-create-va',
    });

    dialogRef
      .afterClosed()
      .pipe(
        tap((result) => {
          if (result?.action === ActionModal.Submit) {
            this.loadingService.showLoader();
          }
        }),
        filter((result) => result?.action === ActionModal.Submit),
        tap((result) => {
          if (result?.data) {
            const newItem: VehicleAccessory = {
              id: result.data.id,
              item: result.data.item,
              srp: result.data.srp,
              quantity: result.data.quantity,
              coverage: result.data.coverageAmount,
              status: result.data.approved ? 'APPROVED' : 'REJECTED',
            };
            this.rowDataVehicleAccessory = [
              ...this.rowDataVehicleAccessory,
              newItem,
            ];
            this.insuranceUpdated.emit();
          }
          this.loadingService.hideLoader();
        })
      )
      .subscribe(() => {
        this.loadingService.hideLoader();
      });
  }

  onDownloadFile(file: Documents) {
    downloadFile(file.realFileName, file.downloadUrl);
  }

  onFileSelected(fileList: FileList) {
    if (!fileList?.length) return;

    const files = Array.from(fileList);
    this.loadingService.showLoader();

    const uploads = files.map((file) =>
      this.vehicleAccessoriesService
        .uploadInvoice(file, this.insuranceCode)
        .pipe(
          catchError((error) => {
            this.notificationService.showError(`Upload failed: ${file.name}`);
            return of(null);
          })
        )
    );

    forkJoin(uploads)
      .pipe(finalize(() => this.loadingService.hideLoader()))
      .subscribe({
        next: (responses) => {
          const successfulUploads = responses.filter(
            (r) => r?.code === 'SUCCESS'
          );

          if (successfulUploads.length > 0) {
            this.notificationService.showSuccess(
              `Uploaded ${successfulUploads.length} file(s) successfully`
            );
            this.insuranceUpdated.emit();
          }
        },
      });
  }

  onDelete(e: VehicleAccessory) {
    const dialogRef = this.dialog.open(DialogConfirmComponent, {
      width: '530px',
      data: {
        title: this.translateService.instant(
          'vehicleAccessories.modal.removeItem'
        ),
        icon: 'ic-delete',
        class: 'ic-color-red',
        confirmMsg: this.translateService.instant(
          'vehicleAccessories.modal.removeAttachmentConfirm',
          { item: e?.item }
        ),
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.remove'),
      },
    });
    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe(() => {
        this.loadingService.showLoader();
        this.vehicleAccessoriesService
          .deleteVehicleAccessory(this.insuranceCode, e.id)
          .pipe(finalize(() => this.loadingService.hideLoader()))
          .subscribe({
            next: (res) => {
              if (res.code === 'SUCCESS') {
                this.notificationService.showSuccess(
                  this.translateService.instant(
                    'vehicleAccessories.modal.deleteSuccess'
                  )
                );
                this.insuranceUpdated.emit();
              } else {
                this.notificationService.showError(res.message);
              }
            },
            error: (err) => {
              this.notificationService.showError(
                this.translateService.instant(
                  'vehicleAccessories.modal.deleteError'
                )
              );
            },
          });
      });
  }

  onRemoveFile(e: any) {
    const { file } = e;
    const dialogRef = this.dialog.open(DialogConfirmComponent, {
      width: '530px',
      data: {
        title: this.translateService.instant(
          'insurance.modal.removeAttachment'
        ),
        icon: 'ic-delete',
        class: 'ic-color-red',
        confirmMsg: this.translateService.instant(
          'insurance.modal.removeAttachmentConfirm',
          { fileName: file?.realFileName }
        ),
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.remove'),
      },
    });

    dialogRef
      .afterClosed()
      .pipe(
        filter((result) => result?.action === ActionModal.Submit),
        switchMap(() => {
          this.loadingService.showLoader();
          return this.vehicleAccessoriesService
            .deleteFile(file.code)
            .pipe(finalize(() => this.loadingService.hideLoader()));
        })
      )
      .subscribe({
        next: () => {
          this.notificationService.showSuccess(
            this.translateService.instant('insurance.attachmentRemoved')
          );
          this.insuranceUpdated.emit();
        },
        error: (err) => {
          this.notificationService.showError(
            this.translateService.instant('insurance.attachmentRemovedError')
          );
        },
      });
  }
}
