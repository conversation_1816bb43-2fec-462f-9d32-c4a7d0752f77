import { CommonModule } from '@angular/common';
import { Component, EventEmitter, inject, Input, Output, SimpleChanges } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { Subject, takeUntil, filter } from 'rxjs';
import { ROLES } from '../../../../../core/constants/roles.const';
import { DateFormat } from '../../../../../core/enums';
import { INSURANCE_STATUS } from '../../../constants/new-vehicle-insurance-detail.const';
import { ResponseCustomerInformation } from '../../../interfaces';
import { NewVehicleInsuranceDetailService } from '../../../services/new-vehicle-insurance/new-vehicle-insurance-detail.service';
import { CustomerInformationUpdateComponent } from './customer-information-update/customer-information-update.component';
import { InsuranceInput } from '../../../enum';
import { ModalCreateInsuranceUpdateCustomerComponent } from '../../../pages/insurance-renewals/create-insurance/modal-create-insurance-update-customer/modal-create-insurance-update-customer.component';

@Component({
  selector: 'app-customer-information',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatIconModule
  ],
  templateUrl: './customer-information.component.html',
  styleUrl: './customer-information.component.scss'
})
export class CustomerInformationComponent {
  INSURANCE_INPUT = InsuranceInput;
  @Input() customerInformation: ResponseCustomerInformation;
  @Input() insuranceId: string;
  @Input() currentRole: string;
  @Input() insuranceInput: string = this.INSURANCE_INPUT.NEW;
  @Input() isAlreadyAssigned: boolean = true;
  @Output() handleReloadCustomerInformation = new EventEmitter();
  insuranceStatus: string;
  isEditable: boolean = false;
  dateFormat = DateFormat;
  ROLES = ROLES;
  dialog = inject(MatDialog);

  insuranceService = inject(NewVehicleInsuranceDetailService);
  private destroy$ = new Subject<void>();

  ngOnInit(): void {
    this.insuranceService.insuranceStatus$
    .pipe(takeUntil(this.destroy$))
    .subscribe((status) => {
      this.insuranceStatus = status;
      this.setIsEditable();
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['isAlreadyAssigned']) {
      this.setIsEditable();
    }
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onEditCustomerInformation(): void {
    if (this.insuranceInput === this.INSURANCE_INPUT.CREATE) {
      const dialogRef = this.dialog.open(ModalCreateInsuranceUpdateCustomerComponent, {
        width: '850px',
        maxHeight: '90vh',
        disableClose: true,
        autoFocus: false,
        panelClass: 'dialog-insurance-custom',
        data: {
          firstName: this.customerInformation?.insuredData?.firstName,
          lastName: this.customerInformation?.insuredData?.lastName,
          email: this.customerInformation?.customerEmail,
          phone: this.customerInformation?.phone,
        },
      });
      dialogRef.componentInstance.handleUpdateCustomerInformation.subscribe((data) => {
        this.handleReloadCustomerInformation.emit(data);
      });
    } else {
      const dialogRef = this.dialog.open(CustomerInformationUpdateComponent, {
        width: '850px',
        maxHeight: '90vh',
        disableClose: true,
        autoFocus: false,
        panelClass: 'dialog-insurance-custom',
        data: {
          uid: this.customerInformation?.uid,
          insuranceId: this.insuranceId,
          insuranceInput: this.insuranceInput,
          getCustomerInformation: (uid: string) => this.insuranceService.getCustomerDetails(uid),
          saveCustomerInformation: (insuranceId: string, payload) => this.insuranceService.updateCustomerDetails(insuranceId, payload),
        },
      });

      dialogRef.afterClosed()
      .pipe(filter((result) => result))
      .subscribe((() => {
        this.handleReloadCustomerInformation.emit();
      }));
    }
  }

  setIsEditable(): void {
    // Coordinator + new insurance with status 'Pending', 'Cancelled', 'Failed to Assign' can edit customer information only
    // Coordinator + renewal insurance with status 'Pending', 'Cancelled', 'Partially paid', 'Payment confirmed', 'Payment processing', 'Failed to Assign' can edit customer information only
    if (
      ((this.currentRole === this.ROLES.COORDINATORGROUP || this.currentRole === this.ROLES.INSURANCEADMINGROUP)
        && (this.insuranceInput === this.INSURANCE_INPUT.NEW)
        && [INSURANCE_STATUS.PENDING, INSURANCE_STATUS.CANCELLED, INSURANCE_STATUS.FAILED].includes(this.insuranceStatus))
      || ((this.currentRole === this.ROLES.COORDINATORGROUP || this.currentRole === this.ROLES.INSURANCEADMINGROUP)
        && this.insuranceInput === this.INSURANCE_INPUT.RENEWAL
        && [INSURANCE_STATUS.PENDING, INSURANCE_STATUS.CANCELLED, INSURANCE_STATUS.PARTPAID, INSURANCE_STATUS.PAYMENT_CONFIRM, INSURANCE_STATUS.PAYMENT_PROCESSING, INSURANCE_STATUS.FAILED].includes(this.insuranceStatus))
      || (this.currentRole === this.ROLES.COORDINATORGROUP
        && this.insuranceInput === this.INSURANCE_INPUT.CREATE)
      || (this.currentRole !== this.ROLES.INSURANCEDEPARTMENTGROUP && !this.isAlreadyAssigned && this.currentRole === this.ROLES.COORDINATORGROUP)
    ) {
      this.isEditable = true;
    } else {
      this.isEditable = false;
    }
  }
}
