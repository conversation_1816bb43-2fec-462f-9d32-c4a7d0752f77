import { Component, inject, OnD<PERSON>roy, OnInit } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { ActionModal } from '../../../../../core/enums';
import { FormGroupComponent } from '../../../../../core/shared';
import { ScreenSizeService, LoadingService, NotificationService } from '../../../../../core/services';


@Component({
  selector: 'app-popup-reject-promotion-claim',
  standalone: true,
  imports: [
    MatDialogModule,
    MatIconModule,
    TranslateModule,
    ReactiveFormsModule,
    FormGroupComponent
  ],
  providers: [
    ScreenSizeService,
    LoadingService,
    NotificationService,
  ],
  templateUrl: './popup-approval-insurance-claim.component.html',
  styleUrls: ['./popup-approval-insurance-claim.component.scss'],
})
export class PopupApprovalInsuranceClaimComponent implements OnInit, OnDestroy {
  data = inject(MAT_DIALOG_DATA);
  dialogRef = inject(MatDialogRef<PopupApprovalInsuranceClaimComponent>);

  notificationService = inject(NotificationService);

  dataReasonOption: any = []

  onCancel() {
    this.dialogRef.close();
  }

  onConfirm() {
    this.dialogRef.close({ action: ActionModal.Submit });
  }

  onChangeDataType(e: any) {

  }

  ngOnInit(): void {}

  ngOnDestroy() {

  }

  disableSubmit() {
    let value = this.data?.approveConfirmedInsuranceClaimForm?.value?.remarks
    return !(value !== null && value !== undefined && value.toString().trim() !== "")
  }
}
