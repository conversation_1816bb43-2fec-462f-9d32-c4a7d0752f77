<div class="insurance-info-form">
    <h2 class="title-dialog">
        {{ "newVehicleInsurance.detail.vehicleInformation.modal.updateVehicleInformation" | translate }}
    </h2>
    <mat-dialog-content class="container-dialog">
        <form [formGroup]="mainForm">
            <app-form-group [label]="'newVehicleInsurance.detail.vehicleInformation.vin' | translate"
                [control]="chasissNum" [isReadOnly]="true"></app-form-group>
            <app-form-group [label]="'newVehicleInsurance.detail.vehicleInformation.make' | translate"
                [control]="makeBy" [required]="true"></app-form-group>
            <app-form-group [label]="'newVehicleInsurance.detail.vehicleInformation.model' | translate"
                [control]="vehicleModel" [required]="true"></app-form-group>
            <app-form-group [label]="'newVehicleInsurance.detail.vehicleInformation.modelYear' | translate"
                [control]="modelYear" [required]="true"></app-form-group>
            <app-form-group [label]="'newVehicleInsurance.detail.vehicleInformation.color' | translate"
                [control]="color" [required]="true"></app-form-group>
            <app-form-group [label]="'newVehicleInsurance.detail.vehicleInformation.engine' | translate"
                [control]="engineNumber" [required]="true"></app-form-group>
            <app-form-group [label]="'newVehicleInsurance.detail.vehicleInformation.plateNo' | translate"
                [control]="plateNumberCSNumber" [required]="true"></app-form-group>
            <app-form-group [label]="'newVehicleInsurance.detail.vehicleInformation.orderNo' | translate"
                [control]="orderNumber"></app-form-group>
            <app-form-group [label]="'newVehicleInsurance.detail.vehicleInformation.orderStatus' | translate"
                [control]="orderStatus"></app-form-group>
            <app-form-group [label]="'newVehicleInsurance.detail.vehicleInformation.paidPrice' | translate"
                [control]="paidPrice" [required]="true" [useCurrencyFormat]="true"></app-form-group>
            <app-date-time-picker-form-group [label]="'newVehicleInsurance.detail.vehicleInformation.vsiDate' | translate"
                [control]="vsiDate" controlId="vsiDate" [required]="true"></app-date-time-picker-form-group>
            <app-form-group [label]="'newVehicleInsurance.detail.vehicleInformation.modelSalesCode' | translate"
                [control]="code" [required]="true"></app-form-group>
            <app-form-group [label]="'newVehicleInsurance.detail.vehicleInformation.variant' | translate"
                [control]="variant" [required]="true"></app-form-group>
            <app-dropdown-form-group [label]="'newVehicleInsurance.detail.vehicleInformation.bodyType'"
                [control]="bodyType" [required]="true"
                [options]="bodyTypeOptions"></app-dropdown-form-group>
            <app-form-group [label]="'newVehicleInsurance.detail.vehicleInformation.powerTransmission' | translate"
                [control]="powerTransmission" [required]="true"></app-form-group>
            <app-dropdown-form-group [label]="'newVehicleInsurance.detail.vehicleInformation.fuelType'"
                [control]="fuelType" [required]="true"
                [options]="fuelTypeOptions"></app-dropdown-form-group>
            <app-form-group [label]="'newVehicleInsurance.detail.vehicleInformation.seats' | translate"
                [control]="seats" [required]="true"></app-form-group>
            <app-form-group [label]="'newVehicleInsurance.detail.vehicleInformation.unloadedWeight' | translate"
                [control]="unloadedWeight" [required]="true"></app-form-group>
            <app-form-group [label]="'newVehicleInsurance.detail.vehicleInformation.maximumWeight' | translate"
                [control]="maximumWeight" [required]="true"></app-form-group>
            <app-dropdown-form-group [label]="'newVehicleInsurance.detail.vehicleInformation.productClassification'"
                [control]="productClassification" [required]="true"
                [options]="productClassificationOptions"></app-dropdown-form-group>
        </form>
    </mat-dialog-content>

    <mat-dialog-actions class="action-dialog insurance-info-form__actions">
        <button type="button" class="btn-quaternary" (click)="onCancel()">
            {{ "common.cancel" | translate }}
        </button>
        <button class="btn-primary" type="submit" [disabled]="!mainForm.valid" (click)="onSubmit()">
            {{ "common.update" | translate }}
        </button>
    </mat-dialog-actions>
</div>