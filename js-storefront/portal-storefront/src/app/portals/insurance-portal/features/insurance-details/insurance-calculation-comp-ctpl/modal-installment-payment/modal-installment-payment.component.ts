import { Component, inject, OnInit } from '@angular/core';
import { ReactiveFormsModule, FormGroup, FormControl, Validators } from '@angular/forms';
import { MatDialogModule, MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { debounceTime, Subscription } from 'rxjs';
import { ActionModal } from '../../../../../../core/enums';
import { handleErrors } from '../../../../../../core/helpers';
import { OptionDropdown } from '../../../../../../core/interfaces';
import { NotificationService, LoadingService } from '../../../../../../core/services';
import { DropdownFormGroupComponent, FormGroupComponent } from '../../../../../../core/shared';
import { ReceivableService } from '../../../../../claims-portal/services/receivable/receivable.service';
import { InsuranceCalculationService } from '../../../../services/insurance-calculation/insurance-calculation.service';
import { ModalCloseComponent } from '../../../../../iot-portal/features/vehicle/vehicle-detail/emergency-tab/modal-close/modal-close.component';

@Component({
  selector: 'app-modal-installment-payment',
  standalone: true,
  imports: [
    MatDialogModule,
    MatIconModule,
    TranslateModule,
    ReactiveFormsModule,
    FormGroupComponent,
  ],
  providers: [NotificationService, ReceivableService],
  templateUrl: './modal-installment-payment.component.html',
  styleUrls: ['./modal-installment-payment.component.scss']
})
export class ModalInstallmentPaymentComponent implements OnInit {
  data = inject(MAT_DIALOG_DATA);
  dialogRef = inject(MatDialogRef<ModalCloseComponent>);
  loadingService = inject(LoadingService);
  notificationService = inject(NotificationService);
  translateService = inject(TranslateService);
  insuranceService = inject(InsuranceCalculationService);
  mainSubscription = new Subscription();
  form: FormGroup = new FormGroup({
    numberOfMonths: new FormControl('', [
      Validators.min(1),
    ]),
    amountPerMonth: new FormControl('')
  });
  ngOnInit() {
    this.form.patchValue({
      numberOfMonths: this.data?.insuranceCalculation?.installmentMonths,
      amountPerMonth: this.data?.insuranceCalculation?.installmentEachMonth?.value,
    });

    this.form.controls['numberOfMonths'].valueChanges.pipe(debounceTime(300)).subscribe((value) => {
      if (!value) {
        this.form.controls['amountPerMonth'].setValue('');
      } else {
        this.form.controls['amountPerMonth'].setValue(this.data?.monthlyPayment && value != 0 ? ((this.data?.monthlyPayment / value).toFixed(2)) : '');
      }
    });
  }

  get numberOfMonths() {
    return this.form.controls['numberOfMonths'] as FormControl;
  }

  get amountPerMonth() {
    return this.form.controls['amountPerMonth'] as FormControl;
  }


  ngOnDestroy() {
    this.mainSubscription && this.mainSubscription.unsubscribe();
  }

  onCancel() {
    this.dialogRef.close();
  }

  onConfirm() {
    const formData = this.form?.value;
    const params: any = {
      numberOfMonths: +formData.numberOfMonths,
      amountPerMonth: +formData.amountPerMonth,
    };
    this.loadingService.showLoader();
    this.mainSubscription.add(
      this.insuranceService.updateInstallment(this.data?.insuranceId, params).subscribe(
        (res) => {
          if (res.code === '200') {
            this.notificationService.showSuccess(
              this.translateService.instant(
                'newVehicleInsurance.detail.insuranceCalculation.updateInstallmentPayment'
              )
            );
            this.dialogRef.close({ action: ActionModal.Submit });
          } else {
            this.notificationService.showError(res?.message);
          }
          this.loadingService.hideLoader();
        },
        (error) => {
          this.loadingService.hideLoader();
          handleErrors(error, this.notificationService);
        }
      )
    );
  }
}
