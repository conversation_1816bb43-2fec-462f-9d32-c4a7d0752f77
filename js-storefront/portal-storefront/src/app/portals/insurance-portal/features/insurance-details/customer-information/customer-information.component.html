<div class="detail-box header-with-icon">
  <div class="detail-box-header">
    <div class="detail-box-title">
      <mat-icon svgIcon="ic-owner-details" class="medium-icon section-header--icon" ></mat-icon>
      {{ "newVehicleInsurance.detail.customerInformation.sectionName" | translate }}
    </div>
    @if (isEditable) {
      <button class="btn-link" (click)="onEditCustomerInformation()">
        <mat-icon svgIcon="ic-edit" class="small-icon" aria-hidden="true"></mat-icon>
        {{ "common.edit" | translate }}
      </button>
    }
  </div>
  <div class="detail-content">
    @if (currentRole === ROLES.COORDINATORGROUP || currentRole === ROLES.INSURANCEADMINGROUP || currentRole === ROLES.INSURANCEDEPARTMENTGROUP) {
      <div class="detail-row row">
        <div class="detail-element col-2">
          <div class="detail-label">{{ "newVehicleInsurance.detail.customerInformation.name" | translate }}</div>
          @if (insuranceInput === this.INSURANCE_INPUT.CREATE) {
            <div class="detail-value">{{ customerInformation?.insuredData?.firstName + ' ' + customerInformation?.insuredData?.lastName || '-' }}</div>
          } @else {
            <div class="detail-value">{{ customerInformation?.customerName || '-' }}</div>
          }
        </div>
        <div class="detail-element col-2">
          <div class="detail-label">{{ "newVehicleInsurance.detail.customerInformation.phone" | translate }}</div>
          <div class="detail-value">{{ customerInformation?.phone || '-' }}</div>
        </div>
        <div class="detail-element col-2">
          <div class="detail-label">{{ "newVehicleInsurance.detail.customerInformation.birthDate" | translate }}</div>
          <div class="detail-value">{{
            !customerInformation?.customerBirthDate
            ? '-'
            : customerInformation?.customerBirthDate | date : dateFormat.ShortDate
          }}</div>
        </div>
        @if (insuranceInput === this.INSURANCE_INPUT.RENEWAL
          || insuranceInput === this.INSURANCE_INPUT.CREATE
          || insuranceInput === this.INSURANCE_INPUT.NEW) {
          <div class="detail-element col-2">
            <div class="detail-label">{{ "newVehicleInsurance.detail.customerInformation.vehicleOwnership" | translate }}</div>
            <div class="detail-value">{{ customerInformation?.ownership?.name || '-' }}</div>
          </div>
        }
        <div class="detail-element col-2">
          <div class="detail-label">{{ "newVehicleInsurance.detail.customerInformation.email" | translate }}</div>
          <div class="detail-value">{{ customerInformation?.customerEmail || '-' }}</div>
        </div>
        <div class="detail-element col-2">
          <div class="detail-label">{{ "newVehicleInsurance.detail.customerInformation.group" | translate }}</div>
          <div class="detail-value">{{ customerInformation?.customerGroup?.name || '-' }}</div>
        </div>
      </div>
    }
    @if (currentRole === ROLES.INSURANCEPARTNERGROUP) {
      <div class="detail-row">
        <div class="detail-element">
          <div class="detail-label">{{ "newVehicleInsurance.detail.customerInformation.name" | translate }}</div>
          <div class="detail-value">{{ customerInformation?.customerName || '-' }}</div>
        </div>
        <div class="detail-element">
          <div class="detail-label">{{ "newVehicleInsurance.detail.customerInformation.birthDate" | translate }}</div>
          <div class="detail-value">{{
            !customerInformation?.customerBirthDate
            ? '-'
            : customerInformation?.customerBirthDate | date : dateFormat.ShortDate
          }}</div>
        </div>
        @if (insuranceInput === this.INSURANCE_INPUT.NEW) {
          <div class="detail-element">
            <div class="detail-label">{{ "newVehicleInsurance.detail.customerInformation.gender" | translate }}</div>
            <div class="detail-value">{{ customerInformation?.customerSex?.name || '-' }}</div>
          </div>
          <div class="detail-element">
            <div class="detail-label">{{ "newVehicleInsurance.detail.customerInformation.group" | translate }}</div>
            <div class="detail-value">{{ customerInformation?.customerGroup?.name || '-' }}</div>
          </div>
        }
        @if (insuranceInput === this.INSURANCE_INPUT.RENEWAL) {
          <div class="detail-element">
            <div class="detail-label">{{ "newVehicleInsurance.detail.customerInformation.vehicleOwnership" | translate }}</div>
            <div class="detail-value">{{ customerInformation?.ownership?.name }}</div>
          </div>
          <div class="detail-element">
            <div class="detail-label">{{ "newVehicleInsurance.detail.customerInformation.group" | translate }}</div>
            <div class="detail-value">{{ customerInformation?.customerGroup?.name || '-' }}</div>
          </div>
          <div class="detail-element">
            <div class="detail-label">{{ "newVehicleInsurance.detail.customerInformation.gender" | translate }}</div>
            <div class="detail-value">{{ customerInformation?.customerSex?.name || '-' }}</div>
          </div>
        }
      </div>
    }
    <div class="detail-row row">
      @if ((insuranceInput === this.INSURANCE_INPUT.RENEWAL
        || insuranceInput === this.INSURANCE_INPUT.CREATE
        || insuranceInput === this.INSURANCE_INPUT.NEW)
        && (currentRole === ROLES.COORDINATORGROUP || currentRole === ROLES.INSURANCEADMINGROUP || currentRole === ROLES.INSURANCEDEPARTMENTGROUP)) {
        <div class="detail-element col-2">
          <div class="detail-label">{{ "newVehicleInsurance.detail.customerInformation.gender" | translate }}</div>
          <div class="detail-value">{{ customerInformation?.customerSex?.name || '-' }}</div>
        </div>
      }
      <div class="detail-element col-10">
        <div class="detail-label">{{ "newVehicleInsurance.detail.customerInformation.mainAddress" | translate }}</div>
        <div class="detail-value">{{ customerInformation?.addressDisplay || '-' }}</div>
      </div>
    </div>
  </div>
</div>