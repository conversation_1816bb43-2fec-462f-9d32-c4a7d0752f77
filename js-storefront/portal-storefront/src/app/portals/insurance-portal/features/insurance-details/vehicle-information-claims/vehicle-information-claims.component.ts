import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { CurrencyComponent } from '../../../../../core/shared/currency/currency.component';
import { VehicleInfoData } from '../../../interfaces';

@Component({
  selector: 'app-vehicle-information-claims',
  standalone: true,
  imports: [CommonModule, TranslateModule, MatIconModule, CurrencyComponent],
  templateUrl: './vehicle-information-claims.component.html',
  styleUrls: ['./vehicle-information-claims.component.scss'],
})
export class VehicleInformationClaimsComponent {
  @Input() data: VehicleInfoData;
}
