<div class="detail-box">
  <div class="detail-box-header">
    <div class="detail-box-title">
      <mat-icon svgIcon="ic-vehicle-information" class="medium-icon section-header--icon" ></mat-icon>
      {{ "newVehicleInsurance.detail.vehicleInformation.sectionName" | translate }}
    </div>
  </div>
  <div class="detail-content">
    <div class="detail-row">
      <div class="detail-element">
        <div class="detail-label">{{ "newVehicleInsurance.detail.vehicleInformation.model" | translate }}</div>
        <div class="detail-value">{{ data?.name || '-' }}</div>
      </div>
      <div class="detail-element">
        <div class="detail-label">{{ "newVehicleInsurance.detail.vehicleInformation.vin" | translate }}</div>
        <div class="detail-value">{{ data?.chassisNum || '-' }}</div>
      </div>
      <div class="detail-element">
        <div class="detail-label">{{ "newVehicleInsurance.detail.vehicleInformation.color" | translate }}</div>
        <div class="detail-value">{{ data?.color || '-' }}</div>
      </div>
      <div class="detail-element">
        <div class="detail-label">{{ "newVehicleInsurance.detail.vehicleInformation.plateNo" | translate }}</div>
        <div class="detail-value">{{ data?.plateNumber || '-' }}</div>
      </div>
      <div class="detail-element">
        <div class="detail-label">{{ "newVehicleInsurance.detail.vehicleInformation.engine" | translate }}</div>
        <div class="detail-value">{{ data?.engineNumber || '-' }}</div>
      </div>
      <div class="detail-element">
        <div class="detail-label">{{ "newVehicleInsurance.detail.vehicleInformation.mileage" | translate }}</div>
        <div class="detail-value">{{ data?.odometer || '-' }}</div>
      </div>
    </div>
  </div>
</div>
