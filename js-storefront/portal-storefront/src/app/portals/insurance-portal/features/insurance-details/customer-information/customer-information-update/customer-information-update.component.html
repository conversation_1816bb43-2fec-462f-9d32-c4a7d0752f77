<h2 class="title-dialog">
  {{ "newVehicleInsurance.detail.customerInformation.updateCustomerInformation" | translate }}
</h2>

<mat-dialog-content
  class="container-dialog customer-information-update"
  [class.loading]="loadingService.isLoading"
>
<form [formGroup]="form">
  <div class="row">
    <div class="col-6">
      <app-form-group
        [label]="'newVehicleInsurance.detail.customerInformation.firstName' | translate"
        [control]="form.controls.firstName"
        [required]="true"
        [maxLength]="255"
      ></app-form-group>
    </div>
    <div class="col-6">
      <app-form-group
        [label]="'newVehicleInsurance.detail.customerInformation.middleName' | translate"
        [control]="form.controls.middleName"
        [required]="true"
        [maxLength]="255"
      ></app-form-group>
    </div>
  </div>
  <div class="row">
    <div class="col-6">
      <app-form-group
        [label]="'newVehicleInsurance.detail.customerInformation.lastName' | translate"
        [control]="form.controls.lastName"
        [required]="true"
        [maxLength]="255"
      ></app-form-group>
    </div>
    <div class="col-6">
      <app-date-form-group
        [label]="'newVehicleInsurance.detail.customerInformation.birthday' | translate"
        [control]="form.controls.birthday"
        controlId="birthday"
        [required]="true"
        [errorMessage]="'validation.fillInData'| translate"
      ></app-date-form-group>
    </div>
  </div>
  <div class="row">
    <div class="col-6">
      <app-form-group
        [label]="'newVehicleInsurance.detail.customerInformation.phone' | translate"
        [control]="form.controls.phone"
        [required]="true"
        [type]="'tel'"
        [otherErrorMessages]="{
          pattern: 'validation.phone' | translate
        }"
      ></app-form-group>
    </div>
    <div class="col-6">
      <app-form-group
        [label]="'newVehicleInsurance.detail.customerInformation.email' | translate"
        [control]="form.controls.email"
        [required]="true"
        [type]="'email'"
        [otherErrorMessages]="{
          email: 'validation.email' | translate
        }"
      ></app-form-group>
    </div>
  </div>
  <div class="row">
    <div class="col-6">
      <app-dropdown-form-group
        [label]="'newVehicleInsurance.detail.customerInformation.titles' | translate"
        [control]="form.controls.titles"
        [options]="titlesOption"
        [required]="true"
      ></app-dropdown-form-group>
    </div>
    <div class="col-6">
      <app-dropdown-form-group
        [label]="'newVehicleInsurance.detail.customerInformation.genders' | translate"
        [control]="form.controls.genders"
        [options]="gendersOption"
        [required]="true"
      ></app-dropdown-form-group>
    </div>
  </div>
  <div class="row">
    <div class="col-6">
      <app-form-group
        [label]="'newVehicleInsurance.detail.customerInformation.streetNumber' | translate"
        [control]="form.controls.streetNumber"
        [required]="true"
        [maxLength]="255"
      ></app-form-group>
    </div>
    <div class="col-6">
      <app-form-group
        [label]="'newVehicleInsurance.detail.customerInformation.apartment' | translate"
        [control]="form.controls.apartment"
      ></app-form-group>
    </div>
  </div>
  <div class="row">
    <div class="col-12">
      <app-form-group
        [label]="'newVehicleInsurance.detail.customerInformation.streetName' | translate"
        [control]="form.controls.streetName"
      ></app-form-group>
    </div>
  </div>
  <div class="row">
    <div class="col-6">
      <app-form-group
        [label]="'newVehicleInsurance.detail.customerInformation.village' | translate"
        [control]="form.controls.village"
        [className]="'village'"
      ></app-form-group>
    </div>
    <div class="col-6">
      <app-dropdown-form-group
        [label]="'newVehicleInsurance.detail.customerInformation.region' | translate"
        [control]="form.controls.region"
        [options]="regionOption"
        [required]="true"
        (changeOption)="onChangeOption($event, 'region')"
      ></app-dropdown-form-group>
    </div>
  </div>
  <div class="row">
    <div class="col-6">
      <app-dropdown-form-group
        [label]="'newVehicleInsurance.detail.customerInformation.province' | translate"
        [control]="form.controls.province"
        [options]="provinceOption"
        [required]="true"
        (changeOption)="onChangeOption($event, 'province')"
      ></app-dropdown-form-group>
    </div>
    <div class="col-6">
      <app-dropdown-form-group
        [label]="'newVehicleInsurance.detail.customerInformation.city' | translate"
        [control]="form.controls.city"
        [options]="cityOption"
        [required]="true"
        (changeOption)="onChangeOption($event, 'city')"
      ></app-dropdown-form-group>
    </div>
  </div>
  <div class="row">
    <div class="col-6">
      <app-dropdown-form-group
        [label]="'newVehicleInsurance.detail.customerInformation.barangay' | translate"
        [control]="form.controls.barangay"
        [options]="barangayOption"
        [required]="true"
      ></app-dropdown-form-group>
    </div>
    <div class="col-6">
      <app-dropdown-form-group
        [label]="'newVehicleInsurance.detail.customerInformation.zipCode' | translate"
        [control]="form.controls.zipCode"
        [options]="zipCodeOption"
        [required]="true"
      ></app-dropdown-form-group>
    </div>
  </div>
</form>
</mat-dialog-content>
<mat-dialog-actions class="action-dialog">
  <button class="btn-quaternary" (click)="onCancel()">
    {{ "common.cancel" | translate }}
  </button>
  <button
    class="btn-primary"
    [disabled]="form.invalid"
    (click)="updateCustomerInformation()"
  >
    {{ "common.saveChanges" | translate }}
  </button>
</mat-dialog-actions>