import { Component, EventEmitter, inject, Input, Output } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { Subject, takeUntil } from 'rxjs';
import { ROLES } from '../../../../../core/constants/roles.const';
import { DateFormat, InsuranceType } from '../../../../../core/enums';
import { INSURANCE_STATUS } from '../../../constants/new-vehicle-insurance-detail.const';
import { Documents, ResponseInsurerPartner } from '../../../interfaces';
import { NewVehicleInsuranceDetailService } from '../../../services/new-vehicle-insurance/new-vehicle-insurance-detail.service';
import { InsuranceCoverageList, InsuranceInput } from '../../../enum';
import { FileListComponent } from '../../file-list/file-list.component';
import { downloadFile } from '../../../../../core/helpers/function-common.helper';
import { DateTimeHelper } from '../../../../../core/helpers/date-time.helper';

@Component({
  selector: 'app-insurer-information',
  standalone: true,
  imports: [
    TranslateModule,
    MatIconModule,
    CommonModule,
    FileListComponent
  ],
  providers: [DateTimeHelper],
  templateUrl: './insurer-information.component.html',
  styleUrl: './insurer-information.component.scss'
})
export class InsurerInformationComponent {
  INSURANCE_INPUT = InsuranceInput;
  INSURANCE_STATUS = INSURANCE_STATUS;
  insuranceType = InsuranceType;
  @Input() insurerInformation: ResponseInsurerPartner | any;
  @Input() currentRole: string;
  @Input() insuranceInput: string = this.INSURANCE_INPUT.NEW;
  @Input() isAlreadyAssigned: boolean = true;
  @Input() insuranceCoverage: string;

  @Output() editInsurerInformation = new EventEmitter();
  insuranceStatus: string;
  isEditable: boolean = false;
  dateFormat = DateFormat
  ROLES = ROLES;
  InsuranceCoverageList = InsuranceCoverageList;
  insuranceService = inject(NewVehicleInsuranceDetailService);
  dateTimeHelper = inject(DateTimeHelper)
  private destroy$ = new Subject<void>();

  ngOnInit(): void {
    this.insuranceService.insuranceStatus$
    .pipe(takeUntil(this.destroy$))
    .subscribe((status) => {
      this.insuranceStatus = status;
      this.setIsEditable();
    });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onEditInsurerInformation(insurerInformation: ResponseInsurerPartner) {
    this.editInsurerInformation.emit(insurerInformation);
  }

  setIsEditable(): void {
    // Coordinator + new insurance with status 'Pending', 'Cancelled', 'Failed to Assign' can edit insurer information only
    // Coordinator + renewal insurance with status 'Pending', 'Partially Paid', 'Payment confirmed', 'Failed to Assign' can edit insurer information only
    if (
      (
        (this.currentRole === ROLES.COORDINATORGROUP || this.currentRole === ROLES.INSURANCEADMINGROUP)
        && this.insuranceInput === this.INSURANCE_INPUT.NEW
        && [INSURANCE_STATUS.PENDING, INSURANCE_STATUS.CANCELLED, INSURANCE_STATUS.FAILED].includes(this.insuranceStatus)
      )
      || (
        (this.currentRole === ROLES.COORDINATORGROUP)
        && this.insuranceInput === this.INSURANCE_INPUT.RENEWAL
        && [INSURANCE_STATUS.PENDING, INSURANCE_STATUS.PARTPAID, INSURANCE_STATUS.PAYMENT_CONFIRM, INSURANCE_STATUS.FAILED].includes(this.insuranceStatus)
      )
      || (
        (this.currentRole === ROLES.INSURANCEADMINGROUP)
        && this.insuranceInput === this.INSURANCE_INPUT.RENEWAL
        && [INSURANCE_STATUS.PENDING, INSURANCE_STATUS.CANCELLED, INSURANCE_STATUS.FAILED, INSURANCE_STATUS.PARTPAID, INSURANCE_STATUS.PAYMENT_CONFIRM].includes(this.insuranceStatus)
      )
    ) {
      this.isEditable = true;
    } else {
      this.isEditable = false;
    }
  }

  onDownloadFile(event: Documents) {
    if (event.code) {
      downloadFile(event.realFileName, event.downloadUrl);
    } else {
      const a = document.createElement('a');
      a.style.display = "none";
      a.href = event.downloadUrl;
      a.download = event.realFileName;
      document.body.appendChild(a);
      a.click();
      a.remove()
    }
  }

  
  convertUTCtoDisplayDate(date) {
    if (!date) return '-'

    return this.dateTimeHelper.convertUTCtoDisplayDate(date, DateFormat.ShortDate)
  }
}
