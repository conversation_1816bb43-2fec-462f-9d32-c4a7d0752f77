import { Component, Input, OnInit } from '@angular/core';
import { CurrencyComponent } from '../../../../../../core/shared/currency/currency.component';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-grand-total',
  standalone: true,
  imports: [CurrencyComponent, TranslateModule],
  templateUrl: './grand-total.component.html',
  styleUrls: ['./grand-total.component.scss']
})
export class GrandTotalComponent implements OnInit {
  @Input() disclaimerText?: string = '';
  @Input() data: any;
  constructor() { }

  ngOnInit() {
  }

}
