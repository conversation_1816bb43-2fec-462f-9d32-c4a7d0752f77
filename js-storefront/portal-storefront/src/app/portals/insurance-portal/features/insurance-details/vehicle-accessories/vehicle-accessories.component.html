<div class="detail-box">
  <div class="detail-box-header">
    <div class="detail-box-title">
      <mat-icon
        svgIcon="ic-vehicle-information"
        class="medium-icon section-header--icon"
      ></mat-icon>
      {{
        "newVehicleInsurance.detail.insuranceInformation.vehicleAccessories"
          | translate
      }}
    </div>
    @if (currentRole === ROLES.COORDINATORGROUP && editStatuses.includes(status)) {
    <button class="btn-link" (click)="onAddItem()">
      <mat-icon
        svgIcon="ic-add-red"
        class="small-icon"
        aria-hidden="true"
      ></mat-icon>
      {{ "common.add" | translate }}
    </button>
    }
  </div>
  <div class="detail-content">
    <app-table-custom
      [rowData]="rowDataVehicleAccessory"
      [colDefs]="colDataVehicleAccessory"
      (rowSelected)="handleGetRowSelectedVehicleAccessory($event)"
    ></app-table-custom>
    <div class="vehicle-accessories-bottom">
      <app-upload-invoice
        [documents]="docsVehicleAccessories"
        [isReadonly]="isInvoiceReadonly"
        (handleDownload)="onDownloadFile($event)"
        (handleSelectedFiles)="onFileSelected($event)"
        (handleDelete)="onRemoveFile($event)"
        (documentsEmpty)="onDocumentsEmpty($event)"
      ></app-upload-invoice>
      <div class="remarks-section">
        <label>
          {{ "vehicleAccessories.remarks" | translate }}
          <span *ngIf="isRemarksRequired" class="required-star">*</span>
        </label>
        <ng-container *ngIf="isRemarksReadonly; else editableRemarks">
          <div class="remarks-text" [ngClass]="{'no-data': !remarks}">
            {{ remarks || ("vehicleAccessories.remarkNoData" | translate) }}
          </div>
        </ng-container>
        <ng-template #editableRemarks>
          <textarea
            class="remarks-textarea"
            [(ngModel)]="remarks"
            rows="3"
            [required]="isRemarksRequired"
            placeholder=""
            (ngModelChange)="onRemarksChanged($event)"
          ></textarea>
          <button
            class="btn-small btn-primary"
            (click)="onCreateRemark()"
            type="button"
          >
            {{ "vehicleAccessories.createRemark" | translate }}
          </button>
        </ng-template>
      </div>      
    </div>
  </div>
</div>
