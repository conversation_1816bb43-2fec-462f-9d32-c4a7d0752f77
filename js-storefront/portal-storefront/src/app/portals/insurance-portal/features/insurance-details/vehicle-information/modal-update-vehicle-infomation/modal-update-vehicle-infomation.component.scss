.insurance-info-form {
  .title-dialog {
    text-align: center;
    padding: 45px 55px 35px;
    margin: 0;
  }
  .container-dialog {
    padding: 0 55px 35px;
    form {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-gap: 30px;
    }
  }

  input {
    &:disabled {
        background-color: #F5F5F5 !important;
    }
  }

  &__actions {
    display: flex;
    justify-content: center;
    gap: 20px;

    .btn {
      border-radius: 0;
      min-width: 215px;
      height: 50px;
    }
  }
}

:host {
    ::ng-deep {
        input:disabled {
            background-color: #F5F5F5 !important;
        }
    }
}