import { Component, EventEmitter, inject, Input, Output } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Documents } from '../../../interfaces';
import { FileListComponent } from '../../file-list/file-list.component';
import { UploadFileComponent } from '../../upload-file/upload-file.component';
import { NotificationService } from '../../../../../core/services';

@Component({
  selector: 'app-documents',
  standalone: true,
  imports: [
    TranslateModule,
    MatIconModule,
    UploadFileComponent,
    FileListComponent,
  ],
  providers: [NotificationService],
  templateUrl: './documents.component.html',
  styleUrl: './documents.component.scss',
})
export class DocumentsComponent {
  @Input() documents: Documents[];

  @Output() handleSelectedFiles = new EventEmitter();
  @Output() handleDownload = new EventEmitter<Documents>();
  @Output() handleDelete = new EventEmitter<any>();
  
  private dialog = inject(MatDialog);
  private translateService = inject(TranslateService);
  private notificationService = inject(NotificationService)
  onFileSelected(event: FileList): void {
    const invalidLimit: boolean = this.checkLimitFiles(event);
    if (invalidLimit) {
      this.notificationService.showError(this.translateService.instant('uploadFile.invalidLimit5MB'));
      return;
    }
    this.handleSelectedFiles.emit({files: event});
  }
  onRemoveFile(e: Documents) {
    this.handleDelete.emit({file: e});
  }

  onDownloadFile(e: Documents) {
    this.handleDownload.emit(e);
  }

  checkLimitFiles(event: any) {
    let invalid: boolean = false;
    if (event && event.length > 0) {
      for (let index = 0; index < event.length; index++) {
        const element = event[index];
        if (element.size > 5 * 1024 * 1024) {
          invalid = true;
          return invalid;
        }
      }
    }
    return invalid;
  }
}
