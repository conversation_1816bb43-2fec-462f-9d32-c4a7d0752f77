import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  inject,
  Input,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Subject, takeUntil, filter } from 'rxjs';
import { ROLES } from '../../../../../core/constants/roles.const';
import { ActionModal } from '../../../../../core/enums';
import { IconModule } from '../../../../../core/icon/icon.module';
import {
  NotificationService,
  LoadingService,
} from '../../../../../core/services';
import { INSURANCE_STATUS } from '../../../constants/new-vehicle-insurance-detail.const';
import { InsuranceCoverageList, InsuranceInput } from '../../../enum';
import {
  InsuranceQuotation,
  InsuranceQuotationCTPL,
  ResponseInsuranceInformation,
} from '../../../interfaces';
import { NewVehicleInsuranceDetailService } from '../../../services/new-vehicle-insurance/new-vehicle-insurance-detail.service';
import { ModalCalculateCompulsoryTplComponent } from './modal-calculate-compulsory-tpl/modal-calculate-compulsory-tpl.component';
import { ModalInstallmentPaymentComponent } from './modal-installment-payment/modal-installment-payment.component';
import { ModalUpdateCalculationCompComponent } from './modal-update-calculation-comp/modal-update-calculation-comp.component';

@Component({
  selector: 'app-insurance-calculation-comp-ctpl',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    IconModule,
    ReactiveFormsModule,
    MatDialogModule,
  ],
  providers: [NotificationService, LoadingService, MatDialog],
  templateUrl: './insurance-calculation-comp-ctpl.component.html',
  styleUrls: ['./insurance-calculation-comp-ctpl.component.scss'],
})
export class InsuranceCalculationCompCtplComponent implements OnInit {
  @Input() insuranceId: string;
  @Input() paidPriceVehicle: string;
  @Input() insuranceCalculation:
    | InsuranceQuotation
    | InsuranceQuotationCTPL
    | any;
  @Input() currentRole: string;
  @Input() insuranceInput: string;
  @Input() insuranceInformation: ResponseInsuranceInformation;
  @Input() isRenewal: boolean = false;
  @Input() isComprehensiveCompulsoryTPL: boolean = false;
  @Input() monthlyPayment: any;

  @Output() insuranceUpdated = new EventEmitter<void>();

  insuranceStatus: string;
  isEditable: boolean = false;
  form: any;
  ROLES = ROLES;
  insuranceCoverageList = InsuranceCoverageList;

  dialog = inject(MatDialog);
  notificationService = inject(NotificationService);
  translateService = inject(TranslateService);
  loadingService = inject(LoadingService);

  insuranceService = inject(NewVehicleInsuranceDetailService);
  private destroy$ = new Subject<void>();

  ngOnChanges(changes: SimpleChanges): void {
    if (
      changes['isComprehensiveCompulsoryTPL'] &&
      changes['isComprehensiveCompulsoryTPL'].currentValue
    ) {
      this.setIsEditable();
    }
  }

  ngOnInit(): void {
    this.insuranceService.insuranceStatus$
      .pipe(takeUntil(this.destroy$))
      .subscribe((status) => {
        this.insuranceStatus = status;
        this.setIsEditable();
      });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  openEditDialog(): void {
    const dialogRef = this.dialog.open(
      ModalUpdateCalculationCompComponent,
      {
        width: '1000px',
        data: {
          title: this.translateService.instant(
            'newVehicleInsurance.detail.insuranceCalculation.comprehensive'
          ),
          isComprehensiveCompulsoryTPL: this.isComprehensiveCompulsoryTPL,
          disableClose: true,
          autoFocus: true,
          restoreFocus: true,
          cancelBtn: this.translateService.instant('common.cancel'),
          submitBtn: this.translateService.instant('common.update'),
          formData: this.form?.value,
          insuranceId: this.insuranceId,
          paidPriceVehicle: this.paidPriceVehicle,
          insuranceCalculation: this.insuranceCalculation?.compData,
          insuranceInput: this.insuranceInput,
        },
      }
    );

    dialogRef.componentInstance.insuranceUpdated.subscribe((data) => {
      this.insuranceUpdated.emit(data);
    });
  }

  openEditCTPLDialog(): void {
    const dialogRef = this.dialog.open(ModalCalculateCompulsoryTplComponent, {
      width: '1000px',
      data: {
        title: this.translateService.instant(
          'newVehicleInsurance.detail.insuranceCalculation.compulsoryTPL'
        ),
        icon: '',
        confirmMsg: this.translateService.instant(
          'newVehicleInsurance.detail.insuranceCalculation.editMsg'
        ),
        disableClose: true,
        isRenewal: this.isRenewal,
        autoFocus: true,
        restoreFocus: true,
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.update'),
        formData: this.form?.value,
        insuranceId: this.insuranceId,
        paidPriceVehicle: this.paidPriceVehicle,
        insuranceCalculation: this.insuranceCalculation?.ctplData,
        insuranceInput: this.insuranceInput,
      },
    });

    dialogRef.componentInstance.insuranceUpdated.subscribe((data) => {
      this.insuranceUpdated.emit(data);
    });
  }

  openInstallmentDialog() {
    const dialogRef = this.dialog.open(ModalInstallmentPaymentComponent, {
      width: '700px',
      data: {
        title: this.translateService.instant(
          'newVehicleInsurance.detail.insuranceCalculation.installmentPayment'
        ),
        monthlyPayment: this.monthlyPayment,
        isRenewal: this.isRenewal,
        disableClose: true,
        autoFocus: true,
        restoreFocus: true,
        cancelBtn: this.translateService.instant('common.cancel'),
        submitBtn: this.translateService.instant('common.update'),
        formData: this.form?.value,
        insuranceId: this.insuranceId,
        paidPriceVehicle: this.paidPriceVehicle,
        insuranceCalculation: this.insuranceCalculation,
        insuranceInput: this.insuranceInput,
      },
    });

    dialogRef
      .afterClosed()
      .pipe(filter((result) => result?.action === ActionModal.Submit))
      .subscribe((res) => {
        if (res) {
          this.insuranceUpdated.emit();
        }
      });
  }

  setIsEditable(): void {
    // Coordinator + new insurance/renewal insurance with status 'Pending' can edit customer information only
    // create insurance can edit customer information
    if (this.isComprehensiveCompulsoryTPL) {
      if ([INSURANCE_STATUS.PENDING].includes(this.insuranceStatus)) {
        this.isEditable = true;
      } else {
        this.isEditable = false;
      }
    } else {
      if (
        (this.currentRole === this.ROLES.COORDINATORGROUP &&
          this.insuranceStatus === INSURANCE_STATUS.PENDING) ||
        this.insuranceInput === InsuranceInput.CREATE ||
        (this.currentRole === this.ROLES.INSURANCEADMINGROUP &&
          [
            INSURANCE_STATUS.PENDING,
            INSURANCE_STATUS.CANCELLED,
            INSURANCE_STATUS.FAILED,
          ].includes(this.insuranceStatus))
      ) {
        this.isEditable = true;
      } else {
        this.isEditable = false;
      }
    }
  }
}
