.grid-6 {
  display: grid !important;
  grid-template-columns: repeat(6, 1fr);
}

.line-break {
  border: 0;
  border-top: 1px solid #ccc;
  margin: 10px 0 10px;
}

.detail-element-header {
  display: flex;
  align-items: center;
  margin-bottom: 0;
  .form-group__label {
    color: var(--Primary-Dark-Grey, #3A3A3A);
    /* Small Text Paragraph/Large */
    font-family: "Toyota Type";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 14px;
    margin-right: 0;
    margin-bottom: 0;
  }
  .acion-release {
    color: var(--Primary-Red, #EB0A1E);
    /* Body/S Bold */
    font-family: "Toyota Type";
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px;
    text-align: left;
    cursor: pointer;
  }
}

.detail-element-box {
  display: flex;
  align-items: flex-start;
  gap: 100px;
  align-self: stretch;
  width: 100%;
  max-width: 1240px;
  .detail-element-upload {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 15px;
  }


  .detail-element-file {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 15px;
  }
}

.max-w-450{
    max-width: 450px;
}

.mb-1 {
    margin-bottom: 10px;
}