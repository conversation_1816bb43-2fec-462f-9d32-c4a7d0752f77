import { Component, inject, OnInit } from '@angular/core';
import { MatDialogModule, MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { Subscription } from 'rxjs';
import { ActionModal } from '../../../../../../core/enums';
import { OptionDropdown } from '../../../../../../core/interfaces';
import { DropdownFormGroupComponent, DialogConfirmComponent } from '../../../../../../core/shared';
import { NewVehicleInsuranceDetailService } from '../../../../services/new-vehicle-insurance/new-vehicle-insurance-detail.service';

@Component({
  selector: 'app-modal-cancel-insurance',
  standalone: true,
  imports: [MatDialogModule, MatIconModule, TranslateModule, DropdownFormGroupComponent, ReactiveFormsModule],
  templateUrl: './modal-cancel-insurance.component.html',
  styleUrls: ['./modal-cancel-insurance.component.scss']
})
export class ModalCancelInsuranceComponent implements OnInit {
  data = inject(MAT_DIALOG_DATA);
  form: FormGroup = new FormGroup({});
  dialogRef = inject(MatDialogRef<DialogConfirmComponent>);
  insuranceDetailService = inject(NewVehicleInsuranceDetailService)
  subscription = new Subscription();
  cancelInsuranceReasonOpt: OptionDropdown[] = [];

  public get reason(): FormControl {
    return this.form.get('reason') as FormControl;
  }

  ngOnInit() {
    this.form = this.data.form as FormGroup;
    this.cancelInsuranceReason();
  }

  cancelInsuranceReason() {
    this.subscription.add(
      this.insuranceDetailService
        .getInsuranceTypeOption()
        .subscribe((data: any) => {
          if (data?.enums?.cancelInsuranceReason && data?.enums?.cancelInsuranceReason.length) {
            this.cancelInsuranceReasonOpt = data?.enums?.cancelInsuranceReason.sort((a,b) => a.name < b.name ? -1 : 1);
          } else {
            this.cancelInsuranceReasonOpt = []
          }
        })
    );
  }

  onCancel() {
    this.dialogRef.close();
  }

  onConfirm() {
    this.dialogRef.close({ action: ActionModal.Submit });
  }
}
