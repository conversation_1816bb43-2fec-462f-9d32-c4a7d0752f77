import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatIcon } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { TranslateModule } from '@ngx-translate/core';
import { Documents } from '../../interfaces';
import { DateFormat } from '../../../../core/enums';

@Component({
  selector: 'app-file-list',
  standalone: true,
  imports: [CommonModule, MatSelectModule, MatIcon, ReactiveFormsModule, TranslateModule],
  templateUrl: './file-list.component.html',
  styleUrl: './file-list.component.scss'
})
export class FileListComponent {
  @Input() fileList: Documents[];
  @Input() isHiddenRemove: boolean = false;
  @Input() isHiddenDownload: boolean = false;

  @Output() handleDownload = new EventEmitter<Documents>();
  @Output() handleDelete = new EventEmitter<Documents>();

  dateFormat = DateFormat;

  downloadFile(file: Documents) {
    if (this.isHiddenDownload) {
      return;
    }
    file && this.handleDownload.emit(file);
  }

  removeFile(file: Documents) {
    if (this.isHiddenRemove) {
      return;
    }
    this.handleDelete.emit(file);
  }

  getFileNameWithoutExtension(realFileName: string) {
    return realFileName ? realFileName.split('.').shift() : '';
  }

  getFileExtension(realFileName: string) {
    return realFileName ? realFileName.split('.').pop() : '';
  }
}
