<div class="upload-file-list">
  @for (file of fileList; track $index) {
    <div class="upload-line">
      <div class="upload-file">
        <mat-icon svgIcon="ic-file" class="small-icon"></mat-icon>
        <span class="upload-file-type">{{ getFileExtension(file?.realFileName) }}</span>
      </div>
      <div class="upload-file-name">
        {{ getFileNameWithoutExtension(file?.realFileName) }}
        <div class="upload-date">{{
          file?.creationTime | date: dateFormat.ShortDate
        }}</div>
      </div>
      <div class="upload-file-actions">
        @if (!isHiddenDownload) {
          <div class="upload-download" (click)="downloadFile(file)">
            <mat-icon
              svgIcon="ic-download"
              class="small-icon"
            ></mat-icon>
            {{ "uploadFile.download" | translate }}
          </div>
        }
        @if (!isHiddenRemove) {
          <div class="upload-remove" (click)="removeFile(file)">
            <mat-icon
              svgIcon="ic-delete"
              class="small-icon"
            ></mat-icon>
            {{ "uploadFile.remove" | translate }}
          </div>
        }
      </div>
    </div>
  }
</div>
