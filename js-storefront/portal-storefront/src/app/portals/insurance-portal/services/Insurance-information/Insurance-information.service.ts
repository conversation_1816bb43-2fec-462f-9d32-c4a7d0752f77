import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

import { environment } from '../../../../../environments/environment';
import {
  RequestUpdateInsuranceRolePartner,
  UpdateInsuranceInfoRequest,
  UpdateInsuranceInfoResponse,
} from '../../interfaces';

@Injectable({
  providedIn: 'root',
})
export class InsuranceInformationService {
  private prefixEnv = environment.OCC_BASE_URL + environment.OCC_PREFIX;

  constructor(private http: HttpClient) {}

  updateInsuranceInfo(
    insuranceCode: string,
    request: UpdateInsuranceInfoRequest,
    isRenewal: boolean = false
  ): Observable<UpdateInsuranceInfoResponse> {
    let url = `${this.prefixEnv}portal/insurances/new-insurances/${insuranceCode}/updateInsuranceInfo`;
    if (isRenewal) {
      url = `${this.prefixEnv}portal/insurances/renewal-insurance/${insuranceCode}/update-insurance-info`
    }
    return this.http.patch<UpdateInsuranceInfoResponse>(url, request).pipe(
      map((response) => response),
      catchError((error) => {
        console.error('Error updating insurance info:', error);
        return of({} as UpdateInsuranceInfoResponse);
      })
    );
  }

  getInsuranceFilters(): Observable<any> {
    const url = `${this.prefixEnv}portal/insurances/new-insurances/filters`;

    return this.http.get<any>(url).pipe(
      map((response) => response),
      catchError((error) => {
        console.error('Error fetching insurance filters:', error);
        return of({});
      })
    );
  }

  updateInsuranceRolePartner(insuranceCode: string, request: RequestUpdateInsuranceRolePartner, isRenewal: boolean = false) {
    let url = `${this.prefixEnv}portal/insurances/new-insurances/${insuranceCode}/partner-update`;
    if (isRenewal) {
      url = `${this.prefixEnv}portal/insurances/renewal-insurance/${insuranceCode}/partner-update`;
    }
    return this.http.patch(url, request);
  }

  combinePartnerUpdate(insuranceCode: string, request: any) {
    let url = `${this.prefixEnv}portal/insurances/insurance/${insuranceCode}/combined-partner-update`;
    return this.http.patch(url, request);
  }

  updateInsuranceRoleAdmin(insuranceCode: string, request: RequestUpdateInsuranceRolePartner) {
    let url = `${this.prefixEnv}portal/insurances/new-insurances/${insuranceCode}/updateInsuranceInfo`;
    return this.http.patch(url, request);
  }
}
