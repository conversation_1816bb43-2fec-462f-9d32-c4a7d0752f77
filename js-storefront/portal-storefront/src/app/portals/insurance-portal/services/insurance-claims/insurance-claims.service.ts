import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { catchError, map, Observable, of, throwError } from 'rxjs';
import moment from 'moment';
import { ResponseCommon, WidgetResponse } from '../../../../core/interfaces';
import {
  ClaimItem,
  FilterNewVehicleInsurance,
  InsuranceData,
  RequesteInsuranceClaimsList,
  RequestUpdateCustomerDetail,
  ResponseInsuranceClaimsList,
} from '../../interfaces';
import { environment } from '../../../../../environments/environment';
import { ROLES } from '../../../../core/constants/roles.const';
import { DateFormat } from '../../../../core/enums';
import { DateTimeHelper } from '../../../../core/helpers/date-time.helper';
import { DatePipe } from '@angular/common';

@Injectable({ providedIn: 'root' })
export class InsuranceClaimsService {
  private readonly prefixEnv =
    environment.OCC_BASE_URL + environment.OCC_PREFIX;

  constructor(
    private http: HttpClient,
    private dateTimeHelper: DateTimeHelper,
    private datePipe: DatePipe,
  ) {}

  getInsuranceClaimsList(
    params: RequesteInsuranceClaimsList
  ): Observable<ResponseInsuranceClaimsList> {
    const path = `${this.prefixEnv}portal/insurance-claims/list`;
    const httpParams = this.buildHttpParamsForList(params);

    return this.http
      .get<ResponseInsuranceClaimsList>(path, { params: httpParams })
      .pipe(
        map((response) => {
          if (!response || !response.items) {
            throw new Error('Invalid response received from server.');
          }
          return response;
        }),
        catchError((error) =>
          this.handleError(error, 'Error fetching insurance claims list')
        )
      );
  }

  getWidget(dealer: string): Observable<{ widgets: WidgetResponse[] }> {
    const path = `${this.prefixEnv}portal/insurance-claims/widget`;

    const roles = JSON.parse(localStorage.getItem('roles'))?.roles || [];
    const isCoordinator = roles.includes(ROLES.COORDINATORGROUP);
    const isPartner = roles.includes(ROLES.INSURANCEPARTNERGROUP);

    let params = new HttpParams()
      .set('isCoordinator', isCoordinator.toString())
      .set('dealer', dealer)
      .set('isPartner', isPartner.toString());

    return this.http.get<{ widgets: WidgetResponse[] }>(path, { params }).pipe(
      map((response) => {
        if (!response || !response.widgets) {
          throw new Error('Invalid widget response from server.');
        }
        return response;
      }),
      catchError((error) =>
        this.handleError(error, 'Error fetching widget data')
      )
    );
  }

  getFilter(): Observable<FilterNewVehicleInsurance> {
    const path = `${this.prefixEnv}portal/insurance-claims/filters`;

    return this.http.get<FilterNewVehicleInsurance>(path).pipe(
      map((response) => {
        if (!response) {
          throw new Error('Invalid filter response received.');
        }
        return response;
      }),
      catchError((error) => this.handleError(error, 'Error fetching filters'))
    );
  }

  getInsuranceData(): Observable<InsuranceData> {
    const path = `${this.prefixEnv}portal/insurances/metadata`;

    return this.http.get<InsuranceData>(path, { params: { _t: Date.now().toString() } }).pipe(
      map((response) => {
        if (!response) {
          throw new Error('Invalid insurance metadata response received.');
        }
        return response;
      }),
      catchError((error) =>
        this.handleError(error, 'Error fetching insurance metadata')
      )
    );
  }

  private buildHttpParamsForList(
    params: RequesteInsuranceClaimsList
  ): HttpParams {
    const roles = JSON.parse(localStorage.getItem('roles'))?.roles || [];
    const isCoordinator = roles.includes(ROLES.COORDINATORGROUP);
    const isPartner = roles.includes(ROLES.INSURANCEPARTNERGROUP);

    let httpParams = new HttpParams()
      .set('dealer', params.dealer || '')
      .set('status', params.status || '')
      .set('month', params.month || '')
      .set('vin', params.vin || '')
      .set('sort', params.sort || '')
      .set('repairEstimation', params.repairEstimation || '')
      .set('currentPage', (params.currentPage ?? 0).toString())
      .set('pageSize', (params.pageSize ?? 15).toString())
      .set('isCoordinator', isCoordinator.toString())
      .set('isPartner', isPartner.toString());

    return httpParams;
  }

  getClaimDetails(claimId: string): Observable<ClaimItem> {
    const path = `${this.prefixEnv}portal/insurance-claims/${claimId}`;
    return this.http.get<ClaimItem>(path)
  }

  private handleError(error: any, message: string) {
    console.error(message, error);
    return throwError(() => new Error(message));
  }

  reviseInsuranceClaim(claimId: string): Observable<ResponseCommon> {
    const path = `${this.prefixEnv}portal/insurance-claims/${claimId}/revise-estimation`;
    return this.http.post<ResponseCommon>(path, {})
  }

  approveEstimationInsuranceClaim(claimId: string): Observable<ResponseCommon> {
    const path = `${this.prefixEnv}portal/insurance-claims/${claimId}/approve-estimation`;
    return this.http.post<ResponseCommon>(path, {})
  }

  confirmInsuranceClaim(claimId: string): Observable<ResponseCommon> {
    const path = `${this.prefixEnv}portal/insurance-claims/${claimId}/confirmClaim`;
    return this.http.post<ResponseCommon>(path, {})
  }

  approveInsuranceClaim(data: any, claimId: string): Observable<ResponseCommon> {
    const path = `${this.prefixEnv}portal/insurance-claims/${claimId}/approveItems`;
    return this.http.post<ResponseCommon>(path, data)
  }


  uploadFile(data: any, claimId: string, isLoa: boolean = false): Observable<ResponseCommon> {      
    let path = `${this.prefixEnv}portal/insurance-claims/${claimId}/uploadDocument`;
    if (isLoa) {
      path = `${this.prefixEnv}portal/insurance-claims/${claimId}/uploadLoaDocument`;
    }
    return this.http.post<ResponseCommon>(path, data, {observe: 'response'}).pipe(map((res: any) => {
      if (res.status === 200) {
        const dataRes: ResponseCommon = {
          code: 'SUCCESS',
          message: ''
        }
        return dataRes;
      }
      return res
    })); 
  }

  // Customer Information Update
  getCustomerDetails(claimCode: string) {
    const path = `${this.prefixEnv}portal/insurance-claims/${claimCode}/customer`;
    return this.http.get(path).pipe(map((response: any) => {
      const details = {
        ...response,
        firstName: response?.firstName || '',
        middleName: response?.middleName || '',
        lastName: response?.lastName|| '',
        birthday: response?.birthDate
          ? new Date(this.dateTimeHelper.convertUTCtoDisplayDate(response?.birthDate, DateFormat.ShortDate))
          : '',
        phone: response?.phone || '',
        email: response?.customerEmail || '',
        titles: response?.title || '',
        genders: response?.gender || '',
        streetNumber: response?.streetNumber || '',
        apartment: response?.appartment || '',
        streetName: response?.streetName || '',
        region: response?.region || '',
        province: response?.province || '',
        city: response?.city || '',
        zipCode: response?.postalcode || '',
        village: response?.village || '',
        barangay: response?.barangay || '',
      }
      return details;
    }));
  }

  updateCustomerDetails(claimCode: string, data: any) {
    const path = `${this.prefixEnv}portal/insurance-claims/${claimCode}/customer`;
    const payload = {
      firstName: data?.firstName || '',
      middleName: data?.middleName || '',
      lastName: data?.lastName || '',
      birthDate: moment(data?.birthday).format(DateFormat.UTC),
      phone: data?.phone || '',
      customerEmail: data?.email || '',
      title: data?.titles || '',
      gender: data?.genders || '',
      streetNumber: data?.streetNumber || '',
      appartment: data?.apartment || '',
      streetName: data?.streetName || '',
      village: data?.village || '',
      region: data?.region || '',
      province: data?.province || '',
      city: data?.city || '',
      barangay: data?.barangay || '',
      postalcode: data?.zipCode || '',
    }
    return this.http.patch(path, payload);
  }
}
