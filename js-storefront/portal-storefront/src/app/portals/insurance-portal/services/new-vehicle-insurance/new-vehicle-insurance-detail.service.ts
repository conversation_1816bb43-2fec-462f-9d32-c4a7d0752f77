import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { BehaviorSubject, Observable, map } from 'rxjs';
import { ResponseCustomerInformation, ResponseNewVehicleInsuranceDetail, ResponseInsurerPartner, ResponseVehicleInformation, RequestUpdateCustomerDetail } from '../../interfaces';
import { environment } from '../../../../../environments/environment';
import { ResponseCommon } from '../../../../core/interfaces';
import { OWNER_TYPE } from '../../constants/insurance-renewals.const';
import { DateTimeHelper } from '../../../../core/helpers/date-time.helper';
import { DateFormat } from '../../../../core/enums';
import { DatePipe } from '@angular/common';

@Injectable({
  providedIn: 'root'
})
export class NewVehicleInsuranceDetailService {
  constructor(
    private http: HttpClient,
    private translateService: TranslateService,
    private dateTimeHelper: DateTimeHelper,
    private datePipe: DatePipe
  ) { }

  prefixEnv = environment.OCC_BASE_URL + environment.OCC_PREFIX;

  private statusSubject = new BehaviorSubject<string>('');
  insuranceStatus$ = this.statusSubject.asObservable(); 

  updateInsuranceStatus(newStatus: string) {
    this.statusSubject.next(newStatus);
  }

  getInsuranceDetail(insuranceCode: string): Observable<ResponseNewVehicleInsuranceDetail> {
    const path = `${this.prefixEnv}portal/insurances/details/${insuranceCode}`;
    return this.http.get<ResponseNewVehicleInsuranceDetail>(path);
  }

  getCustomerInformation(uid: string): Observable<ResponseCustomerInformation> {
    const path = `${this.prefixEnv}portal/insurances/insurance/customer/${uid}`;
    return this.http.get<ResponseCustomerInformation>(path);
  }

  getInsurerPartner(insuranceId: string): Observable<ResponseInsurerPartner> {
    const path = `${this.prefixEnv}portal/insurances/insurance/${insuranceId}/partner`;
    return this.http.get<ResponseInsurerPartner>(path);
  }

  getVehicleInformation(insuranceCode: string): Observable<ResponseVehicleInformation> {
    const path = `${this.prefixEnv}portal/insurances/insurance/${insuranceCode}/vehicle`;
    return this.http.get<ResponseVehicleInformation>(path);
  }

  // Customer Information Update
  getCustomerDetails(uid: string) {
    const path = `${this.prefixEnv}portal/insurances/insurance/customer/${uid}`;
    return this.http.get(path).pipe(map((response: any) => {
      const customerAddress = response?.insuredData?.addresses[0] || {};
      const details = {
        firstName: response?.insuredData?.firstName || '',
        middleName: response?.insuredData?.middleName || '',
        lastName: response?.insuredData?.lastName|| '',
        birthday: response?.customerBirthDate
          ? new Date(this.dateTimeHelper.convertUTCtoDisplayDate(response?.customerBirthDate, DateFormat.ShortDate))
          : '',
        phone: response?.phone || '',
        email: response?.customerEmail || '',
        titles: customerAddress?.titleCode || '',
        genders: response?.insuredData?.gender?.code || '',
        streetNumber: customerAddress?.line2 || '',
        apartment: customerAddress?.appartment || '',
        streetName: customerAddress?.line1 || '',
        village: customerAddress?.village || '',
        region: customerAddress?.region?.isocode || '',
        province: customerAddress?.province?.isocode || '',
        city: customerAddress?.city?.isocode || '',
        barangay: customerAddress?.barangay?.isocode || '',
        zipCode: customerAddress?.postalCode || '',
      }
      return details;
    }));
  }

  updateCustomerDetails(insuranceId: string, data: any) {
    const path = `${this.prefixEnv}portal/insurances/insurance/${insuranceId}/customer`;
    const payload: RequestUpdateCustomerDetail = {
      firstName: data?.firstName || '',
      middleName: data?.middleName || '',
      lastName: data?.lastName || '',
      birthday: this.datePipe.transform(data?.birthday || '', DateFormat.ShortDate),
      phoneNumber: data?.phone || '',
      email: data?.email || '',
      title: data?.titles || '',
      gender: data?.genders || '',
      streetNumber: data?.streetNumber || '',
      appartment: data?.apartment || '',
      streetName: data?.streetName || '',
      village: data?.village || '',
      regionIso: data?.region || '',
      provinceIso: data?.province || '',
      cityIso: data?.city || '',
      barangayIso: data?.barangay || '',
      zipCode: data?.zipCode || '',
    };
    return this.http.patch(path, payload);
  }
  
  updateVehicleInfo(insuranceId: string, data: ResponseVehicleInformation) {
    const path = `${this.prefixEnv}portal/insurances/insurance/${insuranceId}/updateVehicleInfo`;
    return this.http.post(path, data);
  }

  deleteFile(documentCode: string) {
    const path = `${this.prefixEnv}portal/insurances/insurance/${encodeURI(documentCode)}/deleteDocument`;
    return this.http.post(path, {});
  }


  uploadFile(data: any, insuranceId: string): Observable<ResponseCommon> {      
    const path = `${this.prefixEnv}portal/insurances/insurance/${insuranceId}/uploadDocument`; 
    return this.http.post<ResponseCommon>(path, data, {observe: 'response'}).pipe(map((res: any) => {
      if (res.status === 200) {
        const dataRes: ResponseCommon = {
          code: 'SUCCESS',
          message: ''
        }
        return dataRes;
      }
      return res
    })); 
  }

  uploadSubEpolicy(data: any, insuranceId: string): Observable<ResponseCommon> {      
    const path = `${this.prefixEnv}portal/insurances/insurances/${insuranceId}/uploadSubEpolicy`; 
    return this.http.post<ResponseCommon>(path, data); 
  }

  releaseEpolicy(insuranceId: string): Observable<ResponseCommon> {
    const path = `${this.prefixEnv}portal/insurances/insurance/${insuranceId}/release`;
    return this.http.post<ResponseCommon>(path, {}, {observe: 'response'}).pipe(map((res: any) => {
      if (res.status === 200) {
        const dataRes: ResponseCommon = {
          code: 'SUCCESS',
          message: 'newVehicleInsurance.detail.releaseSuccess'
        }
        return dataRes;
      }
      return res
    })); 
  }

  releaseSubEpolicy(insuranceId: string): Observable<ResponseCommon> {
    const path = `${this.prefixEnv}portal/insurances/insurance/${insuranceId}/releaseSubPolicy`;
    return this.http.post<ResponseCommon>(path, {}); 
  }

  getMortgagesOption() {
    const path = `${this.prefixEnv}portal/insurances/mortgages`;
    return this.http.get(path);
  }

  getCompanyOption(insuranceCode: string) {
    const path = `${this.prefixEnv}portal/insurances/new-insurances/partners?insuranceCode=${insuranceCode}`;
    return this.http.get(path);
  }

  getInsuranceTypeOption() {
    const path = `${this.prefixEnv}portal/insurances/metadata`;
    return this.http.get(path);
  }

  assignNewInsurances(body: any, insuranceCode: string) {
    const path = `${this.prefixEnv}portal/insurances/new-insurances/${insuranceCode}/assign`;
    return this.http.post(path, body);
  }

  updateInsurerInfor(body: any, insuranceCode: string) {
    const path = `${this.prefixEnv}portal/insurances/${insuranceCode}/updateInsurer`;
    return this.http.post(path, body);
  }

  cancelInsurance(cancelInsuranceReason: string, insuranceCode: string) {
    const path = `${this.prefixEnv}portal/insurances/${insuranceCode}/cancel?reason=${cancelInsuranceReason}`;
    return this.http.post(path, {});
  }

  approveInsurance(insuranceCode: string) {
    const path = `${this.prefixEnv}portal/insurances/new-insurances/${insuranceCode}/partner-approve`;
    return this.http.put(path, {});
  }

  approveInsuranceRenewal(insuranceCode: string, vehicleCode: string) {
    const path = `${this.prefixEnv}portal/insurances/renewal-insurance/${insuranceCode}/approve?vin=${vehicleCode}`;
    return this.http.post(path, {});
  }

  getOwnerShipName(ownerCode: string, relationName: string): string {
    if (ownerCode === OWNER_TYPE.OWNER) {
      return this.translateService.instant(
        'newVehicleInsurance.detail.customerInformation.owner'
      );
    }
    if (ownerCode === OWNER_TYPE.DRIVER) {
      return relationName
        ? this.translateService.instant(
            'newVehicleInsurance.detail.customerInformation.relationToOwner',
            { relationName }
          )
        : this.translateService.instant(
            'newVehicleInsurance.detail.customerInformation.driver'
          );
    }
    return '-';
  }

  notifyInsuranceCost(insuranceCode: string): Observable<ResponseCommon> {
    const path = `${this.prefixEnv}portal/insurances/insurance/${insuranceCode}/notify-insurance-cost`;
    return this.http.post<ResponseCommon>(path, {});
  }
}
