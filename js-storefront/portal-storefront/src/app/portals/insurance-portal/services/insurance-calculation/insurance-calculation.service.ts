import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

import { environment } from '../../../../../environments/environment';
import {
  UpdateInsuranceCostRequest,
  UpdateInsuranceCostResponse,
  UpdateInsuranceInfoRequest,
  UpdateInsuranceInfoResponse,
} from '../../interfaces';
import { ResponseCommon } from '../../../../core/interfaces';

@Injectable({
  providedIn: 'root',
})
export class InsuranceCalculationService {
  private prefixEnv = environment.OCC_BASE_URL + environment.OCC_PREFIX;

  constructor(private http: HttpClient) {}


  updateInsuranceCost(
    insuranceCode: string,
    request: UpdateInsuranceCostRequest,
    isRenewal: boolean = false
  ): Observable<UpdateInsuranceCostResponse> {
    let url = `${this.prefixEnv}portal/insurances/new-insurances/${insuranceCode}/updateInsuranceCost`;
    if (isRenewal) {
      url = `${this.prefixEnv}portal/insurances/renewal-insurance/${insuranceCode}/update-insurance-cost`;
    }
    return this.http.patch<UpdateInsuranceCostResponse>(url, request); 
  }

  updateSubInsuranceCost(
    insuranceCode: string,
    request: UpdateInsuranceCostRequest | any,
  ): Observable<UpdateInsuranceCostResponse> {
    let url = `${this.prefixEnv}portal/insurances/insurances/${insuranceCode}/updateSubInsuranceCost`;
    return this.http.patch<UpdateInsuranceCostResponse>(url, request);
  }

  updateInstallment(
    insuranceCode: string,
    request: {
      numberOfMonths: number,
      amountPerMonth: number
    },
  ): Observable<UpdateInsuranceCostResponse> {
    let url = `${this.prefixEnv}portal/insurances/insurances/${insuranceCode}/updateInstallment`;
    return this.http.patch<UpdateInsuranceCostResponse>(url, request); 
  }

  updateInsuranceInfo(
    insuranceCode: string,
    request: UpdateInsuranceInfoRequest
  ): Observable<UpdateInsuranceInfoResponse> {
    const url = `${this.prefixEnv}portal/insurances/new-insurances/${insuranceCode}/updateInsuranceInfo`;

    return this.http.patch<UpdateInsuranceInfoResponse>(url, request).pipe(
      map((response) => response),
      catchError((error) => {
        console.error('Error updating insurance info:', error);
        return of({} as UpdateInsuranceInfoResponse);
      })
    );
  }

  private buildHttpParams(params: { [key: string]: any }): HttpParams {
    let httpParams = new HttpParams();
    Object.keys(params).forEach((key) => {
      if (params[key] !== null && params[key] !== undefined) {
        httpParams = httpParams.set(key, params[key].toString());
      }
    });
    return httpParams;
  }
}
