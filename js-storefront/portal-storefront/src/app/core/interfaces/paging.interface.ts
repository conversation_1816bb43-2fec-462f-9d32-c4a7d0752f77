export interface PagingInfo {
  totalItems: number;
  currentPage: number;
  numberOfPage: number;
}

export interface ResponsePaging {
  currentPage?: number;
  pageSize?: number;
  totalPages?: number;
  totalResults?: number;
}

export interface ResponsePagination extends ResponsePaging {
  count: number;
  hasNext: boolean;
  hasPrevious: boolean;
  page: number;
  totalCount: number;
  totalPages: number;
}
