import {
  PriorityTicket,
  StatusEngine,
  StatusTicket,
} from '../enums/tickets.enum';
import { OptionDropdown } from './dropdown.interface';
import { StatusTicketDetail } from '../enums/tickets.enum';
import { Assignee } from './tickets.interface';

export interface Location {
  address: string;
  dateTime: string;
}

export interface Ticket {
  id?: string;
  ticketID?: string;
  status?: {
    code?: StatusTicketDetail;
    name?: string;
  };
  priority?: {
    code?: PriorityTicket;
    name?: string;
  };
  assignee?: Assignee;
  isDrivable?: boolean;
  description?: string;
  type?: OptionDropdown;
  title?: string;
  verifiedDate?: string;
  verifiedBy?: string;
  plateNumber?: string;
  vin?: string;
  vehicleSubType?: string;
  vehicleType?: string;
  vehicleModel?: string;
  isVerified?: boolean;
  customerName?: string;
  mobileNumber?: string;
  authorization?: {
    method?: OptionDropdown;
    status?: OptionDropdown;
    verifiedInfo?: {
      displayName?: string;
      verifiedDate?: string;
    };
  };
  warningOccurredTimes?: string[];
}

export interface Vehicle {
  vehicleImage?: string;
  vehicleModel?: string;
  plateNumber?: string;
  odo?: number;
  engineStatus?: StatusEngine;
  fuelRemaining?: string;
  map?: string;
  currentAddress?: string;
  dateAndTime?: string;
  longitude?: string;
  latitude?: string;
  vehicleColor?: string;
  sim?: {
    code?: string;
    name?: string;
  };
  vin?: string;
  vehicleMake?: string;
  vehicleType?: string;
  vehicleSubType?: string;
  deviceType?: string;
  hasActiveCustomerRelation?: boolean;
  iotVehicleProperty?: {
    svtStatusDetail?: {
        svtStatus?: string,
        svtActivationDateTime?: string
    },
    maintenanceModeSetting?: {
      maintenanceMode?: string;
      maintenanceModeOnDate?: string;
    };
    remoteImmobilizerDetail?: {
      remoteImmobilizerStatus?: string;
      remoteImmobilizerDateTime?: string;
    };
  };
}

export interface DealerItem {
  addressDisplay?: string;
  displayName?: string;
  distanceToTargetPoint?: number;
  name?: string;
  phoneNumber?: string;
  defaultEmail?: string;
}

export interface DealerDetails {
  type?: string;
  items?: DealerItem[];
}

export interface DropdownDetails {
  type?: string;
  items?: OptionDropdown[];
}

export interface MetadataTowingDropdown {
  dealerOptions?: OptionDropdown[];
  dealers?: DealerItem[];
  statuses?: OptionDropdown[];
}

export interface PerferDealer {
  addressDisplay: string;
  defaultEmail: string;
  displayName: string;
  distanceToTargetPoint: number;
  name: string;
  phoneNumber: string;
}

export interface TowingServiceDetails {
  creationTime?: string;
  dealer?: {
    addressDisplay?: string;
    displayName?: string;
    name?: string;
    phoneNumber?: string;
  };
  distance?: number;
  id?: string;
  modifyTime?: string;
  personInChargeName?: string;
  personInChargePhone?: string;
  status?: {
    code: string;
    name: string;
  };
  towingCompany?: string;
  dealerSelection?: string;
  ticketId?: string;
}

export interface TicketDetail {
  ticket: Ticket;
  vehicle: Vehicle;
}

export interface requestCreateTowingService {
  id?: string;
  vin?: string;
  dealerCode?: string;
  towingCompany?: string;
  personInChargeName?: string;
  personInChargePhone?: string;
  eta?: string;
  status?: string;
  dealerSelection?: string;
  distance?: string;
}

export interface InsuranceDetails {
  type?: string;
  isActive?: boolean;
  policyCode?: string;
  providerCompany?: string;
  status?: string;
  startDate?: string;
  expiredDate?: string;
  isIncludedRoadsideAssistance?: boolean;
  roadsideAssistanceValue?: string;
}

export interface CallLog {
  id?: string;
  callTime: string;
  type: OptionDropdown;
  result: OptionDropdown;
  comment: string;
  isPlaceholder?: boolean;
}

export interface CallLogResponse {
  type: string;
  items: CallLog[];
}

export interface CallLogMetadata {
  types: OptionDropdown[];
  results: OptionDropdown[];
}

export interface RequestCallLog {
  id?: string;
  callTime: string;
  type: string;
  result: string;
  comment: string;
}

export interface ApprovalStatus {
  method?: {
    code: string;
    name: string;
  };
  status: {
    code: string;
    name: string;
  };
  type?: {
    code: string;
    name: string;
  };
  triggerDate?: string;
  validTill?: string;
  rejectedDate?: string;
}
