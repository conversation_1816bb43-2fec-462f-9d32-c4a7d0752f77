import { Injectable } from '@angular/core';
import { MatIconRegistry } from '@angular/material/icon';
import { DomSanitizer } from '@angular/platform-browser';
import { ICON_PATH, REGISTER_ICON } from '../constants';

@Injectable({
  providedIn: 'root',
})
export class RegisterIconsService {
  constructor(
    private matIconRegistry: MatIconRegistry,
    private domSanitizer: DomSanitizer
  ) {}

  public registerIcons(): void {
      REGISTER_ICON.forEach(icon => {
        this.matIconRegistry.addSvgIcon(
          icon.name,
          this.domSanitizer.bypassSecurityTrustResourceUrl(icon.path)
        );
      });
  }

  public registerIconWithPath(): void {
    ICON_PATH.forEach(icon => {
      this.matIconRegistry.addSvgIconLiteral(
        icon.name, 
        this.domSanitizer.bypassSecurityTrustHtml(icon.path)
      ); 
    })
  }
}
