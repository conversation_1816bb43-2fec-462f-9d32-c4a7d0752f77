export enum VehicleDetailsTab {
  Overview = 'Overview',
  Devices = 'Device & SIM',
  Tickets = 'Tickets',
  Warnings = 'Warnings',
  Emergencies = 'Emergencies',
  Subscriptions = 'Subscriptions',
  ServiceBooking = 'Service Booking',
  ServiceHistory = 'Service History',
  ServiceCampaigns = 'Service Campaigns',
  Trips = 'Trips',
  TroubleCode = 'Trouble Code'
}

export enum ResolutionEmergency {
  all = '',
  duplicateAlarm = 'DUPLICATE_ALARM',
  emergencyResolved = 'EMERGENCY_RESOLVED',
  falseAlarm = 'FALSE_ALARM',
  noResponseFromCustomer = 'NO_RESPONSE_FROM_CUSTOMER',
  other = 'OTHER',
}

export enum MaintenanceModeVehicle {
  On = 'ON',
  Off = 'OFF',
}

export enum RemoteImmobilizerStatus {
  Enable = 'Enabled',
  Disabled = 'Disabled',
  Disabling = 'Disabling in progress',
  Enabling = 'Enabling in progress'
}

export enum SvtStatus {
  Activated = 'Activated',
  Deactivated = 'Deactivated',
  Deactivating = 'Deactivating',
  Activating = 'Activating'
}
