import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class DataStoreService {
  // user data
  private _sortData: BehaviorSubject<{column: string, sort: boolean}> = new BehaviorSubject({column: '', sort: false});
  public readonly sortData: Observable<{column: string, sort: boolean}> = this._sortData.asObservable();

  private _isLoading: BehaviorSubject<boolean> = new BehaviorSubject(false);
  public readonly isLoading: Observable<boolean> = this._isLoading.asObservable();

  
  private _deviceType: BehaviorSubject<string> = new BehaviorSubject('');
  public readonly deviceType: Observable<string> = this._deviceType.asObservable();

  private _isEditNotify: BehaviorSubject<boolean> = new BehaviorSubject(false);
  public readonly isEditNotify: Observable<boolean> = this._isEditNotify.asObservable();

  constructor() { }

  setSortData(sortData: {column: string, sort: boolean}) {
    this._sortData.next(sortData);
  }

  setDeviceType(deviceType: string) {
    this._deviceType.next(deviceType);
  }

  setIsLoading(isLoading: boolean) {
    this._isLoading.next(isLoading);
  }

  setIsEditNotify(isLoading: boolean) {
    this._isEditNotify.next(isLoading);
  }
}