import { Injectable } from '@angular/core';
import { NativeDateAdapter } from '@angular/material/core';


@Injectable({
  providedIn: 'root',
})
export class CustomDateAdapter extends NativeDateAdapter {
  override getDayOfWeekNames(style: 'long' | 'short' | 'narrow'): string[] {
    return ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  }

  override format(date: Date, displayFormat: Object): string {
    if (displayFormat === 'month-year') {
      const month = this.getMonthNames('long')[date.getMonth()];
      const year = date.getFullYear();
      return `${month} ${year}`;
    }
    return super.format(date, displayFormat);
  }
}