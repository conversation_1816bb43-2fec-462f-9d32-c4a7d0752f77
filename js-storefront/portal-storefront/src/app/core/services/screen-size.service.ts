import { inject, Injectable } from '@angular/core';
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { SCREEN_SIZE } from '../constants';

@Injectable()
export class ScreenSizeService {
  breakpointObserver = inject(BreakpointObserver);

  getScreenSize(breakpoints: string[] | string): Observable<boolean> {
    return this.breakpointObserver
      .observe(breakpoints)
      .pipe(map((result) => (result.matches)));
  }

   // Method to check if current screen is mobile
  isMobile(): Observable<boolean> {
    return this.getScreenSize(`(max-width: ${SCREEN_SIZE.Mobile})`);
  }

  isSmallDesktop(): Observable<boolean> {
    return this.getScreenSize(`(max-width: ${SCREEN_SIZE.SmallDesktop})`);
  }
}
