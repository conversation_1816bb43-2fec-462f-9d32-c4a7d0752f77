import { Injectable } from '@angular/core';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { map, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class MediaQueryService {
  constructor(private breakpointObserver: BreakpointObserver) {}

  /**
   * Method to check if the current screen matches the provided media query.
   * @param query - A media query string (e.g., '(max-width: 768px)')
   * @returns Observable<boolean> - Emits true if the media query matches, false otherwise
   */
  isScreenMatch(query: string): Observable<boolean> {
    return this.breakpointObserver
      .observe([query])
      .pipe(map((state: BreakpointState) => state.matches));
  }
}
