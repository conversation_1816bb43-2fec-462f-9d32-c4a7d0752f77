import { inject, Injectable } from '@angular/core';
import {
  HttpErrorResponse,
  HttpEvent,
  HttpHandler,
  HttpInterceptor,
  HttpRequest,
} from '@angular/common/http';
import { catchError, Observable, throwError } from 'rxjs';
import { Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
@Injectable({
  providedIn: 'root',
})
export class AuthInterceptorService implements HttpInterceptor {
  router = inject(Router);
  dialog = inject(MatDialog);
  intercept(
    req: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    let authToken = '';
    let mfaToken = '';
    let mfaSessionId = '';
    if (typeof window !== 'undefined' && window.localStorage) {
      authToken = localStorage.getItem('token') || '';
      mfaToken = localStorage.getItem('mfaToken') || '';
      mfaSessionId = localStorage.getItem('mfaSessionId') || '';
    }
    let clonedRequest = req;
    if (authToken && mfaToken) {
      clonedRequest = req.clone({
        setHeaders: {
          Authorization: `Bearer ${authToken}`,
          mfaToken: mfaToken,
          mfaSessionId: mfaSessionId
        },
      });
    } else if (authToken) {
      clonedRequest = req.clone({
        setHeaders: {
          Authorization: `Bearer ${authToken}`,
        },
      });
    } else if (mfaToken) {
      clonedRequest = req.clone({
        setHeaders: {
          mfaToken: mfaToken,
        },
      });
    }
    return next.handle(clonedRequest).pipe(
      catchError((error: HttpErrorResponse) => {
        if (error.status === 401 || error.status === 403) {
          this.dialog.closeAll();
          this.router.navigate(['/login']);
        }
        return throwError(() => error);
      })
    );
  }
}
