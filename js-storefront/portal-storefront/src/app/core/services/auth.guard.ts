import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import {
  CanActivate,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  Router,
} from '@angular/router';
import { Observable, of } from 'rxjs';
import { isPlatformBrowser } from '@angular/common';
import { AuthService } from './auth.service';
import { LoadingService } from './loading.service'; // Import LoadingService
import { catchError, map, finalize } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class AuthGuard implements CanActivate {
  constructor(
    private router: Router,
    private authService: AuthService,
    private loadingService: LoadingService,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {}

  canActivate(
    next: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    if (isPlatformBrowser(this.platformId)) {
      this.loadingService.showLoader();
      const routeRoles = next.data['roles'] as Array<string>;
      // TODO check if user has required roles

      return this.authService.isAuthenticated().pipe(
        map((isAuthenticated: boolean) => {
          if (isAuthenticated) {
            return true;
          } else {
            console.warn(
              'AuthGuard: User not authenticated. Redirecting to login.'
            );
            this.router.navigate(['/login'], {
              queryParams: { returnUrl: state.url },
            });
            return false;
          }
        }),
        catchError((error) => {
          console.error('AuthGuard: Error during authentication check:', error);
          this.router.navigate(['/login'], {
            queryParams: { returnUrl: state.url },
          });
          return of(false);
        }),
        finalize(() => {
          this.loadingService.hideLoader();
        })
      );
    } else {
      console.warn(
        'AuthGuard: Not running in browser platform. Blocking navigation.'
      );
      return false;
    }
  }
}
