import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { OptionDropdown } from '../../interfaces';

@Injectable()

export class ReportService {
  constructor(
    private http: HttpClient
  ) { }

  getYearOp(): Observable<Array<number>> {
    const path = environment.OCC_BASE_URL + environment.OCC_PREFIX + 'portal/claim/action/transactions-report/years'
    return this.http.get<Array<number>>(path);
  }

  getSubscriptionStatusOp(): Observable<OptionDropdown[]> {
    const path = environment.OCC_BASE_URL + environment.OCC_PREFIX + 'iot-portal/report/dropdown-master/subscription_status'
    return this.http.get<OptionDropdown[]>(path);
  }

  getSubscriptionPaymentStatusOp(): Observable<OptionDropdown[]> {
    const path = environment.OCC_BASE_URL + environment.OCC_PREFIX + 'iot-portal/report/dropdown-master/subscription_payment_status'
    return this.http.get<OptionDropdown[]>(path);
  }

  getTicketTypeOp(): Observable<OptionDropdown[]> {
    const path = environment.OCC_BASE_URL + environment.OCC_PREFIX + 'iot-portal/report/dropdown-master/ticket_type'
    return this.http.get<OptionDropdown[]>(path);
  }

  getTicketPriorityOp(): Observable<OptionDropdown[]> {
    const path = environment.OCC_BASE_URL + environment.OCC_PREFIX + 'iot-portal/report/dropdown-master/ticket_priority'
    return this.http.get<OptionDropdown[]>(path);
  }

  getTicketStateOp(): Observable<OptionDropdown[]> {
    const path = environment.OCC_BASE_URL + environment.OCC_PREFIX + 'iot-portal/report/dropdown-master/ticket_state'
    return this.http.get<OptionDropdown[]>(path);
  }

  getWarningTypeOp(): Observable<OptionDropdown[]> {
    const path = environment.OCC_BASE_URL + environment.OCC_PREFIX + 'iot-portal/report/dropdown-master/warning_type'
    return this.http.get<OptionDropdown[]>(path);
  }

  getWarningPriorityOp(): Observable<OptionDropdown[]> {
    const path = environment.OCC_BASE_URL + environment.OCC_PREFIX + 'iot-portal/report/dropdown-master/warning_type'
    return this.http.get<OptionDropdown[]>(path);
  }

  getWarningStatusOp(): Observable<OptionDropdown[]> {
    const path = environment.OCC_BASE_URL + environment.OCC_PREFIX + 'iot-portal/report/dropdown-master/warning_status'
    return this.http.get<OptionDropdown[]>(path);
  }
  
  generateReportSubscription(body: any) {
    const path = environment.OCC_BASE_URL + environment.OCC_PREFIX + 'iot-portal/report/subscription'
    return this.http.get(path, { responseType: 'blob', params: body })
  }

  generateReportTicket(body: any) {
    const path = environment.OCC_BASE_URL + environment.OCC_PREFIX + 'iot-portal/report/ticket'
    return this.http.get(path, { responseType: 'blob', params: body })
  }

  generateReportWarning(body: any) {
    const path = environment.OCC_BASE_URL + environment.OCC_PREFIX + 'iot-portal/report/warning'
    return this.http.get(path, { responseType: 'blob', params: body })
  }

  generateReportTransaction(body: any) {
    const formData = new FormData();
    Object.keys(body).forEach((key) => {
      formData.append(key, body[key]);
    });
    const path = environment.OCC_BASE_URL + environment.OCC_PREFIX + 'portal/claim/action/transactions-report/export'
    return this.http.post(path, formData, { responseType: 'blob' })
  }

  getPromoTypeList(): Observable<OptionDropdown[]> {
    const path = environment.OCC_BASE_URL + environment.OCC_PREFIX + 'portal/claim/promotion-transactions/promotions/promotion-types'
    return this.http.get<OptionDropdown[]>(path);
  }
}
