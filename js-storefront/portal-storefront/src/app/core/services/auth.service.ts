import { inject, Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { BehaviorSubject, Observable, of, throwError } from 'rxjs';
import { catchError, map, finalize } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { Router } from '@angular/router';
import { LoadingService } from './loading.service';
import { ChangePasswordResponse } from '../interfaces/authen.interface';
import { ResponseCommon } from '../interfaces';
import { UserService } from './user';
import { v4 as uuidv4 } from 'uuid';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private tokenUrl = `${environment.OCC_BASE_URL}authorizationserver/oauth/token`;
  private clientId = 'angularapp2';

  prefixEnv = `${environment.OCC_BASE_URL}${environment.OCC_PREFIX}`;

  errorGetOTP = new BehaviorSubject<string>('');

  constructor(
    private http: HttpClient,
    private router: Router,
    private loadingService: LoadingService,
    private userService: UserService
  ) {}

  getToken(username: string, password: string): Observable<any> {
    const params = new HttpParams()
      .set('client_id', this.clientId)
      .set('username', username)
      .set('password', password)
      .set('grant_type', 'password');

    const headers = new HttpHeaders({
      'Content-Type': 'application/x-www-form-urlencoded',
    });

    this.loadingService.showLoader();

    return this.http.post(this.tokenUrl, null, { headers, params }).pipe(
      finalize(() => {
        this.loadingService.hideLoader();
      })
    );
  }

  isAuthenticated(): Observable<boolean> {
    const token = localStorage.getItem('token');
    const expiresAt = localStorage.getItem('expires_at');
    if (!token || !expiresAt) {
      return of(false);
    }

    const isExpired = Date.now() > parseInt(expiresAt, 10);
    return of(!isExpired);
  }

  logout(): void {
    this.revokeLogin().subscribe((response) => {
      if (response?.code === '200') {
        localStorage.removeItem('token');
        localStorage.removeItem('expires_at');
        localStorage.removeItem('mfaToken');
        localStorage.setItem('resetPasswordStep', '1');
        localStorage.setItem('successSendMail', 'false');
        localStorage.setItem('successResetPass', 'false');
        this.userService.clearRoles();
        this.router.navigateByUrl('/login');
      }
    });
  }

  revokeLogin(): Observable<ResponseCommon> {
    const path = `${this.prefixEnv}portal/user/logout`;

    return this.http.post<ResponseCommon>(path, null).pipe(
      catchError((error) => {
        return throwError(() => new Error('Failed to revoke login'));
      })
    );
  }

  getMFA(): Observable<any> {
    this.loadingService.showLoader();

    return this.http.get<any>(`${this.prefixEnv}portal/mfa/authorize`).pipe(
      catchError(() => {
        return of(false);
      }),
      finalize(() => {
        this.loadingService.hideLoader();
      })
    );
  }

  getOTP(): Observable<any> {
    this.loadingService.showLoader();

    return this.http.get<any>(`${this.prefixEnv}portal/mfa/otp/generate`).pipe(
      finalize(() => {
        this.loadingService.hideLoader();
      })
    );
  }

  executeGetOtp(notification: any): void {
    this.getOTP().subscribe(
      (response) => {
        notification.showSuccess(
          response?.message || 'OTP generated successfully.'
        );
      },
      () => {
        notification.showError('Failed to generate OTP.');
      }
    );
  }

  otpVerification(otp: string, notification: any): Observable<any> {
    const uuid = uuidv4();
    const params = new HttpParams().set('otp', otp).set("mSessionId", uuid);
    this.loadingService.showLoader();

    return this.http
      .post<any>(`${this.prefixEnv}portal/mfa/otp/verify`, null, { params })
      .pipe(
        map((response) => {
          if (response?.mfaToken) {
            localStorage.setItem('mfaToken', response.mfaToken);
            localStorage.setItem('mfaSessionId', uuid);
            notification.showSuccess('OTP verified successfully.');
          }
          return response;
        }),
        catchError(() => {
          notification.showError('Failed to verify OTP. Please try again.');
          return throwError(() => new Error('Failed to verify OTP'));
        }),
        finalize(() => {
          this.loadingService.hideLoader();
        })
      );
  }

  changePassword(
    currentPassword: string,
    newPassword: string
  ): Observable<ChangePasswordResponse> {
    const url = `${this.prefixEnv}portal/user/password-change`;

    const body = {
      currentPassword: currentPassword,
      newPassword: newPassword,
    };

    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
    });

    this.loadingService.showLoader();

    return this.http.post<ChangePasswordResponse>(url, body, { headers }).pipe(
      map((response) => {
        if (response.code === '200') {
          return response;
        }
        throw response;
      }),
      catchError((error) => {
        return throwError(() => error || 'Password change failed.');
      }),
      finalize(() => {
        this.loadingService.hideLoader();
      })
    );
  }

  validateForgetPassword(
    username: string,
    email: string
  ): Observable<ResponseCommon> {
    const path = `${this.prefixEnv}portal/user/forget-password/validate`;
    const body = { username, email };

    this.loadingService.showLoader();

    return this.http.post<ResponseCommon>(path, body).pipe(
      finalize(() => this.loadingService.hideLoader()) // Cleaner handling of loader
    );
  }

  forgetPassword(username: string, email: string): Observable<ResponseCommon> {
    const path = `${this.prefixEnv}portal/user/forget-password`;
    const body = { username, email };

    this.loadingService.showLoader();

    return this.http.post<ResponseCommon>(path, body).pipe(
      map((response) => {
        if (response?.code === '422') {
          throw new Error(response.message);
        }
        return response;
      }),
      finalize(() => this.loadingService.hideLoader())
    );
  }

  resetPassword(token: string): Observable<any> {
    const path = `${this.prefixEnv}portal/user/reset-password`;
    const params = new HttpParams().set('token', token);

    this.loadingService.showLoader();

    return this.http
      .get<any>(path, { params })
      .pipe(finalize(() => this.loadingService.hideLoader()));
  }

  validatePasswordSetup(
    pwd: string,
    checkPwd: string,
    token: string
  ): Observable<ResponseCommon> {
    const path = `${this.prefixEnv}portal/user/password-setup/validate`;
    const body = { pwd, checkPwd, token };

    this.loadingService.showLoader();

    return this.http
      .post<ResponseCommon>(path, body)
      .pipe(finalize(() => this.loadingService.hideLoader()));
  }

  setupPassword(
    pwd: string,
    checkPwd: string,
    token: string
  ): Observable<ResponseCommon> {
    const path = `${this.prefixEnv}portal/user/password-setup`;
    const body = { pwd, checkPwd, token };

    this.loadingService.showLoader();

    return this.http
      .post<ResponseCommon>(path, body)
      .pipe(finalize(() => this.loadingService.hideLoader()));
  }
}
