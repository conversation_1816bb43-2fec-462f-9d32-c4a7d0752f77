import { inject, Injectable } from '@angular/core';
import { MatSnackBar, MatSnackBarRef } from '@angular/material/snack-bar';
import { NotificationContentComponent } from '../shared/notifications/notification-content.component';
interface NotificationOptions {
    icon?: string;
    panelClass?: string | string[];
    duration?: number;
    [key: string]: any;
  }
  
@Injectable()
    
export class NotificationService {
  snackBar = inject(MatSnackBar);

  persistentNotification!: MatSnackBarRef<any>;

  showPersistentNotification(
    message: string,
    type: 'success' | 'error' | 'warning' | 'info'
  ): void {
    const icon = this.getIcon(type);
    const panelClass = `${type}-snackbar`;

    // Dismiss any previous persistent notification
    if (this.persistentNotification) {
      this.persistentNotification.dismiss();
    }

    this.persistentNotification = this.snackBar.openFromComponent(
      NotificationContentComponent,
      {
        data: { message, icon },
        panelClass: [panelClass],
        horizontalPosition: 'right',
        verticalPosition: 'top',
        duration: 0, // Persistent: Doesn't dismiss automatically
      }
    );
  }

  showNotification(
    message: string,
    type: 'success' | 'error' | 'warning' | 'info',
    duration: number = 5000,
    options?: NotificationOptions
  ) {
    const panelClass = options?.panelClass || `${type}-snackbar`;
    const icon = options?.icon || this.getIcon(type);

    this.snackBar.openFromComponent(NotificationContentComponent, {
      data: { message, icon },
      duration,
      horizontalPosition: 'right',
      verticalPosition: 'top',
      panelClass: Array.isArray(panelClass) ? panelClass : [panelClass],
    });
  }

  showSuccess(message: string, duration: number = 5000,  options?: NotificationOptions) {
    this.showNotification(message, 'success', duration, options);
  }

  showError(message: string, duration: number = 5000, options?: NotificationOptions) {
    this.showNotification(message, 'error', duration, options);
  }

  showWarning(message: string, duration: number = 5000, options?: NotificationOptions) {
    this.showNotification(message, 'warning', duration, options);
  }

  showInfo(message: string, duration: number = 5000, options?: NotificationOptions) {
    this.showNotification(message, 'info', duration, options);
  }

  private getIcon(type: 'success' | 'error' | 'warning' | 'info'): string {
    switch (type) {
      case 'success':
        return 'ic-check-circle';
      case 'error':
        return 'error';
      case 'warning':
        return 'warning';
      case 'info':
        return 'info';
      default:
        return '';
    }
  }
}
