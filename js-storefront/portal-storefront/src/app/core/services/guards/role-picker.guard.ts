import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import {
  CanActivate,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  Router,
} from '@angular/router';
import { Observable } from 'rxjs';
import { isPlatformBrowser } from '@angular/common';
import { UserService } from '../user';

@Injectable({
  providedIn: 'root',
})
export class RolePickerGuard implements CanActivate {
  constructor(
    private router: Router,
    private userService: UserService,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | boolean {
    if (!isPlatformBrowser(this.platformId)) {
      console.warn(
        'AuthGuard: Not running in browser platform. Blocking navigation.'
      );
      return false;
    }

    const userInfo = this.userService.loadRolesFromStorage();
    const userRoles = this.userService.checkUserRoles(userInfo?.roles || []);
    const isUserHasMultipleRoles = this.userService.checkUserMultipleRoles(userRoles);
    if (isUserHasMultipleRoles) {
      return true;
    } else {
      this.router.navigate(["/login"]);
      return false;
    }
  }
}
