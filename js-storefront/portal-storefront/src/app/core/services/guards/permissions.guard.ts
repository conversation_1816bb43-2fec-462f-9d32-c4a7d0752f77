import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import {
  CanActivate,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  Router,
} from '@angular/router';
import { EMPTY, Observable, of } from 'rxjs';
import { isPlatformBrowser } from '@angular/common';
import { UserService } from '../user';
import { NEW_WARNING_ROUTER, PERMISSIONS_CODE } from '../../constants';
import { switchMap, catchError, map, take } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class PermissionsGuard implements CanActivate {
  constructor(
    private router: Router,
    private userService: UserService,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | boolean {
    if (!isPlatformBrowser(this.platformId)) {
      console.warn(
        'AuthGuard: Not running in browser platform. Blocking navigation.'
      );
      return false;
    }

    return this.userService.userRoles$.pipe(
      take(1),
      switchMap((cachedUserInfo) => {
        if (cachedUserInfo) {
          return of(cachedUserInfo);
        }
        return this.userService.getUserRoles().pipe(
          catchError(() => {
            this.router.navigate(['/login'], {
              queryParams: { returnUrl: state?.url },
            });
            return EMPTY;
          })
        );
      }),
      map((userInfo) => {
        const { permissions = [] } = route.data || {};
        const id = route.params['id'];
        const isEdit = route.queryParams['edit'] === 'true';

        let permission = permissions;

        if (id === NEW_WARNING_ROUTER) {
          permission = [PERMISSIONS_CODE.IOT_WARNING_MASTER_CREATE];
        } else if (isEdit && route?.routeConfig?.path === 'warning-master') {
          permission = [PERMISSIONS_CODE.IOT_WARNING_MASTER_EDIT];
        }

        // DTENGROUP doesn't have permission to view the dashboard
        if (
          route?.routeConfig?.path === 'dashboard' &&
          this.userService.isHasPermission([PERMISSIONS_CODE.DTENGROUP]) &&
          !this.userService.isHasPermission([PERMISSIONS_CODE.IOT_EMPLOYEE_SUPER_ADMIN])
        ) {
          this.router.navigate(['/devices']);
          return false; 
        }

        const hasPermission = this.userService.isHasPermission(permission);

        if (!hasPermission) {
          this.router.navigate(['/dashboard']);
        }

        return hasPermission;
      }),
      catchError(() => {
        this.router.navigate(['/dashboard']);
        return of(false);
      })
    );
  }
}
