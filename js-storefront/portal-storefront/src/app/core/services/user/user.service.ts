import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { BehaviorSubject, map, Observable, tap } from 'rxjs';
import * as _ from 'lodash-es';
import { environment } from '../../../../environments/environment';
import { UserInfo } from '../../interfaces';
import { CLAIM_PORTAL_PREFIX, CLAIM_ROLES, IBP_PORTAL_PREFIX, IBP_ROLES, INS_PORTAL_PREFIX, INS_ROLES, IOT_PORTAL_PREFIX, IOT_ROLES, ROLES } from '../../constants/roles.const';
import { Router } from '@angular/router';
import { PERMISSIONS_CODE } from '../../constants';

@Injectable({
  providedIn: 'root',
})
export class UserService {
  #http = inject(HttpClient);
  router = inject(Router);
  prefixEnv = environment.OCC_BASE_URL + environment.OCC_PREFIX;

  userRoles$ = new BehaviorSubject<UserInfo>(null);
  userInfo: {
    fullName: string;
    username: string;
  }
  allPermission: string[];

  getUserRoles(): Observable<UserInfo> {
    const path = `${this.prefixEnv}portal/user/roles`;
    return this.#http.get<UserInfo>(path).pipe(
      tap((userInfo) => {
        this.userRoles$.next(userInfo);
        this.allPermission = userInfo?.roles;
        this.userInfo = {
          fullName: userInfo?.fullName,
          username: userInfo?.username
        }
      })
    );
  }

  isHasPermission(permissions: string[]): boolean {
    return permissions?.some((item) => this.allPermission?.includes(item));
  }

  setRoles(roles: UserInfo) {
    this.userRoles$.next(roles);
    localStorage.setItem('roles', JSON.stringify(roles));
  }

  getUserRoles$(): Observable<UserInfo> {
    return this.userRoles$.asObservable();
  }

  loadRolesFromStorage() {
    const savedRoles = localStorage.getItem('roles');
    if (savedRoles) {
      const roles = JSON.parse(savedRoles)
      this.userRoles$.next(roles);
      this.allPermission = roles?.roles;
      return roles;
    }
    return null;
  }

  clearRoles() {
    this.userRoles$.next(null);
    localStorage.removeItem('roles');
  }

  getRedirectUrl(roles: string[], returnUrl?: string) {
    if (returnUrl) {
      return returnUrl;
    } else if (roles.some((role) => INS_ROLES.includes(role))) { // Insurance portal
      return '/insurance';
    } else if (roles.some((role) => CLAIM_ROLES.includes(role))) { // Claim portal
      return '/claims';
    } else { // IOT portal
      // DTENGROUP doesn't have permission to view the dashboard
      const url = this.isHasPermission([PERMISSIONS_CODE.DTENGROUP]) && !this.isHasPermission([PERMISSIONS_CODE.IOT_EMPLOYEE_SUPER_ADMIN]) 
              ? '/devices': '/dashboard'
      return url;
    }
  }

  getUserTitles(): Observable<any> {
    const path = `${this.prefixEnv}util/user/titles`;
    return this.#http.get(path);
  }

  getGroupRole(currentRoles: string[]): string {
    if (currentRoles.includes(ROLES.INSURANCEADMINGROUP)) {
      return ROLES.INSURANCEADMINGROUP;
    }
    if (currentRoles.includes(ROLES.COORDINATORGROUP)) {
      return ROLES.COORDINATORGROUP;
    }
    if (currentRoles.includes(ROLES.INSURANCEPARTNERGROUP)) {
      return ROLES.INSURANCEPARTNERGROUP;
    }
    if (currentRoles.includes(ROLES.INSURANCEDEPARTMENTGROUP)) {
      return ROLES.INSURANCEDEPARTMENTGROUP;
    } 
    if (currentRoles.includes(ROLES.CASHIERGROUP)) {
      return ROLES.CASHIERGROUP;
    }
    if (currentRoles.includes(ROLES.MARKETINGGROUP)) {
      return ROLES.MARKETINGGROUP;
    }
    if (currentRoles.includes(ROLES.TREASURYGROUP)) {
      return ROLES.TREASURYGROUP;
    }
    if (currentRoles.includes(ROLES.CRDEMPLOYEEGROUP)) {
      return ROLES.CRDEMPLOYEEGROUP;
    }
    return '';
  }

  getPortalPrefixByRole(): string {
    const currentRoles = this.userRoles$.value?.roles;
    if (_.intersection(currentRoles, IOT_ROLES).length > 0) {
      return IOT_PORTAL_PREFIX;
    }
    if (_.intersection(currentRoles, INS_ROLES).length > 0) {
      return INS_PORTAL_PREFIX;
    }
    if (_.intersection(currentRoles, CLAIM_ROLES).length > 0) {
      return CLAIM_PORTAL_PREFIX;
    }
    if (_.intersection(currentRoles, IBP_ROLES).length > 0) {
      return IBP_PORTAL_PREFIX;
    }
    return '';
  }

  checkUserRoles(roles: string[]) {
    const rolesIOT = new Set(IOT_ROLES);
    const rolesInsurance = new Set(INS_ROLES);
    const rolesClaims = new Set(CLAIM_ROLES);
    const userRoles = {
      iot: false,
      insurance: false,
      claims: false
    };
    
    roles.forEach((role) => {
      if (rolesIOT.has(role)) {
        userRoles.iot = true;
      }
      if (rolesInsurance.has(role)) {
        userRoles.insurance = true;
      }
      if (rolesClaims.has(role)) {
        userRoles.claims = true;
      }
    });
    return userRoles;
  }

  checkUserMultipleRoles({iot, insurance, claims}: {iot: boolean, insurance: boolean, claims: boolean}) {
    return (iot && insurance) || (iot && claims) || (insurance && claims);
  }
}
