import { DecimalPipe } from '@angular/common';
import { Directive, ElementRef, HostListener, Input, Optional, Self } from '@angular/core';
import { NgControl } from '@angular/forms';
@Directive({
  selector: '[appInputNumber]',
  standalone: true,
})
export class InputNumberDirective {
  @Input() allowedPattern = null;
  @Input() min: number;
  @Input() max: number;

  constructor(private el: ElementRef, @Optional() @Self() private ngControl: NgControl) {}

  @HostListener('keydown', ['$event']) onKeyPress(event: any) {
    this.integerOnly(event);
  }

  @HostListener('paste', ['$event']) blockPaste(e: KeyboardEvent) {
    e.preventDefault();
  }

  @HostListener('drop', ['$event']) blockDrop(e: KeyboardEvent) {
    e.preventDefault();
  }

  integerOnly(event: any) {
    const e = event;
    if (e.key === 'Tab' || e.key === 'TAB') {
      return;
    }
    if (
      [46, 8, 9, 27, 13, 110].indexOf(e.keyCode) !== -1 ||
      // Allow: Ctrl+A
      (e.keyCode === 65 && e.ctrlKey === true) ||
      // Allow: Ctrl+C
      (e.keyCode === 67 && e.ctrlKey === true) ||
      // Allow: Ctrl+V
      (e.keyCode === 86 && e.ctrlKey === true) ||
      // Allow: Ctrl+X
      (e.keyCode === 88 && e.ctrlKey === true)
    ) {
      // let it happen, don't do anything
      return;
    }
    if (
      ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'].indexOf(e.key) === -1
    ) {
      e.preventDefault();
    }
  }

  @HostListener('blur', ['$event']) onInput(event: any) {
    const inputValue = this.el.nativeElement.value;
    const numericValue = parseInt(inputValue, 10);

    if (this.min !== null && numericValue < this.min) {
      this.updateControlValue(this.min);
    } else if (this.max !== null && numericValue > this.max) {
      this.updateControlValue(this.max);
    }
  }

  updateControlValue(value: number) {
    if (this.ngControl && this.ngControl.control) {
      this.ngControl.control.setValue(value);
    }
  }
}
