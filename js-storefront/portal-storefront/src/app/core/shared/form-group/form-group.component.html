<div class="form-group">
    <label [for]="controlId" class="form-group__label">
        {{ label }}
        <ng-container *ngIf="required">
            <span class="required">*</span>
        </ng-container>
    </label>
    <div class="form-group__input" [ngClass]="{ 'form-group__icon': icon }">
        @if (isTextArea) {
            <textarea [formControl]="control" [placeholder]="placeholder" [id]="controlId" class="form-textarea"
            (keyup.enter)="onEnter.emit()" (input)="onInput($event)"[attr.disabled]="isReadOnly ? true : null"></textarea>
        } @else {
            @if (noLitmitCharacter) {
                <input [type]="type" [formControl]="control" [placeholder]="placeholder" [id]="controlId" class="form-control"
                (keyup.enter)="onEnter.emit()" [attr.disabled]="isReadOnly ? true : null"/>
            }
            @else if(hasRange) {
              <input [type]="type" [formControl]="control" [placeholder]="placeholder" [id]="controlId" class="form-control"
                     [attr.maxlength]="maxLength"
                     appInputNumber
                     [min]="min"
                     [max]="max"
                     (keyup.enter)="onEnter.emit()" (input)="onInput($event)" (keypress)="onKeypress.emit($event)" [attr.disabled]="isReadOnly ? true : null"/>
            }
            @else {
              @if (onlyNumber) {
                <input appInputNumber [onlyNumber]="onlyNumber" [type]="type" [formControl]="control" [placeholder]="placeholder" [id]="controlId" class="form-control"
                       [attr.maxlength]="maxLength" (keyup.enter)="onEnter.emit()" (input)="onInput($event)" (keypress)="onKeypress.emit($event)" [attr.disabled]="isReadOnly ? true : null"/>
              } @else if (applyCurrencyFormatter){
                <input [type]="applyCurrencyFormatter ? 'text' : type" [formControl]="control" [placeholder]="placeholder" [id]="controlId" class="form-control"
                       appCurrencyInputFormatter
                       [enabled]="applyCurrencyFormatter"
                       [currencyCode]="currencyCode"
                       [locale]="locale"
                       [attr.maxlength]="maxLength" (keyup.enter)="onEnter.emit()" (input)="onInput($event)" (keypress)="onKeypress.emit($event)" [attr.disabled]="isReadOnly ? true : null"/>

              }@else {
                <input [type]="type" [formControl]="control" [placeholder]="placeholder" [id]="controlId" class="form-control"
                [attr.maxlength]="maxLength" (keyup.enter)="onEnter.emit()" (input)="onInput($event)" 
                (keypress)="onKeypress.emit($event)"
                (paste)="removeSpaceOnPaste($event)"
                [attr.disabled]="isReadOnly ? true : null"/>
              }
            }
            <ng-content select="[suffix]"></ng-content>
            <mat-icon *ngIf="icon" [svgIcon]="icon"></mat-icon>
        }
    </div>
    <div *ngIf="control.hasError('required') && control.touched" class="form-group__error">
        {{ errorMessage }}
    </div>
    <ng-container *ngIf="!control.hasError('required') && control.touched">
        <div *ngFor="let key of control.errors | keyvalue">
            <div *ngIf="control.hasError(key.key)"  class="form-group__error">
                {{ otherErrorMessages?.[key.key] }}
            </div>
        </div>
    </ng-container>
</div>
