import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormControl, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { InputNumberDirective } from '../../directives/input-interger.directive';
import { InputTermCalculateDirective } from '../../directives/input-term-calculate.directive';
import { CurrencyInputFormatterDirective } from '../../directives/currency-input-formatter.directive';

@Component({
  selector: 'app-form-group',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    InputNumberDirective,
    InputTermCalculateDirective,
    CurrencyInputFormatterDirective,
  ],
  templateUrl: './form-group.component.html',
})
export class FormGroupComponent {
  @Input() isTextArea: boolean = false;
  @Input() noLitmitCharacter: boolean = false;
  @Input() label: string = '';
  @Input() control!: FormControl;
  @Input() placeholder: string = '';
  @Input() type: string = 'text';
  @Input() controlId: string = '';
  @Input() required: boolean = false;
  @Input() maxLength: number = 128;
  @Input() errorMessage: string = 'Please fill in data';
  @Input() otherErrorMessages?: { [key: string]: string } = {};
  @Input() icon: string = '';
  @Input() isReadOnly: boolean = false;
  @Input() hasRange: boolean = false;
  @Input() min: number;
  @Input() max: number;
  @Input() preventSpace = false;
  @Input() onlyNumber: boolean = false;
  @Input() useCurrencyFormat: boolean = false;
  @Input() currencyCode: string = 'PHP';
  @Input() locale: string = 'en-PH';

  @Output() onEnter = new EventEmitter();
  @Output() onKeypress = new EventEmitter();

  ngOnInit() {
    if (this.maxLength && !this.noLitmitCharacter) {
      this.control.addValidators(Validators.maxLength(this.maxLength));
    }
    if (this.type === 'email') {
      this.control.addValidators(Validators.email);
    }
    if (this.type === 'tel') {
      this.control.addValidators(
        Validators.pattern(
          '^(\\+\\d{1,3}( )?)?((\\(\\d{1,3}\\))|\\d{1,3})[- .]?\\d{3,4}[- .]?\\d{4}$'
        )
      );
    }
  }

  onInput(event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    if (inputElement.value.length > this.maxLength) {
      const trimmedValue = inputElement.value.slice(0, this.maxLength);
      inputElement.value = trimmedValue;
      this.control.setValue(trimmedValue, { emitEvent: false });
    }
  }

  get applyCurrencyFormatter(): boolean {
    return this.useCurrencyFormat && !this.isTextArea && !this.onlyNumber;
  }

  removeSpaceOnPaste(event: ClipboardEvent) {
    if (this.preventSpace) {
      // Prevent the default paste behavior
      event.preventDefault();

      // Get the pasted text from clipboard
      const pastedText = event.clipboardData?.getData('text') ?? '';

      // Remove all whitespace characters from the pasted text
      const cleanText = pastedText.replace(/\s+/g, '');

      // Get the input element where the paste event occurred
      const input = event.target as HTMLInputElement;

      // Get the current value and selection range of the input
      const currentValue = input.value;
      const start = input.selectionStart || 0;
      const end = input.selectionEnd || 0;

      // Insert the cleaned text at the cursor position, replacing any selected text
      const newValue =
        currentValue.slice(0, start) + cleanText + currentValue.slice(end);
      input.value = newValue;
      this.control.setValue(newValue, { emitEvent: false });

      // Move the cursor to the end of the inserted text
      const caretPos = start + cleanText.length;
      setTimeout(() => input.setSelectionRange(caretPos, caretPos), 0);
    }
  }
}
