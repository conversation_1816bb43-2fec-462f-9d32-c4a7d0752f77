<div class="widgets-summary">
  @for (item of widgets; track $index) {
  <div
    [class.noAction]="noAction"
    class="widgets-summary__item widgets-summary__item--active"
    (click)="onChangeTab(item)"
  >
    <div class="widgets-summary__item__status">
      <span>{{ item?.count }}</span>
      <span>{{ item?.description | translate}}</span>
    </div>
    <div class="widgets-summary__item__icon">
      <mat-icon [svgIcon]="item.icon"></mat-icon>
      <div *ngIf="!noAction">
        <span>View</span>
        <mat-icon svgIcon="ic-arrow-next" class="small-icon"></mat-icon>
      </div>
    </div>
  </div>
  }
</div>
