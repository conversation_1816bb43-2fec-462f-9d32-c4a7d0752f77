<div class="breadcrumb-header">
    <div *ngIf="breadcrumbs?.length > 0" class="breadcrumb-header__breadcrumbs">
        <ng-container *ngFor="let item of breadcrumbs; let i = index">
            <span class="breadcrumb-header__breadcrumbs-item" (click)="onBreadcrumbClick(i)">
                {{ item | translate }}
            </span>
            <mat-icon *ngIf="i !== breadcrumbs.length - 1" aria-hidden="true" class="small-icon"
                svgIcon="ic-right"></mat-icon>
        </ng-container>
    </div>
    <div *ngIf="label" class="breadcrumb-header__id">
        <img *ngIf="prefixTitle" [src]="prefixTitle" />
        <h2 class="breadcrumb-header__id-text">{{ label }}</h2>
    </div>
</div>