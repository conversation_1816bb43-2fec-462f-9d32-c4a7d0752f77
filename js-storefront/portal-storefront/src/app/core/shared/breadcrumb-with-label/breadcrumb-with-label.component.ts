import { Component, EventEmitter, inject, input, Input, Output, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconModule } from '../../icon/icon.module';
import { TranslateModule } from '@ngx-translate/core';
import { Router } from '@angular/router';
import { environment } from '../../../../environments/environment';

@Component({
  selector: 'app-breadcrumb-with-label',
  standalone: true,
  imports: [CommonModule, IconModule, TranslateModule],
  templateUrl: './breadcrumb-with-label.component.html',
})
export class BreadcrumbWithLabelComponent {
  private _breadcrumbs = signal<string[]>([]);
  private _label = signal<string>('');
  @Input() breadcrumbMulLink?: boolean = false;
  @Input() value: string[] = [];
  @Input() breadcrumbLinks: string[] = [];
  @Input()
  set breadcrumbs(value: string[]) {
    this._breadcrumbs.set(value || []);
  }
  @Output() breadcrumbClick = new EventEmitter<number>();

  get breadcrumbs(): string[] {
    return this._breadcrumbs();
  }

  @Input()
  set label(value: string) {
    this._label.set(value || '');
  }
  get label(): string {
    return this._label();
  }

  @Input() set iconTitle(value: string) {
    if (value) {
      this.prefixTitle = environment.OCC_BASE_URL?.slice(0, -1) + value;
    }
  }

  prefixTitle: string;

  router = inject(Router);

  getQueryParamsFromUrl(url: string): { queryParams: { [key: string]: string}, baseUrl: string } {
    const queryParams: { [key: string]: string } = {};
    const urlParts = url.split('?');
    const baseUrl = urlParts[0];
    const queryString = urlParts[1];
    if (!queryString) {
      return { queryParams, baseUrl };
    }

    queryString.split('&').forEach(param => {
      const [key, value] = param.split('=');
      if (key) {
        queryParams[key] = value;
      }
    });

    return { queryParams, baseUrl };
  }

  onBreadcrumbClick(i: number): void {
    if (!this.breadcrumbMulLink) {
      const urlObject = this.getQueryParamsFromUrl(this.breadcrumbLinks[i]);
      this.router.navigate([urlObject.baseUrl], {
        queryParams: { ...urlObject.queryParams },
        queryParamsHandling: 'merge',
      });
    } else {
      this.breadcrumbClick.emit(i);
    }
  }
}
