:host {
  width: 100%;
  
  .input-time {
    display: flex;
    align-items: flex-end;
    gap: 20px;
    align-items: flex-end;

    .form-group {
      margin-bottom: 0px;
    }
  }

  .item-hours {
    display: flex;
    position: relative;
    align-items: flex-end;

    mat-icon {
      position: absolute;
      right: 0px;
      bottom: 7px;
    }

    &__separator {
      padding-bottom: 7px;
      border-bottom: 1px solid #cccccc;
      transition: border-color 0.3s;
    }

    &__time-hh {
      max-width: 25px;
    }

    &__time-mm {
      padding-left: 5px;
    }

    &:has(.form-control:focus) {
      .form-control {
        border-bottom: 1px solid #000 !important;
      }

      .item-hours__separator {
        border-bottom: 1px solid #000 !important;
      }
    }
  }

  .item-date {
    .form-group__input {
      position: relative;

      mat-icon {
        position: absolute;
        right: 0;
        bottom: 7px;
        cursor: pointer;
      }

      .form-control {
        cursor: pointer;
      }
    }
  }
}
