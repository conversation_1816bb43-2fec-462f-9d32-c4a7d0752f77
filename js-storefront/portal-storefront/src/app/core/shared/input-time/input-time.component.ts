import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IconModule } from '../../icon/icon.module';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { InputNumberDirective } from '../../directives/input-interger.directive';

@Component({
  selector: 'app-input-time',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    IconModule,
    ReactiveFormsModule,
    InputNumberDirective,
  ],
  templateUrl: './input-time.component.html',
  styleUrl: './input-time.component.scss',
})
export class InputTimeComponent {
  @Input() controlHH: FormControl;
  @Input() controlMM: FormControl;
  @Input() required = false;
  @Input() label: string;
  @Input() errorMessage: string;
  @Input() otherErrorMessages?: { [key: string]: string } = {};
  @Input() isShowError = true;

  onInputTimeHH(event) {
    const value = event.target.value;
    if (value.length === 3) {
      value.substr(value.length - 2);
    }
    if (value.length === 1 && +value >= 0) {
      this.controlHH?.patchValue('0' + value);
      return;
    }
    if (value >= 24) {
      this.controlHH?.patchValue('0' + value.substr(value.length - 1));
      return;
    }
    if (value.length === 3 && +value.substr(value.length - 2) >= 24) {
      this.controlHH?.patchValue('0' + value.substr(value.length - 1));
      return;
    }
    if (value.length === 3 && +value.substr(value.length - 2) < 24) {
      this.controlHH?.patchValue(value.substr(value.length - 2));
      return;
    }
  }

  onInputTimeMM(event) {
    const value = event.target.value;
    if (value.length === 3) {
      value.substr(value.length - 2);
    }
    if (value.length === 1 && +value >= 0) {
      this.controlMM?.patchValue('0' + value);
      return;
    }
    if (value >= 60) {
      this.controlMM?.patchValue('0' + value.substr(value.length - 1));
      return;
    }
    if (value.length === 3 && +value.substr(value.length - 2) >= 60) {
      this.controlMM?.patchValue('0' + value.substr(value.length - 1));
      return;
    }
    if (value.length === 3 && +value.substr(value.length - 2) < 60) {
      this.controlMM?.patchValue(value.substr(value.length - 2));
      return;
    }
  }
}
