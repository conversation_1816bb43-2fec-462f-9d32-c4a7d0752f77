<div class="form-group" [class.chip-list]="control?.enabled">
  <label class="form-group__label"
    >{{ label | translate }}
    <span *ngIf="required" class="required">*</span></label
  >
  <mat-chip-grid #chipGrid class="custom-chip-grid" [formControl]="control">
    @for (item of option; track item; let index = $index) {
    <mat-chip-row (removed)="remove(index)">
      {{ item }}
      <button *ngIf="control?.enabled" matChipRemove>
        <mat-icon>cancel</mat-icon>
      </button>
    </mat-chip-row>
    }

    <input class="hide-input" [matChipInputFor]="chipGrid" />
    <div class="form-group__input" *ngIf="control?.enabled && limitLength > 0">
      <input
        class="form-control"
        [matChipInputFor]="chipGrid"
        [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
        [matChipInputAddOnBlur]="addOnBlur"
        [maxLength]="limitLength"
        (matChipInputTokenEnd)="add($event)"
      />
    </div>

    <div *ngIf="control?.invalid && control.touched" class="form-group__error">
      {{ errorMessage }}
    </div>
  </mat-chip-grid>
</div>
