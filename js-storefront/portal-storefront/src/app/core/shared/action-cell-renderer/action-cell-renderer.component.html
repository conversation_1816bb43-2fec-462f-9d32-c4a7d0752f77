<div class="action-buttons-cell-renderer">
  <button class="edit-button" (click)="onEdit()" *ngIf="hasEdit">
    <mat-icon svgIcon="ic-edit" class="small-icon"></mat-icon>
    @if (!isLabelHidden) {
      {{ 'common.edit' | translate }}
    }
  </button>
  <button class="remove-button" (click)="onRemove()" *ngIf="hasRemove">
    <mat-icon svgIcon="ic-delete" class="small-icon"></mat-icon>
    @if (!isLabelHidden) {
      {{ 'common.remove' | translate }}
    }
  </button>
  <button class="remove-button" (click)="onRemove()" *ngIf="hasDelete">
    <mat-icon svgIcon="ic-delete" class="small-icon"></mat-icon>
    @if (!isLabelHidden) {
      {{ 'common.delete' | translate }}
    }
  </button>
  <button class="cancel-button" (click)="onCancel()" *ngIf="hasCancel">
    <mat-icon svgIcon="ic-view" class="small-icon"></mat-icon>
    @if (!isLabelHidden) {
      {{ 'common.cancel' | translate }}
    }
  </button>

  <button class="view-map-button" (click)="onViewMap()" *ngIf="hasViewMap">
    <mat-icon svgIcon="ic-view-map" class="small-icon"></mat-icon>
    @if (!isLabelHidden) {
      {{ 'common.viewMap' | translate }}
    }
  </button>

  <button class="common-button" (click)="onRowAction()" *ngIf="params?.actionDetail">
    <mat-icon [svgIcon]="params?.actionDetail?.icon" class="small-icon"></mat-icon>
    @if (!isLabelHidden) {
      {{ params?.actionDetail?.text | translate }}
    }
  </button>
</div>
