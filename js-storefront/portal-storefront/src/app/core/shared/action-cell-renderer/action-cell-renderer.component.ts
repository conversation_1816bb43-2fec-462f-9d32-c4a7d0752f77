import { Component, EventEmitter, Input, Output } from '@angular/core';
import { IconModule } from '../../../core/icon/icon.module';
import { ActionTable } from '../../../core/enums';
import { TranslateModule } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-action-cell-renderer',
  standalone: true,
  imports: [
    IconModule,
    TranslateModule,
    CommonModule
  ],
  templateUrl: './action-cell-renderer.component.html',
  styleUrl: './action-cell-renderer.component.scss'
})
export class ActionCellRendererComponent {
  @Input() rowIndex!: number;
  @Output() edit = new EventEmitter<void>();
  @Output() remove = new EventEmitter<void>();

  params!: any;
  hasEdit: boolean = false;
  hasCancel: boolean = false;
  hasRemove: boolean = false;
  hasViewMap: boolean = false;
  isLabelHidden: boolean = false;
  hasDelete: boolean = false;

  agInit(params: any): void {
    this.params = params;
    this.rowIndex = params.rowIndex;
    this.hasEdit = params.hasEdit === undefined ? false : params.hasEdit;
    this.hasCancel = params.hasCancel === undefined ? false : params.hasCancel;
    this.hasRemove = params.hasRemove === undefined ? false : params.hasRemove;
    this.hasViewMap = params.hasViewMap === undefined ? false : params.hasViewMap;
    this.isLabelHidden = params.isLabelHidden === undefined ? false : params.isLabelHidden;
    this.hasDelete = params.hasDelete === undefined ? false : params.hasDelete;
  }

  refresh(params: any): boolean {
    return true;
  }

  onEdit(): void {
    this.params.onClick(ActionTable.Edit, this.params.data);
  }

  onRemove(): void {
    this.params.onClick(ActionTable.Remove, this.params.data);
  }

  onCancel(): void {
    this.params.onClick(ActionTable.Cancel, this.params.data);
  }

  onViewMap(): void {
    this.params.onClick(ActionTable.Cancel, this.params.data);
  }

  onRowAction(): void {
    this.params.onClick(this.params?.data);
  }
}
