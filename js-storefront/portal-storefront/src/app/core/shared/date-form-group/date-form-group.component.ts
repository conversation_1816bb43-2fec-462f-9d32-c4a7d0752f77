import {
  Component,
  EventEmitter,
  Input,
  Output,
  OnInit,
  ChangeDetectionStrategy,
  ViewChild,
  ElementRef,
} from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { CommonModule } from '@angular/common';
import {
  MatNativeDateModule,
} from '@angular/material/core';
import { IconModule } from '../../icon/icon.module';
import { DATE_FORMATS } from '../../constants';
import { provideMomentDateAdapter } from '@angular/material-moment-adapter';

@Component({
  selector: 'app-date-form-group',
  standalone: true,
  imports: [
    CommonModule,
    MatDatepickerModule,
    MatFormFieldModule,
    ReactiveFormsModule,
    MatInputModule,
    MatNativeDateModule,
    IconModule,
  ],
  providers: [
    provideMomentDateAdapter(DATE_FORMATS),
  ],
  templateUrl: './date-form-group.component.html',
  styleUrls: ['./date-form-group.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})

export class DateFormGroupComponent implements OnInit {
  @Input() label: string = '';
  @Input() control!: FormControl<Date | null>;
  @Input() placeholder: string = '';
  @Input() controlId: string = '';
  @Input() required: boolean = false;
  @Input() errorMessage: string = 'Invalid date';
  @Input() isReadOnly: boolean = false;
  @Input() minDate: any;
  @Input() maxDate: any;
  @Input() preventKeyboard = false;

  @Output() onDateChangeEvent = new EventEmitter<Date>();

  @ViewChild('dateInput') dateInput!: ElementRef;

  ngOnInit(): void {
    if (!this.control) {
      this.control = new FormControl<Date | null>(null);
    }
  }

  onDateChange(event: any): void {
    this.onDateChangeEvent.emit(event.value);
    setTimeout(() => {
      this.dateInput?.nativeElement?.focus();
    });
  }

  onKeypress(event): void {
    if(!this.preventKeyboard) {
      event.preventDefault();
    }
  }
}
