<div class="form-group form-group-date">
    <label [for]="controlId" class="form-group__label">
        {{ label }}
        <ng-container *ngIf="required"><span class="required">*</span></ng-container>
    </label>
    <div class="form-group__input no-border-field">
        <input #dateInput matInput [matDatepicker]="picker1" [formControl]="control" [placeholder]="placeholder"
            [id]="controlId" class="form-control" (dateChange)="onDateChange($event)" (keypress)="onKeypress($event)" (keydown)="onKeypress($event)"
            [attr.disabled]="isReadOnly ? true : null" [min]="minDate" [max]="maxDate"
            (click)="isReadOnly ? false : picker1.open()"
        />
        <mat-icon
            svgIcon="ic-calendar"
            (click)="isReadOnly ? false : picker1.open()"
        ></mat-icon>
        <mat-datepicker #picker1></mat-datepicker>
    </div>
    <div *ngIf="control.invalid && control.touched" class="form-group__error">
        {{ errorMessage }}
    </div>
</div>
