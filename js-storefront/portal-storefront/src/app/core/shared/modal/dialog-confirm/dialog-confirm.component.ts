import { Component, inject } from '@angular/core';

import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { ActionModal } from '../../../enums';
import { CommonModule } from '@angular/common';
@Component({
  selector: 'app-dialog-confirm',
  standalone: true,
  imports: [CommonModule, MatDialogModule, MatIconModule, TranslateModule],
  templateUrl: './dialog-confirm.component.html',
})
export class DialogConfirmComponent {
  data = inject(MAT_DIALOG_DATA);
  dialogRef = inject(MatDialogRef<DialogConfirmComponent>);

  onCancel() {
    this.dialogRef.close();
  }

  onConfirm() {
    this.dialogRef.close({ action: ActionModal.Submit });
  }
}
