<mat-icon [svgIcon]="data?.icon" class="icon-title"></mat-icon>
<h2 class="title-dialog">{{ data?.title }}</h2>

<div class="container-dialog">
  <div class="confirm-msg" [innerHTML]="data?.confirmMsg"></div>
</div>

<div class="action-dialog">
  <button class="btn-quaternary" (click)="onCancel()">
    {{ data?.cancelBtn }}
  </button>

  <button *ngIf="data?.submitBtn" class="btn-primary btn-confirm" (click)="onConfirm()">
    {{ data?.submitBtn }}
  </button>
</div>
