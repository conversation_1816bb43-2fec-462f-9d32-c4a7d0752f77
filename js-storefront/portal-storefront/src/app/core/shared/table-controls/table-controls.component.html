<div class="pagination-container table-controls">
  <div *ngIf="!isTablet" class="pagination-info">
    {{ (totalResults === 1? 'paging.oneResult' : 'paging.resultPage') | translate : {
      currentPageStart,
      currentPageEnd,
      totalResults
    } }}
  </div>
  <div *ngIf="isTablet" class="pagination-info-header">
    <div class="pagination-info">
      {{ (totalResults === 1? 'paging.oneResult' : 'paging.resultPage') | translate : {
        currentPageStart,
        currentPageEnd,
        totalResults
      } }}
    </div>
    <div class="pagination-options-dropdown">
      <label for="resultsPerPage" class="pagination-label">{{ 'paging.show' | translate}}</label>
      <mat-form-field appearance="outline" class="pagination-select">
        <mat-select
          panelClass="custom-overlay-panel custom-panel"
          [(ngModel)]="resultsPerPage"
          (selectionChange)="updateResultsPerPage($event?.value)"
          disableRipple
          class="custom-select"
        >
          <mat-option
            class="custom-dropdown-options"
            *ngFor="let option of resultsPerPageOptions"
            [value]="option"
          >
            {{ option }}
          </mat-option>
        </mat-select>
        <mat-icon matSuffix svgIcon="ic-down"></mat-icon>
      </mat-form-field>
    </div>
  </div>
  <app-table-action
    [isShowActionExport]="isShowActionExport"
    [resultsPerPage]="resultsPerPage"
    (changeItemPerPage)="updateResultsPerPage($event)"
    (exportData)="exportData.emit()"
  ></app-table-action>
</div>