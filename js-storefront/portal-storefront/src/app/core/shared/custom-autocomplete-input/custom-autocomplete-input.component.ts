import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { ReactiveFormsModule, FormControl } from '@angular/forms';
import { MatAutocompleteModule, MatAutocompleteTrigger } from '@angular/material/autocomplete';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TranslateModule } from '@ngx-translate/core';
import { Observable, of, debounceTime, distinctUntilChanged, tap, filter, switchMap, Subject } from 'rxjs';
import { OptionDropdown } from '../../interfaces';
import { MatPseudoCheckboxModule } from '@angular/material/core';

@Component({
  selector: 'app-custom-autocomplete-input',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatAutocompleteModule,
    MatFormFieldModule,
    MatInputModule,
    TranslateModule,
    MatProgressSpinnerModule,
    MatPseudoCheckboxModule
  ],
  templateUrl: './custom-autocomplete-input.component.html',
  styleUrls: ['./custom-autocomplete-input.component.scss']
})
export class CustomAutocompleteInputComponent implements OnInit {
  @Input() label: string = '';
  @Input() placeholder: string = '';
  @Input() required: boolean = false;
  @Input() isLoading: boolean = false;
  @Input() controlId: string = '';
  @Input() maxLength: number | null = null;
  @Input() suggestions: Array<OptionDropdown> = [];
  @Input() errorMessage: string = '';
  @Input() control!: FormControl;
  @Input() controlSearch!: FormControl;
  @Output() optionSelected = new EventEmitter<string | OptionDropdown>();
  @Output() onSearch = new EventEmitter<string>();
  @Output() onScrolled = new EventEmitter();
  @Input() otherErrorMessages?: { [key: string]: string } = {};
  @Input() set isDisabled(value: boolean) {
    if (value) {
      this.control.disable();
    } else {
      this.control.enable();
    }
  }

  private seachrEvent$ = new Subject<string>();

  ngOnInit(): void {
    this.seachrEvent$.pipe(
      debounceTime(500),
    ).subscribe(value => {
      this.onSearch.emit(value);
    });
  }

  onChangeSearch(e) {
    this.seachrEvent$.next(e?.target?.value)
  }

  onOptionSelect(selectedValue: OptionDropdown): void {
    this.control.setValue(selectedValue?.uid || selectedValue?.code);
    this.optionSelected.emit(selectedValue);
  }

  onBlur(): void {
    this.control.markAsTouched();
  }

  displayFn(option: OptionDropdown): string {
    return option?.name || option?.displayName;
  }

  onScroll(e) {
    const scrolled = e.target.scrollTop;
    const height = e.target.offsetHeight;
    const scrollHeight = e.target.scrollHeight;
    if (height + scrolled + 1 >= scrollHeight && !this.isLoading) {
      this.onScrolled.emit();
    }
  }
}
