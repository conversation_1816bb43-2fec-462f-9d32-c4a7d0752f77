import { Component, Input, ViewEncapsulation } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatInputModule } from '@angular/material/input';
import { IconModule } from '../../icon/icon.module';

@Component({
  selector: 'app-time-form-group',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDatepickerModule,
    MatInputModule,
    IconModule,
  ],
  templateUrl: './time-form-group.component.html',
  encapsulation: ViewEncapsulation.None,
})
export class ChooseTimeFormGroupComponent {
  @Input() label: string = '';
  @Input() control!: FormControl;
  @Input() placeholder: string = '';
  @Input() controlId: string = '';
  @Input() required: boolean = false;
  @Input() errorMessage: string = 'Invalid input';

  formatTime(event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    let value = inputElement.value.replace(/[^0-9]/g, ''); 

    if (value.length > 2) {
      value = value.slice(0, 2) + ':' + value.slice(2, 4);
    }

    if (value.length > 5) {
      value = value.slice(0, 5);
    }

    inputElement.value = value;
    this.control.setValue(value); 
  }
}
