import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  inject,
  Input,
  Output,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { AgGridAngular } from 'ag-grid-angular';
import { AllCommunityModule, ColDef, ModuleRegistry, provideGlobalGridOptions, RowClassRules } from 'ag-grid-community';
import { ScreenSizeService } from '../../services';
import { PaginationComponent } from '../pagination/pagination.component';
import { TableControlsComponent } from '../table-controls/table-controls.component';
import { ImportResultSummaryComponent } from '../import-result-summary/import-result-summary.component';
import { LastImportResult, PagingInfo } from '../../interfaces';

ModuleRegistry.registerModules([AllCommunityModule]);
provideGlobalGridOptions({ theme: "legacy"});
@Component({
  selector: 'app-ag-grid-custom',
  standalone: true,
  imports: [
    CommonModule,
    AgGridAngular,
    PaginationComponent,
    TableControlsComponent,
    ImportResultSummaryComponent
  ],
  providers: [ScreenSizeService],
  templateUrl: './ag-grid-custom.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AgGridCustomComponent {
  @ViewChild(AgGridAngular) agGrid!: AgGridAngular;
  @Input() rowData: any[];
  @Input() colDefs: ColDef[];
  @Input() defaultColDef: ColDef<any, any>;
  @Input() gridOptions: any;
  @Input() rowHeight = 52;
  @Input() rowClassRules: RowClassRules

  @Input() templates: { [key: string]: TemplateRef<any> } = {}; 

  @Input() onlyTable = false;
  @Input() isPaging = false;
  @Input() pagingInfo: PagingInfo;

  @Input() isImportSummary = false;
  @Input() importInfo: LastImportResult;
  @Input() isLoadingLastImport: boolean = false;
  @Input() isChangeItemPerPageTop = false;

  @Input() isShowActionExport: boolean = true;
  @Input() isTextWrap: boolean = false;

  @Input() isVerticalScroll: boolean = false;

  @Output() onPageChange = new EventEmitter<number>();
  @Output() onResultsPerPageChange = new EventEmitter<number>();
  @Output() changeItemPerPage = new EventEmitter();
  @Output() exportData = new EventEmitter<void>();
  @Output() downloadFile = new EventEmitter();

  screenSizeService = inject(ScreenSizeService);

  ngOnInit(): void {
    if (this.isTextWrap) {
      this.defaultColDef.wrapText = true; // Wrap Text
      this.defaultColDef.autoHeight = true; // Adjust Cell Height to Fit Wrapped Text
      this.defaultColDef.wrapHeaderText = true; // Wrap Header Text
      this.defaultColDef.autoHeaderHeight = true; // Adjust Header Height to Fit Wrapped Text
    }
  }

  changeItemPerPageTop(event): void {
    this.isChangeItemPerPageTop = !this.isChangeItemPerPageTop;
    this.changeItemPerPage.emit(event);
  }

  getSelectedRows() {
    return this.agGrid.api.getSelectedRows();
  }
}
