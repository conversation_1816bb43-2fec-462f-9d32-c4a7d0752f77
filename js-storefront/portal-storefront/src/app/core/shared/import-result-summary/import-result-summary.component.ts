import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { environment } from '../../../../environments/environment';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-import-result-summary',
  templateUrl: './import-result-summary.component.html',
  imports: [CommonModule, MatProgressSpinnerModule, TranslateModule],
  standalone: true,
})
export class ImportResultSummaryComponent {
  @Input() importDate!: Date;
  @Input() resultMessage: string = '';
  @Input() fileName: string | null = null;
  @Input() set downloadUrl(value) {
    const prefixEnv = environment.BACKOFFICE_BASE_URL;
    this._downloadUrl = prefixEnv?.slice(0, -1) + value;
  }
  @Input() isLoading: boolean = false;
  @Input() title?: string;

  @Output() downloadFile = new EventEmitter();

  _downloadUrl: string;
}
