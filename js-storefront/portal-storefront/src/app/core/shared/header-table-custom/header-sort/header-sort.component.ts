import { Component, inject, OnInit } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { DataStoreService } from '../../../services/data-store.service';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-header-sort',
  standalone: true,
  imports: [
    TranslateModule,
    MatIconModule
  ],
  templateUrl: './header-sort.component.html',
  styleUrls: ['./header-sort.component.scss']
})
export class HeaderSortComponent implements OnInit {
  public params: any;
  public headerTxt: string = '';
  sortDes = false;
  dataStoreService = inject(DataStoreService);

  ngOnInit() {
  }

  agInit(params: any): void {
    this.params = params
    this.headerTxt = this.params.headerTxt !== undefined ? this.params.headerTxt : '';
  }

  onSort(column: string, sort: boolean) {
    this.sortDes = sort
    this.dataStoreService.setSortData({column, sort});
  }
}
