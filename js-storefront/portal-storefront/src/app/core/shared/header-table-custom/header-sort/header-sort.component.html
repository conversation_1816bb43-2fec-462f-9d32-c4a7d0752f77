<div class="sortheader">
  <div class="sortheadercontainer">
    <span>{{this.headerTxt | translate}}</span>
    <div class="sortactions">
      <mat-icon
        [svgIcon]="!sortDes ? 'ic-arrowupred' : 'ic-arrowup'"
        (click)="onSort(this.headerTxt,false)"></mat-icon>
      <mat-icon
        [svgIcon]="sortDes ? 'ic-arrowupred' : 'ic-arrowup'"
        (click)="onSort(this.headerTxt,true)"></mat-icon>
    </div>
  </div>
</div>
