import { CommonModule } from '@angular/common';
import { Component, Input, Output, EventEmitter, OnInit, inject, SimpleChanges } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { IconModule } from '../../icon/icon.module';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule, MatSelectChange } from '@angular/material/select'; // Import MatSelectChange
import { MediaQueryService } from '../../services/media-query.service';
import { TableActionComponent } from '../table-action/table-action.component';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-pagination',
  templateUrl: './pagination.component.html',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IconModule,
    MatSelectModule,
    MatFormFieldModule,
    TranslateModule,
    TableActionComponent,
  ],
})
export class PaginationComponent implements OnInit {
  @Input() totalResults: number = 100;
  @Input() currentPage : number = 1;
  @Input() resultsPerPage: number = 10;
  @Input() set itemPerPageTop(value) {
   this.updatePagesArray();
  }
  
  @Input() isShowActionExport: boolean = true;


  @Output() currentPageChange = new EventEmitter<number>();
  @Output() resultsPerPageChange = new EventEmitter<number>();
  @Output() changeItemPerPage = new EventEmitter();
  @Output() exportData = new EventEmitter<void>();
  
  mediaQueryService = inject(MediaQueryService);

  resultsPerPageOptions: number[] = [10, 20, 30];
  
  pages: (number | string)[] = [];
  isTablet: boolean = false;

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['resultsPerPage'] && changes['resultsPerPage'].currentValue) {
      this.updatePagesArray();
    }
    if (changes['totalResults'] && changes['totalResults'].currentValue !== undefined && changes['totalResults'].currentValue !== null) {
      this.updatePagesArray();
    }
  }
  ngOnInit(): void {
    this.updatePagesArray();
    this.mediaQueryService
      .isScreenMatch('(max-width: 768px)')
      .subscribe((matches) => {
        this.isTablet = matches;
      });
  }

  get currentPageStart(): number {
    return this.currentPage * this.resultsPerPage + 1;
  }
  
  get currentPageEnd(): number {
    return (this.currentPage + 1) * this.resultsPerPage;
  } 

  get totalPages(): number {
    return Math.ceil(this.totalResults / this.resultsPerPage);
  }

  isNumber(value: string | number): boolean {
    return typeof value === 'number';
  }

  goToPage(page: number | string): void {
    if (typeof page === 'number') {
      this.currentPage = page - 1;
      this.currentPageChange.emit(this.currentPage);
      this.updatePagesArray();
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }

  previousPage(): void {
    if (this.currentPage >= 1) {
      this.currentPage--;
      this.updatePagesArray();
      this.currentPageChange.emit(this.currentPage);
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }

  nextPage(): void {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
      this.updatePagesArray();
      this.currentPageChange.emit(this.currentPage);
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }

  updateResultsPerPage(value: number): void {
    this.resultsPerPage = value;
    this.currentPage = 0;
    this.updatePagesArray();
    this.changeItemPerPage.emit(value);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  shouldShowEndingEllipsis(): boolean {
    return this.currentPage < this.totalPages - 2;
  }

  updatePagesArray(): void {
    if (this.totalPages <= 7) {
      // Show all pages if there are 7 or fewer pages
      this.pages = Array.from({ length: this.totalPages }, (_, i) => i + 1);
    } else {
      if (this.currentPage <= 3) {
        // Near the start (e.g., pages 1 to 5)
        this.pages = [1, 2, 3, 4, 5, '...', this.totalPages];
      } else if (this.currentPage >= this.totalPages - 4) {
        // Near the end (e.g., last 5 pages)
        this.pages = [
          1,
          '...',
          this.totalPages - 4,
          this.totalPages - 3,
          this.totalPages - 2,
          this.totalPages - 1,
          this.totalPages,
        ];
      } else {
        // In the middle (e.g., showing current, previous, and next pages with ellipses)
        this.pages = [
          1,
          '...',
          this.currentPage - 1,
          this.currentPage,
          this.currentPage + 1,
          this.currentPage + 2,
          '...',
          this.totalPages,
        ];
      }
    }
  }
}
