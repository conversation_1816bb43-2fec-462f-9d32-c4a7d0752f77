import { Component } from '@angular/core';
import { MatSnackBar, MatSnackBarRef } from '@angular/material/snack-bar';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatIconModule } from '@angular/material/icon';
import { NotificationContentComponent } from './notification-content.component';

@Component({
  selector: 'app-notification',
  standalone: true,
  imports: [MatSnackBarModule, MatIconModule],
  templateUrl: './notification.component.html',
})
export class NotificationComponent {
  private persistentNotification!: MatSnackBarRef<any>;
  constructor(private snackBar: MatSnackBar) {}

//  ngOnInit(): void {
//    this.showPersistentNotification(
//      'This is a persistent notification for styling',
//      'success'
//    );
    //  }
    
  // this function for test to make it always show just put in  ngOnInit
  showPersistentNotification(
    message: string,
    type: 'success' | 'error' | 'warning' | 'info'
  ): void {
    const icon = this.getIcon(type);
    const panelClass = `${type}-snackbar`;

    // Dismiss any previous persistent notification
    if (this.persistentNotification) {
      this.persistentNotification.dismiss();
    }

    this.persistentNotification = this.snackBar.openFromComponent(
      NotificationContentComponent,
      {
        data: { message, icon },
        panelClass: [panelClass],
        horizontalPosition: 'right',
        verticalPosition: 'top',
        duration: 0, // Persistent: Doesn't dismiss automatically
      }
    );
  }

  showNotification(
    message: string,
    type: 'success' | 'error' | 'warning' | 'info',
    duration: number = 5000
  ) {
    const panelClass = `${type}-snackbar`;
    const icon = this.getIcon(type);

    this.snackBar.openFromComponent(NotificationContentComponent, {
      data: { message, icon },
      duration,
      horizontalPosition: 'right',
      verticalPosition: 'top',
      panelClass: [panelClass],
    });
  }

  showSuccess(message: string, duration: number = 5000) {
    this.showNotification(message, 'success', duration);
  }

  showError(message: string, duration: number = 5000) {
    this.showNotification(message, 'error', duration);
  }

  showWarning(message: string, duration: number = 5000) {
    this.showNotification(message, 'warning', duration);
  }

  showInfo(message: string, duration: number = 5000) {
    this.showNotification(message, 'info', duration);
  }

  private getIcon(type: 'success' | 'error' | 'warning' | 'info'): string {
    switch (type) {
      case 'success':
        return 'check_circle';
      case 'error':
        return 'error';
      case 'warning':
        return 'warning';
      case 'info':
        return 'info';
      default:
        return '';
    }
  }
}
