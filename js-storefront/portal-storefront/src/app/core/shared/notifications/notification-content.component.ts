import { Component, Inject } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import {
  MAT_SNACK_BAR_DATA,
  MatSnackBarRef,
} from '@angular/material/snack-bar';
import { IconModule } from '../../icon/icon.module';
import { TranslateModule } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-notification-content',
  template: `
    <div class="notification-content">
      <div class="notification-content__content">
        @if(data?.icon === 'ic-check-circle' || data?.icon === 'ic-warning-triangle') {
          <mat-icon class="notification-icon" [svgIcon]="data.icon"></mat-icon>
        } @else {
          <mat-icon class="notification-icon">{{ data.icon }}</mat-icon>
        }
        <span class="notification-message">{{ data.message | translate }}</span>
      </div>
      <mat-icon matSuffix svgIcon="ic-close" (click)="close()"></mat-icon>
    </div>
  `,
  standalone: true,
  imports: [MatIconModule, IconModule, TranslateModule, CommonModule],
})
export class NotificationContentComponent {
  constructor(
    @Inject(MAT_SNACK_BAR_DATA) public data: { message: string; icon: string },
    private snackBarRef: MatSnackBarRef<NotificationContentComponent>
  ) {}

  close(): void {
    this.snackBarRef.dismiss();
  }
}
