<div class="form-group form-group-date">
  <label [for]="controlId" class="form-group__label">
    {{ label }}
    <ng-container *ngIf="required"
      ><span class="required">*</span></ng-container
    >
  </label>
  <div class="form-group__input">
    <div class="no-border-field">
      <input [owlDateTime]="dt1" [owlDateTimeTrigger]="dt1" [min]="minDate" [max]="maxDate" [formControl]="control" [placeholder]="placeholder"
      [id]="controlId" class="form-control" (dateChange)="onDateChange($event)" (keypress)="false" (keydown)="false" readonly="true">
      <mat-icon class="small-icon date-picker-icon" [owlDateTimeTrigger]="dt1" svgIcon="ic-calendar"></mat-icon>
      <owl-date-time #dt1 [hour12Timer]="true"></owl-date-time>
    </div>
  </div>
  <div *ngIf="control.invalid && control.touched" class="form-group__error">
    {{ errorMessage }}
  </div>
</div>
