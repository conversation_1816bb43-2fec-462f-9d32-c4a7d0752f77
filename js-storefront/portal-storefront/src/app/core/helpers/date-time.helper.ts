import { inject, Injectable } from '@angular/core';
import { DateFormat, TimeZone } from '../enums';
import { DatePipe } from '@angular/common';

@Injectable({
  providedIn: 'root',
})
export class DateTimeHelper {
  datePipe = inject(DatePipe);

  /**
   * Convert UTC date to display date in locale
   * @param utcDate can be number string (Ex: '1740740821449') / string of date (Ex: '03/08/2025')/ Date
   * @param format Format in DateFormat enum (using by angular date pipe)
   * @returns String of date in display format and locale
   */
  convertUTCtoDisplayDate(
    utcDate: string | Date,
    format: string = DateFormat.Full
  ): string {
    if (!utcDate) return '';
    const timeZone = TimeZone.UTC8;

    try {
      const convertDate = this.datePipe.transform(utcDate, format, timeZone);
      return convertDate;
    } catch (error) {
      console.error(`Invalid date: ${utcDate}`);
      return null;
    }
  }
}
