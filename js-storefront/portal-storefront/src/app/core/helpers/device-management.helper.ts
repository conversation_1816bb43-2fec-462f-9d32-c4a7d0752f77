import {
  CanActiveForSim,
  DeviceAction,
  DeviceStatus,
  PairingStatus,
  SimStatus,
} from '../enums';
import { ItemDevice } from '../interfaces';
import { PERMISSIONS_CODE } from '../constants';

export const getDeviceAction = (
  itemDevice: ItemDevice,
  isLDCM: boolean,
  userService
) => {
  const {
    pairingStatus,
    simStatus,
    deviceStatus,
    signalTestingStatus,
    canActiveFor,
  } = itemDevice;
  let labelAction = '';
  let iconAction = '';
  let id = '';

  if (
    isLDCM &&
    pairingStatus?.code === PairingStatus.NotPaired &&
    userService.isHasPermission([PERMISSIONS_CODE.IOT_DEVICE_PAIR])
  ) {
    labelAction = 'deviceAction.pairDevice';
    iconAction = 'ic-link-pair-red';
    id = DeviceAction.PairDevice;
  } else if (
    isLDCM &&
    simStatus?.code === SimStatus.Pending &&
    canActiveFor !== CanActiveForSim.None &&
    userService.isHasPermission([PERMISSIONS_CODE.IOT_SIM_ENABLE_DISABLE])
  ) {
    labelAction = 'deviceAction.enableSIM';
    iconAction = 'ic-enable-sim-red';
    id = DeviceAction.EnableSIM;
  } else if (
    isLDCM &&
    (simStatus?.code === SimStatus.ActiveTesting ||
      simStatus?.code === SimStatus.ActiveSubscription) &&
    userService.isHasPermission([PERMISSIONS_CODE.IOT_SIM_ENABLE_DISABLE])
  ) {
    labelAction = 'deviceAction.disableSim';
    iconAction = 'ic-disable-sim-red';
    id = DeviceAction.DisableSIM;
  } else if (
    isLDCM &&
    signalTestingStatus === 'Not Verified' &&
    userService.isHasPermission([PERMISSIONS_CODE.IOT_DEVICE_PAIR])
  ) {
    labelAction = 'deviceAction.confirmPairing';
    iconAction = 'ic-link-confirm';
    id = DeviceAction.ConfirmPairing;
  }
  // for GDCM
  else if (
    !isLDCM &&
    deviceStatus?.code === DeviceStatus.ForTermination &&
    userService.isHasPermission([PERMISSIONS_CODE.IOT_DEVICE_TERMINATE])
  ) {
    labelAction = 'deviceAction.terminateDevice';
    iconAction = 'ic-terminate';
    id = DeviceAction.TerminateDevice;
  }

  return {
    text: labelAction,
    icon: iconAction,
    id,
  };
};
