@charset "UTF-8";
@import "@danielmoncada/angular-datetime-picker/assets/style/picker.min.css";
@import 'ag-grid-community/styles/ag-grid.css';
@import 'ag-grid-community/styles/ag-theme-quartz.css';

// 1. Configuration and helpers
@import
  'abstracts/variables',
  'abstracts/functions',
  'abstracts/mixins';

// 2. Vendors
@import
  'vendors/normalize';

// 3. Base stuff
@import
  'base/base',
  'base/fonts',
  'base/typography',
  'base/grid',
  'base/form',
  'base/helpers';

// 4. Layout-related sections
@import
  'layout/header',
  'layout/footer',
  'layout/sideNav',
  'layout/dashboard';


// 5. Components
@import
  'components/accordion',
  'components/theme',
  'components/button',
  'components/table',
  'components/datepicker',
  'components/navigation',
  'components/tab',
  'components/tooltip',
  'components/form',
  'components/loader',
  'components/ag-grid',
  'components/_card',
  'components/_import-result',
  'components/_pagination',
  'components/_notification',
  'components/dialog',
  'components/import-file',
  'components/breadcrumbs',
  'components/pie-chart',
  'components/widget-table';

// 6. Page-specific styles
@import
  'pages/home', 
  'pages/login',
  'pages/device-list',
  'pages/vehicle-model',
  'pages/vehicle-list',
  'pages/device-detail',
  'pages/notifications',
  'pages/reset-pass',
  'pages/dashboard',
  'pages/vehicle-details';

// 7. Themes
@import
  'themes/default';
  //Sections
 