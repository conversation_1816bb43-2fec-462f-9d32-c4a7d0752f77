.theme-container-wrap {
    display: flex;
    flex-direction: column;
    min-height: 100%;
    background-color: $brand-secondary;
    word-break: unset;
    word-wrap: unset;
}

.theme-body {
    list-style: none;
    padding: 0;

    .mat-expansion-panel {
        box-shadow: none;

    }

    .mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:hover:not([aria-disabled=true]) {
        background: $brand-secondary;
    }

    .mat-expansion-panel-header {
        padding: 0;
        display: inline;
        font-size: $fs-medium;
        font-weight: 700;
        line-height: 22px;
    }

    &--title-page {
        img {
            margin-right: 12px;
        }
    }

    &--item {
        display: flex;
        align-items: center;
        border-bottom: 1px solid $border-color-3;
        padding: 15px 0;
        font-size: 16px;
        font-weight: 700;
        line-height: 22px;
        cursor: pointer;

        .icon-dropdown {
            margin-left: auto;
        }

        &__sub {
            padding-top: 5px;
            padding-bottom: 5px;
            padding-left: 0;
            border-bottom: 1px solid $border-color-3;

            .sub-item {
                font-weight: 500;
                padding-top: 0;

                &.active {
                    font-weight: 700;
                }
            }

            .sub-item-array {
                padding-bottom: 20px;
                border-bottom: 1px solid $border-color-3;
                margin-bottom: 20px;
            }
        }
    }
}

.customise-header {
    min-height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 700;
    font-size: 20px;
}

.customise-item {
    padding: 20px 0;
}

.customise-form {
    padding: 0 20px;

    &__slide-toggle {
        border-bottom: 1px solid $border-color-3;
        padding-bottom: 20px;
        margin-bottom: 20px;
    }

    &--slide-toggle {
        &:not(:last-child) {
            padding-bottom: 20px;
        }
    }

    &--logo {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 245px;
        border-radius: 10px;
        background: $input-background;
        .text-image {
            color: #A4A4A4;
            font-size: $fs18;
            img {
                padding-right: 8px;
            }
        }
    }
    &--remove-section-image {
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        width: 30px;
        height: 30px;
        background-color: $brand-secondary;
        border-radius: 50%;
        top: 10px;
        right: 10px;
        cursor: pointer;
    }

    .nbtn {
        border: 2px solid $brand-primary;
    }

    &--btn {
        margin-bottom: 15px !important;
        margin-top: 20px !important;
    }

    .upload-label {
        margin-bottom: 0;
        cursor: pointer;
    }

    .file-upload-input {
        visibility: hidden;
        height: 0px;
        width: 0px;
        padding: 0;
        min-height: inherit;
    }

    &--group {
        margin-bottom: 0 !important;
    }

    .link-to {
        margin-bottom: 0;
        margin-top: 20px;
    }
    .external-link-to {
        margin-bottom: 30px;
        margin-top: 0;
    }

    .icon-remove {
        &--red {
            color: #CC1A1A;
        }
        &--category {
            padding-left: 17px;
        }
    }
}