.mat-mdc-snack-bar-container {
  &.success-snackbar,
  &.error-snackbar,
  &.info-snackbar,
  &.warning-snackbar {
    .mdc-snackbar__surface {
      border-radius: 0 !important;
      box-shadow: none !important;
    }
  }

  &.info-snackbar {
    .mdc-snackbar__surface {
      background: linear-gradient(
          0deg,
          rgba(0, 76, 222, 0.1) 0%,
          rgba(0, 76, 222, 0.1) 100%
        ),
        #fff !important;
    }
  }

  &.error-snackbar {
    .mdc-snackbar__surface {
      background-color: #fef2f3 !important;
    }
  }

  &.success-snackbar {
    .mdc-snackbar__surface {
      background-color: #f4fbf7 !important;
    }
  }

  &.warning-snackbar {
    .mdc-snackbar__surface {
      background-color: #fffcf5 !important;
    }
  }

  .mdc-snackbar__label {
    font-family: inherit !important;
  }
}
.success-snackbar {
  app-notification-content {
    color: #2aba6c;
    font-weight: 600;
  }
}

.error-snackbar {
  app-notification-content {
    color: #f44336;
    font-weight: 600;
  }
}

.warning-snackbar {
  app-notification-content {
    color: #fcbf45;
    font-weight: 600;
  }
}

.info-snackbar {
  app-notification-content {
    color: #266ef2;
    font-weight: 600;
  }
}
.mat-mdc-snack-bar-container .mdc-snackbar__surface {
  min-width: unset !important;
}
.mdc-snackbar__label {
  padding: 0 !important;
}
.notification-content {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 20px;
  max-width: 400px;
  width: 100%;

  &__content {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .mat-icon {
    height: 20px;
    width: 20px;
    font-size: 20px;
    min-width: 20px;
  }

  mat-icon[svgicon="ic-close"] {
    height: 14px;
    width: 14px;
    font-size: 14px;
    cursor: pointer;
  }
}
