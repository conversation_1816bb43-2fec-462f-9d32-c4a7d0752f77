.navigation-container {
    display: flex;
    justify-content: center;
    margin-top: 10px;
    gap: 8px;
    align-items: center;

    .previous-container, .next-container {
        display: flex;
        gap: 4px;

        button {
            display: flex;
            padding: 4px;
            justify-content: center;
            align-items: center;
            border-radius: 4px;
            border: 1px solid $border-color;
            width: 24px;
            height: 24px;

            &:hover {
                cursor: pointer;
            }
        }
    }

    .pagination-container {
        display: flex;
        gap: 8px;

        .pagination-item {
            display: flex;
            width: 24px;
            height: 24px;
            justify-content: center;
            align-items: center;
            color: $text-color-tab;
            font-size: $fs11;
            font-weight: $fw400;

            &:hover {
                cursor: pointer;
            }
        }

        .page-active {
            background-color: $brand-primary;
            color: $text-color-white;
            border-radius: 4px;
        }
    }
}