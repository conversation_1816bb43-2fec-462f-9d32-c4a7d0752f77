.pie-chart {
    background: $bg-color-1;

    h2 {
        font-size: $fs22;
        font-weight: $fw600;
        margin-bottom: 25px;

        @include xl {
            display: flex;
            justify-content: space-between;
        }

        .view-list {
            min-width: 80px;
            font-size: $fs14;
            color: $brand-primary;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding: 0;
            cursor: pointer;
            font-weight: 600;

            .mat-icon {
                width: 16px;
                margin-right: 5px;

                path {
                    fill: $brand-primary;   
                }
            }
        }
    }

    &__content {
        @include xl {
            display: flex;
            align-items: center;
        }

        &.col-1 {
            max-width: 400px;
            margin: auto;
        }

        &.col-2 {
            .chart {
                width: 265px;

                @include xl {
                    flex: 0 0 265px;
                    max-width: 265px;
                }

                &__title {
                    font-size: $fs16;
    
                    strong {
                        font-size: $fs28;
                    }
                }
            }

            .legend {
                @include xl {
                    flex: 0 0 calc(100% - 265px);
                    max-width: calc(100% - 265px);
                }

                @media screen and (min-width: 1400px) {
                    padding-left: 80px;
                }
            }
        }

        .chart {
            position: relative;
            width: 200px;
            margin: auto;

            @include xl {
                flex: 0 0 200px;
                max-width: 200px;
            }

            &__title {
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
                text-align: center;
                font-size: $fs12;

                strong {
                    font-weight: $fw600;
                    font-size: $fs22;
                }
            }
        }

        .legend {
            @include xl {
                flex: 0 0 calc(100% - 200px);
                max-width: calc(100% - 200px);
                padding-left: 40px;
            }

            &__item {
                margin-top: 15px;
                padding-left: 24px;
                position: relative;
                cursor: pointer;

                &.hide {
                    color: $text-color-2 !important;

                    span {
                        background-color: $text-color-2 !important;
                    }
                }
            }

            &.col-2 {
               @include lg {
                    display: flex;
                    flex-flow: row wrap;

                    .legend__item {
                        flex: 0 0 50%;
                        max-width: 50%;
                    }
                }
            }

            span {
                width: 14px;
                height: 14px;
                display: block;
                position: absolute;
                left: 0;
                top: 4px;
            }

            p {
                margin-bottom: 0;
                font-size: $fs14;
            }

            strong {
                font-weight: $fw600;
            }
        }
    }
}
