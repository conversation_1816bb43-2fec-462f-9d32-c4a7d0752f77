body.loading {
    visibility: hidden;
}

body.loading app-loader {
    visibility: visible;
}

.spinner {
    border: 2px solid #101010;
    border-top: 2px solid #ffffff;
    border-radius: 50%;
    width: 100px;
    height: 100px;
    animation: spin 800ms linear infinite;
    top: 45%;
    left: 45%;
    position: absolute;

    @media screen and (max-width: 1099px) {
        top: 30%;
        left: 40%;
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.loader-container {
    background-color: white;
    width: 100vw;
    height: 100vh;
    z-index: 10;
    position: fixed;
    left: 0;
    top: 0;

    @media screen and (max-width: 1099px) {
        top: 0;
        width: 100vw;
    }

    -moz-animation: cssAnimation 0.5s ease-in 1s forwards;
    /* Firefox */
    animation: cssAnimation 0.5s ease-in 1s forwards;
    /* Safari and Chrome */
    -o-animation: cssAnimation 0.5s ease-in 1s forwards;
    /* Opera */
    animation: cssAnimation 0.5s ease-in 1s forwards;
    animation-fill-mode: forwards;
    animation-fill-mode: forwards;
}

@keyframes cssAnimation {
    to {
        opacity: 0;
        display: none;
        visibility: hidden;
    }
}

@keyframes cssAnimation {
    to {
        visibility: hidden;
        opacity: 0;
        display: none;
    }
}

app-loading {
    width: 100vw;
    height: 100vh;
    position: fixed;
    background-color: $white-bg;
    opacity: 0.3;
    left: 0;
    z-index: 10000;
    top: 0;
  }

  .custom-spinner {
    --mdc-circular-progress-active-indicator-color: #101010;

    &--red {
        --mdc-circular-progress-active-indicator-color: #eb0a1e;
    }
  }
