.clearfix::after {
  clear: both;
  content: '';
  display: table;
}

.container-fluid {
  max-width: $full-width; /* 1 */
  margin-left: auto; /* 2 */
  margin-right: auto; /* 2 */
  padding-left: $padding-container; /* 3 */
  padding-right: $padding-container; /* 3 */
  width: 100%; /* 1 */
  
  @include sm-max{
    padding-left: $grid-gutter-width; /* 3 */
    padding-right: $grid-gutter-width; /* 3 */
  }
}
.container {
  max-width: $container-width; /* 1 */
  margin-left: auto; /* 2 */
  margin-right: auto; /* 2 */
  padding-left: $padding-container; /* 3 */
  padding-right: $padding-container; /* 3 */
  width: 100%; /* 1 */

  @include sm-max{
    padding-left: $grid-gutter-width; /* 3 */
    padding-right: $grid-gutter-width; /* 3 */
  }
}

.img-loading img {
    animation: blink 1.5s ease-in-out infinite;
    transition: opacity 0.3s ease-in-out;
}

@keyframes blink {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }

    100% {
        opacity: 1;
    }
}

.hide-text {
  overflow: hidden;
  padding: 0; /* 1 */
  text-indent: 101%;
  white-space: nowrap;
}

.lower-case{
    text-transform: lowercase;
    color: inherit !important;
}

.visually-hidden {
  border: 0;
  clip: rect(0 0 0 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
}

.overflow-hidden {
  overflow: hidden;
}

/* margin x */
.m-x-20 {
  margin: 0 20px;
}

/* margin right */
.m-r-15 {
  margin-right: 15px;
}

/* margin left */
.m-l-40 {
  margin-left: 40px;
}
.m-l-30 {
  margin-left: 30px;
}
.m-l-20 {
  margin-left: 20px;
}
.m-l-15 {
  margin-left: 15px;
}
/* margin top */
.m-t-40 {
  margin-top: 40px !important;
}
.m-t-30 {
  margin-top: 30px !important;
}
.m-t-25 {
  margin-top: 25px !important;
}
.m-t-20 {
  margin-top: 20px !important;
}

.m-t-10 {
  margin-top: 10px !important;
}

/* margin bottom */
.m-b-40 {
  margin-bottom: 40px !important;
}

.m-b-30 {
  margin-bottom: 30px !important;
}

.m-b-20 {
  margin-bottom: 20px !important;
}

.m-b-15 {
  margin-bottom: 15px !important;
}

.m-b-10 {
  margin-bottom: 10px !important;
}

.m-b-0 {
  margin-bottom: 0 !important;
}

/* padding bottom */
.p-b-0 {
  padding-bottom: 0 !important;
}

/* padding top */
.p-t-30 {
  padding-top: 30px !important;
}

.p-t-15 {
  padding-top: 15px !important;
}

.p-t-0 {
  padding-top: 0 !important;
}

/* padding left */
.p-l-0 {
  padding-left: 0 !important;
}

/* padding left */
.p-r-0 {
  padding-right: 0 !important;
}
/* padding  */

.p-0 {
    padding: 0 !important;
}

.width-full {
  width: 100%;
}

.align-center {
    align-items: center;
}

.display-block {
  display: block !important;
}

.display-flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.display-flex-wrap {
  display: flex;
  flex-wrap: wrap;
}

.remove-border-bottom {
  border-bottom: 0 !important;
}

.justify-content-center {
  justify-content: center;
}

.justify-content-space-between {
  justify-content: space-between;
}

.tag-icon-close {
  height: 13px;
  width: 1px;
  margin-left: 0px;
  background-color: black;
  transform: rotate(45deg);
  Z-index: 1;
  cursor: pointer;
  div {
      height: 13px;
      width: 1px;
      background-color: black;
      transform: rotate(90deg);
      Z-index: 2;
      cursor: pointer;
  }
}

.layout-overlay {
  display: none;
  position: absolute;
  width: 100%;
  height: 100%;
  background: #000000;
  z-index: 999997;
  top: 0;
  left: 0;
  opacity: 0.7;
  overflow: hidden;
  &.show {
      display: block;
  }
}

.payment-title {
  font-size: 25px;
  font-weight: 700;
  margin-bottom: 30px;
}

.mat-dialog-container {
  border-radius: 25px !important;
}

/* icon */

.small-icon {
    width: 16px !important;
    height: 16px !important;
}

.medium-icon {
    width: 32px !important;
    height: 32px !important;
}

.normal-icon {
    width: 20px !important;
    height: 20px !important;
}

.large-icon {
    width: 45px !important;
    height: 45px !important;
}

// buttons

.btn-link {
    background: none;
    border: none;
    color: #eb0a1e;
    cursor: pointer;
    font-weight: 600;
    display: inline-flex;
    align-items: center;

    mat-icon {
        margin-right: 4px;
    }
}

// customs 

.text-left {
    text-align: left;
}

.ighttext-r {
    text-align: right;
}

.truncate {
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
    display: inline-block !important;
}

a[href^="http://maps.google.com/maps"]{display:none !important}
a[href^="https://maps.google.com/maps"]{display:none !important}

.gmnoprint a, .gmnoprint span, .gm-style-cc {
    display:none;
}
.gmnoprint div {
    background:none !important;
}
.font-weight-600 {
  font-weight: 600;
}
.width-full {
  width: 100% !important;
}

.m-t-20 {
  margin-top: 20px;
}

.p-t-0 {
  padding-top: 0 !important;
}

.p-b-0 {
  padding-bottom: 0 !important;
}

.loading {
  position: relative;
  opacity: 0.3;
  .spinner {
    width: 20px;
    height: 20px;
  }
}

.super-small-icon {
  width: 14px !important;
  height: 14px !important;
}

.green {
  color: #4caf50;
}