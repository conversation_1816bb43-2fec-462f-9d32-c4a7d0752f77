$height-header: 60px;

@mixin profile-image {
  &__info {
    display: flex;
    gap: 7px;
    width: 100%;

    &__profile-info {
      display: flex;
      align-items: center;

      width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      color: $text-color-white;
      font-size: $fs13;

      &.has-email {
        flex-direction: column;
        align-items: flex-start;
      }

      span {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        &:nth-child(1) {
          font-weight: $fw600;
        }
      }
    }
  }
}

@mixin item-menu-hover {
  background-color: $white-bg;
  a {
    opacity: 1;
  }

  img {
    filter: brightness(0);
  }

  .nav-text {
    color: $text-color-6;
    font-weight: $fw600;
  }
}

app-sidebar {
  background: $bg-color-8;
}

.main-menu {
  background: $bg-color-8;
  width: 80px;
  position: fixed;
  top: 0;
  bottom: 0;
  height: 100%;
  color: $text-color-2;
  transition: width 0.3s;
  white-space: nowrap;
  overflow: visible;
  z-index: 1000;

  .logo {
    margin: 30px 0 25px;
    text-align: center;
    p {
      font-size: $fs14;
      font-weight: $fw700;
      opacity: 0;
      display: flex;
      gap: 5px;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      mat-icon {
        width: 16px;
        height: 16px;
        &.down {
          transform: rotate(180deg);
        }
      }
    }
  }

  .dropdown-switch-portal {
    display: none;
    position: absolute;
    top: 89px;
    width: 100%;
    justify-content: center;
    z-index: 10;
    &.active {
      display: flex;
    }
    &__list {
      width: 220px;
      display: flex;
      padding: 10px 0px;
      flex-direction: column;
      align-items: flex-start;
      align-self: stretch;
      background: var(--Neutral-Off-White, #f5f5f5);
      &--item {
        display: flex;
        padding: 12px 15px;
        align-items: center;
        gap: 12px;
        align-self: stretch;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        color: #101010;
        cursor: pointer;
        mat-icon {
          margin-left: auto;
        }
        &:hover {
          background: rgba(0, 0, 0, .04);
        }
      }
    }
  }

  &:hover,
  &.active {
    width: 250px;
    transition: width 0.3s;

    .logo {
      p {
        opacity: 0.7;
      }
    }

    .nav-list {
      position: relative;
      hr {
        width: 80%;
      }

      li {
        a {
          span {
            display: block;
          }
        }
      }
    }

    .logout {
      @include profile-image;
    }
  }

  .nav-list {
    display: flex;
    flex-direction: column;
    justify-content: center;
    .main-nav {
      overflow-y: auto;
      overflow-x: hidden;
      height: calc(100% - 65px);
      &.scrollFirefox {
        scrollbar-width: none;
      }
      &::-webkit-scrollbar {
        width: 3px;
      }

      /* Track */
      &::-webkit-scrollbar-track {
        background: #f1f1f1;
      }

      /* Handle */
      &::-webkit-scrollbar-thumb {
        background: #888;
      }

      /* Handle on hover */
      &::-webkit-scrollbar-thumb:hover {
        background: #555;
      }

      //   @media screen and (min-width: 1280px) and (max-width: 1980px) {
      //          overflow-y: scroll;
      //          max-height: 250px;
      //          overflow-x: hidden;

      //          @include custom-scroll;
      //      }

      //      @media screen and (min-width: 767px) and (max-width: 1024px) {
      //          overflow-y: scroll;
      //          max-height: 250px;
      //          overflow-x: hidden;

      //          @include custom-scroll;
      //      }
    }
    hr {
      width: 70%;
      border: none;
      height: 1px;
      background-color: $border-color-11;
    }

    li {
      height: 63px;
      padding: 19px 25px;
      cursor: pointer;

      @media screen and (max-width: 1480px) {
        height: 55px;
        padding: 15px 25px 15px 28px;
      }

      a {
        transition: color 0.3s;
        span {
          display: none;
        }

        .nav-label {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;

          .nav-subtext {
            padding: 5px;
            background-color: $white-bg;
            border-radius: 3px;
            font-size: $fs13;
            color: $text-color-6;
            &.box-noti {
              padding: 3px 5px;
            }
          }
        }
      }

      &:hover,
      &.active {
        @include item-menu-hover;

        .box-noti {
          color: $text-color-white !important;
          background-color: $bg-color-8 !important;
        }
      }
    }
  }

  .logout {
    position: relative;
    bottom: 30px;
    padding: 19px;

    @media screen and (max-width: 1480px) {
      bottom: 10px;
    }

    &__info {
      &__profile-info {
        display: none;
        span {
          width: 100%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      &__profile-image {
        // TODO
        &__action {
          position: absolute;
          top: -105px;
          color: $text-color-6;
          background-color: $white-bg;
          padding: 10px 15px;
          width: 100%;

          span {
            cursor: pointer;
            display: flex;
            gap: 12px;
            padding: 12px 0;
            align-items: center;

            mat-icon {
              width: 18px;
              height: 18px;
            }
          }

          &::after {
            content: "";
            width: 15px;
            height: 15px;
            display: block;
            position: absolute;
            transform: rotate(-137deg);
            left: 12px;
            background-color: $white-bg;
          }
        }
      }
    }
  }
}

.main-menu-mobile {
  background-color: $bg-color-8;

  .header-menu {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    height: $height-header;

    &__left {
      display: flex;
      align-items: end;
      gap: 10px;

      span {
        margin-bottom: -5px;
        font-size: $fs10;
        font-weight: $fw600;
        color: $text-color-white;
        opacity: 0.5;
      }
    }

    &__right {
      display: flex;
      align-items: center;
      gap: 10px;

      &__menu-icon {
        transition: transform 0.3s ease;
        width: 26px;

        &--open {
          transform: rotate(0deg);
        }

        &--close {
          transform: rotate(90deg);
        }
      }
    }
  }

  .list-menu {
    display: none;

    &--active {
      display: block;
      border-top: 1px solid $border-color-11;

      background: inherit;
      position: absolute;
      z-index: 100;
      width: 100%;
      height: calc(100vh - $height-header);
    }

    .nav-list {
      margin: 0;
      li {
        padding: 19px 28px;
      }
    }
  }

  .logout {
    bottom: 0px;
    width: 100%;
    padding: 24px 28px;

    display: flex;
    justify-content: space-between;
    align-items: center;

    border-top: 1px solid $border-color-11;

    @include profile-image;

    .logout {
      &__add {
        width: 24px;
        height: 24px;
      }
    }
  }
}

// common
.main-menu,
.main-menu-mobile {
  .nav-list {
    list-style: none;
    padding: 0;
    height: calc(100% - 210px);

    li {
      &.notification {
        padding-left: 28px;
      }
      &.active-link {
        &.notification {
          background-color: transparent;
          img {
            filter: initial;
          }

          .nav-text {
            color: $text-color-2;
          }

          &:hover,
          &.active {
            @include item-menu-hover;

            .nav-subtext {
              color: $text-color-white;
              background-color: $bg-color-8;
            }
          }
        }

        @include item-menu-hover;
      }
      a {
        display: flex;
        align-items: center;
        color: $text-color-2;
        text-decoration: none;
        font-size: 16px;
        transition: color 0.3s;
        display: flex;
        gap: 10px;

        &.no-link {
          pointer-events: none;
        }
      }
    }
  }

  .logout {
    // position: absolute;

    &__info {
      &__profile-image {
        .avatar {
          width: 40px;
          height: 40px;
          background-color: $white-bg;
          border-radius: 50px;
          cursor: pointer;
        }
      }
    }
  }
}

.nav-item__bell {
  position: relative;
  min-width: 24px;

  .red-dot {
    position: absolute;
    right: 0;
    top: 0;
  }
}