
@mixin on-event($self: false) {
  @if $self {
    &,
    &:hover,
    &:active,
    &:focus {
      @content;
    }
  } @else {
    &:hover,
    &:active,
    &:focus {
      @content;
    }
  }
}

@mixin when-inside($context) {
  #{$context} & {
    @content;
  }
}

// Small devices
@mixin sm {
  @media (min-width: #{$screen-sm-min}) {
      @content;
  }
}
@mixin sm-max {
  @media (max-width: #{$screen-md-min}) {
      @content;
  }
}


// Medium devices
@mixin md {
  @media (min-width: #{$screen-md-min}) {
      @content;
  }
}
@mixin md-max {
  @media (max-width: #{$screen-lg-min}) {
      @content;
  }
}

// Large devices
@mixin lg {
  @media (min-width: #{$screen-lg-min}) {
      @content;
  }
}
@mixin lg-max {
  @media (max-width: #{$screen-xl-min}) {
      @content;
  }
}

// Extra large devices
@mixin xl {
  @media (min-width: #{$screen-xl-min}) {
      @content;
  }
}

@mixin flex-center {
  display: flex;
  align-items: center;
}

@mixin custom-scroll {
  ::-webkit-scrollbar {
    width: 7px;
    min-width: 7px;
    max-width: 7px;
    flex-basis: 7px;
    height: 7px;
  }

  ::-webkit-scrollbar-thumb {
    background-color: transparent;
    border-radius: 10px;
    background-clip: content-box;
  }
  ::-webkit-scrollbar-thumb:hover {
    background: #888;
  }
  @supports (scrollbar-color: #c2c2c2 transparent) {
    scrollbar-width: thin;
    scrollbar-color: #c2c2c2 transparent;
  }
  -ms-overflow-style: scrollbar;
}

@mixin value-tag {
  padding: 2px 8px;
  background-color: $bg-color-2;
  border: 1px solid rgba(16, 16, 16, 0.05);
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  letter-spacing: -0.42px;
  display: inline-block;
}

@mixin red-tag {
  color: $text-color-white;
  background-color: $bg-color-11;
  font-weight: 600;
}

@mixin grey-tag {
  color: $text-color-tag;
  background-color: $bg-color-2;
}

@mixin yellow-tag {
  background-color: $bg-color-12;
  font-weight: 600;
}

@mixin box-wrapper {
  background-color: $bg-color-3;
  padding: 25px;
  box-shadow: 0 4px 5px 5px #00000008;
}

@mixin detail-content($gapRow, $gapCol) {
  display: flex;
  flex-direction: column;
  gap: $gapRow;
  .detail-row {
    display: flex;
    gap: $gapCol;
    &.row {
      display: block !important;
    }
    .detail-element {
      flex: 1;
      .detail-label {
        font-size: $fs14;
        font-weight: $fw400;
        line-height: 20px;
        margin-bottom: 3px;
      }
      .detail-value {
        font-size: $fs16;
        font-weight: $fw600;
        line-height: 22px;
      }
    }
  }
}