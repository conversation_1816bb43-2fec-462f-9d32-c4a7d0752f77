.section-header {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 12px;
  flex: 0 1;

  &--content {
    margin-right: auto;
    display: flex;
    flex-direction: column;

    span {
      font-size: 22px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px;
      color: #101010;
    }
  }
}

.section-no-data {
  color: var(--Primary-Mid-Grey, #808080);
  text-align: center;
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 26px;
  display: flex;
  justify-content: center;
}

.tag-box {
  display: flex;
  padding: 8px;
  align-items: center;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 14px;
  letter-spacing: -0.42px;
}

.red-tag {
  span {
    color: $text-color-white !important;
    background-color: $bg-color-11 !important;
    font-weight: $fw600 !important;
  }
}

.yellow-tag {
  span {
    background-color: $bg-color-12 !important;
    font-weight: $fw600 !important;
  }
}

.green-tag {
  span {
    color: $text-color-white !important;
    background: var(--Support-Green, #190);
    font-weight: $fw600 !important;
  }
}

.grey-tag {
  span {
    color: #3a3a3a;
    background-color: #eee;
    border: 1px solid rgba(16, 16, 16, 0.05);
  }
}
