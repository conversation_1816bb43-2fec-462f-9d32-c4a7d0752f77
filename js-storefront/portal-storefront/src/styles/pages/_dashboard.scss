@mixin viewMoreLess {
    .view-more, .view-less {
        margin-top: 5px;
        color: #eb0a1e;
        cursor: pointer;
        font-weight: 600;

        display: flex;
        gap: 5px;
        align-items: center;

        width: fit-content;

        mat-icon {
            width: 16px;
            height: 16px;
            path {
                fill: #eb0a1e;  
            }
        }
    }

    .view-less {
        mat-icon { 
            transform: rotate(180deg);
            transition: transform 0.3s ease;
        }
    }
}

.ticket-by-type {
    position: relative;

    h2 {
        @include xl {
            align-items: center;
            position: absolute;
            left: 345px;
            top: 0;
            right: 0;
        }
    }

    .pie-chart__content {
        .legend {
            @include lg {
                padding-top: 45px;
            }
        }
    }
}

.dashboard-content {
    background: $bg-color-10;
    padding: 30px;
    margin-top: -30px;

    .dashboard-due,
    .dashboard-campaign,
    .dashboard-device-paired,
    .dashboard-non-device {
        .table-container {
            &::after {
                height: 75px;
            }
        }

        .content-table {
            max-height: 400px;

            @include viewMoreLess;
        }
    }

    .table-container {
        @include md {
            position: relative;
        
            &::after {
                position: absolute;
                right: 0;
                top: 0;
                width: 5px;
                height: 55px;
                content: '';
                background: #fff;
            }
        }
    }

    .content-table {
        @include md {
            max-height: 280px;
            overflow-y: auto;
            padding-right: 15px;
        }

        a {
            cursor: pointer;
        }

        &::-webkit-scrollbar {
            width: 3px;
            height: 5px;
        }
    
        &::-webkit-scrollbar-thumb {
            border-radius: 2px;
            background: #ccc;
        }
    
        &::-webkit-scrollbar-track {
            border-radius: 2px;
            background: #fff;
        }
    }

    table {
        border-collapse: collapse;
        width: 100%;

        @include md-max {
            display: block;
        }

        .action {
            color: $text-color-5;
            font-weight: $fw600;
            min-width: 90px;

            span {
                display: flex;
                align-items: center;
                cursor: pointer;
            }

            .mat-icon {
                margin-right: 5px;
                width: 16px;
                height: 16px;

                path {
                    fill: $text-color-5;
                }
            }
        }

        .header-mobile {
            font-weight: $fw600;

            @include md {
                display: none;
            }
        }

        .status {
            padding: 4px 8px;
            background-color: #eee;
            border: 1px solid rgba(16, 16, 16, .05);
            display: inline-block;
        }

        th {
            font-size: $fs16;
            font-weight: $fw600;
            position: sticky;
            top: 0;
            background: #fff;
            z-index: 2;

            @include md-max {
                display: none;
            }

            @include md {
                padding: 10px 15px;
                text-align: left;
            }
        }

        tr {
            @include md-max {
                display: block;
            }

            &:nth-child(even) {
                background: $bg-color-10;
            }
        }

        td {
            padding: 6px 20px;
            
            @include md-max {
                display: flex;
                justify-content: space-between;
            }

            @include md {
                padding: 12px 15px;
                text-align: left;
            }
        }
    }

    app-loading {
        position: absolute;
        width: auto;
        height: auto;
        left: -25px;
        right: -25px;
        top: -25px;
        bottom: -25px;
    }

    .subscription-block {
        margin-bottom: 20px;
    
        .dashboard-row {
            margin-bottom: 0;
        }
    }
    
    .vehicle-device-block {
        .pie-chart__content {
            .chart {
                @include lg {
                    max-width: 200px;
                    flex: 0 0 200px;
                }
            }

            .legend {
                @include lg {
                    max-width: calc(100% - 200px);
                    flex: 0 0 calc(100% - 200px);
                    padding-left: 40px;
                }
            }
        }
    }

    .ag-root-wrapper {
        border-radius: 0;
    }

    .ag-body-vertical-scroll,
    .ag-body-vertical-scroll-viewport,
    .ag-body-vertical-scroll-container {

        &::-webkit-scrollbar {
            width: 3px;
            height: 5px;
        }
    
        &::-webkit-scrollbar-thumb {
            border-radius: 2px;
            background: #ccc;
        }
    
        &::-webkit-scrollbar-track {
            border-radius: 2px;
            background: #fff;
        }
    }

    .map-content {
        @include lg {
            display: flex;
            margin-right: -25px;
        }

        @include md-max {
            padding-right: 15px;
        }

        .current-location__header {
            display: none;
        }

        app-current-location,
        .map-list {
            flex: 1;
        }

        .map-list {
            padding-right: 15px;
            max-height: 280px;
            overflow-y: auto;

            &::-webkit-scrollbar {
                width: 3px;
                height: 5px;
            }
        
            &::-webkit-scrollbar-thumb {
                border-radius: 2px;
                background: #ccc;
            }
        
            &::-webkit-scrollbar-track {
                border-radius: 2px;
                background: #fff;
            }

            .item {
                position: relative;
                font-size: $fs14;
                padding: 15px 90px 15px 15px;

                &:nth-child(odd) {
                    background: $bg-color-10;
                }

                p {
                    margin-bottom: 0;
                   &:first-child {
                        &:hover {
                            text-decoration: underline;
                            cursor: pointer;
                        }
                   }
                }

                strong {
                    font-weight: $fw600;
                }

                .view-btn {
                    position: absolute;
                    right: 15px;
                    top: 50%;
                    transform: translateY(-50%);
                    min-width: 80px;
                    font-size: $fs14;
                    color: $brand-primary;
                    display: flex;
                    align-items: center;
                    justify-content: flex-end;
                    padding: 0;
                    font-weight: 600;
                    cursor: pointer;
        
                    .mat-icon {
                        width: 16px;
                        margin-right: 5px;
        
                        path {
                            fill: $brand-primary;   
                        }
                    }
                }
            }
        }
    }

    .menu {
        display: flex;
        gap: 25px;
        padding: 0;
        margin-bottom: 20px;

        li {
            list-style: none;
            font-size: $fs18;
            font-weight: $fw600;
            color: $text-color-4;
            border-bottom: 3px solid transparent;
            padding-bottom: 10px;
            cursor: pointer;

            &.active {
                color: $text-color-6;
                border-color: $border-color-12;
            }
        }
    }

    &__box {
        @include box-wrapper;

        @include viewMoreLess();

        position: relative;
        height: 100%;

        @include md-max {
            padding-right: 10px !important;
        }

        .ag-body {
            max-height: 274px;
        }
    
        .ag-theme-quartz {
            padding-bottom: 0;
        }

        h2 {
            font-size: $fs22;
            font-weight: $fw600;
            margin-bottom: 20px;
    
            @include xl {
                display: flex;
                justify-content: space-between;
            }
    
            a {
                font-size: $fs14;
                color: $brand-primary;
                display: flex;
                align-items: center;
                cursor: pointer;
    
                .mat-icon {
                    width: 16px;
                    margin-right: 5px;
    
                    path {
                        fill: $brand-primary;   
                    }
                }

                &:hover {
                    text-decoration: unset;
                }
            }
        }

        &__view-list {
            padding-right: 25px;
        }
    }
}

.dashboard-row {
    margin-bottom: 20px;
    @include viewMoreLess;

    .view-more, .view-less {
        padding-left: 15px;
    }

    @include md {
        display: flex;
        gap: 20px;

        .col-6 {
            flex: 1;
            position: relative;
        }

        .col-9 {
            flex: 2;
            position: relative;
        }

        .col-3 {
            flex: 1;
            position: relative;
        }

        .col-7 {
            flex: 3;
        }

        .col-5 {
            flex: 2;
        }
    }

    .ag-body {
        max-height: 274px;
    }

    .flex-block {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }
}

.helpdesk-role {
    .pie-chart__content {
        max-width: 900px;
        margin: auto;
        
        .legend.col-2 .legend__item {
            @include lg {
                max-width: calc(100%/3);
                flex: 0 0 calc(100%/3);
            }
        }
    }
}

.tmsph-role {
    app-dashboard-approval-ticket {
        .dashboard-row {
            flex-direction: row-reverse;
        }
    }
}

.dashboard-subscriptions-activate,
.dashboard-device-paired,
.dashboard-device-to-paired,
.dashboard-non-device {
    margin-bottom: 20px;
}

.ticket,
.dashboard-campaign,
.dashboard-device,
.dashboard-device-paired,
.dashboard-device-to-paired,
.dashboard-due,
.dashboard-immo,
.dashboard-non-device,
.dashboard-remote,
.dashboard-sim-enabled,
.subscription-block,
.dashboard-subscriptions-activate,
.dashboard-ticket,
.dashboard-vehicle-device-by-sim {
    min-height: 250px;
}
