.vehicle-model-section {
    padding: 30px;
    &__header {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
    }
    &__input {
        .form-group__label {
            text-transform: capitalize;
        }
    }

    &__title {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 20px;
    }

    &__form {
        display: flex;
        flex-direction: column;
        gap: 20px;

        &-group {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 30px;
        }

        .vehicle-model-section__input {
            flex: 1;
        }

        .vehicle-model-section__button {
            height: 40px;
            border: 1px solid #101010;
            font-size: $fs12;
            font-weight: $fw600;
            min-width: 88px;
            text-transform: uppercase;
            background-color: transparent;
            color: #101010;
            border-radius: 0;
        }
    }

    &__button-group {
        display: flex;
        gap: 15px;
        justify-content: flex-end;

        .btn {
            min-width: 162px;
            min-height: 50px;
            text-transform: uppercase;
            color: $text-color-white;
            font-weight: $fw600;
            font-size: $fs14;
            padding: 5px 20px;
            border-radius: 0;
        }
    }
}

.action-buttons-cell-renderer {
    button {
        font-size: $fs14;
        font-weight: $fw600;
        color: $text-color-5;
        cursor: pointer;
        mat-icon {
            margin-right: 5px;
        }
    }
}