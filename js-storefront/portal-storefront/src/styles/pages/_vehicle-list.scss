app-vehicle-list {
    .header-section {
        background-color: $bg-color-9;
        padding: 20px 16px 100px;

        @include md {
            padding: 30px 30px 135px;
        }

        .title-page {
            color: $text-color-white;
            font-size: $fs38;
            margin: 0;

            padding-bottom: 25px;

            @include md {
                padding-bottom: 35px;
            }
        }

        .device-action {
            @include md {
                display: flex;
                justify-content: space-between;
            }

            &__tabname {
                display: flex;
                gap: 25px;
                padding: 5px 0 25px;

                font-size: $fs22;
                font-weight: $fw600;
                color: $text-color-4;

                span {
                    padding: 0 15px 12px;

                    &.active {
                        color: $text-color-white;
                        border-bottom: 3px solid $border-color-12;
                    }
                }
            }

            &__btn-action {
                display: flex;
                flex-direction: column;
                gap: 12px;

                @include md {
                    flex-direction: row;
                    gap: 15px;
                }

                .btn-secondary {
                    border: 1px solid $btn-bg-secondary-color;

                    &:hover {
                        border: 1px solid $btn-secondary-hover-background;
                    }
                }

                .btn-primary {
                    border: 1px solid $btn-bg-primary-color;
                }
            }
        }
    }

    .device-status-summary {
        padding: 20px 16px;
        display: flex;
        gap: 20px;
        overflow-x: auto;

        margin-top: -90px;

        display: grid;
        grid-template-columns: repeat(3, 1fr);

        @include md {
            padding: 40px 30px 20px;
            display: flex;
            margin-top: -135px;
        }

        gap: 16px;
        width: 100%;

        &__item {
            background-color: $white-bg;
            padding: 15px 21px 25px;
            box-shadow: 0px 4px 20px 0px #0000001a;

            display: flex;
            justify-content: space-between;
            gap: 20px;
            width: 300px;

            cursor: pointer;

            @include md {
                width: 100%;
            }

            &--active {
                border-bottom: 2px solid $border-color-14;
            }

            &__status {
                display: flex;
                flex-direction: column;
                gap: 10px;

                :nth-child(1) {
                    font-size: $fs38;
                    font-weight: $fw600;
                    line-height: 50px;
                }

                :nth-child(2) {
                    font-size: $fs14;
                }
            }

            &__icon {
                >img {
                    width: 33px;
                }

                >div {
                    display: none;
                }
            }

            &:hover {
                .device-status-summary__item__icon {
                    color: $text-color-5;
                    font-weight: $fw600;

                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    align-items: end;

                    >div {
                        display: flex;
                        align-items: center;
                        gap: 4px;
                    }
                }
            }
        }
    }

    .vehicle-list {
        padding: 0 30px 30px;

        .no-data {
            font-size: $fs18;
            font-weight: $fw600;
            text-align: center;
            padding: 20px;
        }

    }

    app-filter-vehicle {
        @include sm-max {
            background-color: $bg-color-10;
            padding: 20px 16px 0;
            display: block;

            margin-top: 10px;
        }

        form {
            @include md {
                padding: 30px 30px 0;
            }

            display: flex;
            gap: 20px;
            justify-content: space-around;
            flex-direction: column;

            @include md {
                flex-direction: row;

                >* {
                    width: 100%;
                }

                :first-child {
                    min-width: 30%;
                }

                app-dropdown-form-group {
                    @include lg {
                        min-width: 150px;
                    }
                }
            }

            .search {
                color: $text-color-6;
                border: 1px solid $border-color-14;
                padding: 11px 20px;
                width: 100%;

                @include md {
                    min-width: 90px;
                    max-width: 90px;
                    margin-top: 10px;
                }
            }

            .clear-all {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 5px;
                color: black;
                margin-bottom: 20px;
                border: none;

                &[disabled] {
                    color: $text-color-6;
                }
            }
        }

        .search-device {
            display: flex;
            justify-content: space-between;
            padding: 15px 0;

            span {
                font-size: $fs22;
                font-weight: $fw600;
            }
        }
    }
}