# PortalStorefront

This project is a web application developed using **Angular** version `17.3.11`. It includes server-side rendering (SSR), Angular Material for UI components, and supports modern web application development.

---

## **Prerequisites**
Ensure the following tools are installed on your system:
- [Node.js](https://nodejs.org/) (v18.x or higher recommended)
- [Angular CLI](https://angular.io/cli) (`17.3.11`)

---

## **Development Server**

1. Install dependencies:
   ```bash
   npm install
   ```

2. Start the development server:
   ```bash
   ng serve
   ```
##
or you can run npm run start or yarn start
##
for run dev
npm run start:dev

3. Open your browser and navigate to:
   ```
   http://localhost:4200/
   ```
   The application will automatically reload if any source files are modified.

---

## **Code Scaffolding**

Generate Angular components, directives, pipes, or other building blocks using the Angular CLI:
```bash
ng generate component component-name
```

Other scaffolding options:
```bash
ng generate directive|pipe|service|class|guard|interface|enum|module
```

---

## **Building the Project**

To create a production-ready build, run:
```bash
ng build
```

The output will be stored in the `dist/` directory.

---

## **Running Unit Tests**

Execute unit tests using [Karma](https://karma-runner.github.io):
```bash
ng test
```

This will generate a detailed test report in the console.

---

## **Running End-to-End Tests**

For end-to-end testing, first install a testing framework such as [Cypress](https://www.cypress.io/) or [Protractor](https://www.protractortest.org/):
```bash
npm install @cypress/schematic --save-dev
```

Then run:
```bash
ng e2e
```

---

## **Server-Side Rendering (SSR)**

The project is configured with SSR using Angular Universal:

1. Build the application for SSR:
   ```bash
   npm run build:ssr
   ```

2. Serve the application:
   ```bash
   npm run serve:ssr
   ```

---

## **Angular Material**

The project leverages Angular Material for UI components. To customize Material components:
- Modify styles in `src/styles.scss`.
- Refer to the [Angular Material Documentation](https://material.angular.io/) for guidelines.

---

## **Caching and Performance**

The project uses `.angular/cache` for caching build artifacts. If you face issues with cache locking:

Clear the cache:
```bash
ng cache clean
```

---

## **Environment Configuration**

Environment-specific settings are stored in:
```bash
src/environments/environment.ts
```

Update the `OCC_BASE_URL`, `STOREFRONT_BASE_URL`, or other environment variables as needed.

---

## **Linting and Code Quality**

Run the linter to ensure code quality:
```bash
ng lint
```

---

## **Further Help**

- For more information about Angular CLI, visit the [CLI Documentation](https://angular.io/cli).
- For issues or contributions, refer to the [official Angular repository](https://github.com/angular/angular-cli).

---
