package com.saidigital.tmp.services.claim.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "rebate_claim_configurations")
@Data
@ToString
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class RebateClaimConfiguration extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "gl_code", nullable = false)
    private String glCode;
    
    @Column(name = "internal_order_no", nullable = false)
    private String internalOrderNo;
    
    @Column(name = "cost_center", nullable = false)
    private String costCenter;
    
    @Column(name = "profit_center", nullable = false)
    private String profitCenter;
    
    @Column(name = "mktg_pic_name")
    private String mktgPICName;
    
    @Column(name = "mktg_pic_email")
    private String mktgPICEmail;
}
