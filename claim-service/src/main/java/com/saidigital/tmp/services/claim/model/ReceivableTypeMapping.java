package com.saidigital.tmp.services.claim.model;

import com.saidigital.tmp.services.claim.enums.ReceivableType;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "receivable_type_mappings")
@Data
@ToString
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ReceivableTypeMapping extends BaseEntity {

    @Id
    @Column(nullable = false, unique = true, updatable = false)
    private String id;
    
    @Enumerated(EnumType.STRING)
    private ReceivableType type;
    
    @Column(name = "document_type", nullable = false)
    private String documentType;
    
    @Column(name = "tax_code", nullable = false)
    private String taxCode;
    
    @Column(name = "wtax_code")
    private String wtaxCode;
    
    @Column(name = "mktg_pic_name")
    private String mktgPICName;
    
    @Column(name = "mktg_pic_email")
    private String mktgPICEmail;
}
