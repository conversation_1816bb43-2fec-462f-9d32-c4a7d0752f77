package com.saidigital.tmp.services.claim.model;

import com.saidigital.tmp.services.claim.enums.CDCCustomerType;
import com.saidigital.tmp.services.claim.enums.JobType;
import com.saidigital.tmp.services.claim.enums.OrderStatus;
import com.saidigital.tmp.services.claim.enums.PTOrderType;
import com.saidigital.tmp.services.claim.enums.PromotionTransactionStatus;
import com.saidigital.tmp.services.claim.enums.PromotionType;
import com.saidigital.tmp.services.claim.enums.converter.EnumListConverter;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Table(name = "promotion_transactions")
@Data
@ToString
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Entity
public class PromotionTransaction extends BaseEntity {
    
    @Id
    @Column(nullable = false, unique = true)
    private String id;
    
    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private PromotionTransactionStatus status;
    
    @Column(name = "rebate_status", nullable = false)
    @Enumerated(EnumType.STRING)
    private PromotionTransactionStatus rebateStatus;
    
    @Column(name = "post_billing_approval_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date postBillingApprovalDate;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "promotion_type")
    private PromotionType promotionType;
    
    @Column(name = "cx_promotion_code")
    private String cxPromotionCode;
    
    @Column(name = "dealer")
    private String dealer; // Simplified from TmpDealer
    
    @Column(name = "customer")
    private String customer; // Simplified from Customer
    
    @Column(name = "dbm_order_number")
    private String dbmOrderNumber;
    
    @Column(name = "billing_document")
    private String billingDocument;
    
    @Column(name = "invoice_creation_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date invoiceCreationDate;
    
    @Column(name = "vehicle_model")
    private String vehicleModel;
    
    @Column(name = "variant")
    private String variant;
    
    @Column(name = "model_year")
    private String modelYear;
    
    @Column(name = "mileage")
    private String mileage;
    
    @Column(name = "bill_to")
    private String billTo;
    
    @Column(name = "split")
    private Double split;

    @Column(name = "order_types")
    @Convert(converter = EnumListConverter.class)
    private List<PTOrderType> orderTypes;

    @Column(name = "job_types")
    @Convert(converter = EnumListConverter.class)
    private List<JobType> jobTypes;
    
    @Column(name = "release_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date releaseDate;
    
    @Column(name = "material_number")
    private String materialNumber;
    
    @Column(name = "material_description")
    private String materialDescription;
    
    @Column(name = "quantity")
    private Double quantity;
    
    @Column(name = "discount")
    private Double discount;
    
    @Column(name = "vehicle_guid")
    private String vehicleGuid;
    
    @Column(name = "post_billing_approval_doc_no")
    private String postBillingApprovalDocNo;
    
    @Column(name = "for_delivery_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date forDeliveryDate;
    
    @Column(name = "reservation_doc_no")
    private String reservationDocNo;
    
    @Column(name = "payment_method")
    private String paymentMethod;
    
    @Column(name = "customer_advisor")
    private String customerAdvisor;
    
    @Column(name = "vin")
    private String vin;
    
    @Column(name = "reservation_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date reservationDate;
    
    @Column(name = "delivery_doc_no")
    private String deliveryDocNo;
    
    @Column(name = "order_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date orderDate;
    
    @Column(name = "vehicle_delivery_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date vehicleDeliveryDate;
    
    @Column(name = "service_billing_amount")
    private Double serviceBillingAmount;
    
    @Column(name = "package_id")
    private String packageId;
    
    @Column(name = "order_reason")
    private String orderReason;
    
    @Column(name = "reservation_approval_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date reservationApprovalDate;
    
    @Column(name = "order_total")
    private Double orderTotal;
    
    @Column(name = "srp")
    private Double srp;
    
    @Column(name = "order_status", nullable = false)
    @Enumerated(EnumType.STRING)
    private OrderStatus orderStatus;
    
    @Column(name = "customer_group")
    private String customerGroup;
    
    @Column(name = "remarks")
    private String remarks;
    
    @Column(name = "customer_id")
    private String customerId;
    
    @Column(name = "customer_type")
    @Enumerated(EnumType.STRING)
    private CDCCustomerType customerType;
    
    @Column(name = "dbm_order_status")
    private String dbmOrderStatus;
    
    @Column(name = "post_billing_approval")
    private String postBillingApproval;
    
    @Column(name = "document_no")
    private String documentNo;
    
    @Column(name = "claimable_amount")
    private Double claimableAmount;
    
    @Column(name = "customer_name")
    private String customerName;
    
    @Column(name = "rebate")
    private Boolean rebate = false;
    
    @Column(name = "version")
    private Integer version = 0;
    
    @Column(name = "model_sales_code")
    private String modelSalesCode;
    
    @Column(name = "last_status_change_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastStatusChangeDate;
    
    @Column(name = "status_version")
    private Integer statusVersion = 1;
    
    @Column(name = "post_billing_approval_year")
    private Integer postBillingApprovalYear;
}
