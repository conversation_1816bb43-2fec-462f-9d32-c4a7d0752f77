package com.saidigital.tmp.services.claim.service.provider.impl;

import com.saidigital.tmp.services.claim.service.provider.EnumFormattingProvider;
import org.springframework.stereotype.Component;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Default implementation of EnumFormattingProvider
 * This maintains backward compatibility with the current static method logic
 * Can be extended with configuration or business rule engine integration
 */
@Component
public class DefaultEnumFormattingProvider implements EnumFormattingProvider {
    
    private static final Pattern WORD_BOUNDARY = Pattern.compile("\\b\\w");
    
    @Override
    public String formatEnumDisplayName(Enum<?> enumValue, String enumType) {
        if (enumValue == null) {
            return null;
        }
        
        // TODO: Add business rule engine or configuration-based formatting
        // For now, maintain backward compatibility with existing logic
        return formatEnumName(enumValue.name());
    }
    
    @Override
    public EnumDisplayData getEnumDisplayData(Enum<?> enumValue, String enumType) {
        if (enumValue == null) {
            return null;
        }
        
        return new EnumDisplayData(
            enumValue.name(),
            formatEnumDisplayName(enumValue, enumType),
            enumType,
            null // description can be added later
        );
    }
    
    private String formatEnumName(String enumName) {
        String lower = enumName.replace("_", " ").toLowerCase();
        return capitalizeWords(lower);
    }
    
    private String capitalizeWords(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }
        
        StringBuilder result = new StringBuilder();
        Matcher matcher = WORD_BOUNDARY.matcher(input);
        
        while (matcher.find()) {
            matcher.appendReplacement(result, matcher.group().toUpperCase());
        }
        matcher.appendTail(result);
        
        return result.toString();
    }
}
