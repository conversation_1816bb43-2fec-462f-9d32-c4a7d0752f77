package com.saidigital.tmp.services.claim.populator;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;

import java.util.Arrays;

/**
 * Abstract base class for configurable populators that provides common functionality.
 * 
 * @param <SOURCE> the source object type
 * @param <TARGET> the target object type
 */
public abstract class AbstractConfigurablePopulator<SOURCE, TARGET> implements ConfigurablePopulator<SOURCE, TARGET> {
    
    @Autowired
    private Environment environment;
    
    private final Class<SOURCE> sourceType;
    private final Class<TARGET> targetType;
    
    protected AbstractConfigurablePopulator(Class<SOURCE> sourceType, Class<TARGET> targetType) {
        this.sourceType = sourceType;
        this.targetType = targetType;
    }
    
    @Override
    public boolean supports(Class<?> sourceType, Class<?> targetType) {
        return this.sourceType.isAssignableFrom(sourceType) && 
               this.targetType.isAssignableFrom(targetType);
    }
    
    @Override
    public boolean isEnabled() {
        // Check if this populator is enabled via configuration
        String configKey = getConfigurationKey();
        return environment.getProperty(configKey, Boolean.class, true);
    }
    
    @Override
    public boolean shouldApply(PopulationContext context) {
        if (!isEnabled()) {
            return false;
        }
        
        // Check view type support
        String[] supportedViewTypes = getSupportedViewTypes();
        if (supportedViewTypes != null && supportedViewTypes.length > 0) {
            String currentViewType = context.getViewType();
            if (!Arrays.asList(supportedViewTypes).contains(currentViewType)) {
                return false;
            }
        }
        
        // Check user role requirements
        String[] requiredRoles = getRequiredUserRoles();
        if (requiredRoles != null && requiredRoles.length > 0) {
            String userRole = context.getUserRole().orElse(null);
            if (userRole == null || !Arrays.asList(requiredRoles).contains(userRole)) {
                return false;
            }
        }
        
        return true;
    }
    
    @Override
    public int getPriority() {
        // Check if priority is overridden in configuration
        String priorityKey = getConfigurationKey().replace(".enabled", ".priority");
        return environment.getProperty(priorityKey, Integer.class, getDefaultPriority());
    }
    
    /**
     * Get the default priority for this populator.
     * Override this method to provide a different default priority.
     * 
     * @return the default priority
     */
    protected int getDefaultPriority() {
        return 1000;
    }
    
    /**
     * Get the source type for this populator.
     * 
     * @return the source type
     */
    public Class<SOURCE> getSourceType() {
        return sourceType;
    }
    
    /**
     * Get the target type for this populator.
     * 
     * @return the target type
     */
    public Class<TARGET> getTargetType() {
        return targetType;
    }
    
    /**
     * Utility method to safely get a string value from source object.
     * 
     * @param value the value to convert
     * @return the string value or null if the input is null
     */
    protected String safeString(Object value) {
        return value != null ? value.toString() : null;
    }
    
    /**
     * Utility method to check if a string has text.
     * 
     * @param str the string to check
     * @return true if the string is not null and not empty
     */
    protected boolean hasText(String str) {
        return str != null && !str.trim().isEmpty();
    }
    
    /**
     * Utility method to handle population exceptions.
     * 
     * @param message the error message
     * @param cause the cause of the error
     * @throws PopulationException the wrapped exception
     */
    protected void handlePopulationError(String message, Throwable cause) throws PopulationException {
        throw new PopulationException(message, cause, this.getClass().getSimpleName(), sourceType, targetType);
    }
    
    /**
     * Utility method to handle population exceptions.
     * 
     * @param message the error message
     * @throws PopulationException the wrapped exception
     */
    protected void handlePopulationError(String message) throws PopulationException {
        throw new PopulationException(message, this.getClass().getSimpleName(), sourceType, targetType);
    }
}
