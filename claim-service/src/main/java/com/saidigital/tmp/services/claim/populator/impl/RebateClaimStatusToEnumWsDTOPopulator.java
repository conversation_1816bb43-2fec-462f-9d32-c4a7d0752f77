package com.saidigital.tmp.services.claim.populator.impl;

import com.saidigital.tmp.services.claim.dto.EnumWsDTO;
import com.saidigital.tmp.services.claim.enums.RebateClaimStatus;
import com.saidigital.tmp.services.claim.populator.AbstractConfigurablePopulator;
import com.saidigital.tmp.services.claim.populator.PopulationContext;
import com.saidigital.tmp.services.claim.populator.PopulatorComponent;
import com.saidigital.tmp.services.claim.service.provider.EnumFormattingProvider;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Populator that converts RebateClaimStatus enum to EnumWsDTO.
 * This replaces the static factory method approach with a flexible populator pattern.
 */
@PopulatorComponent(
    sourceType = RebateClaimStatus.class,
    targetType = EnumWsDTO.class,
    priority = 100,
    description = "Populates EnumWsDTO from RebateClaimStatus enum"
)
public class RebateClaimStatusToEnumWsDTOPopulator extends AbstractConfigurablePopulator<RebateClaimStatus, EnumWsDTO> {
    
    @Autowired
    private EnumFormattingProvider enumFormattingProvider;
    
    public RebateClaimStatusToEnumWsDTOPopulator() {
        super(RebateClaimStatus.class, EnumWsDTO.class);
    }
    
    @Override
    public void populate(RebateClaimStatus status, EnumWsDTO target, PopulationContext context) {
        if (status == null) {
            return;
        }
        
        try {
            EnumFormattingProvider.EnumDisplayData displayData = 
                enumFormattingProvider.getEnumDisplayData(status, "RebateClaimStatus");
            
            if (displayData == null) {
                handlePopulationError("No display data found for status: " + status);
                return;
            }
            
            // Always populate basic fields
            target.setCode(displayData.getCode());
            target.setName(displayData.getName());
            
            // Conditionally populate additional fields based on context
            if (shouldIncludeType(context)) {
                target.setType(displayData.getType());
            }
            
        } catch (Exception e) {
            handlePopulationError("Failed to populate enum data for status: " + status, e);
        }
    }
    
    @Override
    public String[] getSupportedViewTypes() {
        return new String[]{
            PopulationContext.VIEW_TYPE_LIST,
            PopulationContext.VIEW_TYPE_DETAIL,
            PopulationContext.VIEW_TYPE_SUMMARY,
            PopulationContext.VIEW_TYPE_EXPORT
        };
    }
    
    @Override
    protected int getDefaultPriority() {
        return 100;
    }
    
    /**
     * Determine if type information should be included
     */
    private boolean shouldIncludeType(PopulationContext context) {
        // Include type for detail view or when explicitly requested
        return context.isDetailView() || 
               context.hasAttribute("includeEnumType") ||
               PopulationContext.DETAIL_LEVEL_FULL.equals(context.getDetailLevel());
    }
}
