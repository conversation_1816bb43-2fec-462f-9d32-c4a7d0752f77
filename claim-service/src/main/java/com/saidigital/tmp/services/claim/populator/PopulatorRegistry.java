package com.saidigital.tmp.services.claim.populator;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * Registry for managing and discovering populators automatically.
 * This component scans for all populators and provides methods to retrieve them by type.
 */
@Component
public class PopulatorRegistry {
    
    @Autowired
    private ApplicationContext applicationContext;
    
    // Cache for populators by source and target type
    private final Map<String, List<Populator<?, ?>>> populatorCache = new ConcurrentHashMap<>();
    
    // All registered populators
    private final List<PopulatorInfo> allPopulators = new ArrayList<>();
    
    @PostConstruct
    public void initialize() {
        discoverPopulators();
    }
    
    /**
     * Get all populators that can convert from source type to target type.
     * 
     * @param sourceType the source type
     * @param targetType the target type
     * @return list of applicable populators
     */
    @SuppressWarnings("unchecked")
    public <S, T> List<Populator<S, T>> getPopulators(Class<S> sourceType, Class<T> targetType) {
        String key = createCacheKey(sourceType, targetType);
        
        return (List<Populator<S, T>>) populatorCache.computeIfAbsent(key, k -> {
            return allPopulators.stream()
                    .filter(info -> info.supportsConversion(sourceType, targetType))
                    .map(PopulatorInfo::getPopulator)
                    .sorted(Comparator.comparingInt(Populator::getPriority))
                    .collect(Collectors.toList());
        });
    }
    
    /**
     * Get all registered populators.
     * 
     * @return list of all populators
     */
    public List<PopulatorInfo> getAllPopulators() {
        return new ArrayList<>(allPopulators);
    }
    
    /**
     * Get populators by annotation configuration.
     * 
     * @param sourceType the source type
     * @param targetType the target type
     * @return list of populators with annotation configuration
     */
    public List<PopulatorInfo> getPopulatorsByAnnotation(Class<?> sourceType, Class<?> targetType) {
        return allPopulators.stream()
                .filter(info -> info.hasAnnotation())
                .filter(info -> info.supportsConversion(sourceType, targetType))
                .collect(Collectors.toList());
    }
    
    /**
     * Clear the cache and re-discover populators.
     */
    public void refresh() {
        populatorCache.clear();
        allPopulators.clear();
        discoverPopulators();
    }
    
    /**
     * Discover all populators in the application context.
     */
    private void discoverPopulators() {
        // Find all beans that implement Populator interface
        Map<String, Populator> populatorBeans = applicationContext.getBeansOfType(Populator.class);
        
        for (Map.Entry<String, Populator> entry : populatorBeans.entrySet()) {
            String beanName = entry.getKey();
            Populator<?, ?> populator = entry.getValue();
            
            PopulatorInfo info = createPopulatorInfo(beanName, populator);
            allPopulators.add(info);
        }
        
        // Sort by priority
        allPopulators.sort(Comparator.comparingInt(info -> info.getPopulator().getPriority()));
    }
    
    /**
     * Create populator info from a populator instance.
     */
    private PopulatorInfo createPopulatorInfo(String beanName, Populator<?, ?> populator) {
        PopulatorComponent annotation = AnnotationUtils.findAnnotation(populator.getClass(), PopulatorComponent.class);
        
        // Extract generic types from the populator
        TypeInfo typeInfo = extractGenericTypes(populator);
        
        return new PopulatorInfo(
                beanName,
                populator,
                typeInfo.sourceType,
                typeInfo.targetType,
                annotation
        );
    }
    
    /**
     * Extract generic types from populator interface.
     */
    private TypeInfo extractGenericTypes(Populator<?, ?> populator) {
        Class<?> populatorClass = populator.getClass();
        
        // Look for Populator interface in the class hierarchy
        Type[] genericInterfaces = populatorClass.getGenericInterfaces();
        for (Type genericInterface : genericInterfaces) {
            if (genericInterface instanceof ParameterizedType) {
                ParameterizedType parameterizedType = (ParameterizedType) genericInterface;
                if (parameterizedType.getRawType().equals(Populator.class) ||
                    parameterizedType.getRawType().equals(ConfigurablePopulator.class)) {
                    
                    Type[] typeArguments = parameterizedType.getActualTypeArguments();
                    if (typeArguments.length == 2) {
                        Class<?> sourceType = (Class<?>) typeArguments[0];
                        Class<?> targetType = (Class<?>) typeArguments[1];
                        return new TypeInfo(sourceType, targetType);
                    }
                }
            }
        }
        
        // Check superclass
        Type genericSuperclass = populatorClass.getGenericSuperclass();
        if (genericSuperclass instanceof ParameterizedType) {
            ParameterizedType parameterizedType = (ParameterizedType) genericSuperclass;
            Type[] typeArguments = parameterizedType.getActualTypeArguments();
            if (typeArguments.length == 2) {
                Class<?> sourceType = (Class<?>) typeArguments[0];
                Class<?> targetType = (Class<?>) typeArguments[1];
                return new TypeInfo(sourceType, targetType);
            }
        }
        
        // Fallback to Object types
        return new TypeInfo(Object.class, Object.class);
    }
    
    /**
     * Create cache key for source and target types.
     */
    private String createCacheKey(Class<?> sourceType, Class<?> targetType) {
        return sourceType.getName() + "->" + targetType.getName();
    }
    
    /**
     * Helper class to hold type information.
     */
    private static class TypeInfo {
        final Class<?> sourceType;
        final Class<?> targetType;
        
        TypeInfo(Class<?> sourceType, Class<?> targetType) {
            this.sourceType = sourceType;
            this.targetType = targetType;
        }
    }
    
    /**
     * Information about a registered populator.
     */
    public static class PopulatorInfo {
        private final String beanName;
        private final Populator<?, ?> populator;
        private final Class<?> sourceType;
        private final Class<?> targetType;
        private final PopulatorComponent annotation;
        
        public PopulatorInfo(String beanName, Populator<?, ?> populator, Class<?> sourceType, 
                           Class<?> targetType, PopulatorComponent annotation) {
            this.beanName = beanName;
            this.populator = populator;
            this.sourceType = sourceType;
            this.targetType = targetType;
            this.annotation = annotation;
        }
        
        public boolean supportsConversion(Class<?> fromType, Class<?> toType) {
            return sourceType.isAssignableFrom(fromType) && targetType.isAssignableFrom(toType);
        }
        
        public boolean hasAnnotation() {
            return annotation != null;
        }
        
        // Getters
        public String getBeanName() { return beanName; }
        public Populator<?, ?> getPopulator() { return populator; }
        public Class<?> getSourceType() { return sourceType; }
        public Class<?> getTargetType() { return targetType; }
        public PopulatorComponent getAnnotation() { return annotation; }
    }
}
