package com.saidigital.tmp.services.claim.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
public class RebateClaimListWsDTO extends BasicSearchWsDTO<ClaimListItemWsDTO> {

    public static RebateClaimListWsDTO createWithPagination(
            List<ClaimListItemWsDTO> items,
            int currentPage,
            int pageSize,
            long totalResults) {

        int totalPages = (int) Math.ceil((double) totalResults / pageSize);

        PageableWsDTO pagination = PageableWsDTO.builder()
                .currentPage(currentPage)
                .pageSize(pageSize)
                .totalPages(totalPages)
                .totalResults(totalResults)
                .build();

        return RebateClaimListWsDTO.builder()
                .type("rebateClaimListWsDTO")
                .pagination(pagination)
                .items(items)
                .build();
    }
}
