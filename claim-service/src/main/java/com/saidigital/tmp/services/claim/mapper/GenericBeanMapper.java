package com.saidigital.tmp.services.claim.mapper;

import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Generic bean mapper that uses reflection to copy properties between objects.
 * Supports field mapping, method mapping, and custom transformation rules.
 */
@Component
public class GenericBeanMapper {
    
    // Cache for reflection metadata to improve performance
    private final Map<Class<?>, Map<String, Field>> fieldCache = new ConcurrentHashMap<>();
    private final Map<Class<?>, Map<String, Method>> getterCache = new ConcurrentHashMap<>();
    private final Map<Class<?>, Map<String, Method>> setterCache = new ConcurrentHashMap<>();
    
    /**
     * Map properties from source to target using default mapping rules
     */
    public <S, T> void map(S source, T target) {
        map(source, target, MappingConfig.defaultConfig());
    }
    
    /**
     * Map properties from source to target using custom mapping configuration
     */
    public <S, T> void map(S source, T target, MappingConfig config) {
        if (source == null || target == null) {
            return;
        }
        
        Class<?> sourceClass = source.getClass();
        Class<?> targetClass = target.getClass();
        
        Map<String, Field> sourceFields = getFields(sourceClass);
        Map<String, Field> targetFields = getFields(targetClass);
        Map<String, Method> sourceGetters = getGetters(sourceClass);
        Map<String, Method> targetSetters = getSetters(targetClass);
        
        // Map fields based on configuration
        for (String sourceProperty : sourceFields.keySet()) {
            String targetProperty = config.getTargetProperty(sourceProperty);
            
            if (config.shouldIgnore(sourceProperty) || targetProperty == null) {
                continue;
            }
            
            try {
                Object value = getPropertyValue(source, sourceProperty, sourceFields, sourceGetters);
                if (value != null || config.shouldMapNullValues()) {
                    // Apply transformation if configured
                    value = config.transformValue(sourceProperty, value);
                    setPropertyValue(target, targetProperty, value, targetFields, targetSetters);
                }
            } catch (Exception e) {
                if (config.isStrictMode()) {
                    throw new MappingException("Failed to map property: " + sourceProperty, e);
                }
                // Log warning in non-strict mode
                System.err.println("Warning: Failed to map property " + sourceProperty + ": " + e.getMessage());
            }
        }
    }
    
    /**
     * Create a new instance of target type and map properties from source
     */
    public <S, T> T map(S source, Class<T> targetClass) {
        return map(source, targetClass, MappingConfig.defaultConfig());
    }
    
    /**
     * Create a new instance of target type and map properties from source with custom config
     */
    public <S, T> T map(S source, Class<T> targetClass, MappingConfig config) {
        if (source == null) {
            return null;
        }
        
        try {
            T target = createInstance(targetClass);
            map(source, target, config);
            return target;
        } catch (Exception e) {
            throw new MappingException("Failed to create and map instance of " + targetClass.getSimpleName(), e);
        }
    }
    
    /**
     * Map a list of objects
     */
    public <S, T> List<T> mapList(List<S> sourceList, Class<T> targetClass) {
        return mapList(sourceList, targetClass, MappingConfig.defaultConfig());
    }
    
    /**
     * Map a list of objects with custom configuration
     */
    public <S, T> List<T> mapList(List<S> sourceList, Class<T> targetClass, MappingConfig config) {
        if (sourceList == null) {
            return null;
        }
        
        List<T> targetList = new ArrayList<>();
        for (S source : sourceList) {
            T target = map(source, targetClass, config);
            if (target != null) {
                targetList.add(target);
            }
        }
        return targetList;
    }
    
    /**
     * Get property value from source object
     */
    private Object getPropertyValue(Object source, String propertyName, 
                                   Map<String, Field> fields, Map<String, Method> getters) throws Exception {
        // Try getter method first
        Method getter = getters.get(propertyName);
        if (getter != null) {
            return getter.invoke(source);
        }
        
        // Fall back to direct field access
        Field field = fields.get(propertyName);
        if (field != null) {
            field.setAccessible(true);
            return field.get(source);
        }
        
        return null;
    }
    
    /**
     * Set property value on target object
     */
    private void setPropertyValue(Object target, String propertyName, Object value,
                                 Map<String, Field> fields, Map<String, Method> setters) throws Exception {
        // Try setter method first
        Method setter = setters.get(propertyName);
        if (setter != null) {
            // Convert value if necessary
            Class<?> parameterType = setter.getParameterTypes()[0];
            Object convertedValue = convertValue(value, parameterType);
            setter.invoke(target, convertedValue);
            return;
        }
        
        // Fall back to direct field access
        Field field = fields.get(propertyName);
        if (field != null) {
            field.setAccessible(true);
            Object convertedValue = convertValue(value, field.getType());
            field.set(target, convertedValue);
        }
    }
    
    /**
     * Convert value to target type
     */
    private Object convertValue(Object value, Class<?> targetType) {
        if (value == null || targetType.isAssignableFrom(value.getClass())) {
            return value;
        }
        
        // Basic type conversions
        if (targetType == String.class) {
            return value.toString();
        }
        
        if (targetType == Integer.class || targetType == int.class) {
            if (value instanceof Number) {
                return ((Number) value).intValue();
            }
            if (value instanceof String) {
                return Integer.parseInt((String) value);
            }
        }
        
        if (targetType == Double.class || targetType == double.class) {
            if (value instanceof Number) {
                return ((Number) value).doubleValue();
            }
            if (value instanceof String) {
                return Double.parseDouble((String) value);
            }
        }
        
        // Add more conversions as needed
        return value;
    }
    
    /**
     * Create instance of target class
     */
    @SuppressWarnings("unchecked")
    private <T> T createInstance(Class<T> clazz) throws Exception {
        try {
            // Try no-arg constructor
            return clazz.getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            // Try builder pattern
            try {
                Method builderMethod = clazz.getDeclaredMethod("builder");
                Object builder = builderMethod.invoke(null);
                Method buildMethod = builder.getClass().getDeclaredMethod("build");
                return (T) buildMethod.invoke(builder);
            } catch (Exception builderException) {
                throw new Exception("Cannot create instance of " + clazz.getSimpleName() + 
                                  ". No accessible constructor or builder pattern found.", e);
            }
        }
    }
    
    /**
     * Get all fields of a class (cached)
     */
    private Map<String, Field> getFields(Class<?> clazz) {
        return fieldCache.computeIfAbsent(clazz, this::extractFields);
    }
    
    /**
     * Get all getter methods of a class (cached)
     */
    private Map<String, Method> getGetters(Class<?> clazz) {
        return getterCache.computeIfAbsent(clazz, this::extractGetters);
    }
    
    /**
     * Get all setter methods of a class (cached)
     */
    private Map<String, Method> getSetters(Class<?> clazz) {
        return setterCache.computeIfAbsent(clazz, this::extractSetters);
    }
    
    /**
     * Extract all fields from class hierarchy
     */
    private Map<String, Field> extractFields(Class<?> clazz) {
        Map<String, Field> fields = new HashMap<>();
        Class<?> currentClass = clazz;
        
        while (currentClass != null && currentClass != Object.class) {
            for (Field field : currentClass.getDeclaredFields()) {
                if (!field.isSynthetic() && !java.lang.reflect.Modifier.isStatic(field.getModifiers())) {
                    fields.put(field.getName(), field);
                }
            }
            currentClass = currentClass.getSuperclass();
        }
        
        return fields;
    }
    
    /**
     * Extract getter methods
     */
    private Map<String, Method> extractGetters(Class<?> clazz) {
        Map<String, Method> getters = new HashMap<>();
        
        for (Method method : clazz.getMethods()) {
            String methodName = method.getName();
            if (method.getParameterCount() == 0 && method.getReturnType() != void.class) {
                String propertyName = null;
                
                if (methodName.startsWith("get") && methodName.length() > 3) {
                    propertyName = decapitalize(methodName.substring(3));
                } else if (methodName.startsWith("is") && methodName.length() > 2 && 
                          (method.getReturnType() == boolean.class || method.getReturnType() == Boolean.class)) {
                    propertyName = decapitalize(methodName.substring(2));
                }
                
                if (propertyName != null) {
                    getters.put(propertyName, method);
                }
            }
        }
        
        return getters;
    }
    
    /**
     * Extract setter methods
     */
    private Map<String, Method> extractSetters(Class<?> clazz) {
        Map<String, Method> setters = new HashMap<>();
        
        for (Method method : clazz.getMethods()) {
            String methodName = method.getName();
            if (methodName.startsWith("set") && methodName.length() > 3 && 
                method.getParameterCount() == 1 && method.getReturnType() == void.class) {
                String propertyName = decapitalize(methodName.substring(3));
                setters.put(propertyName, method);
            }
        }
        
        return setters;
    }
    
    /**
     * Decapitalize first letter of string
     */
    private String decapitalize(String name) {
        if (name == null || name.length() == 0) {
            return name;
        }
        if (name.length() > 1 && Character.isUpperCase(name.charAt(1))) {
            return name;
        }
        return Character.toLowerCase(name.charAt(0)) + name.substring(1);
    }
}
