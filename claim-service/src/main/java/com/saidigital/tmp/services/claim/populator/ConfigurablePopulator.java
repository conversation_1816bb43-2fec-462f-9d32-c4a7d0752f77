package com.saidigital.tmp.services.claim.populator;

/**
 * Extended populator interface that supports context-aware population.
 * This allows populators to behave differently based on the population context.
 * 
 * @param <SOURCE> the source object type
 * @param <TARGET> the target object type
 */
public interface ConfigurablePopulator<SOURCE, TARGET> extends Populator<SOURCE, TARGET> {
    
    /**
     * Populate the target object with data from the source object using the given context.
     * 
     * @param source the source object to read data from
     * @param target the target object to populate
     * @param context the population context
     * @throws PopulationException if population fails
     */
    void populate(SOURCE source, TARGET target, PopulationContext context) throws PopulationException;
    
    /**
     * Default implementation that delegates to the context-less populate method.
     * Override this method to provide context-aware population.
     */
    @Override
    default void populate(SOURCE source, TARGET target) throws PopulationException {
        populate(source, target, new PopulationContext());
    }
    
    /**
     * Check if this populator should be applied for the given context.
     * This allows conditional population based on business rules.
     * 
     * @param context the population context
     * @return true if this populator should be applied
     */
    default boolean shouldApply(PopulationContext context) {
        return isEnabled();
    }
    
    /**
     * Check if this populator is enabled.
     * This can be controlled by configuration properties.
     * 
     * @return true if this populator is enabled
     */
    default boolean isEnabled() {
        return true;
    }
    
    /**
     * Get the configuration key for this populator.
     * This is used to enable/disable the populator via configuration.
     * 
     * @return the configuration key
     */
    default String getConfigurationKey() {
        return "tmp.claim.populators." + this.getClass().getSimpleName().toLowerCase() + ".enabled";
    }
    
    /**
     * Get the supported contexts for this populator.
     * Return null or empty array to support all contexts.
     * 
     * @return array of supported view types
     */
    default String[] getSupportedViewTypes() {
        return null; // Support all view types by default
    }
    
    /**
     * Get the required user roles for this populator.
     * Return null or empty array if no specific roles are required.
     * 
     * @return array of required user roles
     */
    default String[] getRequiredUserRoles() {
        return null; // No specific roles required by default
    }
}
