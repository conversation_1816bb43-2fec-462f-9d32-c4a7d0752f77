package com.saidigital.tmp.services.claim.service.provider;

/**
 * Interface for providing employee information
 */
public interface EmployeeDataProvider {
    
    /**
     * Get employee information by employee ID
     * 
     * @param employeeId the employee ID
     * @return employee data or null if not found
     */
    EmployeeData getEmployeeById(String employeeId);
    
    /**
     * Data class representing employee information
     */
    class EmployeeData {
        private final String uid;
        private final String name;
        private final String displayName;
        private final String firstName;
        private final String lastName;
        
        public EmployeeData(String uid, String name, String displayName, String firstName, String lastName) {
            this.uid = uid;
            this.name = name;
            this.displayName = displayName;
            this.firstName = firstName;
            this.lastName = lastName;
        }
        
        public String getUid() { return uid; }
        public String getName() { return name; }
        public String getDisplayName() { return displayName; }
        public String getFirstName() { return firstName; }
        public String getLastName() { return lastName; }
    }
}
