package com.saidigital.tmp.services.claim.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "claim_portal_status_changes")
@Data
@ToString
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ClaimPortalStatusChange extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    //TODO: Implement the Item class
    @Column(name = "target", nullable = false)
    private String target;
    
    @Column(name = "from_status")
    private String fromStatus;
    
    @Column(name = "to_status")
    private String toStatus;
    
    @Column(name = "created_by")
    private String createdBy;
    
    @Column(name = "claim_id")
    private String claimId;
    
    @Column(name = "action_id")
    private String actionID;
    
    @Column(name = "dealer_code")
    private String dealerCode;
    
    @Column(name = "dealer_name")
    private String dealerName;
    
    @Column(name = "status_aging")
    private Long statusAging;
    
    @Column(name = "post_billing_approval_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date postBillingApprovalDate;
    
    @Column(name = "version")
    private String version;
}
