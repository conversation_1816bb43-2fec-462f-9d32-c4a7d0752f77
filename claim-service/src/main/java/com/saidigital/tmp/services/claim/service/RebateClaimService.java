package com.saidigital.tmp.services.claim.service;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.saidigital.tmp.services.claim.dto.BasicDealerWsDTO;
import com.saidigital.tmp.services.claim.dto.BasicEmployeeDataWsDTO;
import com.saidigital.tmp.services.claim.dto.ClaimListItemWsDTO;
import com.saidigital.tmp.services.claim.dto.EnumWsDTO;
import com.saidigital.tmp.services.claim.dto.PageableWsDTO;
import com.saidigital.tmp.services.claim.dto.PriceDataWsDTO;
import com.saidigital.tmp.services.claim.dto.RebateClaimListWsDTO;
import com.saidigital.tmp.services.claim.dto.RebateClaimResponseDTO;
import com.saidigital.tmp.services.claim.enums.RebateClaimStatus;
import com.saidigital.tmp.services.claim.model.RebateClaim;
import com.saidigital.tmp.services.claim.repository.RebateClaimRepository;

@Service
@Transactional(readOnly = true)
public class RebateClaimService {

    @Autowired
    private RebateClaimRepository rebateClaimRepository;

    /**
     * Get all rebate claims with pagination and filtering
     *
     * @param dealer Optional dealer filter
     * @param status Optional status filter
     * @param pagination Pagination parameters
     * @return Paginated rebate claims response
     */
    public RebateClaimListWsDTO getAllRebateClaims(
            String dealer,
            String status,
            PageableWsDTO pagination) {

        // Parse status parameter
        RebateClaimStatus rebateClaimStatus = null;
        if (StringUtils.hasText(status)) {
            try {
                rebateClaimStatus = RebateClaimStatus.valueOf(status.toUpperCase());
            } catch (IllegalArgumentException e) {
                // Invalid status, will be ignored in query
            }
        }

        // Create pageable with sorting
        Pageable pageable = createPageable(pagination.getCurrentPage(), pagination.getPageSize(), pagination.getSort());

        // Execute query with filters
        Page<RebateClaim> claimsPage = rebateClaimRepository.findAllRebateClaimsWithFilters(
                dealer, rebateClaimStatus, pageable);

        // Convert to DTOs
        List<ClaimListItemWsDTO> claimDTOs = claimsPage.getContent()
                .stream()
                .map(this::convertToClaimListItemWsDTO)
                .collect(Collectors.toList());

        // Create response
        return RebateClaimListWsDTO.createWithPagination(
                claimDTOs,
                claimsPage.getNumber(),
                claimsPage.getSize(),
                claimsPage.getTotalElements(),
                pagination.getSort());
    }

    /**
     * Convert RebateClaim entity to DTO
     */
    private RebateClaimResponseDTO convertToDTO(RebateClaim claim) {
        return RebateClaimResponseDTO.builder()
                .code(claim.getCode())
                .status(RebateClaimResponseDTO.StatusDTO.fromEnum(claim.getStatus()))
                .claimableAmount(claim.getClaimableAmount())
                .dealer(claim.getDealer())
                .createdDate(claim.getCreatedTime())
                .lastUpdatedDate(claim.getUpdatedTime())
                .lastUpdatedBy(claim.getUpdatedBy())
                .referenceNumber(claim.getReferenceNumber())
                .dmcmReference(claim.getDmcmReference())
                .sbReference(claim.getSbReference())
                .sapDocNo(claim.getSapDocNo())
                .clearingDocNo(claim.getClearingDocNo())
                .orNumber(claim.getOrNumber())
                .exported(claim.isExported())
                .requiredDoc(claim.getRequiredDoc())
                .generalConfigVersion(claim.getGeneralConfigVersion())
                .build();
    }

    /**
     * Convert RebateClaim entity to ClaimListItemWsDTO with complex nested DTOs
     */
    private ClaimListItemWsDTO convertToClaimListItemWsDTO(RebateClaim claim) {
        return ClaimListItemWsDTO.builder()
                .id(claim.getCode())
                .referenceNumber(claim.getReferenceNumber())
                .dealer(BasicDealerWsDTO.fromDealerCode(claim.getDealer()))
                .claimableAmount(PriceDataWsDTO.fromDouble(claim.getClaimableAmount()))
                .status(EnumWsDTO.fromRebateClaimStatus(claim.getStatus()))
                .updatedBy(BasicEmployeeDataWsDTO.fromEmployeeId(claim.getUpdatedBy()))
                .createDate(claim.getCreatedTime())
                .updatedDate(claim.getUpdatedTime())
                // Backward compatibility fields
                .createdDate(claim.getCreatedTime())
                .lastUpdatedDate(claim.getUpdatedTime())
                .lastUpdatedBy(claim.getUpdatedBy())
                .dmcmReference(claim.getDmcmReference())
                .sbReference(claim.getSbReference())
                .sapDocNo(claim.getSapDocNo())
                .clearingDocNo(claim.getClearingDocNo())
                .orNumber(claim.getOrNumber())
                .exported(claim.isExported())
                .requiredDoc(claim.getRequiredDoc())
                .generalConfigVersion(claim.getGeneralConfigVersion())
                .build();
    }

    /**
     * Create Pageable object with proper sorting
     */
    private Pageable createPageable(int page, int size, String sortCode) {
        // Default sort by creation time descending (matching legacy behavior)
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");

        // Parse custom sort if provided
        if (StringUtils.hasText(sortCode)) {
            String[] sortParts = sortCode.split(",");
            if (sortParts.length == 2) {
                String property = sortParts[0].trim();
                String direction = sortParts[1].trim();
                
                Sort.Direction sortDirection = "asc".equalsIgnoreCase(direction) 
                        ? Sort.Direction.ASC 
                        : Sort.Direction.DESC;
                
                sort = Sort.by(sortDirection, property);
            }
        }

        return PageRequest.of(page, size, sort);
    }

    /**
     * Get rebate claim by code
     */
    public ClaimListItemWsDTO getRebateClaimByCode(String code) {
        return rebateClaimRepository.findById(code)
                .map(this::convertToClaimListItemWsDTO)
                .orElse(null);
    }

    /**
     * Get total count of rebate claims with filters
     */
    public long getTotalRebateClaimsCount(String dealer, String status) {
        RebateClaimStatus rebateClaimStatus = null;
        if (StringUtils.hasText(status)) {
            try {
                rebateClaimStatus = RebateClaimStatus.valueOf(status.toUpperCase());
            } catch (IllegalArgumentException e) {
                // Invalid status, will be ignored in query
            }
        }

        return rebateClaimRepository.countRebateClaimsWithFilters(dealer, rebateClaimStatus);
    }
}
