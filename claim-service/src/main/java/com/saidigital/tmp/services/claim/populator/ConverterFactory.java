package com.saidigital.tmp.services.claim.populator;

import com.saidigital.tmp.services.claim.populator.impl.DefaultConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * Factory for creating and caching converters.
 * This ensures that converters are reused and properly configured.
 */
@Component
public class ConverterFactory {
    
    @Autowired
    private PopulatorRegistry populatorRegistry;
    
    // Cache for converters by source and target type
    private final ConcurrentMap<String, Converter<?, ?>> converterCache = new ConcurrentHashMap<>();
    
    /**
     * Get or create a converter for the specified source and target types.
     * 
     * @param sourceType the source type
     * @param targetType the target type
     * @param <S> the source type parameter
     * @param <T> the target type parameter
     * @return the converter instance
     */
    @SuppressWarnings("unchecked")
    public <S, T> Converter<S, T> getConverter(Class<S> sourceType, Class<T> targetType) {
        String key = createCacheKey(sourceType, targetType);
        
        return (Converter<S, T>) converterCache.computeIfAbsent(key, k -> {
            DefaultConverter<S, T> converter = new DefaultConverter<>(sourceType, targetType);
            // Manually inject dependencies since this is not a Spring-managed bean
            injectDependencies(converter);
            return converter;
        });
    }
    
    /**
     * Create a new converter instance without caching.
     * Useful for testing or when you need a fresh instance.
     * 
     * @param sourceType the source type
     * @param targetType the target type
     * @param <S> the source type parameter
     * @param <T> the target type parameter
     * @return a new converter instance
     */
    public <S, T> Converter<S, T> createConverter(Class<S> sourceType, Class<T> targetType) {
        DefaultConverter<S, T> converter = new DefaultConverter<>(sourceType, targetType);
        injectDependencies(converter);
        return converter;
    }
    
    /**
     * Clear the converter cache.
     * Useful when populators are dynamically added or removed.
     */
    public void clearCache() {
        converterCache.clear();
    }
    
    /**
     * Get the number of cached converters.
     * 
     * @return the cache size
     */
    public int getCacheSize() {
        return converterCache.size();
    }
    
    /**
     * Check if a converter exists in the cache.
     * 
     * @param sourceType the source type
     * @param targetType the target type
     * @return true if the converter is cached
     */
    public boolean isCached(Class<?> sourceType, Class<?> targetType) {
        String key = createCacheKey(sourceType, targetType);
        return converterCache.containsKey(key);
    }
    
    /**
     * Create cache key for source and target types.
     * 
     * @param sourceType the source type
     * @param targetType the target type
     * @return the cache key
     */
    private String createCacheKey(Class<?> sourceType, Class<?> targetType) {
        return sourceType.getName() + "->" + targetType.getName();
    }
    
    /**
     * Manually inject dependencies into the converter.
     * This is needed because converters are created programmatically.
     * 
     * @param converter the converter to inject dependencies into
     */
    private void injectDependencies(DefaultConverter<?, ?> converter) {
        try {
            // Use reflection to set the populatorRegistry field
            java.lang.reflect.Field field = DefaultConverter.class.getDeclaredField("populatorRegistry");
            field.setAccessible(true);
            field.set(converter, populatorRegistry);
        } catch (Exception e) {
            throw new RuntimeException("Failed to inject dependencies into converter", e);
        }
    }
}
