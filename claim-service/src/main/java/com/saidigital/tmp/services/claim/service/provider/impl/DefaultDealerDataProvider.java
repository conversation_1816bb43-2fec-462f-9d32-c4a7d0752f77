package com.saidigital.tmp.services.claim.service.provider.impl;

import com.saidigital.tmp.services.claim.service.provider.DealerDataProvider;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * Default implementation of DealerDataProvider
 * This maintains backward compatibility with the current static method logic
 * Can be replaced with actual dealer service integration
 */
@Component
public class DefaultDealerDataProvider implements DealerDataProvider {
    
    @Override
    public DealerData getDealerByCode(String dealerCode) {
        if (!StringUtils.hasText(dealerCode)) {
            return null;
        }
        
        // TODO: Replace with actual dealer service call
        // For now, maintain backward compatibility with existing logic
        return new DealerData(
            dealerCode,
            dealerCode, // name = code for backward compatibility
            formatDealerDisplayName(dealerCode),
            dealerCode // uid = code for backward compatibility
        );
    }
    
    private String formatDealerDisplayName(String dealerCode) {
        // This should ideally come from a dealer service or lookup table
        // For now, we'll format it as "Dealer {code}" to maintain backward compatibility
        return "Dealer " + dealerCode;
    }
}
