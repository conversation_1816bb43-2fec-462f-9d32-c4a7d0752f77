package com.saidigital.tmp.services.claim.model;

import java.util.Date;

import com.saidigital.tmp.services.claim.enums.TmpOnlinePaymentTransactionStatus;
import com.saidigital.tmp.services.claim.enums.TmpOrderType;
import com.saidigital.tmp.services.claim.enums.TmpPaymentTransactionStatus;
import com.saidigital.tmp.services.claim.enums.TmpPaymentTransactionType;
import com.saidigital.tmp.services.claim.enums.TmpPaymentType;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "payment_transactions")
@Data
@ToString
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class PaymentTransaction extends BaseEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "transaction_type", nullable = false)
    @Enumerated(EnumType.STRING)
    private TmpPaymentTransactionType transactionType;

    //TODO: Implement the Customer class
    @Column(name = "customer", nullable = false)
    private String customer;
    
    @Column(name = "payment_type", nullable = false)
    @Enumerated(EnumType.STRING)
    private TmpPaymentType paymentType;
    
    @Column(name = "order_type", nullable = false)
    @Enumerated(EnumType.STRING)
    private TmpOrderType orderType;
    
    @Column(name = "payment_date", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date paymentDate;
    
    @Column(name = "confirming_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date confirmingDate;
    
    @Column(name = "payment_status", nullable = false)
    @Enumerated(EnumType.STRING)
    private TmpPaymentTransactionStatus paymentStatus;
    
    @Column(name = "online_payment_status")
    @Enumerated(EnumType.STRING)
    private TmpOnlinePaymentTransactionStatus onlinePaymentStatus;
    
    @Column(name = "clearing_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date clearingDate;
    
    @Column(name = "receipt_no")
    private String receiptNo;
    
    @Column(name = "dbm_order_no")
    private String dbmOrderNo;
    
    @Column(name = "addition_information")
    private String additionInformation;

    //TODO: Implement the TmpDealer class
    @Column(name = "dealer", nullable = false)
    private String dealer;
    
    @Column(name = "amount", nullable = false)
    private Double amount;
    
    @Column(name = "plate_chassis_num")
    private String plateChasissNum;
    
    @Column(name = "calling_time")
    private Integer callingTime;
    
    @Column(name = "date_to_pay")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dateToPay;
}
