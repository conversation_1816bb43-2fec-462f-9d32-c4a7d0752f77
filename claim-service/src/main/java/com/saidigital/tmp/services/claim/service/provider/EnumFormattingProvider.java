package com.saidigital.tmp.services.claim.service.provider;

/**
 * Interface for providing enum formatting and display information
 */
public interface EnumFormattingProvider {
    
    /**
     * Format enum value for display
     * 
     * @param enumValue the enum value
     * @param enumType the enum type name
     * @return formatted display name
     */
    String formatEnumDisplayName(Enum<?> enumValue, String enumType);
    
    /**
     * Get enum display information
     * 
     * @param enumValue the enum value
     * @param enumType the enum type name
     * @return enum display data
     */
    EnumDisplayData getEnumDisplayData(Enum<?> enumValue, String enumType);
    
    /**
     * Data class representing enum display information
     */
    class EnumDisplayData {
        private final String code;
        private final String name;
        private final String type;
        private final String description;
        
        public EnumDisplayData(String code, String name, String type, String description) {
            this.code = code;
            this.name = name;
            this.type = type;
            this.description = description;
        }
        
        public String getCode() { return code; }
        public String getName() { return name; }
        public String getType() { return type; }
        public String getDescription() { return description; }
    }
}
