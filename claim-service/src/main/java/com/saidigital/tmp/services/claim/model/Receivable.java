package com.saidigital.tmp.services.claim.model;

import com.saidigital.tmp.services.claim.enums.ReceivableStatus;
import com.saidigital.tmp.services.claim.enums.ReceivableType;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Inheritance;
import jakarta.persistence.InheritanceType;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@Table(name = "receivables")
@Inheritance(strategy = InheritanceType.TABLE_PER_CLASS)
@Data
@ToString
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Entity
public class Receivable extends BaseEntity {
    @Id
    @Column(nullable = false, unique = true)
    private String id;
    
    @Enumerated(EnumType.STRING)
    private ReceivableStatus status;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private ReceivableType type;
    
    @Column(name = "dealer", nullable = false)
    private String dealer;
    
    @Column(name = "created_by")
    private String createdBy;
    
    @Column(name = "updated_by")
    private String updatedBy;
    
    @Column(name = "reject_reason")
    private String rejectReason;
    
    // TODO: Handle TmpUnawareMediaList type
    // private List<Media> documents;
    
    @Column(name = "remarks")
    private String remarks;
}
