package com.saidigital.tmp.services.claim.populator.impl;

import com.saidigital.tmp.services.claim.dto.BasicEmployeeDataWsDTO;
import com.saidigital.tmp.services.claim.populator.AbstractConfigurablePopulator;
import com.saidigital.tmp.services.claim.populator.PopulationContext;
import com.saidigital.tmp.services.claim.populator.PopulatorComponent;
import com.saidigital.tmp.services.claim.service.provider.EmployeeDataProvider;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Populator that converts an employee ID string to BasicEmployeeDataWsDTO.
 * This replaces the static factory method approach with a flexible populator pattern.
 */
@PopulatorComponent(
    sourceType = String.class,
    targetType = BasicEmployeeDataWsDTO.class,
    priority = 100,
    description = "Populates BasicEmployeeDataWsDTO from employee ID string"
)
public class EmployeeIdToBasicEmployeeDataWsDTOPopulator extends AbstractConfigurablePopulator<String, BasicEmployeeDataWsDTO> {
    
    @Autowired
    private EmployeeDataProvider employeeDataProvider;
    
    public EmployeeIdToBasicEmployeeDataWsDTOPopulator() {
        super(String.class, BasicEmployeeDataWsDTO.class);
    }
    
    @Override
    public void populate(String employeeId, BasicEmployeeDataWsDTO target, PopulationContext context) {
        if (!hasText(employeeId)) {
            return;
        }
        
        try {
            EmployeeDataProvider.EmployeeData employeeData = employeeDataProvider.getEmployeeById(employeeId);
            if (employeeData == null) {
                handlePopulationError("No employee data found for ID: " + employeeId);
                return;
            }
            
            // Always populate basic fields
            target.setUid(employeeData.getUid());
            target.setName(employeeData.getName());
            
            // Conditionally populate additional fields based on context and user permissions
            if (shouldIncludeDetailedInfo(context)) {
                target.setDisplayName(employeeData.getDisplayName());
                target.setFirstName(employeeData.getFirstName());
                target.setLastName(employeeData.getLastName());
            }
            
        } catch (Exception e) {
            handlePopulationError("Failed to populate employee data for ID: " + employeeId, e);
        }
    }
    
    @Override
    public String[] getSupportedViewTypes() {
        return new String[]{
            PopulationContext.VIEW_TYPE_LIST,
            PopulationContext.VIEW_TYPE_DETAIL,
            PopulationContext.VIEW_TYPE_SUMMARY
        };
    }
    
    @Override
    public String[] getRequiredUserRoles() {
        // No specific roles required for basic employee info
        return null;
    }
    
    @Override
    protected int getDefaultPriority() {
        return 100;
    }
    
    /**
     * Determine if detailed information should be included based on context
     */
    private boolean shouldIncludeDetailedInfo(PopulationContext context) {
        // Include detailed info for detail view or when explicitly requested
        return context.isDetailView() || 
               PopulationContext.DETAIL_LEVEL_FULL.equals(context.getDetailLevel());
    }
}
