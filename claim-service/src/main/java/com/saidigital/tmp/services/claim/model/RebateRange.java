package com.saidigital.tmp.services.claim.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "rebate_ranges")
@Data
@ToString
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class RebateRange extends BaseEntity {

    @Id
    @Column(name = "name", nullable = false, unique = true)
    private String name;

    // "from" is a reserved word in SQL
    @Column(name = "from_value", nullable = false)
    private Integer from;

    // "to" is a reserved word in SQL
    @Column(name = "to_value", nullable = false)
    private Integer to;
    
    @Column(name = "incentive", nullable = false)
    private Integer incentive;
}
