package com.saidigital.tmp.services.claim.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.saidigital.tmp.services.claim.enums.ReceivableClaimStatus;
import com.saidigital.tmp.services.claim.model.ReceivableClaim;

@Repository
public interface ReceivableClaimRepository extends JpaRepository<ReceivableClaim, String> {
	List<ReceivableClaim> findByStatus(ReceivableClaimStatus status);
}
