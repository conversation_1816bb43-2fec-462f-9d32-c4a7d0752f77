package com.saidigital.tmp.services.claim.dto;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClaimListItemWsDTO {

    @JsonProperty("id")
    private String id;

    @JsonProperty("dealer")
    private BasicDealerWsDTO dealer;

    @JsonProperty("claimableAmount")
    private PriceDataWsDTO claimableAmount;

    @JsonProperty("status")
    private EnumWsDTO status;

    @JsonProperty("updatedBy")
    private BasicEmployeeDataWsDTO updatedBy;

    @JsonProperty("createDate")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ssZ", timezone = "UTC")
    private Date createDate;

    @JsonProperty("updatedDate")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ssZ", timezone = "UTC")
    private Date updatedDate;

    @JsonProperty("referenceNumber")
    private String referenceNumber;

}
