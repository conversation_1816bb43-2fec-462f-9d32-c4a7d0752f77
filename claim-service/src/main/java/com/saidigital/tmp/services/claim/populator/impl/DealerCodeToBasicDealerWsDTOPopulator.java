package com.saidigital.tmp.services.claim.populator.impl;

import com.saidigital.tmp.services.claim.dto.BasicDealerWsDTO;
import com.saidigital.tmp.services.claim.populator.AbstractConfigurablePopulator;
import com.saidigital.tmp.services.claim.populator.PopulationContext;
import com.saidigital.tmp.services.claim.populator.PopulatorComponent;
import com.saidigital.tmp.services.claim.service.provider.DealerDataProvider;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Populator that converts a dealer code string to BasicDealerWsDTO.
 * This replaces the static factory method approach with a flexible populator pattern.
 */
@PopulatorComponent(
    sourceType = String.class,
    targetType = BasicDealerWsDTO.class,
    priority = 100,
    description = "Populates BasicDealerWsDTO from dealer code string"
)
public class DealerCodeToBasicDealerWsDTOPopulator extends AbstractConfigurablePopulator<String, BasicDealerWsDTO> {
    
    @Autowired
    private DealerDataProvider dealerDataProvider;
    
    public DealerCodeToBasicDealerWsDTOPopulator() {
        super(String.class, BasicDealerWsDTO.class);
    }
    
    @Override
    public void populate(String dealerCode, BasicDealerWsDTO target, PopulationContext context) {
        if (!hasText(dealerCode)) {
            return;
        }
        
        try {
            DealerDataProvider.DealerData dealerData = dealerDataProvider.getDealerByCode(dealerCode);
            if (dealerData == null) {
                handlePopulationError("No dealer data found for code: " + dealerCode);
                return;
            }
            
            // Populate basic fields
            target.setName(dealerData.getName());
            target.setDisplayName(dealerData.getDisplayName());
            
            // Conditionally populate additional fields based on context
            if (shouldIncludeDetailedInfo(context)) {
                target.setCode(dealerData.getCode());
                target.setUid(dealerData.getUid());
            }
            
        } catch (Exception e) {
            handlePopulationError("Failed to populate dealer data for code: " + dealerCode, e);
        }
    }
    
    @Override
    public String[] getSupportedViewTypes() {
        return new String[]{
            PopulationContext.VIEW_TYPE_LIST,
            PopulationContext.VIEW_TYPE_DETAIL,
            PopulationContext.VIEW_TYPE_SUMMARY
        };
    }
    
    @Override
    protected int getDefaultPriority() {
        return 100;
    }
    
    /**
     * Determine if detailed information should be included based on context
     */
    private boolean shouldIncludeDetailedInfo(PopulationContext context) {
        // Include detailed info for detail view or when explicitly requested
        return context.isDetailView() || 
               PopulationContext.DETAIL_LEVEL_FULL.equals(context.getDetailLevel());
    }
}
