package com.saidigital.tmp.services.claim.model;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@Table(name = "returned_unit_receivables")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Entity
public class ReturnedUnitReceivable extends Receivable {

    //TODO: Implement the Customer class
    @Column(name = "customer", nullable = false)
    private String customer;

    //TODO: Implement the VehicleProduct class
    @Column(name = "model")
    private String model;
    
    @Column(name = "cs_number", nullable = false)
    private String csNumber;
    
    @Column(name = "amount_unit", nullable = false)
    private Double amountUnit;
    
    @Column(name = "invoice_date", nullable = false)
    private Date invoiceDate;

    //TODO: Implement the TmpCancelReason class
    @Column(name = "reason", nullable = false)
    private String reason;
    
    @Column(name = "invoice_cancellation_date", nullable = false)
    private Date invoiceCancellationDate;
    
    @Column(name = "deallocation_date", nullable = false)
    private Date deallocationDate;
    
    @Column(name = "returned_date", nullable = false)
    private Date returnedDate;
    
    @Column(name = "received_date", nullable = false)
    private Date receivedDate;
    
    @Column(name = "vehicle_sale_invoice", nullable = false)
    private String vehicleSaleInvoice;
    
    @Column(name = "vehicle_delivery_note", nullable = false)
    private String vehicleDeliveryNote;
    
    @Column(name = "debit_memo", nullable = false)
    private String debitMemo;
    
    @Column(name = "debit_memo_amount", nullable = false)
    private Double debitMemoAmount;
}
