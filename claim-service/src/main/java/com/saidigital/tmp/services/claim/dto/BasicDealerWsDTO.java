package com.saidigital.tmp.services.claim.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BasicDealerWsDTO {

    @JsonProperty("name")
    private String name;

    @JsonProperty("displayName")
    private String displayName;

    // Hide these fields from JSON output to match legacy format
    @JsonIgnore
    private String code;

    @JsonIgnore
    private String uid;


}
