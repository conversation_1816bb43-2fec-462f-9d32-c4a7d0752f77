package com.saidigital.tmp.services.claim.service;

import com.saidigital.tmp.services.claim.dto.*;
import com.saidigital.tmp.services.claim.enums.RebateClaimStatus;
import com.saidigital.tmp.services.claim.model.RebateClaim;
import com.saidigital.tmp.services.claim.repository.TmpRebateClaimRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Transactional(readOnly = true)
public class TmpRebateClaimService {

    @Autowired
    private TmpRebateClaimRepository rebateClaimRepository;

    @Autowired
    private DtoBuilderService dtoBuilderService;

    /**
     * Get all rebate claims with pagination and filtering
     *
     * @param dealer     Optional dealer filter
     * @param status     Optional status filter
     * @param pagination Pagination parameters
     * @return Paginated rebate claims response
     */
    public RebateClaimListWsDTO getAllRebateClaims(
            String dealer,
            String status,
            PageableWsDTO pagination) {

        // Parse status parameter
        RebateClaimStatus rebateClaimStatus = null;
        if (StringUtils.hasText(status)) {
            try {
                rebateClaimStatus = RebateClaimStatus.valueOf(status.toUpperCase());
            } catch (IllegalArgumentException e) {
                // Invalid status, will be ignored in query
            }
        }

        // Create pageable with sorting
        Pageable pageable = createPageable(pagination.getCurrentPage(), pagination.getPageSize());

        // Execute query with filters
        Page<RebateClaim> claimsPage = rebateClaimRepository.findAllRebateClaimsWithFilters(
                dealer, rebateClaimStatus, pageable);

        // Convert to DTOs
        List<ClaimListItemWsDTO> claimDTOs = claimsPage.getContent()
                .stream()
                .map(this::convertToClaimListItemWsDTO)
                .collect(Collectors.toList());

        // Create response
        return RebateClaimListWsDTO.createWithPagination(
                claimDTOs,
                claimsPage.getNumber(),
                claimsPage.getSize(),
                claimsPage.getTotalElements());
    }

    /**
     * Convert RebateClaim entity to ClaimListItemWsDTO with complex nested DTOs
     */
    private ClaimListItemWsDTO convertToClaimListItemWsDTO(RebateClaim claim) {
        return ClaimListItemWsDTO.builder()
                .id(claim.getCode())
                .referenceNumber(claim.getReferenceNumber())
                .dealer(BasicDealerWsDTO.fromDealerCode(claim.getDealer()))
                .claimableAmount(PriceDataWsDTO.fromDouble(claim.getClaimableAmount()))
                .status(EnumWsDTO.fromRebateClaimStatus(claim.getStatus()))
                .updatedBy(BasicEmployeeDataWsDTO.fromEmployeeId(claim.getUpdatedBy()))
                .createDate(claim.getCreatedTime())
                .updatedDate(claim.getUpdatedTime())
                .build();
    }

    /**
     * Create Pageable object with proper sorting
     */
    private Pageable createPageable(int page, int size) {
        // Default sort by creation time descending (matching legacy behavior)
        return PageRequest.of(page, size);
    }

}
