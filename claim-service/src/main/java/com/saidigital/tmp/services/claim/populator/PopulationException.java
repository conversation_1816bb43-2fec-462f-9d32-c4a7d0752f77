package com.saidigital.tmp.services.claim.populator;

/**
 * Exception thrown when population fails
 */
public class PopulationException extends RuntimeException {
    
    private final String populatorName;
    private final Class<?> sourceType;
    private final Class<?> targetType;
    
    public PopulationException(String message) {
        super(message);
        this.populatorName = null;
        this.sourceType = null;
        this.targetType = null;
    }
    
    public PopulationException(String message, Throwable cause) {
        super(message, cause);
        this.populatorName = null;
        this.sourceType = null;
        this.targetType = null;
    }
    
    public PopulationException(String message, String populatorName, Class<?> sourceType, Class<?> targetType) {
        super(message);
        this.populatorName = populatorName;
        this.sourceType = sourceType;
        this.targetType = targetType;
    }
    
    public PopulationException(String message, Throwable cause, String populatorName, Class<?> sourceType, Class<?> targetType) {
        super(message, cause);
        this.populatorName = populatorName;
        this.sourceType = sourceType;
        this.targetType = targetType;
    }
    
    public String getPopulatorName() {
        return populatorName;
    }
    
    public Class<?> getSourceType() {
        return sourceType;
    }
    
    public Class<?> getTargetType() {
        return targetType;
    }
    
    @Override
    public String getMessage() {
        StringBuilder sb = new StringBuilder(super.getMessage());
        if (populatorName != null) {
            sb.append(" [Populator: ").append(populatorName).append("]");
        }
        if (sourceType != null && targetType != null) {
            sb.append(" [").append(sourceType.getSimpleName())
              .append(" -> ").append(targetType.getSimpleName()).append("]");
        }
        return sb.toString();
    }
}
