package com.saidigital.tmp.services.claim.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import org.hibernate.annotations.IdGeneratorType;

import com.saidigital.tmp.services.claim.generator.ClaimCodeGenerator;

@IdGeneratorType(ClaimCodeGenerator.class)
@Target({ ElementType.FIELD })
@Retention(RetentionPolicy.RUNTIME)
public @interface ClaimGeneratedCode {

}
