package com.saidigital.tmp.services.claim.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BasicEmployeeDataWsDTO {

    @JsonProperty("uid")
    private String uid;

    @JsonProperty("name")
    private String name;

    // Hide these fields from JSON output to match legacy format
    @JsonIgnore
    private String displayName;

    @JsonIgnore
    private String firstName;

    @JsonIgnore
    private String lastName;


}
