package com.saidigital.tmp.services.claim.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BasicEmployeeDataWsDTO {

    @JsonProperty("uid")
    private String uid;

    @JsonProperty("name")
    private String name;

    @JsonProperty("displayName")
    private String displayName;

    @JsonProperty("firstName")
    private String firstName;

    @JsonProperty("lastName")
    private String lastName;

    public static BasicEmployeeDataWsDTO fromEmployeeId(String employeeId) {
        if (employeeId == null || employeeId.trim().isEmpty()) {
            return null;
        }
        
        return BasicEmployeeDataWsDTO.builder()
                .uid(employeeId)
                .name(employeeId)
                .displayName(formatEmployeeDisplayName(employeeId))
                .firstName(extractFirstName(employeeId))
                .lastName(extractLastName(employeeId))
                .build();
    }

    private static String formatEmployeeDisplayName(String employeeId) {
        // This should ideally come from an employee service or lookup table
        // For now, we'll format it as the employee ID
        return employeeId;
    }

    private static String extractFirstName(String employeeId) {
        // This should ideally come from an employee service
        // For now, return the employee ID
        return employeeId;
    }

    private static String extractLastName(String employeeId) {
        // This should ideally come from an employee service
        // For now, return empty string
        return "";
    }
}
