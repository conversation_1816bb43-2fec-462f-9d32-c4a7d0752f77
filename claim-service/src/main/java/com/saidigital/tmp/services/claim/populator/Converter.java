package com.saidigital.tmp.services.claim.populator;

import java.util.List;

/**
 * Generic converter interface that orchestrates multiple populators to transform objects.
 * Similar to SAP Hybris Commerce's Converter pattern.
 * 
 * @param <SOURCE> the source object type
 * @param <TARGET> the target object type
 */
public interface Converter<SOURCE, TARGET> {
    
    /**
     * Convert a source object to a target object using registered populators.
     * 
     * @param source the source object
     * @return the converted target object
     * @throws PopulationException if conversion fails
     */
    TARGET convert(SOURCE source) throws PopulationException;
    
    /**
     * Convert a source object to a target object with a specific context.
     * 
     * @param source the source object
     * @param context the population context
     * @return the converted target object
     * @throws PopulationException if conversion fails
     */
    TARGET convert(SOURCE source, PopulationContext context) throws PopulationException;
    
    /**
     * Convert a list of source objects to a list of target objects.
     * 
     * @param sources the list of source objects
     * @return the list of converted target objects
     * @throws PopulationException if conversion fails
     */
    List<TARGET> convertAll(List<SOURCE> sources) throws PopulationException;
    
    /**
     * Convert a list of source objects to a list of target objects with a specific context.
     * 
     * @param sources the list of source objects
     * @param context the population context
     * @return the list of converted target objects
     * @throws PopulationException if conversion fails
     */
    List<TARGET> convertAll(List<SOURCE> sources, PopulationContext context) throws PopulationException;
    
    /**
     * Populate an existing target object with data from a source object.
     * 
     * @param source the source object
     * @param target the target object to populate
     * @throws PopulationException if population fails
     */
    void populate(SOURCE source, TARGET target) throws PopulationException;
    
    /**
     * Populate an existing target object with data from a source object with a specific context.
     * 
     * @param source the source object
     * @param target the target object to populate
     * @param context the population context
     * @throws PopulationException if population fails
     */
    void populate(SOURCE source, TARGET target, PopulationContext context) throws PopulationException;
    
    /**
     * Get the target class that this converter produces.
     * 
     * @return the target class
     */
    Class<TARGET> getTargetClass();
    
    /**
     * Get the source class that this converter accepts.
     * 
     * @return the source class
     */
    Class<SOURCE> getSourceClass();
}
