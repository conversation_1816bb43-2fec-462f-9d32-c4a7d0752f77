package com.saidigital.tmp.services.claim.service.provider.impl;

import com.saidigital.tmp.services.claim.service.provider.CurrencyDataProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * Default implementation of CurrencyDataProvider
 * This maintains backward compatibility with the current static method logic
 * Can be extended with configuration or external currency service integration
 */
@Component
public class DefaultCurrencyDataProvider implements CurrencyDataProvider {
    
    @Value("${tmp.claim.currency.default:PHP}")
    private String defaultCurrencyIso;
    
    @Override
    public CurrencyData getCurrencyData(String currencyIso) {
        String currency = StringUtils.hasText(currencyIso) ? currencyIso : defaultCurrencyIso;
        return createCurrencyData(currency);
    }
    
    @Override
    public CurrencyData getDefaultCurrency() {
        return createCurrencyData(defaultCurrencyIso);
    }
    
    @Override
    public String formatAmount(Double amount, String currencyIso) {
        if (amount == null) {
            return null;
        }
        
        CurrencyData currency = getCurrencyData(currencyIso);
        String format = "%." + currency.getDecimalPlaces() + "f";
        return String.format("%s" + format, currency.getSymbol(), amount);
    }
    
    private CurrencyData createCurrencyData(String currencyIso) {
        // TODO: Replace with actual currency service or configuration
        // For now, maintain backward compatibility with existing logic
        switch (currencyIso.toUpperCase()) {
            case "PHP":
                return new CurrencyData("PHP", "₱", "Philippine Peso", 2);
            case "USD":
                return new CurrencyData("USD", "$", "US Dollar", 2);
            case "EUR":
                return new CurrencyData("EUR", "€", "Euro", 2);
            default:
                return new CurrencyData(currencyIso, currencyIso + " ", currencyIso, 2);
        }
    }
}
