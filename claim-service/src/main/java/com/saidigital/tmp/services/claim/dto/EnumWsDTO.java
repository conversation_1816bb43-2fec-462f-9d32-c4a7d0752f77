package com.saidigital.tmp.services.claim.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.saidigital.tmp.services.claim.enums.RebateClaimStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EnumWsDTO {

    @JsonProperty("code")
    private String code;

    @JsonProperty("name")
    private String name;

    @JsonProperty("type")
    private String type;

    private static final Pattern WORD_BOUNDARY = Pattern.compile("\\b\\w");

    public static EnumWsDTO fromRebateClaimStatus(RebateClaimStatus status) {
        if (status == null) {
            return null;
        }

        return EnumWsDTO.builder()
                .code(status.name())
                .name(formatStatusName(status.name()))
                .type("RebateClaimStatus")
                .build();
    }

    public static EnumWsDTO fromEnum(Enum<?> enumValue, String enumType) {
        if (enumValue == null) {
            return null;
        }

        return EnumWsDTO.builder()
                .code(enumValue.name())
                .name(formatEnumName(enumValue.name()))
                .type(enumType)
                .build();
    }

    private static String formatStatusName(String statusCode) {
        String lower = statusCode.replace("_", " ").toLowerCase();
        return capitalizeWords(lower);
    }

    private static String formatEnumName(String enumName) {
        String lower = enumName.replace("_", " ").toLowerCase();
        return capitalizeWords(lower);
    }

    private static String capitalizeWords(String text) {
        Matcher matcher = WORD_BOUNDARY.matcher(text);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, matcher.group().toUpperCase());
        }
        matcher.appendTail(sb);
        return sb.toString();
    }
}
