package com.saidigital.tmp.services.claim.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.saidigital.tmp.services.claim.enums.RebateClaimStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EnumWsDTO {

    @JsonProperty("code")
    private String code;

    @JsonProperty("name")
    private String name;

    // Hide type field from JSON output to match legacy format
    @JsonIgnore
    private String type;



    private static String capitalizeWords(String text) {
        Matcher matcher = WORD_BOUNDARY.matcher(text);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, matcher.group().toUpperCase());
        }
        matcher.appendTail(sb);
        return sb.toString();
    }
}
