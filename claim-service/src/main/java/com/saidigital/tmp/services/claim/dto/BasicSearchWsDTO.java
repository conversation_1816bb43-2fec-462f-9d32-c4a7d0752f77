package com.saidigital.tmp.services.claim.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public abstract class BasicSearchWsDTO<T> {

    @JsonProperty("type")
    private String type;

    @JsonProperty("pagination")
    private PageableWsDTO pagination;

    @JsonProperty("sorts")
    private List<SortWsDTO> sorts;

    @JsonProperty("results")
    private List<T> results;

    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SortWsDTO {
        @JsonProperty("code")
        private String code;

        @JsonProperty("name")
        private String name;

        @JsonProperty("selected")
        private boolean selected;
    }
}
