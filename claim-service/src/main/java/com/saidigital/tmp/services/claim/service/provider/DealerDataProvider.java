package com.saidigital.tmp.services.claim.service.provider;

/**
 * Interface for providing dealer information
 */
public interface DealerDataProvider {
    
    /**
     * Get dealer information by dealer code
     * 
     * @param dealerCode the dealer code
     * @return dealer data or null if not found
     */
    DealerData getDealerByCode(String dealerCode);
    
    /**
     * Data class representing dealer information
     */
    class DealerData {
        private final String code;
        private final String name;
        private final String displayName;
        private final String uid;
        
        public DealerData(String code, String name, String displayName, String uid) {
            this.code = code;
            this.name = name;
            this.displayName = displayName;
            this.uid = uid;
        }
        
        public String getCode() { return code; }
        public String getName() { return name; }
        public String getDisplayName() { return displayName; }
        public String getUid() { return uid; }
    }
}
