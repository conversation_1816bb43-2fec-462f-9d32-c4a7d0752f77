package com.saidigital.tmp.services.claim.service.provider.impl;

import com.saidigital.tmp.services.claim.service.provider.EmployeeDataProvider;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * Default implementation of EmployeeDataProvider
 * This maintains backward compatibility with the current static method logic
 * Can be replaced with actual employee service integration
 */
@Component
public class DefaultEmployeeDataProvider implements EmployeeDataProvider {
    
    @Override
    public EmployeeData getEmployeeById(String employeeId) {
        if (!StringUtils.hasText(employeeId)) {
            return null;
        }
        
        // TODO: Replace with actual employee service call
        // For now, maintain backward compatibility with existing logic
        return new EmployeeData(
            employeeId,
            employeeId, // name = employeeId for backward compatibility
            formatEmployeeDisplayName(employeeId),
            extractFirstName(employeeId),
            extractLastName(employeeId)
        );
    }
    
    private String formatEmployeeDisplayName(String employeeId) {
        // This should ideally come from an employee service or lookup table
        // For now, we'll format it as the employee ID to maintain backward compatibility
        return employeeId;
    }
    
    private String extractFirstName(String employeeId) {
        // This should ideally come from an employee service
        // For now, return the employee ID to maintain backward compatibility
        return employeeId;
    }
    
    private String extractLastName(String employeeId) {
        // This should ideally come from an employee service
        // For now, return empty string to maintain backward compatibility
        return "";
    }
}
