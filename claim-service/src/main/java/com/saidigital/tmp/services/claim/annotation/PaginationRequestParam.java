package com.saidigital.tmp.services.claim.annotation;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Parameters({
        @Parameter(name = "pageSize", description = "The maximum number of elements in the result list.",
                schema = @Schema(type = "string", defaultValue = "10"), in = ParameterIn.QUERY),
        @Parameter(name = "currentPage", description = "The requested page number",
                schema = @Schema(type = "string", defaultValue = "0"), in = ParameterIn.QUERY),
        @Parameter(name = "sort", description = "The string field the results will be sorted with",
                schema = @Schema(type = "string"), in = ParameterIn.QUERY)
})
public @interface PaginationRequestParam {
}
