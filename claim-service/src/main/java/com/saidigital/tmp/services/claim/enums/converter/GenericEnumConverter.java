package com.saidigital.tmp.services.claim.enums.converter;

import java.util.stream.Stream;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

@Converter(autoApply = true)
public class GenericEnumConverter<E extends Enum<E>> implements AttributeConverter<E, String> {

    private Class<E> enumClass;

    // Default constructor is required by JPA
    public GenericEnumConverter() {}

    public GenericEnumConverter(Class<E> enumClass) {
        this.enumClass = enumClass;
    }


    @Override
    public String convertToDatabaseColumn(E attribute) {
        if (attribute == null) {
            return null;
        }
        return attribute.name();
    }

    @Override
    public E convertToEntityAttribute(String dbData) {
       if (dbData == null) {
            return null;
        }
        return Stream.of(enumClass.getEnumConstants())
            .filter(e -> e.name().equals(dbData))
            .findFirst()
            .orElseThrow(IllegalArgumentException::new);
    }
}