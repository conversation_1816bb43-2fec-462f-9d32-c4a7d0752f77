package com.saidigital.tmp.services.claim.dto;

import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.saidigital.tmp.services.claim.enums.RebateClaimStatus;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RebateClaimResponseDTO {

    @JsonProperty("id")
    private String code;

    @JsonProperty("status")
    private StatusDTO status;

    @JsonProperty("claimableAmount")
    private Double claimableAmount;

    @JsonProperty("dealer")
    private String dealer;

    @JsonProperty("createdDate")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Date createdDate;

    @JsonProperty("lastUpdatedDate")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Date lastUpdatedDate;

    @JsonProperty("lastUpdatedBy")
    private String lastUpdatedBy;

    @JsonProperty("referenceNumber")
    private String referenceNumber;

    @JsonProperty("dmcmReference")
    private String dmcmReference;

    @JsonProperty("sbReference")
    private String sbReference;

    @JsonProperty("sapDocNo")
    private String sapDocNo;

    @JsonProperty("clearingDocNo")
    private String clearingDocNo;

    @JsonProperty("orNumber")
    private String orNumber;

    @JsonProperty("exported")
    private Boolean exported;

    @JsonProperty("requiredDoc")
    private String requiredDoc;

    @JsonProperty("generalConfigVersion")
    private Integer generalConfigVersion;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StatusDTO {
        @JsonProperty("code")
        private String code;

        @JsonProperty("name")
        private String name;

        private static final Pattern WORD_BOUNDARY = Pattern.compile("\\b\\w");

        public static StatusDTO fromEnum(RebateClaimStatus status) {
            if (status == null) {
                return null;
            }
            return StatusDTO.builder()
                    .code(status.name())
                    .name(formatStatusName(status.name()))
                    .build();
        }

        private static String formatStatusName(String statusCode) {
            String lower = statusCode.replace("_", " ").toLowerCase();
            Matcher matcher = WORD_BOUNDARY.matcher(lower);
            StringBuffer sb = new StringBuffer();
            while (matcher.find()) {
                matcher.appendReplacement(sb, matcher.group().toUpperCase());
            }
            matcher.appendTail(sb);
            return sb.toString();
        }
    }
}
