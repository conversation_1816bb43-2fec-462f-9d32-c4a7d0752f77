package com.saidigital.tmp.services.claim.service.provider;

/**
 * Interface for providing currency and formatting information
 */
public interface CurrencyDataProvider {
    
    /**
     * Get currency information
     * 
     * @param currencyIso the currency ISO code (optional, uses default if null)
     * @return currency data
     */
    CurrencyData getCurrencyData(String currencyIso);
    
    /**
     * Get default currency
     * 
     * @return default currency data
     */
    CurrencyData getDefaultCurrency();
    
    /**
     * Format amount with currency
     * 
     * @param amount the amount to format
     * @param currencyIso the currency ISO code
     * @return formatted amount string
     */
    String formatAmount(Double amount, String currencyIso);
    
    /**
     * Data class representing currency information
     */
    class CurrencyData {
        private final String currencyIso;
        private final String symbol;
        private final String displayName;
        private final int decimalPlaces;
        
        public CurrencyData(String currencyIso, String symbol, String displayName, int decimalPlaces) {
            this.currencyIso = currencyIso;
            this.symbol = symbol;
            this.displayName = displayName;
            this.decimalPlaces = decimalPlaces;
        }
        
        public String getCurrencyIso() { return currencyIso; }
        public String getSymbol() { return symbol; }
        public String getDisplayName() { return displayName; }
        public int getDecimalPlaces() { return decimalPlaces; }
    }
}
