package com.saidigital.tmp.services.claim.populator.impl;

import com.saidigital.tmp.services.claim.dto.PriceDataWsDTO;
import com.saidigital.tmp.services.claim.populator.AbstractConfigurablePopulator;
import com.saidigital.tmp.services.claim.populator.PopulationContext;
import com.saidigital.tmp.services.claim.populator.PopulatorComponent;
import com.saidigital.tmp.services.claim.service.provider.CurrencyDataProvider;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Populator that converts a Double amount to PriceDataWsDTO.
 * This replaces the static factory method approach with a flexible populator pattern.
 */
@PopulatorComponent(
    sourceType = Double.class,
    targetType = PriceDataWsDTO.class,
    priority = 100,
    description = "Populates PriceDataWsDTO from Double amount"
)
public class DoubleToPriceDataWsDTOPopulator extends AbstractConfigurablePopulator<Double, PriceDataWsDTO> {
    
    @Autowired
    private CurrencyDataProvider currencyDataProvider;
    
    public DoubleToPriceDataWsDTOPopulator() {
        super(Double.class, PriceDataWsDTO.class);
    }
    
    @Override
    public void populate(Double amount, PriceDataWsDTO target, PopulationContext context) {
        if (amount == null) {
            return;
        }
        
        try {
            // Get currency from context or use default
            String currencyIso = getCurrencyFromContext(context);
            CurrencyDataProvider.CurrencyData currencyData = currencyDataProvider.getCurrencyData(currencyIso);
            
            // Always populate basic fields
            target.setValue(String.format("%.2f", amount));
            target.setCurrencyIso(currencyData.getCurrencyIso());
            
            // Conditionally populate additional fields based on context
            if (shouldIncludeFormattedValue(context)) {
                String formattedValue = currencyDataProvider.formatAmount(amount, currencyIso);
                target.setFormattedValue(formattedValue);
            }
            
            if (shouldIncludePriceType(context)) {
                target.setPriceType(getPriceTypeFromContext(context));
            }
            
        } catch (Exception e) {
            handlePopulationError("Failed to populate price data for amount: " + amount, e);
        }
    }
    
    @Override
    public String[] getSupportedViewTypes() {
        return new String[]{
            PopulationContext.VIEW_TYPE_LIST,
            PopulationContext.VIEW_TYPE_DETAIL,
            PopulationContext.VIEW_TYPE_SUMMARY,
            PopulationContext.VIEW_TYPE_EXPORT
        };
    }
    
    @Override
    protected int getDefaultPriority() {
        return 100;
    }
    
    /**
     * Get currency ISO code from context or use default
     */
    private String getCurrencyFromContext(PopulationContext context) {
        return context.getAttribute("currencyIso", String.class)
                .orElse(currencyDataProvider.getDefaultCurrency().getCurrencyIso());
    }
    
    /**
     * Get price type from context or use default
     */
    private String getPriceTypeFromContext(PopulationContext context) {
        return context.getAttribute("priceType", String.class, "BUY");
    }
    
    /**
     * Determine if formatted value should be included
     */
    private boolean shouldIncludeFormattedValue(PopulationContext context) {
        // Include formatted value for detail view or export
        return context.isDetailView() || 
               PopulationContext.VIEW_TYPE_EXPORT.equals(context.getViewType()) ||
               PopulationContext.DETAIL_LEVEL_FULL.equals(context.getDetailLevel());
    }
    
    /**
     * Determine if price type should be included
     */
    private boolean shouldIncludePriceType(PopulationContext context) {
        // Include price type for detail view or when explicitly requested
        return context.isDetailView() || 
               context.hasAttribute("includePriceType") ||
               PopulationContext.DETAIL_LEVEL_FULL.equals(context.getDetailLevel());
    }
}
