package com.saidigital.tmp.services.claim.model;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@Table(name = "rental_car_receivables")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Entity
public class RentalCarReceivable extends Receivable {

    //TODO: Implement the VehicleProduct class
    @Column(name = "model")
    private String model;

    @Column(name = "plate_number")
    private String plateNumber;

    @Column(name = "cs_number")
    private String csNumber;

    //TODO: Implement the Customer class
    @Column(name = "customer")
    private String customer;

    @Column(name = "submission_date")
    private Date submissionDate;

    @Column(name = "dm_number")
    private String dmNumber;

    @Column(name = "rfp_number")
    private String rfpNumber;

    @Column(name = "rfp_description")
    private String rfpDescription;

    @Column(name = "total_amount")
    private Double totalAmount;
}
