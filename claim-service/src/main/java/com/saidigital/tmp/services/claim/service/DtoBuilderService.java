package com.saidigital.tmp.services.claim.service;

import com.saidigital.tmp.services.claim.dto.*;
import com.saidigital.tmp.services.claim.enums.RebateClaimStatus;
import com.saidigital.tmp.services.claim.populator.Converter;
import com.saidigital.tmp.services.claim.populator.ConverterFactory;
import com.saidigital.tmp.services.claim.populator.PopulationContext;
import com.saidigital.tmp.services.claim.service.provider.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Generic DTO builder service that separates data transformation logic from DTOs
 * This service provides flexible mapping strategies and can be extended for different business cases
 */
@Service
public class DtoBuilderService {
    
    @Autowired
    private DealerDataProvider dealerDataProvider;
    
    @Autowired
    private EmployeeDataProvider employeeDataProvider;
    
    @Autowired
    private CurrencyDataProvider currencyDataProvider;
    
    @Autowired
    private EnumFormattingProvider enumFormattingProvider;
    
    /**
     * Build BasicDealerWsDTO from dealer code
     * 
     * @param dealerCode the dealer code
     * @return BasicDealerWsDTO or null if dealer code is invalid
     */
    public BasicDealerWsDTO buildDealerDto(String dealerCode) {
        DealerDataProvider.DealerData dealerData = dealerDataProvider.getDealerByCode(dealerCode);
        if (dealerData == null) {
            return null;
        }
        
        return BasicDealerWsDTO.builder()
                .name(dealerData.getName())
                .displayName(dealerData.getDisplayName())
                .code(dealerData.getCode())
                .uid(dealerData.getUid())
                .build();
    }
    
    /**
     * Build BasicEmployeeDataWsDTO from employee ID
     * 
     * @param employeeId the employee ID
     * @return BasicEmployeeDataWsDTO or null if employee ID is invalid
     */
    public BasicEmployeeDataWsDTO buildEmployeeDto(String employeeId) {
        EmployeeDataProvider.EmployeeData employeeData = employeeDataProvider.getEmployeeById(employeeId);
        if (employeeData == null) {
            return null;
        }
        
        return BasicEmployeeDataWsDTO.builder()
                .uid(employeeData.getUid())
                .name(employeeData.getName())
                .displayName(employeeData.getDisplayName())
                .firstName(employeeData.getFirstName())
                .lastName(employeeData.getLastName())
                .build();
    }
    
    /**
     * Build PriceDataWsDTO from amount with default currency
     * 
     * @param amount the amount
     * @return PriceDataWsDTO or null if amount is null
     */
    public PriceDataWsDTO buildPriceDto(Double amount) {
        return buildPriceDto(amount, null);
    }
    
    /**
     * Build PriceDataWsDTO from amount and currency
     * 
     * @param amount the amount
     * @param currencyIso the currency ISO code (optional, uses default if null)
     * @return PriceDataWsDTO or null if amount is null
     */
    public PriceDataWsDTO buildPriceDto(Double amount, String currencyIso) {
        if (amount == null) {
            return null;
        }
        
        CurrencyDataProvider.CurrencyData currencyData = currencyDataProvider.getCurrencyData(currencyIso);
        String formattedValue = currencyDataProvider.formatAmount(amount, currencyIso);
        
        return PriceDataWsDTO.builder()
                .value(String.format("%.2f", amount))
                .currencyIso(currencyData.getCurrencyIso())
                .formattedValue(formattedValue)
                .priceType("BUY") // TODO: Make this configurable
                .build();
    }
    
    /**
     * Build EnumWsDTO from RebateClaimStatus
     * 
     * @param status the rebate claim status
     * @return EnumWsDTO or null if status is null
     */
    public EnumWsDTO buildStatusDto(RebateClaimStatus status) {
        return buildEnumDto(status, "RebateClaimStatus");
    }
    
    /**
     * Build EnumWsDTO from any enum
     * 
     * @param enumValue the enum value
     * @param enumType the enum type name
     * @return EnumWsDTO or null if enumValue is null
     */
    public EnumWsDTO buildEnumDto(Enum<?> enumValue, String enumType) {
        if (enumValue == null) {
            return null;
        }
        
        EnumFormattingProvider.EnumDisplayData displayData = 
            enumFormattingProvider.getEnumDisplayData(enumValue, enumType);
        
        return EnumWsDTO.builder()
                .code(displayData.getCode())
                .name(displayData.getName())
                .type(displayData.getType())
                .build();
    }
}
