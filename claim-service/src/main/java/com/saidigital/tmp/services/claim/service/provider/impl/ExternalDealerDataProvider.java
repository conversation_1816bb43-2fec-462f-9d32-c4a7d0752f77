package com.saidigital.tmp.services.claim.service.provider.impl;

import com.saidigital.tmp.services.claim.config.DtoMappingProperties;
import com.saidigital.tmp.services.claim.service.provider.DealerDataProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

/**
 * Example implementation that integrates with an external dealer service
 * This shows how to replace the default implementation with actual service integration
 * 
 * Activated when tmp.claim.dto.mapping.dealer.use-external-service=true
 */
@Component
@ConditionalOnProperty(name = "tmp.claim.dto.mapping.dealer.use-external-service", havingValue = "true")
public class ExternalDealerDataProvider implements DealerDataProvider {
    
    @Autowired
    private DtoMappingProperties mappingProperties;
    
    @Autowired
    private RestTemplate restTemplate;
    
    @Override
    public DealerData getDealerByCode(String dealerCode) {
        if (!StringUtils.hasText(dealerCode)) {
            return null;
        }
        
        try {
            // Example: Call external dealer service
            String serviceUrl = mappingProperties.getDealer().getServiceUrl();
            if (!StringUtils.hasText(serviceUrl)) {
                // Fallback to default behavior if service URL not configured
                return createDefaultDealerData(dealerCode);
            }
            
            // TODO: Replace with actual service call
            // ExternalDealerResponse response = restTemplate.getForObject(
            //     serviceUrl + "/dealers/" + dealerCode, 
            //     ExternalDealerResponse.class
            // );
            
            // For now, simulate external service response
            return simulateExternalServiceCall(dealerCode);
            
        } catch (Exception e) {
            // Log error and fallback to default behavior
            // logger.warn("Failed to fetch dealer data from external service for code: " + dealerCode, e);
            return createDefaultDealerData(dealerCode);
        }
    }
    
    private DealerData simulateExternalServiceCall(String dealerCode) {
        // Simulate different dealer data based on code
        switch (dealerCode) {
            case "0300":
                return new DealerData(dealerCode, "0300", "TOYOTA ALABANG, INC.", dealerCode);
            case "0030":
                return new DealerData(dealerCode, "0030", "TOYOTA MAKATI, INC.", dealerCode);
            default:
                return createDefaultDealerData(dealerCode);
        }
    }
    
    private DealerData createDefaultDealerData(String dealerCode) {
        String displayNameFormat = mappingProperties.getDealer().getDisplayNameFormat();
        String displayName = displayNameFormat.replace("{code}", dealerCode);
        
        return new DealerData(
            dealerCode,
            dealerCode,
            displayName,
            dealerCode
        );
    }
    
    // Example DTO for external service response
    public static class ExternalDealerResponse {
        private String code;
        private String name;
        private String displayName;
        private String address;
        private String contactNumber;
        
        // Getters and setters...
        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public String getDisplayName() { return displayName; }
        public void setDisplayName(String displayName) { this.displayName = displayName; }
        
        public String getAddress() { return address; }
        public void setAddress(String address) { this.address = address; }
        
        public String getContactNumber() { return contactNumber; }
        public void setContactNumber(String contactNumber) { this.contactNumber = contactNumber; }
    }
}
