package com.saidigital.tmp.services.claim.controller;

import com.saidigital.tmp.services.claim.annotation.PaginationRequestParam;
import com.saidigital.tmp.services.claim.constants.TmpClaimServiceConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.saidigital.tmp.services.claim.dto.ClaimListItemWsDTO;
import com.saidigital.tmp.services.claim.dto.PageableWsDTO;
import com.saidigital.tmp.services.claim.dto.RebateClaimListWsDTO;
import com.saidigital.tmp.services.claim.service.RebateClaimService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;

@RestController
@RequestMapping("/portal/claim")
@Tag(name = "Rebate Claims", description = "Rebate Claims Management API")
public class RebateClaimController {

    @Autowired
    private RebateClaimService rebateClaimService;

    @Value("${tmp.claim.portal.claims.pagesize:30}")
    private int defaultPageSize;

    @GetMapping(value = "/rebate-claims", produces = {MediaType.APPLICATION_JSON_VALUE})
    /*@Operation(summary = "Get paginated list of rebate claims with filter", security = {@SecurityRequirement(name = "oauth2_client_credentials")})*/
    @PaginationRequestParam
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved rebate claims"),
        @ApiResponse(responseCode = "400", description = "Invalid request parameters"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    /*@Secured({TmpClaimServiceConstants.AUTHORIZATION_GROUP.ROLE_CASHIERGROUP, TmpClaimServiceConstants.AUTHORIZATION_GROUP.ROLE_MARKETINGGROUP,
            TmpClaimServiceConstants.AUTHORIZATION_GROUP.ROLE_TREASURYGROUP})*/
    @ResponseBody
    public ResponseEntity<RebateClaimListWsDTO> getRebateClaims(
            @Parameter(description = "Base site identifier", example = "tmp")
            @PathVariable String baseSiteId,

            @Parameter(description = "Dealer code filter", example = "0030")
            @RequestParam(name = "dealer", required = false, defaultValue = "") String dealer,

            @Parameter(description = "Status filter", example = "MARKETING_VALIDATED")
            @RequestParam(name = "status", required = false, defaultValue = "") String status,

            @Parameter(description = "Pagination parameters")
            @ModelAttribute PageableWsDTO pagination) {

        // Use default page size if not provided
        int pageSize = (pagination.getPageSize() > 0) ? pagination.getPageSize() : defaultPageSize;
        int page = Math.max(0, pagination.getCurrentPage());

        // Validate page parameters
        if (pageSize <= 0) {
            pageSize = defaultPageSize;
        }
        if (pageSize > 100) {
            pageSize = 100; // Maximum page size limit
        }

        // Update pagination object with validated values
        pagination.setCurrentPage(page);
        pagination.setPageSize(pageSize);

        RebateClaimListWsDTO response = rebateClaimService.getAllRebateClaims(
                dealer, status, pagination);

        return ResponseEntity.ok(response);
    }

    @GetMapping("/{baseSiteId}/{code}")
    @Secured({"CASHIERGROUP", "MARKETINGGROUP", "TREASURYGROUP"})
    @SecurityRequirement(name = "oauth2_client_credentials")
    @Operation(summary = "Get rebate claim by code",
               description = "Retrieve a specific rebate claim by its code")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved rebate claim"),
        @ApiResponse(responseCode = "404", description = "Rebate claim not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @ResponseBody
    public ResponseEntity<ClaimListItemWsDTO> getRebateClaimByCode(
            @Parameter(description = "Base site identifier", example = "tmp")
            @PathVariable String baseSiteId,
            @Parameter(description = "Rebate claim code", example = "0030-RC-12345")
            @PathVariable String code) {

        ClaimListItemWsDTO claim = rebateClaimService.getRebateClaimByCode(code);

        if (claim == null) {
            return ResponseEntity.notFound().build();
        }

        return ResponseEntity.ok(claim);
    }

    @GetMapping("/{baseSiteId}/count")
    @Secured({"CASHIERGROUP", "MARKETINGGROUP", "TREASURYGROUP"})
    @SecurityRequirement(name = "oauth2_client_credentials")
    @Operation(summary = "Get rebate claims count",
               description = "Get total count of rebate claims with optional filtering")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved count"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @ResponseBody
    public ResponseEntity<Long> getRebateClaimsCount(
            @Parameter(description = "Base site identifier", example = "tmp")
            @PathVariable String baseSiteId,
            @Parameter(description = "Dealer code filter", example = "0030")
            @RequestParam(name = "dealer", required = false, defaultValue = "") String dealer,

            @Parameter(description = "Status filter", example = "MARKETING_VALIDATED")
            @RequestParam(name = "status", required = false, defaultValue = "") String status) {

        long count = rebateClaimService.getTotalRebateClaimsCount(dealer, status);
        return ResponseEntity.ok(count);
    }
}
