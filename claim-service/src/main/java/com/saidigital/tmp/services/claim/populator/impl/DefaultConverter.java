package com.saidigital.tmp.services.claim.populator.impl;

import com.saidigital.tmp.services.claim.populator.*;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.Constructor;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Default implementation of the Converter interface that orchestrates multiple populators.
 * 
 * @param <SOURCE> the source object type
 * @param <TARGET> the target object type
 */
public class DefaultConverter<SOURCE, TARGET> implements Converter<SOURCE, TARGET> {
    
    private final Class<SOURCE> sourceType;
    private final Class<TARGET> targetType;
    
    @Autowired
    private PopulatorRegistry populatorRegistry;
    
    public DefaultConverter(Class<SOURCE> sourceType, Class<TARGET> targetType) {
        this.sourceType = sourceType;
        this.targetType = targetType;
    }
    
    @Override
    public TARGET convert(SOURCE source) throws PopulationException {
        return convert(source, new PopulationContext());
    }
    
    @Override
    public TARGET convert(SOURCE source, PopulationContext context) throws PopulationException {
        if (source == null) {
            return null;
        }
        
        TARGET target = createTargetInstance();
        populate(source, target, context);
        return target;
    }
    
    @Override
    public List<TARGET> convertAll(List<SOURCE> sources) throws PopulationException {
        return convertAll(sources, new PopulationContext());
    }
    
    @Override
    public List<TARGET> convertAll(List<SOURCE> sources, PopulationContext context) throws PopulationException {
        if (sources == null) {
            return null;
        }
        
        return sources.stream()
                .map(source -> convert(source, context))
                .collect(Collectors.toList());
    }
    
    @Override
    public void populate(SOURCE source, TARGET target) throws PopulationException {
        populate(source, target, new PopulationContext());
    }
    
    @Override
    public void populate(SOURCE source, TARGET target, PopulationContext context) throws PopulationException {
        if (source == null || target == null) {
            return;
        }
        
        List<ConfigurablePopulator<SOURCE, TARGET>> populators = getApplicablePopulators(context);
        
        for (ConfigurablePopulator<SOURCE, TARGET> populator : populators) {
            try {
                populator.populate(source, target, context);
            } catch (Exception e) {
                throw new PopulationException(
                    "Failed to populate using " + populator.getClass().getSimpleName(),
                    e,
                    populator.getClass().getSimpleName(),
                    sourceType,
                    targetType
                );
            }
        }
    }
    
    @Override
    public Class<TARGET> getTargetClass() {
        return targetType;
    }
    
    @Override
    public Class<SOURCE> getSourceClass() {
        return sourceType;
    }
    
    /**
     * Create a new instance of the target type.
     * 
     * @return new target instance
     * @throws PopulationException if instance creation fails
     */
    @SuppressWarnings("unchecked")
    private TARGET createTargetInstance() throws PopulationException {
        try {
            // Try to find a no-arg constructor
            Constructor<TARGET> constructor = targetType.getDeclaredConstructor();
            constructor.setAccessible(true);
            return constructor.newInstance();
        } catch (Exception e) {
            // Try to find a builder pattern
            try {
                Object builder = targetType.getDeclaredMethod("builder").invoke(null);
                return (TARGET) builder.getClass().getDeclaredMethod("build").invoke(builder);
            } catch (Exception builderException) {
                throw new PopulationException(
                    "Failed to create instance of " + targetType.getSimpleName() + 
                    ". No accessible no-arg constructor or builder pattern found.",
                    e
                );
            }
        }
    }
    
    /**
     * Get all applicable populators for the given context.
     * 
     * @param context the population context
     * @return list of applicable populators sorted by priority
     */
    @SuppressWarnings("unchecked")
    private List<ConfigurablePopulator<SOURCE, TARGET>> getApplicablePopulators(PopulationContext context) {
        return populatorRegistry.getPopulators(sourceType, targetType).stream()
                .filter(populator -> populator instanceof ConfigurablePopulator)
                .map(populator -> (ConfigurablePopulator<SOURCE, TARGET>) populator)
                .filter(populator -> populator.shouldApply(context))
                .sorted((p1, p2) -> Integer.compare(p1.getPriority(), p2.getPriority()))
                .collect(Collectors.toList());
    }
}
