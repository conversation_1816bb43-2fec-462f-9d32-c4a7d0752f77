package com.saidigital.tmp.services.claim.config;

import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

@Component
public class KafkaConsumer {
    
	@KafkaListener(topics = "tmp-claim", groupId = "tmp", containerFactory = "tmpKafkaListenerContainerFactory")
	public void consumeTask(String message) {
		System.out.println("Got message: " + message);
	}
}
