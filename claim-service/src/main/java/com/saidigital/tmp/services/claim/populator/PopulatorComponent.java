package com.saidigital.tmp.services.claim.populator;

import org.springframework.core.annotation.AliasFor;
import org.springframework.stereotype.Component;

import java.lang.annotation.*;

/**
 * Annotation to mark classes as populators for automatic discovery and registration.
 * This annotation extends Spring's @Component to enable automatic bean creation.
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Component
public @interface PopulatorComponent {
    
    /**
     * The value may indicate a suggestion for a logical component name,
     * to be turned into a Spring bean in case of an autodetected component.
     */
    @AliasFor(annotation = Component.class)
    String value() default "";
    
    /**
     * The source type that this populator handles.
     * Used for automatic registration and type checking.
     */
    Class<?> sourceType() default Object.class;
    
    /**
     * The target type that this populator produces.
     * Used for automatic registration and type checking.
     */
    Class<?> targetType() default Object.class;
    
    /**
     * The priority of this populator when multiple populators are chained.
     * Lower values indicate higher priority (executed first).
     */
    int priority() default 1000;
    
    /**
     * Whether this populator is enabled by default.
     * Can be overridden by configuration properties.
     */
    boolean enabled() default true;
    
    /**
     * The supported view types for this populator.
     * Empty array means all view types are supported.
     */
    String[] supportedViewTypes() default {};
    
    /**
     * The required user roles for this populator.
     * Empty array means no specific roles are required.
     */
    String[] requiredUserRoles() default {};
    
    /**
     * Description of what this populator does.
     * Used for documentation and debugging.
     */
    String description() default "";
}
