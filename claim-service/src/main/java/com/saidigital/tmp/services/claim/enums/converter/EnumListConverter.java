package com.saidigital.tmp.services.claim.enums.converter;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Converter(autoApply = true)
public class EnumListConverter<E extends Enum<E>> implements AttributeConverter<List<E>, String> {

    private Class<E> enumClass;

    public EnumListConverter() {}


    @Override
    public String convertToDatabaseColumn(List<E> attributes) {
        if (CollectionUtils.isEmpty(attributes)) {
            return StringUtils.EMPTY;
        }

        return attributes.stream().map(Enum::name)
                .collect(Collectors.joining(","));
    }

    @Override
    public List<E> convertToEntityAttribute(String dbData) {
        if (StringUtils.isEmpty(dbData)) {
            return Collections.emptyList();
        }
        return Arrays.stream(dbData.split(","))
                .map(String::trim)
                .map(name -> Enum.valueOf(enumClass, name))
                .collect(Collectors.toList());
    }
}