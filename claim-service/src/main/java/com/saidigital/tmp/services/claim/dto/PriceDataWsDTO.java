package com.saidigital.tmp.services.claim.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PriceDataWsDTO {

    @JsonProperty("value")
    private String value;

    @JsonProperty("currencyIso")
    private String currencyIso;

    // Hide these fields from JSON output to match legacy format
    @JsonIgnore
    private String formattedValue;

    @JsonIgnore
    private String priceType;

    public static PriceDataWsDTO fromDouble(Double amount) {
        if (amount == null) {
            return null;
        }
        
        return PriceDataWsDTO.builder()
                .value(String.format("%.2f", amount))
                .currencyIso("PHP") // Default currency, should be configurable
                .formattedValue(String.format("₱%.2f", amount))
                .priceType("BUY")
                .build();
    }

    public static PriceDataWsDTO fromDouble(Double amount, String currencyIso) {
        if (amount == null) {
            return null;
        }
        
        String symbol = getCurrencySymbol(currencyIso);
        
        return PriceDataWsDTO.builder()
                .value(String.format("%.2f", amount))
                .currencyIso(currencyIso != null ? currencyIso : "PHP")
                .formattedValue(String.format("%s%.2f", symbol, amount))
                .priceType("BUY")
                .build();
    }

    private static String getCurrencySymbol(String currencyIso) {
        if (currencyIso == null) {
            return "₱";
        }
        
        switch (currencyIso.toUpperCase()) {
            case "PHP":
                return "₱";
            case "USD":
                return "$";
            case "EUR":
                return "€";
            default:
                return currencyIso + " ";
        }
    }
}
