package com.saidigital.tmp.services.claim.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PriceDataWsDTO {

    @JsonProperty("value")
    private String value;

    @JsonProperty("currencyIso")
    private String currencyIso;

    // Hide these fields from JSON output to match legacy format
    @JsonIgnore
    private String formattedValue;

    @JsonIgnore
    private String priceType;


}
