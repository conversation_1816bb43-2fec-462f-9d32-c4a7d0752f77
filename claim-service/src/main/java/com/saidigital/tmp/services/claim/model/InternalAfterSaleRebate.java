package com.saidigital.tmp.services.claim.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@Table(name = "internal_after_sale_rebates")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Entity
public class InternalAfterSaleRebate extends Receivable {

    @Column(name = "promo_description")
    private String promoDescription;
    
    @Column(name = "debit_memo_amount")
    private Double debitMemoAmount;
}
