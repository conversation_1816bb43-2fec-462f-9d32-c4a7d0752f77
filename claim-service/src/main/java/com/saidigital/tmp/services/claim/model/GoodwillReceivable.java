package com.saidigital.tmp.services.claim.model;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@Table(name = "goodwill_receivables")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@SuperBuilder
//@NoArgsConstructor
@AllArgsConstructor
@Entity
public class GoodwillReceivable extends RentalCarReceivable {
}
