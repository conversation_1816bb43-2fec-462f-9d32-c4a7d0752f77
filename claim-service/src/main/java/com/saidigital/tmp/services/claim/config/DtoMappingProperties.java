package com.saidigital.tmp.services.claim.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Configuration properties for DTO mapping behavior
 */
@Component
@ConfigurationProperties(prefix = "tmp.claim.dto.mapping")
public class DtoMappingProperties {
    
    /**
     * Currency configuration
     */
    private Currency currency = new Currency();
    
    /**
     * Dealer configuration
     */
    private Dealer dealer = new Dealer();
    
    /**
     * Employee configuration
     */
    private Employee employee = new Employee();
    
    /**
     * Enum formatting configuration
     */
    private EnumFormatting enumFormatting = new EnumFormatting();
    
    public Currency getCurrency() { return currency; }
    public void setCurrency(Currency currency) { this.currency = currency; }
    
    public Dealer getDealer() { return dealer; }
    public void setDealer(Dealer dealer) { this.dealer = dealer; }
    
    public Employee getEmployee() { return employee; }
    public void setEmployee(Employee employee) { this.employee = employee; }
    
    public EnumFormatting getEnumFormatting() { return enumFormatting; }
    public void setEnumFormatting(EnumFormatting enumFormatting) { this.enumFormatting = enumFormatting; }
    
    public static class Currency {
        private String defaultCurrency = "PHP";
        private String defaultPriceType = "BUY";
        
        public String getDefaultCurrency() { return defaultCurrency; }
        public void setDefaultCurrency(String defaultCurrency) { this.defaultCurrency = defaultCurrency; }
        
        public String getDefaultPriceType() { return defaultPriceType; }
        public void setDefaultPriceType(String defaultPriceType) { this.defaultPriceType = defaultPriceType; }
    }
    
    public static class Dealer {
        private boolean useExternalService = false;
        private String serviceUrl;
        private String displayNameFormat = "Dealer {code}";
        
        public boolean isUseExternalService() { return useExternalService; }
        public void setUseExternalService(boolean useExternalService) { this.useExternalService = useExternalService; }
        
        public String getServiceUrl() { return serviceUrl; }
        public void setServiceUrl(String serviceUrl) { this.serviceUrl = serviceUrl; }
        
        public String getDisplayNameFormat() { return displayNameFormat; }
        public void setDisplayNameFormat(String displayNameFormat) { this.displayNameFormat = displayNameFormat; }
    }
    
    public static class Employee {
        private boolean useExternalService = false;
        private String serviceUrl;
        
        public boolean isUseExternalService() { return useExternalService; }
        public void setUseExternalService(boolean useExternalService) { this.useExternalService = useExternalService; }
        
        public String getServiceUrl() { return serviceUrl; }
        public void setServiceUrl(String serviceUrl) { this.serviceUrl = serviceUrl; }
    }
    
    public static class EnumFormatting {
        private boolean useBusinessRules = false;
        private String rulesConfigPath;
        
        public boolean isUseBusinessRules() { return useBusinessRules; }
        public void setUseBusinessRules(boolean useBusinessRules) { this.useBusinessRules = useBusinessRules; }
        
        public String getRulesConfigPath() { return rulesConfigPath; }
        public void setRulesConfigPath(String rulesConfigPath) { this.rulesConfigPath = rulesConfigPath; }
    }
}
