package com.saidigital.tmp.services.claim.model;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.saidigital.tmp.services.claim.enums.RebatePromoStatus;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.HashSet;
import java.util.Set;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "general_rebate_promos")
@Data
@ToString
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class GeneralRebatePromo extends BaseEntity {

    @Id
    @Column(nullable = false, unique = true)
    private String id;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    @Builder.Default
    private RebatePromoStatus status = RebatePromoStatus.INACTIVE;
    
    @Column
    private Integer version;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "generalRebatePromo", cascade = CascadeType.ALL, orphanRemoval = true)
    @JsonBackReference
    private Set<GeneralRebatePromoRange> ranges = new HashSet<>();

    //TODO: Dynamic field with attributeHandler="tmpGenerateVersion"
    @Transient
    private Integer versionDisplay;

    //TODO: Dynamic field with attributeHandler="tmpGenerateId"
    @Transient
    private String idDisplay;
}
