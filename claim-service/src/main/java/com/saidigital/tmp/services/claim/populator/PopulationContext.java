package com.saidigital.tmp.services.claim.populator;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * Context object that carries information about the population process.
 * This allows populators to behave differently based on the context.
 */
public class PopulationContext {
    
    public static final String VIEW_TYPE = "viewType";
    public static final String USER_ROLE = "userRole";
    public static final String LOCALE = "locale";
    public static final String INCLUDE_SENSITIVE_DATA = "includeSensitiveData";
    public static final String DETAIL_LEVEL = "detailLevel";
    
    // Predefined view types
    public static final String VIEW_TYPE_LIST = "list";
    public static final String VIEW_TYPE_DETAIL = "detail";
    public static final String VIEW_TYPE_SUMMARY = "summary";
    public static final String VIEW_TYPE_EXPORT = "export";
    
    // Predefined detail levels
    public static final String DETAIL_LEVEL_BASIC = "basic";
    public static final String DETAIL_LEVEL_FULL = "full";
    public static final String DETAIL_LEVEL_MINIMAL = "minimal";
    
    private final Map<String, Object> attributes = new HashMap<>();
    
    public PopulationContext() {
        // Default context
    }
    
    public PopulationContext(String viewType) {
        setAttribute(VIEW_TYPE, viewType);
    }
    
    public PopulationContext(Map<String, Object> attributes) {
        this.attributes.putAll(attributes);
    }
    
    /**
     * Set an attribute in the context
     */
    public PopulationContext setAttribute(String key, Object value) {
        attributes.put(key, value);
        return this;
    }
    
    /**
     * Get an attribute from the context
     */
    @SuppressWarnings("unchecked")
    public <T> Optional<T> getAttribute(String key, Class<T> type) {
        Object value = attributes.get(key);
        if (value != null && type.isAssignableFrom(value.getClass())) {
            return Optional.of((T) value);
        }
        return Optional.empty();
    }
    
    /**
     * Get an attribute with a default value
     */
    public <T> T getAttribute(String key, Class<T> type, T defaultValue) {
        return getAttribute(key, type).orElse(defaultValue);
    }
    
    /**
     * Check if an attribute exists
     */
    public boolean hasAttribute(String key) {
        return attributes.containsKey(key);
    }
    
    /**
     * Get the view type
     */
    public String getViewType() {
        return getAttribute(VIEW_TYPE, String.class, VIEW_TYPE_DETAIL);
    }
    
    /**
     * Get the user role
     */
    public Optional<String> getUserRole() {
        return getAttribute(USER_ROLE, String.class);
    }
    
    /**
     * Get the detail level
     */
    public String getDetailLevel() {
        return getAttribute(DETAIL_LEVEL, String.class, DETAIL_LEVEL_FULL);
    }
    
    /**
     * Check if sensitive data should be included
     */
    public boolean shouldIncludeSensitiveData() {
        return getAttribute(INCLUDE_SENSITIVE_DATA, Boolean.class, false);
    }
    
    /**
     * Check if this is a list view
     */
    public boolean isListView() {
        return VIEW_TYPE_LIST.equals(getViewType());
    }
    
    /**
     * Check if this is a detail view
     */
    public boolean isDetailView() {
        return VIEW_TYPE_DETAIL.equals(getViewType());
    }
    
    /**
     * Create a new context for list view
     */
    public static PopulationContext listView() {
        return new PopulationContext(VIEW_TYPE_LIST);
    }
    
    /**
     * Create a new context for detail view
     */
    public static PopulationContext detailView() {
        return new PopulationContext(VIEW_TYPE_DETAIL);
    }
    
    /**
     * Create a new context for summary view
     */
    public static PopulationContext summaryView() {
        return new PopulationContext(VIEW_TYPE_SUMMARY);
    }
    
    /**
     * Create a builder for complex contexts
     */
    public static Builder builder() {
        return new Builder();
    }
    
    public static class Builder {
        private final Map<String, Object> attributes = new HashMap<>();
        
        public Builder viewType(String viewType) {
            attributes.put(VIEW_TYPE, viewType);
            return this;
        }
        
        public Builder userRole(String userRole) {
            attributes.put(USER_ROLE, userRole);
            return this;
        }
        
        public Builder detailLevel(String detailLevel) {
            attributes.put(DETAIL_LEVEL, detailLevel);
            return this;
        }
        
        public Builder includeSensitiveData(boolean include) {
            attributes.put(INCLUDE_SENSITIVE_DATA, include);
            return this;
        }
        
        public Builder attribute(String key, Object value) {
            attributes.put(key, value);
            return this;
        }
        
        public PopulationContext build() {
            return new PopulationContext(attributes);
        }
    }
}
