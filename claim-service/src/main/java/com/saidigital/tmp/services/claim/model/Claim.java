package com.saidigital.tmp.services.claim.model;

import com.saidigital.tmp.services.claim.annotation.ClaimGeneratedCode;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Inheritance;
import jakarta.persistence.InheritanceType;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@EqualsAndHashCode(callSuper = true)
@Table(name = "claims")
@Inheritance(strategy = InheritanceType.TABLE_PER_CLASS)
@Data
@ToString
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Entity
public class Claim extends BaseEntity {
	@Id
	@Column(nullable = false, unique = true)
	@ClaimGeneratedCode
	private String code;

	@Column(name = "created_by")
	private String createdBy;

	@Column(name = "updated_by")
	private String updatedBy;

	@Column(name = "dmcm_reference")
	private String dmcmReference;

	@Column(name = "reference_number")
	private String referenceNumber;

	@Column(name = "sb_reference")
	private String sbReference;

	@Column(name = "sap_doc_no")
	private String sapDocNo;

	@Column(name = "clearing_doc_no")
	private String clearingDocNo;

	@Column(name = "return_reason")
	private String returnReason;

	@Column(name = "exported")
	@Builder.Default
	private boolean exported = false;

	@Column(name = "posted_accounting_exported")
	@Builder.Default
	private boolean postedAccountingExported = false;

	@Column(name = "or_number")
	private String orNumber;

	@Column(name = "dealer")
	private String dealer;

	@Column(name = "claimable_amount")
	private Double claimableAmount;

	@Column(name = "required_doc")
	private String requiredDoc;
}
