spring:
  config:
    import: "optional:configserver:http://config-service:8088"
    activate:
      on-profile: docker
---
spring:
  application:
    name: claim-service
  config:
    import: "optional:configserver:http://localhost:8088"

---
spring:
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: http://***********:8180/realms/tmp-claim
  datasource:
    url: ********************************************
    username: tmp_claim
    password: 123dzo!
    driver-class-name: org.postgresql.Driver
  jpa:
      database-platform: org.hibernate.dialect.PostgreSQLDialect
      hibernate:
        ddl-auto: update
      show-sql: true
      properties:
        hibernate:
          format_sql: true
          enable_lazy_load_no_trans: true
  sql:
      init:
        mode: always
  kafka:
    bootstrap-servers: ***********:9092
keycloak:
  realm: tmp-claim
  client:
    client-id: tmp-claim-master
    client-secret: 7M3jO4RUEfbpUtd9bz0zA9H8WvUeUbOx
zipkin:
  tracing:
    endpoint: http://***********:9411/api/v2/spans
    
claim:
  code:
    prefix: CLM

tmp:
  claim:
    dto:
      mapping:
        currency:
          default-currency: "PHP"
          default-price-type: "BUY"
        dealer:
          use-external-service: false
          service-url: "http://dealer-service/api"
          display-name-format: "Dealer {code}"
        employee:
          use-external-service: false
          service-url: "http://employee-service/api"
        enum-formatting:
          use-business-rules: false
          rules-config-path: "classpath:enum-formatting-rules.yml"