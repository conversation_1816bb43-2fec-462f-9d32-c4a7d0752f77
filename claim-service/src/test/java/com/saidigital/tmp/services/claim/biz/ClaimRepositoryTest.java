package com.saidigital.tmp.services.claim.biz;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.annotation.Order;

import com.saidigital.tmp.services.claim.enums.ReceivableClaimStatus;
import com.saidigital.tmp.services.claim.model.Claim;
import com.saidigital.tmp.services.claim.repository.ClaimRepository;
import com.saidigital.tmp.services.claim.repository.ReceivableClaimRepository;

@SpringBootTest(properties = { "eureka.client.enabled=false",
        "spring.datasource.url=*************************************************" })
public class ClaimRepositoryTest {
    @Autowired
    private ClaimRepository claimRepository;

    @Autowired
    private ReceivableClaimRepository receivableClaimRepository;

    @Test
    @DisplayName("Test 1")
    @Order(1)
//    @Rollback(value = false)
    public void saveClaimTest() {
        Claim claim = Claim.builder().dealer("0030").build();
        claimRepository.save(claim);
    }

    @Test
    @DisplayName("Test 2")
    @Order(2)
    public void saveReceivableClaimTest() {
        receivableClaimRepository.findByStatus(ReceivableClaimStatus.FOR_FUND_TRANSFER).stream()
                .forEach(System.out::println);
    }
}
