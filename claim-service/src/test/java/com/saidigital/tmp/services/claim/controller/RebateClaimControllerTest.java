package com.saidigital.tmp.services.claim.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.util.Arrays;
import java.util.Date;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import com.saidigital.tmp.services.claim.dto.BasicDealerWsDTO;
import com.saidigital.tmp.services.claim.dto.BasicEmployeeDataWsDTO;
import com.saidigital.tmp.services.claim.dto.ClaimListItemWsDTO;
import com.saidigital.tmp.services.claim.dto.EnumWsDTO;
import com.saidigital.tmp.services.claim.dto.PageableWsDTO;
import com.saidigital.tmp.services.claim.dto.PriceDataWsDTO;
import com.saidigital.tmp.services.claim.dto.RebateClaimListWsDTO;
import com.saidigital.tmp.services.claim.enums.RebateClaimStatus;
import com.saidigital.tmp.services.claim.service.RebateClaimService;

@WebMvcTest(RebateClaimController.class)
public class RebateClaimControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private RebateClaimService rebateClaimService;

    @Test
    public void testGetRebateClaims_Success() throws Exception {
        // Prepare test data
        ClaimListItemWsDTO claim1 = ClaimListItemWsDTO.builder()
                .id("0030-CLM-001")
                .status(EnumWsDTO.fromRebateClaimStatus(RebateClaimStatus.MARKETING_VALIDATED))
                .claimableAmount(PriceDataWsDTO.fromDouble(1000.0))
                .dealer(BasicDealerWsDTO.fromDealerCode("0030"))
                .createDate(new Date())
                .updatedDate(new Date())
                .updatedBy(BasicEmployeeDataWsDTO.fromEmployeeId("testuser"))
                .build();

        ClaimListItemWsDTO claim2 = ClaimListItemWsDTO.builder()
                .id("0030-CLM-002")
                .status(EnumWsDTO.fromRebateClaimStatus(RebateClaimStatus.RFP_PROCESSING))
                .claimableAmount(PriceDataWsDTO.fromDouble(2000.0))
                .dealer(BasicDealerWsDTO.fromDealerCode("0030"))
                .createDate(new Date())
                .updatedDate(new Date())
                .updatedBy(BasicEmployeeDataWsDTO.fromEmployeeId("testuser"))
                .build();

        RebateClaimListWsDTO mockResponse = RebateClaimListWsDTO.createWithPagination(
                Arrays.asList(claim1, claim2),
                0, // currentPage
                30, // pageSize
                2L, // totalResults
                "createdTime,desc" // sort
        );

        // Mock service call
        when(rebateClaimService.getAllRebateClaims(anyString(), anyString(), any(PageableWsDTO.class)))
                .thenReturn(mockResponse);

        // Perform request and verify response
        mockMvc.perform(get("/api/rebate-claims/tmp")
                .param("dealer", "0030")
                .param("status", "MARKETING_VALIDATED")
                .param("currentPage", "0")
                .param("pageSize", "30")
                .param("sort", "createdTime,desc")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.type").value("rebateClaimListWsDTO"))
                .andExpect(jsonPath("$.pagination.currentPage").value(0))
                .andExpect(jsonPath("$.pagination.pageSize").value(30))
                .andExpect(jsonPath("$.pagination.totalResults").value(2))
                .andExpect(jsonPath("$.results").isArray())
                .andExpect(jsonPath("$.results.length()").value(2))
                .andExpect(jsonPath("$.results[0].id").value("0030-CLM-001"))
                .andExpect(jsonPath("$.results[0].dealer.name").value("0030"))
                .andExpect(jsonPath("$.results[0].claimableAmount.value").value("1000.00"))
                .andExpect(jsonPath("$.results[0].status.code").value("MARKETING_VALIDATED"));
    }

    @Test
    public void testGetRebateClaimByCode_Success() throws Exception {
        // Prepare test data
        ClaimListItemWsDTO claim = ClaimListItemWsDTO.builder()
                .id("0030-CLM-001")
                .status(EnumWsDTO.fromRebateClaimStatus(RebateClaimStatus.MARKETING_VALIDATED))
                .claimableAmount(PriceDataWsDTO.fromDouble(1000.0))
                .dealer(BasicDealerWsDTO.fromDealerCode("0030"))
                .createDate(new Date())
                .updatedDate(new Date())
                .updatedBy(BasicEmployeeDataWsDTO.fromEmployeeId("testuser"))
                .build();

        // Mock service call
        when(rebateClaimService.getRebateClaimByCode("0030-CLM-001")).thenReturn(claim);

        // Perform request and verify response
        mockMvc.perform(get("/api/rebate-claims/tmp/0030-CLM-001")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").value("0030-CLM-001"))
                .andExpect(jsonPath("$.dealer.name").value("0030"))
                .andExpect(jsonPath("$.claimableAmount.value").value("1000.00"))
                .andExpect(jsonPath("$.status.code").value("MARKETING_VALIDATED"));
    }

    @Test
    public void testGetRebateClaimByCode_NotFound() throws Exception {
        // Mock service call to return null
        when(rebateClaimService.getRebateClaimByCode("nonexistent")).thenReturn(null);

        // Perform request and verify 404 response
        mockMvc.perform(get("/api/rebate-claims/tmp/nonexistent")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());
    }

    @Test
    public void testGetRebateClaimsCount_Success() throws Exception {
        // Mock service call
        when(rebateClaimService.getTotalRebateClaimsCount(anyString(), anyString())).thenReturn(5L);

        // Perform request and verify response
        mockMvc.perform(get("/api/rebate-claims/tmp/count")
                .param("dealer", "0030")
                .param("status", "MARKETING_VALIDATED")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(content().string("5"));
    }

    @Test
    public void testGetRebateClaims_WithDefaultParameters() throws Exception {
        // Prepare test data
        RebateClaimListWsDTO mockResponse = RebateClaimListWsDTO.create(
                Arrays.asList(),
                0, // currentPage
                30, // pageSize (default)
                0L, // totalResults
                null // sort
        );

        // Mock service call
        when(rebateClaimService.getAllRebateClaims(anyString(), anyString(), any(PageableWsDTO.class)))
                .thenReturn(mockResponse);

        // Perform request without parameters
        mockMvc.perform(get("/api/rebate-claims/tmp")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.type").value("rebateClaimListWsDTO"))
                .andExpect(jsonPath("$.pagination.currentPage").value(0))
                .andExpect(jsonPath("$.pagination.pageSize").value(30));
    }
}
