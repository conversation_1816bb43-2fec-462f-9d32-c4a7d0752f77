# Populator System Architecture

## Overview

This document describes the comprehensive populator and converter system implemented in the claim-service, similar to SAP Hybris Commerce's data transformation patterns. The system provides a flexible, maintainable, and extensible approach to data transformation between different object types.

## Architecture Components

### 1. Core Interfaces

#### Populator<SOURCE, TARGET>
- Basic interface for data transformation
- Provides `populate(source, target)` method
- Supports priority-based ordering
- Includes type checking capabilities

#### ConfigurablePopulator<SOURCE, TARGET>
- Extended interface with context-aware population
- Supports conditional application based on business rules
- Configurable via application properties
- Context-sensitive data transformation

#### Converter<SOURCE, TARGET>
- Orchestrates multiple populators
- Handles object creation and population
- Supports batch conversion
- Context-aware conversion strategies

### 2. Population Context System

#### PopulationContext
- Carries contextual information during population
- Supports different view types (list, detail, summary, export)
- User role-based population
- Detail level control (basic, full, minimal)
- Custom attributes for specific use cases

#### Predefined Contexts
```java
// List view - minimal data for performance
PopulationContext.listView()

// Detail view - full data for comprehensive display
PopulationContext.detailView()

// Summary view - balanced data for overview
PopulationContext.summaryView()

// Custom context with specific attributes
PopulationContext.builder()
    .viewType("custom")
    .userRole("ADMIN")
    .detailLevel("full")
    .attribute("includeMetadata", true)
    .build()
```

### 3. Automatic Discovery and Registration

#### @PopulatorComponent Annotation
```java
@PopulatorComponent(
    sourceType = String.class,
    targetType = BasicDealerWsDTO.class,
    priority = 100,
    description = "Converts dealer code to dealer DTO",
    supportedViewTypes = {"list", "detail"},
    requiredUserRoles = {"USER", "ADMIN"}
)
public class DealerCodePopulator extends AbstractConfigurablePopulator<String, BasicDealerWsDTO> {
    // Implementation
}
```

#### PopulatorRegistry
- Automatically discovers all populators
- Caches populators by type combinations
- Provides debugging and monitoring capabilities
- Supports runtime populator management

### 4. Configuration-Driven Behavior

#### Application Properties
```yaml
tmp:
  claim:
    populators:
      dealercodetobasicdealerwsdtopopulator:
        enabled: true
        priority: 100
      # Other populator configurations
```

#### Dynamic Enable/Disable
- Populators can be enabled/disabled via configuration
- Priority can be overridden
- Context-specific behavior control

## Implementation Examples

### 1. Basic Populator Implementation

```java
@PopulatorComponent(
    sourceType = String.class,
    targetType = BasicDealerWsDTO.class,
    priority = 100
)
public class DealerCodeToBasicDealerWsDTOPopulator 
    extends AbstractConfigurablePopulator<String, BasicDealerWsDTO> {
    
    @Autowired
    private DealerDataProvider dealerDataProvider;
    
    @Override
    public void populate(String dealerCode, BasicDealerWsDTO target, PopulationContext context) {
        DealerData dealerData = dealerDataProvider.getDealerByCode(dealerCode);
        
        target.setName(dealerData.getName());
        target.setDisplayName(dealerData.getDisplayName());
        
        // Context-aware population
        if (context.isDetailView()) {
            target.setCode(dealerData.getCode());
            target.setUid(dealerData.getUid());
        }
    }
}
```

### 2. Context-Aware Service Usage

```java
@Service
public class ContextAwareDtoService {
    
    @Autowired
    private DtoBuilderService dtoBuilderService;
    
    public BasicDealerWsDTO buildDealerForListView(String dealerCode) {
        PopulationContext context = PopulationContext.builder()
            .viewType(PopulationContext.VIEW_TYPE_LIST)
            .detailLevel(PopulationContext.DETAIL_LEVEL_MINIMAL)
            .build();
        
        return dtoBuilderService.buildDealerDto(dealerCode, context);
    }
    
    public BasicDealerWsDTO buildDealerForDetailView(String dealerCode) {
        PopulationContext context = PopulationContext.builder()
            .viewType(PopulationContext.VIEW_TYPE_DETAIL)
            .detailLevel(PopulationContext.DETAIL_LEVEL_FULL)
            .build();
        
        return dtoBuilderService.buildDealerDto(dealerCode, context);
    }
}
```

### 3. Converter Factory Usage

```java
@Service
public class MyService {
    
    @Autowired
    private ConverterFactory converterFactory;
    
    public BasicDealerWsDTO convertDealer(String dealerCode) {
        Converter<String, BasicDealerWsDTO> converter = 
            converterFactory.getConverter(String.class, BasicDealerWsDTO.class);
        
        return converter.convert(dealerCode, PopulationContext.detailView());
    }
}
```

## Benefits

### 1. Separation of Concerns
- Data transformation logic separated from DTOs
- Business rules isolated in populators
- Context-specific behavior encapsulated

### 2. Flexibility and Extensibility
- Easy to add new populators without changing existing code
- Context-driven population strategies
- Configuration-based behavior control

### 3. Type Safety
- Compile-time type checking
- Generic interfaces ensure type consistency
- Runtime type validation

### 4. Performance Optimization
- Context-aware population reduces unnecessary data loading
- Cached converters for performance
- Lazy population strategies

### 5. Testability
- Each populator can be tested independently
- Mock-friendly architecture
- Context-based testing scenarios

### 6. Maintainability
- Clear separation of responsibilities
- Configuration-driven behavior
- Comprehensive debugging capabilities

## Migration from Static Methods

### Before (Static Factory Methods)
```java
// Old approach
BasicDealerWsDTO dealer = BasicDealerWsDTO.fromDealerCode(dealerCode);
PriceDataWsDTO price = PriceDataWsDTO.fromDouble(amount);
```

### After (Populator Pattern)
```java
// New approach
@Autowired
private DtoBuilderService dtoBuilderService;

BasicDealerWsDTO dealer = dtoBuilderService.buildDealerDto(dealerCode);
PriceDataWsDTO price = dtoBuilderService.buildPriceDto(amount);

// With context
BasicDealerWsDTO dealer = dtoBuilderService.buildDealerDto(dealerCode, PopulationContext.listView());
```

## Monitoring and Debugging

### Populator Information Endpoint
```java
@RestController
public class PopulatorInfoController {
    
    @Autowired
    private PopulatorRegistry populatorRegistry;
    
    @GetMapping("/admin/populators")
    public List<PopulatorInfo> getPopulatorInfo() {
        return populatorRegistry.getPopulatorInfo();
    }
}
```

### Configuration Validation
- Automatic validation of populator configurations
- Runtime monitoring of populator performance
- Debug logging for population processes

## Best Practices

1. **Use Context Appropriately**: Choose the right context for your use case
2. **Keep Populators Focused**: Each populator should have a single responsibility
3. **Configure Wisely**: Use configuration to control behavior, not hardcode
4. **Test Thoroughly**: Test populators with different contexts
5. **Monitor Performance**: Use appropriate detail levels for performance
6. **Document Populators**: Provide clear descriptions for debugging

## Future Enhancements

1. **Caching Layer**: Add caching for expensive data lookups
2. **Async Population**: Support asynchronous population for performance
3. **Validation Integration**: Integrate with validation frameworks
4. **Metrics Collection**: Add metrics for population performance
5. **Dynamic Rules**: Support dynamic business rules for population
