package com.saidigital.tmp.services.messaging.business.sendnotification.email.adapter.javamail;

import com.saidigital.tmp.services.messaging.business.sendnotification.email.domain.EmailTemplateContent;
import com.saidigital.tmp.services.messaging.business.shared.domain.event.SendingEmailNotification;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import freemarker.template.TemplateExceptionHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;

import java.io.IOException;
import java.io.StringReader;

@Service
public class NotificationJavaMailAdapter {

    @Autowired
    private JavaMailSender mailSender;


    @Autowired
    private Configuration freemarkerConfig;


    @Async
    @EventListener
    public void handleSendingEmailNotification(SendingEmailNotification event) {
        var from = event.from();
        var to = event.to();
        var content = event.content();
        var message = simpleMailMessage(from, to, content);
        mailSender.send(message);
    }

    private SimpleMailMessage simpleMailMessage(String from, String to, EmailTemplateContent content) {
        SimpleMailMessage message = new SimpleMailMessage();
        message.setFrom(from);
        message.setTo(to);
        message.setSubject(content.subject());

        String emailContent = processTemplateEmailContent(content);

        message.setText(emailContent);
        return message;
    }

    private static String processTemplateEmailContent(EmailTemplateContent content) {
        try {
            Configuration cfg = new Configuration(Configuration.VERSION_2_3_34);
            cfg.setDefaultEncoding("UTF-8");
            cfg.setTemplateExceptionHandler(TemplateExceptionHandler.RETHROW_HANDLER);
            String templateName = "%s/%s/%s".formatted(content.tenantId(), content.templateId(), content.locale());
            Template template = new Template(templateName, new StringReader(content.body()), cfg);
            return FreeMarkerTemplateUtils.processTemplateIntoString(template, content.substitutes());
        } catch (IOException | TemplateException e) {
            throw new RuntimeException(e);
        }
    }
}
