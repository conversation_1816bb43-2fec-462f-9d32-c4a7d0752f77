server:
  port: 8084
---
spring:
  config:
    import: "optional:configserver:http://config-service:8088"
    activate:
      on-profile: docker
---
spring:
  application:
    name: messaging-service
  config:
    import: "optional:configserver:http://localhost:8088"

---
spring:
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: http://***********:8180/realms/tmp-claim
#logging:
#  level:
#    org.springframework.security: TRACE
#    org.springframework.web: DEBUG
---
spring:
  datasource:
    url: ************************************************
    username: tmp_claim
    password: 123dzo!
    driver-class-name: org.postgresql.Driver
  jpa:
    hibernate:
      ddl-auto: update
---
spring:
  mail:
    host: smtp.gmail.com
    port: 587
    username: <EMAIL>
    password: dzxlqobfmwerrjqd
    properties:
      mail.smtp.auth: true
      mail.smtp.starttls.enable: true

zipkin:
  tracing:
    endpoint: http://***********:9411/api/v2/spans