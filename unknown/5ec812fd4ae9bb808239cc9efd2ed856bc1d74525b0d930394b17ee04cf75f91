package com.saidigital.tmp.services.claim.service;

import com.saidigital.tmp.services.claim.dto.*;
import com.saidigital.tmp.services.claim.enums.RebateClaimStatus;
import com.saidigital.tmp.services.claim.populator.PopulationContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Service that provides context-aware DTO building capabilities.
 * This service demonstrates how to use different population contexts for different use cases.
 */
@Service
public class ContextAwareDtoService {
    
    @Autowired
    private DtoBuilderService dtoBuilderService;
    
    /**
     * Build DTOs for list view (minimal information)
     */
    public BasicDealerWsDTO buildDealerForListView(String dealerCode) {
        PopulationContext context = PopulationContext.builder()
                .viewType(PopulationContext.VIEW_TYPE_LIST)
                .detailLevel(PopulationContext.DETAIL_LEVEL_MINIMAL)
                .build();
        
        return dtoBuilderService.buildDealerDto(dealerCode, context);
    }
    
    /**
     * Build DTOs for detail view (full information)
     */
    public BasicDealerWsDTO buildDealerForDetailView(String dealerCode) {
        PopulationContext context = PopulationContext.builder()
                .viewType(PopulationContext.VIEW_TYPE_DETAIL)
                .detailLevel(PopulationContext.DETAIL_LEVEL_FULL)
                .build();
        
        return dtoBuilderService.buildDealerDto(dealerCode, context);
    }
    
    /**
     * Build DTOs for export (includes formatted values)
     */
    public PriceDataWsDTO buildPriceForExport(Double amount, String currencyIso) {
        PopulationContext context = PopulationContext.builder()
                .viewType(PopulationContext.VIEW_TYPE_EXPORT)
                .detailLevel(PopulationContext.DETAIL_LEVEL_FULL)
                .attribute("includePriceType", true)
                .build();
        
        return dtoBuilderService.buildPriceDto(amount, currencyIso, context);
    }
    
    /**
     * Build DTOs for specific user role
     */
    public BasicEmployeeDataWsDTO buildEmployeeForRole(String employeeId, String userRole) {
        PopulationContext context = PopulationContext.builder()
                .viewType(PopulationContext.VIEW_TYPE_DETAIL)
                .userRole(userRole)
                .detailLevel(PopulationContext.DETAIL_LEVEL_FULL)
                .includeSensitiveData("ADMIN".equals(userRole))
                .build();
        
        return dtoBuilderService.buildEmployeeDto(employeeId, context);
    }
    
    /**
     * Build status DTO with type information for admin users
     */
    public EnumWsDTO buildStatusForAdmin(RebateClaimStatus status) {
        PopulationContext context = PopulationContext.builder()
                .viewType(PopulationContext.VIEW_TYPE_DETAIL)
                .userRole("ADMIN")
                .detailLevel(PopulationContext.DETAIL_LEVEL_FULL)
                .attribute("includeEnumType", true)
                .build();
        
        return dtoBuilderService.buildStatusDto(status, context);
    }
    
    /**
     * Build status DTO without type information for regular users
     */
    public EnumWsDTO buildStatusForUser(RebateClaimStatus status) {
        PopulationContext context = PopulationContext.builder()
                .viewType(PopulationContext.VIEW_TYPE_LIST)
                .detailLevel(PopulationContext.DETAIL_LEVEL_BASIC)
                .build();
        
        return dtoBuilderService.buildStatusDto(status, context);
    }
    
    /**
     * Build a complete claim item DTO for list view
     */
    public ClaimListItemWsDTO buildClaimItemForListView(String claimId, String dealerCode, 
                                                        Double amount, RebateClaimStatus status, 
                                                        String updatedBy) {
        PopulationContext listContext = PopulationContext.listView();
        
        return ClaimListItemWsDTO.builder()
                .id(claimId)
                .dealer(dtoBuilderService.buildDealerDto(dealerCode, listContext))
                .claimableAmount(dtoBuilderService.buildPriceDto(amount, null, listContext))
                .status(dtoBuilderService.buildStatusDto(status, listContext))
                .updatedBy(dtoBuilderService.buildEmployeeDto(updatedBy, listContext))
                .build();
    }
    
    /**
     * Build a complete claim item DTO for detail view
     */
    public ClaimListItemWsDTO buildClaimItemForDetailView(String claimId, String dealerCode, 
                                                          Double amount, String currencyIso,
                                                          RebateClaimStatus status, String updatedBy) {
        PopulationContext detailContext = PopulationContext.detailView();
        
        return ClaimListItemWsDTO.builder()
                .id(claimId)
                .dealer(dtoBuilderService.buildDealerDto(dealerCode, detailContext))
                .claimableAmount(dtoBuilderService.buildPriceDto(amount, currencyIso, detailContext))
                .status(dtoBuilderService.buildStatusDto(status, detailContext))
                .updatedBy(dtoBuilderService.buildEmployeeDto(updatedBy, detailContext))
                .build();
    }
    
    /**
     * Build multiple claim items for list view
     */
    public List<ClaimListItemWsDTO> buildClaimItemsForListView(List<ClaimData> claimDataList) {
        PopulationContext listContext = PopulationContext.listView();
        
        return claimDataList.stream()
                .map(claimData -> ClaimListItemWsDTO.builder()
                        .id(claimData.getId())
                        .dealer(dtoBuilderService.buildDealerDto(claimData.getDealerCode(), listContext))
                        .claimableAmount(dtoBuilderService.buildPriceDto(claimData.getAmount(), null, listContext))
                        .status(dtoBuilderService.buildStatusDto(claimData.getStatus(), listContext))
                        .updatedBy(dtoBuilderService.buildEmployeeDto(claimData.getUpdatedBy(), listContext))
                        .build())
                .collect(Collectors.toList());
    }
    
    /**
     * Data class for claim information
     */
    public static class ClaimData {
        private String id;
        private String dealerCode;
        private Double amount;
        private RebateClaimStatus status;
        private String updatedBy;
        
        // Constructors, getters, and setters
        public ClaimData() {}
        
        public ClaimData(String id, String dealerCode, Double amount, RebateClaimStatus status, String updatedBy) {
            this.id = id;
            this.dealerCode = dealerCode;
            this.amount = amount;
            this.status = status;
            this.updatedBy = updatedBy;
        }
        
        // Getters and setters
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        
        public String getDealerCode() { return dealerCode; }
        public void setDealerCode(String dealerCode) { this.dealerCode = dealerCode; }
        
        public Double getAmount() { return amount; }
        public void setAmount(Double amount) { this.amount = amount; }
        
        public RebateClaimStatus getStatus() { return status; }
        public void setStatus(RebateClaimStatus status) { this.status = status; }
        
        public String getUpdatedBy() { return updatedBy; }
        public void setUpdatedBy(String updatedBy) { this.updatedBy = updatedBy; }
    }
}
