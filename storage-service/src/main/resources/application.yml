spring:
  config:
    import: "optional:configserver:http://config-service:8088"
    activate:
      on-profile: docker
---
spring:
  application:
    name: storage-service
  config:
    import: "optional:configserver:http://localhost:8088"
  r2dbc:
    pool:
      maxIdleTime: '30'
      maxSize: '400'
      initialSize: '60'
    username: tmp_claim
    password: 123dzo!
    url: r2dbc:postgresql://***********:5432/storage
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDia1lect
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        enable_lazy_load_no_trans: true
  sql:
    init:
      mode: always
  kafka:
    bootstrap-servers: ***********:9092
  webflux:
    multipart:
      max-in-memory-size: 100MB
      max-headers-size: 5KB
      max-parts: '3'
      max-disk-usage-per-part: 1024KB
file:
  upload:
    path: /Users/<USER>/tmp-claim/runtime/upload
zipkin:
  tracing:
    endpoint: http://***********:9411/api/v2/spans
storage:
  default:
    service: localFileStorage