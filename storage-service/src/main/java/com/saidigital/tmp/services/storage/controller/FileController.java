package com.saidigital.tmp.services.storage.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;

import com.saidigital.tmp.services.storage.message.ResponseMessage;
import com.saidigital.tmp.services.storage.model.FileRecord;
import com.saidigital.tmp.services.storage.model.type.Service;
import com.saidigital.tmp.services.storage.repository.FileRecordRepository;
import com.saidigital.tmp.services.storage.service.FileStorageProxyService;
import com.saidigital.tmp.services.storage.service.FileStorageService;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@RestController
public class FileController {

	private static final String SUCCESS = "SUCCESS";

	private static final String ERROR = "ERROR";

	private static final String NOT_FOUND = "NOT FOUND";

	@Autowired
	private FileRecordRepository fileRecordRepository;

	@Autowired
	private FileStorageProxyService fileStorageProxyService;

	@Autowired
	private FileStorageService fileStorageService;

	@PostMapping(value = "/file")
	public Mono<ResponseEntity<ResponseMessage>> uploadFile(@RequestPart("file") Mono<FilePart> filePart,
			@RequestParam Service service, @RequestParam String entity, @RequestParam String key1,
			@RequestParam(required = false, defaultValue = "#") String key2,
			@RequestHeader("Content-Length") long contentLength) {
		return fileStorageService.save(filePart).flatMap(fileInfo -> {
			FileRecord fileRecord = new FileRecord();
			fileRecord.setService(service);
			fileRecord.setEntity(entity);
			fileRecord.setKey1(key1);
			fileRecord.setKey2(key2);
			fileRecord.setFilename(fileInfo.getName());
			fileRecord.setFileUrl(fileInfo.getUrl());
			fileRecord.setSize(contentLength);
			fileRecord.setProtocol(fileStorageService.getProtocol());
			return fileRecordRepository.save(fileRecord);
		}).map((filename) -> ResponseEntity.ok().body(new ResponseMessage(SUCCESS)));
	}

	@GetMapping("/files")
	public Flux<FileRecord> findFiles(@RequestParam String service, @RequestParam String entity,
			@RequestParam String key1, @RequestParam(required = false, defaultValue = "#") String key2) {
		return fileRecordRepository.findByServiceAndEntityAndKey1AndKey2(Service.valueOf(service), entity, key1, key2);
	}

	@GetMapping("/file/{id}")
	public ResponseEntity<Flux<DataBuffer>> getFile(@PathVariable Long id, @RequestParam String filename) {
		Flux<DataBuffer> file = fileRecordRepository.findById(id)
				.flatMapMany(f -> fileStorageProxyService.getFileStorageService(f.getProtocol()).load(f.getFileUrl()));
		return ResponseEntity.ok().header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
				.contentType(MediaType.APPLICATION_OCTET_STREAM).body(file);
	}

	@DeleteMapping("/file/{id}")
	public Mono<ResponseEntity<ResponseMessage>> deleteFile(@PathVariable Long id) {
		return fileRecordRepository
				.findById(id).flatMap(fileRecord -> Mono.just(fileStorageProxyService
						.getFileStorageService(fileRecord.getProtocol()).delete(fileRecord.getFileUrl())))
				.flatMap(deleted -> {
					if (deleted) {
						return fileRecordRepository.deleteById(id)
								.map(v -> (ResponseEntity.ok().body(new ResponseMessage(String.format(SUCCESS)))));
					}
					return Mono.just(
							ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ResponseMessage(ERROR)));
				}).switchIfEmpty(Mono.just(ResponseEntity.ok().body(new ResponseMessage(NOT_FOUND))));
	}
}
