package com.saidigital.tmp.services.storage.model;

import java.time.LocalDateTime;

import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import com.saidigital.tmp.services.storage.model.type.Protocol;
import com.saidigital.tmp.services.storage.model.type.Service;

import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Table(name = "files")
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
public class FileRecord {

    @Column("id")
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column("service")
    @Enumerated(EnumType.STRING)
    private Service service;

    @Column("entity")
    private String entity;

    @Column("key_1")
    private String key1;

    @Column("key_2")
    private String key2;

    @Column("file_name")
    private String filename;

    @Column("file_url")
    private String fileUrl;

    @Column("size")
    private Long size;

    @Column("protocol")
    @Enumerated(EnumType.STRING)
    private Protocol protocol;
    
    @Column(value = "modified_time")
    private LocalDateTime modifiedTime;
}
