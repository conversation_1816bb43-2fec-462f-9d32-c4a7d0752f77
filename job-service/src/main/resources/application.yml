spring:
  config:
    import: "optional:configserver:http://config-service:8088"
    activate:
      on-profile: docker
---
spring:
  application:
    name: job-service
  config:
    import: "optional:configserver:http://localhost:8088"

---
spring:
  datasource:
    url: ******************************************
    username: tmp_claim
    password: 123dzo!
    driver-class-name: org.postgresql.Driver
  jpa:
      database-platform: org.hibernate.dialect.PostgreSQLDialect
      hibernate:
        ddl-auto: update
      show-sql: true
      properties:
        hibernate:
          format_sql: true
          enable_lazy_load_no_trans: true
  sql:
      init:
        mode: always
  kafka:
    bootstrap-servers: ***********:9092
  quartz:
    job-store-type: "jdbc"
    properties:
      org:
        quartz:
          dataSource:
            quartzDS:
              URL: *****************************************
              user: tmp_claim
              driver: org.postgresql.Driver
              password: 123dzo!
          scheduler:
            instanceId: AUTO
            instanceName: PostgresScheduler
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: '4'
          jobStore:
            dataSource: quartzDS
            driverDelegateClass: org.quartz.impl.jdbcjobstore.PostgreSQLDelegate
            class: org.quartz.impl.jdbcjobstore.JobStoreTX
zipkin:
  tracing:
    endpoint: http://***********:9411/api/v2/spans
org:
  quartz:
    dataSource:
      quartzDS:
        URL: *****************************************
        user: tmp_claim
        driver: org.postgresql.Driver
        password: 123dzo!
    scheduler:
      instanceId: AUTO
      instanceName: PostgresScheduler
    threadPool:
      class: org.quartz.simpl.SimpleThreadPool
      threadCount: '4'
    jobStore:
      dataSource: quartzDS
      driverDelegateClass: org.quartz.impl.jdbcjobstore.PostgreSQLDelegate
      class: org.quartz.impl.jdbcjobstore.JobStoreTX
