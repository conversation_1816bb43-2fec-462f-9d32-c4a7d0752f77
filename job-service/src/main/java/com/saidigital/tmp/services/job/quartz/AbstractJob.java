package com.saidigital.tmp.services.job.quartz;

import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.Scheduler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import com.google.common.base.Stopwatch;
import com.saidigital.tmp.services.job.model.JobModel;
import com.saidigital.tmp.services.job.repository.JobRepository;

//@PersistJobDataAfterExecution
//@DisallowConcurrentExecution
@Component
public abstract class AbstractJob implements Job {

	private static final Logger log = LoggerFactory.getLogger(AbstractJob.class);

	@Autowired
	protected Scheduler scheduler;

	@Autowired
	private JobRepository jobRepository;

	protected abstract void executeJob(JobModel aJob, JobExecutionContext context) throws Exception;

	@Override
	public void execute(JobExecutionContext context) throws JobExecutionException {
		JobModel aJob = null;
		try {
			aJob = jobRepository.getReferenceById(context.getJobDetail().getKey().toString().split("\\.")[1]);
			Assert.notNull(aJob, String.format("Job must exist [%s]", context.getJobDetail().getKey()));
			
			LocalDateTime date = LocalDateTime.now();
			aJob.setRunning(true);
			aJob.setCheckpointReset(true);
			aJob.setLastStartTime(date);
			jobRepository.save(aJob);

			Stopwatch stopwatch = Stopwatch.createStarted();
			executeJob(aJob, context);
			log.info(String.format("Job [%s] finished, duration: %d sec", aJob, stopwatch.elapsed(TimeUnit.SECONDS)));

			aJob.setLastStatus(true);
			aJob.setLastEndTime(LocalDateTime.now());
			aJob.setRunning(false);
			jobRepository.save(aJob);
		} catch (Exception e) {
			log.error(String.format("Error occurred while running job [%s]", aJob), e);
			if (aJob != null) {
				aJob.setLastStatus(false);
				aJob.setRunning(false);
				jobRepository.save(aJob);
			}
		}
	}
}
