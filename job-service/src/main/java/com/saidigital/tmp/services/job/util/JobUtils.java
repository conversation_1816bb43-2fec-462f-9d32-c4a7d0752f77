package com.saidigital.tmp.services.job.util;

import java.util.Map;

import org.quartz.CronScheduleBuilder;
import org.quartz.Job;
import org.quartz.JobBuilder;
import org.quartz.JobDataMap;
import org.quartz.JobDetail;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.Trigger;
import org.quartz.TriggerBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.google.gson.Gson;
import com.saidigital.tmp.services.job.model.JobModel;
import com.saidigital.tmp.services.job.quartz.AbstractJob;

@Service
public class JobUtils {

	private static final Logger log = LoggerFactory.getLogger(AbstractJob.class);
	
	private static final String JOB_BEAN_PREFIX = "job-bean-%s";

	@Autowired
	private ConfigurableApplicationContext configurableApplicationContext;

	@Autowired
	protected Scheduler scheduler;

	@Autowired
	protected Gson gson;

	public void scheduleJob(JobModel aJob) throws Exception {
		JobDetail scheduledJob = loadQuartzBean(aJob);
		JobDataMap jobDataMap = scheduledJob.getJobDataMap();
		Map<String, Object> jobParameters = gson.fromJson(aJob.getParameters(), Map.class);
		if (!CollectionUtils.isEmpty(jobParameters)) {
			jobDataMap.putAll(jobParameters);
		}
		Trigger trigger = TriggerBuilder.newTrigger().withIdentity(aJob.getCode(), aJob.getService().toString())
				.withSchedule(CronScheduleBuilder.cronSchedule((String) aJob.getCronExpression())).build();
		scheduler.scheduleJob(scheduledJob, trigger);
	}

	private JobDetail loadQuartzBean(JobModel aJob) throws Exception {
		String beanName = String.format(JOB_BEAN_PREFIX, aJob.getCode());
		if (configurableApplicationContext.containsBean(beanName)) {
			return (JobDetail) configurableApplicationContext.getBean(beanName);
		}
		log.debug(String.format("Job instance key [%s] not initialzied, initializing..", beanName));
		return registerQuartzJob(aJob);
	}

	@SuppressWarnings("unchecked")
	private synchronized JobDetail registerQuartzJob(JobModel aJob) throws Exception {
		String beanName = String.format(JOB_BEAN_PREFIX, aJob.getCode());
		if (configurableApplicationContext.containsBean(beanName)) {
			return (JobDetail) configurableApplicationContext.getBean(beanName);
		}
		JobDetail scheduledJob = JobBuilder.newJob((Class<Job>) Class.forName((String) aJob.getBean()))
				.withIdentity(aJob.getCode()).build();
		configurableApplicationContext.getBeanFactory().registerSingleton(beanName, scheduledJob);
		return (JobDetail) configurableApplicationContext.getBean(beanName);
	}
}
