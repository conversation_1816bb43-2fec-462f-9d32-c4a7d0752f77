package com.saidigital.tmp.services.job.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ZipkinConnectionDetails
        implements org.springframework.boot.actuate.autoconfigure.tracing.zipkin.ZipkinConnectionDetails {

	@Value("${zipkin.tracing.endpoint}")
	private String tracingEndpoint;

    @Override
    public String getSpanEndpoint() {
        return tracingEndpoint;
    }

}
