package com.saidigital.tmp.services.job.quartz.bean;

import org.quartz.JobExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

import com.saidigital.tmp.services.job.model.JobModel;
import com.saidigital.tmp.services.job.quartz.AbstractJob;

public class Job1 extends AbstractJob {

	@Autowired
	private ApplicationContext context;
	
	@Override
	public void executeJob(JobModel aJob, JobExecutionContext context) throws Exception {
		System.out.println("Cronjob triggered");
	}
}