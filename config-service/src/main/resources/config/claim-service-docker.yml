server:
  port: 8080

eureka:
  client:
    serviceUrl:
      defaultZone: http://discovery-service:8061/eureka/
  instance:
    hostname: claim-service
      
logging:
  pattern: 
    console: "%d{yyyy-MM-dd HH:mm:ss} [%X{traceId:-},%X{spanId:-}] ${LOG_LEVEL_PATTERN:-%5p} %m%n"

springdoc:
  packagesToScan: com.saidigital.tmp.services.claim

spring:
  output:
    ansi:
      enabled: always

management:
  tracing:
    sampling:
      probability: 1.0
  zipkin:
    tracing:
      endpoint: http://***********:9411/api/v2/spans