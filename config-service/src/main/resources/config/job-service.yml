server:
  port: 0
  forward-headers-strategy: framework

eureka:
  client:
    serviceUrl:
      defaultZone: http://localhost:8061/eureka/
  instance:
    instanceId: ${spring.application.name}:${spring.application.instance_id:${random.value}}
      
logging:
  pattern:
    console: "%clr(%d{yyyy-MM-dd HH:mm:ss}){blue} %clr([%X{traceId:-},%X{spanId:-}]){cyan} %clr(${LOG_LEVEL_PATTERN:-%5p}) %m%n"

springdoc:
  packagesToScan: com.saidigital.tmp.services.job
  cache:
    disabled: true
  version: '@springdoc.version@'
  api-docs:
    version: openapi_3_1

spring:
  output:
    ansi:
      enabled: always

management:
  tracing:
    sampling:
      probability: 1.0
  endpoints:
    web:
      exposure:
        include: health,sbom
