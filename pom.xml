<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.4.4</version>
        <relativePath />
    </parent>

    <groupId>com.saidigital.tmp.claim.services</groupId>
    <artifactId>tmp-claim-ms</artifactId>
    <version>1.1-SNAPSHOT</version>
    <packaging>pom</packaging>

    <properties>
        <java.version>21</java.version>
        <spring-cloud.version>2024.0.1</spring-cloud.version>
        <spring-openai-mvc.version>2.8.6</spring-openai-mvc.version>
        <spring-openai-flux.version>2.8.6</spring-openai-flux.version>
        <sonar.projectKey>tmp-claim-ms</sonar.projectKey>
        <sonar.organization>tmp</sonar.organization>
        <sonar.host.url>https://sonarcloud.io</sonar.host.url>
    </properties>

    <modules>
        <module>discovery-service</module>
        <module>config-service</module>
        <module>user-service</module>
        <module>gateway-service</module>
        <module>storage-service</module>
        <module>claim-service</module>
        <module>messaging-service</module>
        <module>job-service</module>
        <module>kafka-service</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.cyclonedx</groupId>
                <artifactId>cyclonedx-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>